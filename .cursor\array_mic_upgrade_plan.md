# 阵列麦克风固件升级功能 - 开发计划 (V3)

## 1. 任务目标

在当前 `systemsetting` App 的项目架构基础上，新增阵列麦克风（Array Microphone）的固件升级功能。此功能将通过 Android 系统执行 ADB 命令来完成对外部阵列麦克风设备的固件更新，并确保整个过程的健壮性和生命周期管理的完整性。

## 2. 核心思路与架构

本次开发将严格遵循项目现有的 `Service -> Manager -> Updater` 分层架构，确保新功能与现有代码（如触摸框升级、系统升级）逻辑分离、互不影响。

-   **架构复用**: 复用 `ota` 模块的设计模式，在 `com.eeo.ota` 包下创建全新的 `arraymic` 子包，用于存放本次任务的所有代码，实现高度内聚、低耦合。
-   **逻辑替换**: 将 `Updater` 内部的实现由“串口通信”替换为“**带有重试、超时和校验机制的、非阻塞的 ADB 命令执行逻辑**”。

## 3. 开发计划 (分步实施)

### 第 1 步：创建新的功能模块和文件结构

在 `ota/src/main/java/com/eeo/ota/` 路径下创建 `arraymic` 包，并规划以下核心类：

-   `ota/src/main/java/com/eeo/ota/arraymic/ArrayMicUpdateService.java`:
    -   **职责**: 后台服务，管理升级流程的生命周期。参考 `SubDeviceUpdateService.java` 实现。
-   `ota/src/main/java/com/eeo/ota/arraymic/ArrayMicUpdate.java`:
    -   **职责**: 升级业务的顶层管理器。负责协调版本检查和启动升级。参考 `SubDeviceUpdate.java` 实现。
-   `ota/src/main/java/com/eeo/ota/arraymic/ArrayMicFirmwareUpdater.java`:
    -   **职责**: 核心升级逻辑执行器。负责实现下文 **"第 2 步"** 中定义的完整升级流程。所有操作（如轮询、延时）都将采用**非阻塞方式**（`Handler.postDelayed`）实现，以防止 ANR。
-   `ota/src/main/java/com/eeo/ota/arraymic/AdbHelper.java`:
    -   **职责**: 封装 ADB 命令的执行和 USB 设备检测。提供 `executeShellCommand(command)`, `pushFile(localPath, remotePath)`, `getDeviceList()` 以及基于 `UsbManager` 的 `findDeviceByPidVid()` 等方法。

### 第 2 步：规划详细、健壮的固件升级流程

`ArrayMicFirmwareUpdater.java` 将严格按照 `adb_upgrade_cmd.txt` 和所有已知需求，实现以下**非阻塞**流程：

1.  **[进入升级模式] 切换 USB 通路**:
    -   调用 `sample_xml_usbsw s side SOC` 命令，将阵列麦克风的 USB 连接到 Android 主板。

2.  **[设备检测阶段] (非阻塞轮询)**:
    -   **检测 USB 设备 (PID/VID)**: 使用 `UsbManager` API 启动一个带重试和超时的轮询任务，检测 `PID/VID` 为 `2207:0019` 的设备。若超时未检测到，则认为失败，执行 **"第 6 步"** 的清理流程。
    -   **检测 ADB 设备**: USB 设备检测成功后，继续轮询 `adb devices`，直到发现序列号为 `303_usb_device` 的设备。若超时未检测到，则认为失败，执行 **"第 6 步"** 的清理流程。

3.  **[版本检查阶段]**:
    -   **读取配置文件**: 解析 `systemsetting/array_mic/mic_config.json`，获取目标版本号 (`targetVersion`) 和固件信息。
    -   **获取当前版本**: 执行 `adb -s 303_usb_device shell cat /usr/bin/qdreamer/qsound/version.txt` 获取 `currentVersion`。
    -   **决策**: 根据以下逻辑判断是否需要升级：
        -   `isVersionLower =` 解析 `currentVersion` 和 `targetVersion` (例如，去除'A'后比较数字大小) 的结果。
        -   `isSpecificErrorVersion = currentVersion.equals("QH303_QSOUND_20231110001")`。
        -   如果 `isVersionLower` 或 `isSpecificErrorVersion` 为 `true`，则继续升级。否则，认为无需升级，直接进入 **"第 6 步"** 的清理流程。

4.  **[执行升级阶段] (严格按序，带校验和重试)**:
    -   **4.1. 停止服务**: 执行 `adb shell /usr/bin/qdreamer/qsound/kill_sound.sh`。等待短暂延时。
    -   **4.2. 清理远程目录**: 执行 `adb shell rm -rf /mnt/UDISK/*`。等待短暂延时。
    -   **4.3. 推送并校验固件 (循环重试，最多3次)**:
        -   a. **推送**: 执行 `adb push <local_firmware_path> /mnt/UDISK/`。此步骤预留 **20秒** 执行时间。
        -   b. **校验**: 通过 `adb shell ls -l /mnt/UDISK/<firmware_file>` 获取远程文件大小，与本地固件文件大小进行比较。
        -   c. **决策**: 如果大小一致，则跳出循环。如果大小不一致或 `push` 失败，则执行 `adb shell rm -rf /mnt/UDISK/*`，然后继续下一次循环。若3次均失败，则认为升级失败，进入 **"第 6 步"** 的清理流程。
    -   **4.4. 删除旧用户数据**: 执行 `adb shell rm -rf /overlay/upper/usr/bin/qdreamer/*`。等待短暂延时。
    -   **4.5. 执行升级命令**: 执行 `adb shell swupdate_cmd.sh -i /mnt/UDISK/<firmware_file> -e stable,upgrade_recovery`。此命令将触发设备重启。

5.  **[重启验证阶段] (非阻塞轮询)**:
    -   **监控设备断开**: 轮询 `adb devices`，等待 `303_usb_device` 消失。
    -   **监控设备重连**: 设备断开后，继续轮询 `adb devices`，等待 `303_usb_device` 重新出现。整个重启过程预计总耗时约60秒，设置合理的总超时。
    -   **最终验证**: 设备重连后，再次执行 `cat version.txt` 获取新版本号，并与 `targetVersion` 对比。如果一致，则报告**升级成功**。否则报告**升级失败**。

6.  **[退出升级模式] 清理与收尾**:
    -   无论升级成功、失败或中途退出，都必须执行此步骤。
    -   调用 `sample_xml_usbsw s side PC` 命令，将阵列麦克风的 USB **切换回 Windows**。
    -   释放所有资源，通知服务升级流程结束。

### 第 3 步：集成升级入口

此部分计划不变。将参考 `dspupdate` 的集成方式，在 `systemsetting` 模块中通过 `BroadcastReceiver` 触发 `ArrayMicUpdateService`，从而启动整个升级流程。

---
**文档版本**: V3
**更新日期**: 2025-07-25
**主要变更**: 修正了升级命令执行顺序，明确了文件校验方案为比较文件大小，明确了USB设备检测方案为使用 `UsbManager` API。
