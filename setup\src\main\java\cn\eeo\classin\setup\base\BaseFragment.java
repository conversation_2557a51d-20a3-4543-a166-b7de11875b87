package cn.eeo.classin.setup.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.elvishew.xlog.XLog;

import butterknife.ButterKnife;
import butterknife.Unbinder;

public abstract class BaseFragment extends Fragment {
    private final String TAG = "BaseFragment";

    View view = null;
    private Unbinder unbinder;

    public BaseFragment(){

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        XLog.d(" onCreateView");
        if (view == null){
            view = LayoutInflater.from(getContext()).inflate(getLayout(),container,false);
            unbinder = ButterKnife.bind(this, view);
            initDate();
        }
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();

    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public abstract int getLayout();

    public abstract void initDate();
}
