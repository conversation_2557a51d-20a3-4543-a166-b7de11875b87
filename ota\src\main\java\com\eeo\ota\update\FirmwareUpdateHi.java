package com.eeo.ota.update;

import android.content.Context;
import android.os.IBinder;
import android.os.RecoverySystem;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.os.storage.IStorageManager;
import android.os.storage.VolumeInfo;
import android.util.Log;

import com.eeo.ota.callback.InstallListener;
import com.hisilicon.android.hisysmanager.HiSysManager;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;

/**
 * Created by liuxinxi on 2022-5-6 15:08:18
 * 海思升级方法
 */
public class FirmwareUpdateHi {
    private final static String TAG = "HiFirmwareUpdate";

    /**
     * 升级
     */
    public static void updateSystem(Context context, final String absolutePayloadPath, InstallListener installListener) {

        if (absolutePayloadPath == null || absolutePayloadPath.equals("")) {
            Log.d(TAG, "updateSystem Error!!! absolutePayloadPath is invalid.");
            if (installListener != null) {
                installListener.onInstallFail("update.zip path is invalid");
            }
            return;
        }

        if (absolutePayloadPath.endsWith("/")) {
            Log.d(TAG, "updateSystem Error!!! absolutePayloadPath is invalid. it is not a file.");
            if (installListener != null) {
                installListener.onInstallFail("update.zip path is invalid");
            }
            return;
        }

        String tempPath = "";
        if (absolutePayloadPath.contains("/storage/")) {
            String devicePoint = absolutePayloadPath.substring("/storage/".length());
            tempPath = "/mnt/media_rw/" + devicePoint;
            Log.d(TAG, "updateSystem update mntPoint to[" + tempPath + "]");
        }

        final String mntPoint = tempPath.substring(0, tempPath.lastIndexOf("/"));
        final String updateFileName = absolutePayloadPath.substring(absolutePayloadPath.lastIndexOf("/") + 1);
        Log.d(TAG, "updateSystem mntPoint[" + mntPoint + "] updateFileName [" + updateFileName + "]");

        HiSysManager hiSysManager = new HiSysManager();
        hiSysManager.upgrade(mntPoint);
        if (mntPoint.contains("sdcard") || mntPoint.contains("/storage/emulated/0")) {
            Log.d(TAG, "updateSystem is sdcard.");
        } else {
            Log.d(TAG, "updateSystem is not sdcard.");
            writeUuid(context, mntPoint);
        }
        Thread updateThread = new Thread() {
            @Override
            public void run() {
                try {
                    //verify package
                    RecoverySystem.verifyPackage(new File(absolutePayloadPath), new RecoverySystem.ProgressListener() {
                        @Override
                        public void onProgress(int progress) {
                            Log.d(TAG, "verifyPackage onProgress:" + progress);
                        }
                    }, null);

                    //install package
                    //反射调用自定义api
                    //RecoverySystem.installPackageWithSdcard(context, new File("/sdcard/" + updateFileName), mntPoint);
                    Method installPackageWithSdcard = RecoverySystem.class.getMethod("installPackageWithSdcard",
                            Context.class, File.class, String.class);
                    installPackageWithSdcard.setAccessible(true);
                    installPackageWithSdcard.invoke(null, context, new File("/sdcard/" + updateFileName), mntPoint);
                } catch (Exception e) {
                    Log.e(TAG, "update exception:" + e.toString());
                    if (installListener != null) {
                        installListener.onInstallFail(e.toString());
                    }
                }
            }
        };
        Executors.newSingleThreadExecutor().submit(updateThread);
    }

    private static void writeUuid(Context context, String path) {
        File updatePoint = new File(path);
        Log.d(TAG, "writeUuid updatePoint[" + updatePoint + "]");
        StringBuilder sb = new StringBuilder();
        Map<String, ArrayList<String>> map = new HashMap<>();
        map = getUuidList(context);
        Log.d(TAG, "uuidlist size =" + map.size());
        if (map.size() == 0) {
            return;
        }
        ArrayList<String> deviceInfoList = map.get(path);
        if (deviceInfoList == null) {
            Log.d(TAG, "deviceInfoList is null");
            return;
        }
        String uuid = deviceInfoList.get(0);
        String type = deviceInfoList.get(1);
        sb.append("UUID=\"" + uuid + "\"; TYPE=\"" + type + "\"");
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream("/cache/recovery/uuid");
            outputStream.write(sb.toString().getBytes());
            Log.d(TAG, "write success");
        } catch (FileNotFoundException e) {
            Log.d(TAG, "fileNotFound");
        } catch (IOException e) {
            Log.d(TAG, "IOException");
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "close outputStream failed: ");
                e.printStackTrace();
            }
        }
    }

    private static Map<String, ArrayList<String>> getUuidList(Context context) {
        IStorageManager service = getStorageManager();
        Map<String, ArrayList<String>> map = new HashMap<>();
        if (service == null) {
            return map;
        }
        try {
            VolumeInfo[] list = service.getVolumes(0);
            List<VolumeInfo> mountList = new ArrayList<VolumeInfo>();
            for (int i = 0; i < list.length; i++) {
                Log.d(TAG, "getUuidList list[i].path=" + list[i].internalPath);
                if (VolumeInfo.STATE_MOUNTED == list[i].state) { //means only add devices which state equals to mounted
                    mountList.add(list[i]);
                }
            }
            for (int i = 0; i < mountList.size(); i++) {
                ArrayList<String> uuidList = new ArrayList<String>();
                VolumeInfo volumeInfo = mountList.get(i);
                String uuid = volumeInfo.fsUuid;
                String type = volumeInfo.fsType;
                String internalPath = volumeInfo.internalPath;
                Log.d(TAG, "uuid =" + uuid + "  type =" + type);
                if (null == uuid || null == type) {
                    continue;
                }
                Log.d(TAG, "internalPath =" + internalPath);

                uuidList.add(uuid);
                uuidList.add(type);
                map.put(internalPath, uuidList);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return map;
    }

    public static IStorageManager getStorageManager() {
        IStorageManager mStorageManager = null;
        IBinder service = ServiceManager.getService("mount");
        if (service != null) {
            mStorageManager = IStorageManager.Stub.asInterface(service);
        }
        return mStorageManager;
    }
}
