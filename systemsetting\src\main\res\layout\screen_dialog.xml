<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/main_cv_width"
    android:layout_height="@dimen/main_cv_height"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.zyp.cardview.YcCardView
        android:id="@+id/cv_wireless_screen"
        style="@style/Main_YcCardView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/screen_dialog_cv_wireless_screen_height">

        <RelativeLayout
            android:id="@+id/rl_wireless_screen"
            android:layout_width="@dimen/main_width"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/screen_dialog_iv_back_width"
                android:layout_height="@dimen/screen_dialog_iv_back_height"
                android:layout_marginStart="@dimen/iv_back_margin_start"
                android:layout_marginTop="@dimen/screen_dialog_iv_back_margin_top"
                android:src="@drawable/select_left_icon" />

            <TextView
                android:id="@+id/tv_title_wireless_screen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/screen_dialog_tv_title_margin_top"
                android:text="@string/wireless_screen_name"
                android:textColor="@color/black_100"
                android:textSize="@dimen/screen_dialog_title_text_size" />

            <TextView
                android:id="@+id/tv_enable_wireless_screen"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/screen_dialog_switch_enable_wireless_screen_height"
                android:layout_below="@id/iv_back"
                android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
                android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
                android:gravity="center_vertical"
                android:text="@string/wireless_screen_enable"
                android:textColor="@color/black_100"
                android:textSize="@dimen/screen_dialog_content_text_size" />

            <Switch
                android:id="@+id/sw_enable_wireless_screen"
                style="@style/NetWork_Switch"
                android:layout_alignTop="@id/tv_enable_wireless_screen"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/screen_dialog_switch_enable_wireless_screen_margin_end"
                android:checked="true"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <RelativeLayout
                android:id="@+id/rl_sub_wireless_screen"
                android:layout_width="@dimen/screen_dialog_rl_sub_wireless_screen_width"
                android:layout_height="match_parent"
                android:layout_below="@id/tv_enable_wireless_screen"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/screen_dialog_rl_sub_wireless_screen_margin_top">

                <View
                    android:id="@+id/line1"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/fragment_about_line_height"
                    android:layout_marginTop="@dimen/item_wifi_line_margin_top"
                    android:background="@color/line2" />

                <TextView
                    android:id="@+id/tv_enable_pin_code"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/screen_dialog_switch_enable_wireless_screen_height"
                    android:layout_below="@id/line1"
                    android:layout_marginTop="@dimen/screen_dialog_tv_enable_pin_code_margin_top"
                    android:gravity="center_vertical"
                    android:text="@string/wireless_screen_enable_pin_code"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_content_text_size" />

                <Switch
                    android:id="@+id/sw_enable_pin_code"
                    style="@style/NetWork_Switch"
                    android:layout_alignTop="@id/tv_enable_pin_code"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/screen_dialog_switch_enable_pin_code_margin_end"
                    android:checked="true"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

                <View
                    android:id="@+id/line2"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/fragment_about_line_height"
                    android:layout_below="@id/tv_enable_pin_code"
                    android:layout_marginTop="@dimen/screen_dialog_line2_margin_top"
                    android:background="@color/line2" />

                <ImageView
                    android:id="@+id/iv_step_1"
                    android:layout_width="@dimen/screen_dialog_iv_step_1_width"
                    android:layout_height="@dimen/screen_dialog_iv_step_1_height"
                    android:layout_below="@id/line2"
                    android:layout_marginTop="@dimen/screen_dialog_iv_step_1_margin_top"
                    android:src="@drawable/step_1" />

                <TextView
                    android:id="@+id/tv_step_1"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/screen_dialog_tv_step_1_height"
                    android:layout_alignTop="@id/iv_step_1"
                    android:layout_marginStart="@dimen/screen_dialog_tv_step_1_margin_start"
                    android:layout_marginTop="@dimen/screen_dialog_tv_step_1_margin_top"
                    android:layout_toEndOf="@id/iv_step_1"
                    android:lineHeight="@dimen/screen_dialog_tv_step_1_line_height"
                    android:text="@string/screen_step_1"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_content_text_size" />

                <ImageView
                    android:id="@+id/iv_step_2"
                    android:layout_width="@dimen/screen_dialog_iv_step_1_width"
                    android:layout_height="@dimen/screen_dialog_iv_step_1_height"
                    android:layout_below="@id/iv_step_1"
                    android:layout_marginTop="@dimen/screen_dialog_iv_step_2_margin_top"
                    android:src="@drawable/step_2" />

                <TextView
                    android:id="@+id/tv_step_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/iv_step_2"
                    android:layout_marginStart="@dimen/screen_dialog_tv_step_1_margin_start"
                    android:layout_marginTop="@dimen/screen_dialog_tv_step_1_margin_top"
                    android:layout_toEndOf="@id/iv_step_2"
                    android:lineHeight="@dimen/screen_dialog_tv_step_1_line_height"
                    android:text="@string/screen_step_2"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_content_text_size" />

                <ImageView
                    android:id="@+id/iv_step_3"
                    android:layout_width="@dimen/screen_dialog_iv_step_1_width"
                    android:layout_height="@dimen/screen_dialog_iv_step_1_height"
                    android:layout_below="@id/iv_step_2"
                    android:layout_marginTop="@dimen/screen_dialog_iv_step_3_margin_top"
                    android:src="@drawable/step_3" />

                <TextView
                    android:id="@+id/tv_step_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/iv_step_3"
                    android:layout_marginStart="@dimen/screen_dialog_tv_step_1_margin_start"
                    android:layout_marginTop="@dimen/screen_dialog_tv_step_1_margin_top"
                    android:layout_toEndOf="@id/iv_step_3"
                    android:lineHeight="@dimen/screen_dialog_tv_step_1_line_height"
                    android:text="@string/screen_step_3"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_content_text_size" />

                <ImageView
                    android:id="@+id/iv_code"
                    android:layout_width="@dimen/screen_dialog_iv_code_width"
                    android:layout_height="@dimen/screen_dialog_iv_code_height"
                    android:layout_below="@id/line2"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/screen_dialog_iv_code_margin_top"
                    android:layout_marginEnd="@dimen/screen_dialog_iv_code_margin_end"
                    android:src="@drawable/transcreen_app" />

                <TextView
                    android:id="@+id/tv_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/iv_code"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/screen_dialog_tv_code_margin_top"
                    android:layout_marginEnd="@dimen/screen_dialog_tv_code_margin_end"
                    android:lineHeight="@dimen/screen_dialog_tv_code_line_height"
                    android:text="@string/screen_msg1"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_tv_code_text_size" />

                <TextView
                    android:id="@+id/tv_pin_code"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/screen_dialog_tv_step_1_height"
                    android:layout_below="@id/tv_step_3"
                    android:layout_alignStart="@id/tv_step_3"
                    android:layout_marginTop="@dimen/screen_dialog_tv_pin_code_margin_top"
                    android:lineHeight="@dimen/screen_dialog_tv_step_1_line_height"
                    android:text="@string/screen_pin_code"
                    android:textColor="@color/black_100"
                    android:textSize="@dimen/screen_dialog_content_text_size"
                    android:visibility="gone" />
            </RelativeLayout>

        </RelativeLayout>
    </com.zyp.cardview.YcCardView>

    <com.zyp.cardview.YcCardView
        android:id="@+id/cv_wired_screen"
        style="@style/Main_YcCardView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/screen_dialog_cv_wired_screen_height"
        android:layout_below="@id/cv_wireless_screen"
        android:layout_marginTop="@dimen/screen_dialog_cv_wired_screen_margin_top">

        <RelativeLayout
            android:id="@+id/rl_wired_screen"
            android:layout_width="@dimen/main_width"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_title_wired_screen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/screen_dialog_tv_title_margin_top"
                android:text="@string/sign"
                android:textColor="@color/black_100"
                android:textSize="@dimen/screen_dialog_title_text_size" />

            <RelativeLayout
                android:id="@+id/rl_sub_wired_screen"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@id/tv_title_wired_screen"
                android:layout_marginTop="@dimen/screen_dialog_rl_sub_wired_screen_margin_top"
                android:splitMotionEvents="false">

                <FrameLayout
                    android:id="@+id/fl_windows"
                    style="@style/Signal_FrameLayout">

                    <ImageView
                        android:id="@+id/iv_windows"
                        style="@style/Signal_ImageView"
                        android:src="@drawable/select_signal_windows_icon" />

                    <TextView
                        android:id="@+id/txt_windows"
                        style="@style/Signal_Text"
                        android:text="@string/windows" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_typec"
                    style="@style/Signal_FrameLayout"
                    android:layout_below="@id/fl_windows"
                    android:layout_marginTop="@dimen/signal_item_margin_top">

                    <ImageView
                        android:id="@+id/iv_typec"
                        style="@style/Signal_ImageView"
                        android:src="@drawable/select_signal_typec_icon" />

                    <TextView
                        android:id="@+id/txt_typec"
                        style="@style/Signal_Text"
                        android:text="@string/front_typec" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_behind_hdmi1"
                    style="@style/Signal_FrameLayout"
                    android:layout_below="@id/fl_typec"
                    android:layout_marginTop="@dimen/signal_item_margin_top">

                    <ImageView
                        android:id="@+id/iv_behind_hdmi1"
                        style="@style/Signal_ImageView"
                        android:src="@drawable/select_signal_hdmi_icon" />

                    <TextView
                        android:id="@+id/txt_behind_hdmi1"
                        style="@style/Signal_Text"
                        android:text="@string/behind_hdmi1" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_behind_hdmi2"
                    style="@style/Signal_FrameLayout"
                    android:layout_below="@id/fl_behind_hdmi1"
                    android:layout_marginTop="@dimen/signal_item_margin_top">

                    <ImageView
                        android:id="@+id/iv_behind_hdmi2"
                        style="@style/Signal_ImageView"
                        android:src="@drawable/select_signal_hdmi_icon" />

                    <TextView
                        android:id="@+id/txt_behind_hdmi2"
                        style="@style/Signal_Text"
                        android:text="@string/behind_hdmi2" />
                </FrameLayout>

            </RelativeLayout>

        </RelativeLayout>
    </com.zyp.cardview.YcCardView>


</RelativeLayout>