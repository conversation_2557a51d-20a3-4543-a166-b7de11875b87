package com.eeo.systemsetting.touchlock;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;

/**
 * 投屏引导页相关
 */
public class TouchLockManager {
    private static final String TAG = "TouchLockManager";
    private static TouchLockManager mTouchLockManager = null;
    private Context mContext;

    /**
     * 触控锁弹窗
     */
    private TouchLockView mTouchLockView;
    private ImageView mTouchLockIv;
    private TextView mTouchLockTv;
    private WindowManager.LayoutParams mTouchLockLayoutParams;
    private boolean mHasTouchLockViewShown;
    private WindowManager mWindowManager;

    private OnTouchLockClickListener mTouchLockClickListener;

    //隐藏触控锁弹窗
    public static final int MSG_HIDE_TOUCH_LOCK_VIEW = 1;
    //隐藏触控锁弹窗
    public static final int MSG_REMOVE_TOUCH_LOCK_VIEW = 2;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                //锁屏状态下，2s后隐藏
                case MSG_HIDE_TOUCH_LOCK_VIEW:
                    Log.i(TAG, "handleMessage: MSG_HIDE_TOUCH_LOCK_VIEW");
                    hideTouchLockView();
                    break;
                case MSG_REMOVE_TOUCH_LOCK_VIEW:
                    dismiss();
                    break;
            }
        }
    };

    private TouchLockManager(Context context) {
        mContext = context;
    }

    public static TouchLockManager getInstance(Context context) {
        if (mTouchLockManager == null) {
            mTouchLockManager = new TouchLockManager(context);
            CommonUtils.updateDensity(context);
        }
        return mTouchLockManager;
    }

    /**
     * @param hide 是否直接显示，设置控制栏消失后再次出现时，不直接显示
     */
    public void showTouchLockView(boolean hide) {
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        }
        if (mTouchLockView == null) {
            mTouchLockView = (TouchLockView) LayoutInflater.from(mContext).inflate(R.layout.dialog_touch_lock, null);
            mTouchLockTv = mTouchLockView.findViewById(R.id.txt_lock);
            mTouchLockTv.setShadowLayer(9, 4, 0, mContext.getColor(R.color.shadow_color_middle));
            mTouchLockIv = mTouchLockView.findViewById(R.id.iv_lock);
            mTouchLockIv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //解锁
                    CommonUtils.setLocked(mContext, false);
                    mTouchLockIv.setImageResource(R.drawable.set_ic_lock_s);
                    mTouchLockTv.setText(mContext.getApplicationContext().getString(R.string.finish_lock));
                    //延迟消失：能看到解锁动画
                    mHandler.removeMessages(MSG_REMOVE_TOUCH_LOCK_VIEW);
                    mHandler.sendEmptyMessageDelayed(MSG_REMOVE_TOUCH_LOCK_VIEW, 300);
                    if (mTouchLockClickListener != null) {
                        mTouchLockClickListener.onClick();
                    }
                }
            });

            mTouchLockLayoutParams = new WindowManager.LayoutParams();
            mTouchLockLayoutParams.format = PixelFormat.RGBA_8888;
            mTouchLockLayoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
            mTouchLockLayoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.BOTTOM;
            mTouchLockLayoutParams.width = mContext.getResources().getDimensionPixelSize(R.dimen.ll_touch_lock_width);
            mTouchLockLayoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            mTouchLockLayoutParams.y = mContext.getResources().getDimensionPixelSize(R.dimen.ll_touch_lock_margin_bottom);
            /*mTouchLockLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                    | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;*/
            mTouchLockView.setHandler(mHandler);
        }
        mTouchLockIv.setImageResource(R.drawable.set_ic_lock_h);
        mTouchLockTv.setText(mContext.getApplicationContext().getString(R.string.click_lock));
        if (hide) {
            //设置弹窗消失后，需要再次换出触控锁，但不显示
            if (!mHasTouchLockViewShown) {
                mTouchLockView.setAlpha(0);
            }
        } else {
            mTouchLockView.setAlpha(1);
        }
        try {
            if (mHasTouchLockViewShown) {
                mWindowManager.updateViewLayout(mTouchLockView, mTouchLockLayoutParams);
            } else {
                mWindowManager.addView(mTouchLockView, mTouchLockLayoutParams);
            }
            mHasTouchLockViewShown = true;
            //两秒后隐藏锁屏弹窗
            mHandler.removeMessages(MSG_HIDE_TOUCH_LOCK_VIEW);
            mHandler.sendEmptyMessageDelayed(MSG_HIDE_TOUCH_LOCK_VIEW, 2000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CommonUtils.enableOsd(mContext, true);
    }

    /**
     * 隐藏触控锁
     * 这里为了能阻止透传，只是把透明度降为0
     */
    public void hideTouchLockView() {
        if (mTouchLockView != null) {
            mTouchLockView.setAlpha(0);
        }
    }

    /**
     * 移除触控锁
     * 打开大屏设置（需要触控）也先移除
     */
    public void dismissTouchLockView() {
        mHandler.sendEmptyMessage(MSG_REMOVE_TOUCH_LOCK_VIEW);
    }

    public void dismiss() {
        if (mWindowManager != null && mTouchLockView != null && mTouchLockView.isAttachedToWindow()) {
            Log.d(TAG, "dismissTouchLockView");
            mWindowManager.removeView(mTouchLockView);
        }
        mHasTouchLockViewShown = false;
        CommonUtils.enableOsd(mContext, false);
    }

    public void setTouchLockClickListener(OnTouchLockClickListener listener) {
        mTouchLockClickListener = listener;
    }

    public interface OnTouchLockClickListener {
        void onClick();
    }
}
