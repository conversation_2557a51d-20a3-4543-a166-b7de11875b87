2025-07-28 09:54:11.503   887-887   EeoApplication==        com.eeo.systemsetting                I  EeoApplication:onCreate - Sending broadcast to internal ArrayMicUpdateCheckReceiver.
2025-07-28 09:54:13.121   887-913   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 113 RECEIVER: 0 / ReceiverData{intent=Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver } packageName=com.eeo.systemsetting resultCode=-1 resultData=null resultExtras=null}
2025-07-28 09:54:13.573   887-887   ActivityThread          com.eeo.systemsetting                V  Performing receive of Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver }: app=com.eeo.systemsetting.EeoApplication@c9ea3d2, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver}, dir=/data/app/~~z8N5mRbH_XBGpWntqj7HWQ==/com.eeo.systemsetting-OMnU7b3EYbgrsAtUHfw3vg==/base.apk
2025-07-28 09:54:13.574   887-887   ArrayMicUp...ckReceiver com.eeo.systemsetting                I  Received action to check Array Microphone update.
2025-07-28 09:54:13.574   887-887   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver.onReceive:20 android.app.ActivityThread.handleReceiver:4030 
2025-07-28 09:54:13.613   887-913   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@5cba442 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-28 09:54:13.617   887-909   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@5cba442 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-07-28 09:54:13.619   887-887   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-07-28 09:54:13.624   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-07-28 09:54:13.633   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-07-28 09:54:13.633   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-07-28 09:54:13.643   887-887   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-28 09:54:13.643   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-28 09:54:13.652   887-1421  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: adb shell sample_xml_usbsw s side SOC
2025-07-28 09:54:16.802   887-1427  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: error: more than one device/emulator
2025-07-28 09:54:16.803   887-1426  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: * daemon not running. starting it now on port 5038 *
2025-07-28 09:54:16.803   887-1426  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: * daemon started successfully *
2025-07-28 09:54:16.804   887-1421  ArrayMicOTA             com.eeo.systemsetting                D  Local command [adb shell sample_xml_usbsw s side SOC] finished with exit code: 1
2025-07-28 09:54:16.805   887-887   ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Command failed: adb shell sample_xml_usbsw s side SOC
2025-07-28 09:54:16.805   887-887   ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Command failed: adb shell sample_xml_usbsw s side SOC
2025-07-28 09:54:16.805   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-28 09:54:16.805   887-887   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-28 09:54:16.805   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-07-28 09:54:16.816   887-1666  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: adb shell sample_xml_usbsw s side PC
2025-07-28 09:54:16.837   887-1666  ArrayMicOTA             com.eeo.systemsetting                D  Local command [adb shell sample_xml_usbsw s side PC] finished with exit code: 1
2025-07-28 09:54:27.054   887-887   ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect on attempt 1
2025-07-28 09:54:27.054   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 2/3 to switch USB to PC.
2025-07-28 09:54:27.072   887-2263  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: adb shell sample_xml_usbsw s side PC
2025-07-28 09:54:27.149   887-2263  ArrayMicOTA             com.eeo.systemsetting                D  Local command [adb shell sample_xml_usbsw s side PC] finished with exit code: 1
2025-07-28 09:54:35.477   887-887   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-28 09:54:35.477   887-887   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-07-28 09:54:35.478   887-887   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-28 09:54:35.487   887-887   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@ace05fe
2025-07-28 09:54:35.487   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-28 09:54:35.487   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-28 09:54:35.487   887-887   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.