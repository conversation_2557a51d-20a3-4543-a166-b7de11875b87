<!-- res/drawable/custom_button_background.xml -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/btn_disable_color" />
            <corners android:radius="14dp" />
            <padding android:left="0dp" android:top="0dp" android:right="0dp" android:bottom="0dp" />
            <size android:width="0dp" android:height="0dp" />
        </shape>
    </item>
    <!-- 按钮正常状态 -->
    <item  android:state_pressed="false">
        <shape>
            <solid android:color="@color/btn_default_bg_white" />
            <corners android:radius="14dp" />
            <padding android:left="0dp" android:top="0dp" android:right="0dp" android:bottom="0dp" />
            <size android:width="0dp" android:height="0dp" />
        </shape>
    </item>
    <!-- 按钮按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/btn_press_color_white" />
            <corners android:radius="14dp" />
            <padding android:left="0dp" android:top="0dp" android:right="0dp" android:bottom="0dp" />
            <size android:width="0dp" android:height="0dp" />
        </shape>
    </item>

</selector>
