<resources>
    <string name="app_name">Setup</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="activate">Activate</string>
    <string name="slogn">ClassinWorld</string>
    <string name="select_region_prompt">Region</string>
    <string name="select_language_prompt">Language</string>
    <string name="next">Next</string>
    <string name="confirm">OK</string>
    <string name="skip">Skip</string>
    <string name="wired_internet_setting">Ethernet Settings</string>
    <string name="basic_setting">General</string>
    <string name="use_type">Type</string>
    <string name="education">Education</string>
    <string name="conference">Conference</string>
    <string name="organization_name">Organization Name</string>
    <string name="device_addr">Device Address</string>
    <string name="projection_text">Enable wireless screen mirroring</string>
    <string name="projection_tips">Enabling wireless screen mirroring will enable the WiFi Hotspot by default.</string>
    <string name="input_hint">Enter</string>

    <string-array name="regions_array">
        <item>Region</item>
        <item>China</item>
        <item>Singapore</item>
        <item>United States</item>
        <item>South Korea</item>
        <item>United Arab Emirates</item>
        <item>United Kingdom</item>
        <item>Others</item>
    </string-array>

    <string-array name="languages_array">
        <item>Language</item>
        <item>简体中文</item>
        <item>English</item>
    </string-array>

    <!-- Sync Preferences -->
    <string name="wifi_network">Wi-Fi Settings</string>
    <string name="user_agreement">Data Protection and Privacy Policy</string>
    <string name="ethernet_network">Ethernet Settings</string>
    <string name="network_settings">Network Settings</string>
    <string name="wifi_settings">Wi-Fi Settings</string>
    <string name="ethernet_settings">Ethernet Settings</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="long_string_tips">Address cannot exceed 10 characters</string>
    <string name="no_select_tips">Please select region or language</string>
    <string name="special_character_tips">Special characters are not supported</string>

    <string name="network_wifi_status_connected_no_internet">Connected but unable to access the internet</string>
    <string name="network_wifi_status_saved">Saved</string>
    <string name="network_wifi_status_idle" />
    <string name="network_wifi_status_disabled">Disactivated</string>
    <string name="network_wifi_status_network_failure">IP confuguration failed</string>
    <string name="network_wifi_status_password_failure">"Authentication failed"</string>
    <string name="network_wifi_status_scanning">Scanning…</string>
    <string name="network_wifi_status_connecting">Connecting…</string>
    <string name="network_wifi_status_authenticating">Authentication in progress…</string>
    <string name="network_wifi_status_obtaining_ip_address">Accessing IP address…</string>
    <string name="network_wifi_status_connected">Connected</string>
    <string name="network_wifi_status_suspended">Paused</string>
    <string name="network_wifi_status_disconnecting">Disconnecting...</string>
    <string name="network_wifi_status_disconnected">Disconnected</string>
    <string name="network_wifi_status_failed">Failed</string>
    <string name="network_wifi_status_blocked">Disactivated</string>
    <string name="network_wifi_status_verifying_poor_link">Temporarily closed (poor network)</string>
    <string name="password">Password</string>
    <string name="join">Join</string>
    <string name="wifi_front_str">Please enter the password of "</string>
    <string name="wifi_behind_str">"</string>
    <string name="password_error">* Wrong password</string>
    <string name="select_network">Select network</string>

    <string name="disconnect">Not connected</string>
    <string name="cancel">Cancel</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <!-- Preference Titles -->
    <string name="messages_header">Messages</string>
    <string name="sync_header">Sync</string>
    <string name="network">Wired Network</string>
    <string name="mac_address">MAC Address</string>
    <string name="manual">Manual</string>
    <string name="auto">Auto</string>

    <!-- Messages Preferences -->
    <string name="signature_title">Your signature</string>
    <string name="reply_title">Default reply action</string>

    <!-- Sync Preferences -->
    <string name="sync_title">Sync email periodically</string>
    <string name="attachment_title">Download incoming attachments</string>
    <string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string>
    <string name="attachment_summary_off">Only download attachments when manually requested</string>
    <!--wifi详情设置页面 -->
    <string name="wifi_more">Wi-Fi Details</string>
    <string name="save">Save</string>
    <string name="disconnect_network">Disconnect this network</string>
    <string name="forget_network">Remove this network</string>
    <string name="ip_setting">IP Setting</string>
    <string name="ip_address">IP</string>
    <string name="subnet_mask">Subnet Mask</string>
    <string name="gateway">Gateway</string>
    <string name="DNS">DNS Server</string>
    <string name="not_network">The network is not connected</string>
    <string name="toast_success">Network connected</string>
    <string name="toast_fail">Network connection failed</string>
    <string name="no_address_tips">Please enter the device address</string>
    <string name="long_name_tips">The name cannot exceed 20 characters</string>
    <string name="no_name_tips">Please enter name</string>
    <string name="no_select_type_tips">Please select type</string>
    <string name="start_activation">Activate</string>
    <string name="user_agreement_content">test</string>
    <string name="agree_txt">Agree</string>
    <string name="system_initialization">The system is initializing, please wait...</string>
</resources>