<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_network_auto_manual_width"
    android:layout_height="@dimen/dialog_network_auto_manual_height"
    android:background="@drawable/shape_network_auto_manual"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/ll_auto"
        style="@style/IP_Dialog_Select_Linear">

        <ImageView
            android:id="@+id/img_auto"
            style="@style/IP_Dialog_Select_Image"
            android:background="@drawable/set_ic_checkbox"
            />

        <TextView
            android:id="@+id/txt_auto"
            style="@style/IP_Dialog_Select_TextView"
            android:text="@string/auto"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/fragment_about_line_height"
        android:background="@color/line1"
        android:layout_marginStart="@dimen/dialog_network_auto_manual_line_margin_left"
        android:layout_marginEnd="@dimen/dialog_network_auto_manual_line_margin_right"
        />

    <LinearLayout
        android:id="@+id/ll_manual"
        style="@style/IP_Dialog_Select_Linear">

        <ImageView
            android:id="@+id/img_manual"
            style="@style/IP_Dialog_Select_Image"
            android:background="@drawable/set_ic_checkbox"/>

        <TextView
            android:id="@+id/txt_manual"
            style="@style/IP_Dialog_Select_TextView"
            android:text="@string/manual"/>

    </LinearLayout>

</LinearLayout>