<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="EeoDialogStyle" parent="@style/Base.Theme.AppCompat.Dialog">

        <item name="android:colorBackground">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <!--&lt;!&ndash;设置没有窗口标题、dialog标题等各种标题&ndash;&gt;-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="windowNoTitle">true</item>
        <item name="android:dialogTitle">@null</item>
        <!--是否悬浮在activity上-->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        <!--        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>-->
        <!--        <item name="android:windowAnimationStyle">@style/Animation</item>-->
        <item name="android:windowContentTransitions">true</item>

        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>

    </style>

    <!--Dialog将白色背景变透明，但是背景也变暗-->
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent
        </item><!-- 设置dialog背景为透明背景 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item><!-- 设置dialog背景变暗 -->
    </style>

    <!--Dialog将白色背景变透明，但是背景不变暗-->
    <style name="Dialog_NO_DARK" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@android:color/transparent
        </item><!-- 设置dialog背景为透明背景 -->
        <item name="android:backgroundDimEnabled">false</item><!-- 设置dialog背景不变暗 -->
    </style>

    <style name="Main_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="Main_ShadowLayout_Circle_Img">
        <item name="android:layout_width">@dimen/sl_lock_width</item>
        <item name="android:layout_height">@dimen/sl_lock_height</item>
        <item name="android:layout_gravity">center</item>
    </style>

    <style name="Main_ShadowLayout_Circle_Text">
        <item name="android:layout_width">@dimen/tv_screen_width</item>
        <item name="android:layout_height">@dimen/tv_screen_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:textSize">@dimen/tv_screen_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
    </style>

    <style name="Main_Shutdown_TextView">
        <item name="android:layout_width">@dimen/tv_shutdown_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">start</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:textSize">@dimen/tv_desktop_text_size</item>
    </style>

    <style name="Main_YcCardView">
        <item name="ycCardBackgroundColor">@color/white_95</item>
        <item name="ycCardCornerRadius">@dimen/main_bg_radius</item>
        <item name="ycCardElevation">@dimen/sl_screen_elevation</item>
        <item name="ycCardPreventCornerOverlap">false</item>
        <item name="ycStartShadowColor">@color/shadow_color</item>
    </style>

    <style name="SeekBar_Light">
        <item name="android:layout_width">@dimen/sb_bright_width</item>
        <item name="android:layout_height">@dimen/sb_bright_height</item>
        <item name="android:maxWidth">@dimen/sb_bright_width</item>
        <item name="android:maxHeight">@dimen/sb_bright_max_height</item>
        <item name="android:paddingStart">@dimen/sb_bright_padding_start</item>
        <item name="android:paddingEnd">@dimen/sb_bright_padding_start</item>
        <item name="android:progressDrawable">@drawable/progress_vertical_gradient_simple_shape
        </item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@null</item>
        <item name="android:max">113</item>
    </style>

    <style name="SeekBar_Rgb">
        <item name="android:layout_width">@dimen/rgb_sb_width</item>
        <item name="android:layout_height">@dimen/rgb_sb_thumb_height</item>
        <item name="android:maxWidth">@dimen/rgb_sb_width</item>
        <item name="android:maxHeight">@dimen/rgb_sb_height</item>
        <item name="android:paddingStart">@dimen/rgb_sb_padding_start</item>
        <item name="android:paddingEnd">@dimen/rgb_sb_padding_end</item>
        <item name="android:layout_marginStart">@dimen/rgb_sb_margin_start</item>
        <item name="android:layout_marginTop">@dimen/rgb_sb_margin_top</item>
        <item name="android:progressDrawable">
            @drawable/progress_vertical_gradient_simple_shape_rgb
        </item>
        <item name="android:splitTrack">false</item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@drawable/thumb_rgb</item>
        <item name="android:max">255</item>
    </style>

    <style name="Shutdown_Icon">
        <item name="android:layout_width">@dimen/iv_shutdown_width</item>
        <item name="android:layout_height">@dimen/iv_shutdown_height</item>
        <item name="android:layout_marginTop">@dimen/iv_shutdown_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/iv_shutdown_margin_left</item>
    </style>

    <style name="Shutdown_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_tittle_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_tittle_margin_left</item>
    </style>

    <style name="Shutdown_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_content_text_size</item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_content_margin_top</item>
        <item name="android:layout_centerHorizontal">true</item>

    </style>

    <style name="Shutdown_Btn_Confirm">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_confirm_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="Shutdown_Btn_Cancel">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_white</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_cancel_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:layout_centerHorizontal">true</item>
    </style>

    <style name="Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/main_line1_height</item>
        <item name="android:background">@color/main_line</item>
    </style>

    <style name="Signal_FrameLayout">
        <item name="android:layout_width">@dimen/signal_tv_width</item>
        <item name="android:layout_height">@dimen/signal_tv_height</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:background">@drawable/shape_signal_bg</item>
    </style>

    <style name="Signal_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:layout_marginStart">@dimen/signal_iv_margin_start</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="Signal_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/signal_tv_margin_start</item>
        <item name="android:textSize">@dimen/signal_tv_text_size</item>
        <item name="android:textColor">@color/main_text_color</item>
    </style>

    <style name="Setting_ShadowLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/sl_network_height</item>
        <item name="hl_layoutBackground_true">@color/press_color</item>
        <item name="hl_layoutBackground">@color/white_0</item>
        <item name="hl_shadowColor">@color/shadow_color</item>
        <item name="hl_shapeMode">selected</item>
        <item name="hl_shadowOffsetX">@dimen/sl_network_shadow_offset_x</item>
        <item name="hl_shadowOffsetY">@dimen/sl_network_shadow_offset_y</item>
        <item name="hl_textColor">@color/text_black_100</item>
        <item name="hl_textColor_true">@color/white_100</item>

    </style>

    <style name="Setting_ImageView">
        <item name="android:layout_width">@dimen/iv_bright_width</item>
        <item name="android:layout_height">@dimen/iv_bright_height</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/sl_network_margin_start</item>
    </style>

    <style name="Setting_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/tv_network_margin_start</item>
        <item name="android:textSize">@dimen/tv_network_text_size</item>
    </style>

    <style name="NetWork_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>

    </style>

    <style name="NetWork_Switch">
        <item name="android:layout_width">@dimen/fragment_network_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_network_sw_height</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>

    <style name="NetWork_TextView">
        <item name="android:layout_width">@dimen/fragment_network_title_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_title_margin_start</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_network_text_size</item>
    </style>

    <style name="Network_EditText">
        <item name="android:layout_width">@dimen/fragment_network_et_width</item>
        <item name="android:layout_height">@dimen/fragment_network_et_height</item>
        <item name="android:textCursorDrawable">@drawable/shape_network_edt_cursor</item>
        <item name="android:textSize">@dimen/fragment_network_et_text_size</item>
        <item name="android:textColor">@color/color_network_edit</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_et_margin_start</item>
        <item name="android:paddingEnd">@dimen/fragment_network_et_padding_end</item>
        <item name="android:gravity">center_vertical|end</item>
    </style>

    <style name="IP_Dialog_Select_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_item_height</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="IP_Dialog_Select_Image">
        <item name="android:layout_width">@dimen/dialog_network_auto_manual_iv_width</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_iv_height</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_iv_margin_start
        </item>
    </style>

    <style name="IP_Dialog_Select_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_tv_margin_start
        </item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:textSize">@dimen/dialog_network_auto_manual_tv_text_size</item>
    </style>

    <style name="About_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/fragment_about_title_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_title_margin_start</item>
    </style>

    <style name="About_Text_Content">
        <item name="android:layout_width">@dimen/fragment_about_content_width</item>
        <item name="android:layout_height">@dimen/fragment_about_content_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_about_title_margin_start</item>
        <item name="android:gravity">end</item>
    </style>

    <style name="About_Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/fragment_about_line_height</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_line_margin_start</item>
        <item name="android:layout_marginRight">@dimen/fragment_about_line_margin_start</item>
        <item name="android:background">@color/line_50</item>
    </style>

    <style name="Extra_Switch">
        <item name="android:layout_width">@dimen/fragment_extra_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_extra_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_extra_sw_margin_end</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>

    <style name="Extra_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_extra_tv_margin_start</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_extra_tv_text_size</item>
    </style>

    <style name="SystemSetting_PopWindow_Host_Btn">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="textAllCaps">false</item>
        <item name="android:outlineProvider">none</item>
    </style>

    <style name="Update_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_update_tv_version_text_size</item>
    </style>

    <style name="Update_Button">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:minWidth">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style>

    <style name="update_progress_horizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <!--进度条的进度颜色drawable文件-->
        <item name="android:progressDrawable">@drawable/progress_indeterminate_horizontal</item>
        <!--进度条的最小高度-->
        <item name="android:minHeight">@dimen/fragment_update_pb_horizontal_min_height</item>
        <!--进度条的最大高度-->
        <item name="android:maxHeight">@dimen/fragment_update_pb_horizontal_max_height</item>
    </style>

    <style name="Wifi_Text_NAME">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/wifi_dialog_input_password_title_margin_top
        </item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/wifi_dialog_input_password_title_text_size</item>
    </style>

    <style name="WiFi_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/item_wifi_more_tv_disconnect_height</item>
        <item name="android:layout_marginStart">@dimen/item_wifi_more_tv_disconnect_margin_start
        </item>
        <item name="android:textSize">@dimen/item_wifi_more_tv_save_text_size</item>
        <item name="android:textColor">@color/text_press_green_black</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="Projection_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/small_window_tv_home_margin_start</item>
        <item name="android:textSize">@dimen/small_window_shortcut_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="Projection_ImageView">
        <item name="android:layout_width">@dimen/small_window_iv_home_width</item>
        <item name="android:layout_height">@dimen/small_window_iv_home_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="Adb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/adb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="Rgb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/rgb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Adb_Switch">
        <item name="android:layout_width">@dimen/adb_sw_width</item>
        <item name="android:layout_height">@dimen/adb_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/adb_sw_margin_end</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/sw_adb_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>

    <style name="Adb_Button">
        <item name="android:layout_width">@dimen/adb_btn_width</item>
        <item name="android:minWidth">@dimen/adb_btn_width</item>
        <item name="android:layout_height">@dimen/adb_btn_height</item>
        <item name="android:minHeight">@dimen/adb_btn_height</item>
        <item name="android:background">@drawable/shape_adb_btn</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/adb_btn_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style>
</resources>