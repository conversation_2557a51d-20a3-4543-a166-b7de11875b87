package com.eeo.udisdk.touch;

import android.content.Context;
import android.util.Log;

import com.eeo.udisdk.UdiConstant;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 触摸相关
 */
public class TouchManager {
    private static final String TAG = "Udi-TouchManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;

    /**
     * 半屏和画中画坐标偏移接口，后调用的生效
     * 避免同时半屏和画中画时，画中画坐标接口后调用，导致半屏模式坐标偏移
     */
    private boolean mDisableSmallWindow = false;

    private final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    public TouchManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    public void setDisableSmallWindow(boolean disableSmallWindow) {
        mDisableSmallWindow = disableSmallWindow;
    }


    public void setSmallWindowWithThread(boolean enable, int left, int top, int right, int bottom, String source) {
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                setSmallWindow(enable, left, top, right, bottom, source);
            }
        });
    }

    /**
     * 设置画中画小窗口
     * @param source 注：当前通道时应传"NONE",非OPS通道时传入当前通道hubs会重新加载
     */
    public void setSmallWindow(boolean enable, int left, int top, int right, int bottom, String source) {
        if (enable && mDisableSmallWindow) {
            Log.d(TAG, "setSmallWindow cancel: disableSmallWindow");
            return;
        }
        JSONObject jsonObject = new JSONObject();
        /*{
            "enable": true,
            "source": "HDMI1",
            "location": {
                    "bottom": 1,
                    "left": 1,
                    "right": 1,
                    "top": 1
            }
        }*/
        try {
            jsonObject.put("enable", enable);
            jsonObject.put("source", source);
            JSONObject locationObject = new JSONObject();
            locationObject.put("left", left);
            locationObject.put("top", top);
            locationObject.put("right", right);
            locationObject.put("bottom", bottom);
            jsonObject.put("location", locationObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/touch/pip/remap"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setSmallWindow:" + "enable=" + enable + " (" + left + "," + top + "," + right + ","
                    + bottom + ") response=" + response);
            //避免已处半屏时，画中画偏移后生效覆盖，这里再次触摸偏移半屏
            if (enable && mDisableSmallWindow) {
                int offset = getScreenOffset();
                if (offset != 0) {
                    Log.d(TAG, "setTouchOffset:offset=" + offset);
                    setTouchOffset(UdiConstant.TYPE_TOUCH_OFFSET_PC, offset);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置触摸偏移
     *
     * @param offset 0-2160
     * @param type
     * @see UdiConstant#TYPE_TOUCH_OFFSET_ALL
     */
    public boolean setTouchOffset(String type, int offset) {
        //        String json = "{\"type\":\"PC\",\"offset\":[0,16383,32767,32767]}"; //下移半屏
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("type", type);
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(0);
            jsonArray.put(offset * 32767 / 2160); //0-2160转化成0-32767
            jsonArray.put(32767);
            jsonArray.put(32767);
            jsonObject.put("offset", jsonArray);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/touch/offset"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            //临时屏蔽画中画坐标偏移，避免半屏时坐标偏移
            mDisableSmallWindow = true;
            Log.d(TAG, "setTouchOffset:type=" + type + ", offset=" + offset + " ,response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean removeTouchArea() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("areaId", 1);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/touch/intercept/area/remove"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "removeTouchArea:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否开启禁止外部通道触摸
     */
    public boolean isOsdEnabled() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/touch/osd/enable"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isOsdEnabled:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isOsdEnabled body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("enable");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 禁用外部通道触摸
     *
     * @param enable true:禁止外部通道 false:恢复触摸
     */
    public boolean enableOsd(boolean enable) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("enable", enable);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/touch/osd/enable"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "enableOsd:enable=" + enable + "  ,response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public int getScreenOffset() {
        try {
            ClassLoader classLoader = mContext.getClassLoader();
            Class<?> systemProperties = classLoader.loadClass("android.os.SystemProperties");
            Method getInt = systemProperties.getMethod("getInt", String.class, int.class);
            getInt.setAccessible(true);
            Object result = getInt.invoke(systemProperties, "vendor.screen.yoffset", 0);
            if (result != null) {
                return (int) result;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
