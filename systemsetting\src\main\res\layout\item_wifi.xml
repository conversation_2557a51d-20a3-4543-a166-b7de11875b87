<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/item_wifi_height"
    android:background="@drawable/select_wifi_item_press_color"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/item_wifi_tv_wifi_height"
        android:layout_marginStart="@dimen/fragment_wifi_ll_network_margin_start"
        android:layout_marginTop="@dimen/item_wifi_tv_wifi_margin_top"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_wifi_name"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/item_wifi_tv_wifi_name_height"
            android:layout_gravity="center_vertical"
            android:ellipsize="end"
            android:maxWidth="@dimen/item_wifi_tv_wifi_name_max_width"
            android:singleLine="true"
            android:text="HDEW506"
            android:textColor="@color/black_100"
            android:textSize="@dimen/fragment_network_text_size" />

        <TextView
            android:id="@+id/txt_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="低安全性"
            android:textColor="@color/black_100"
            android:textSize="@dimen/item_wifi_tv_state_text_size"
            android:visibility="gone" />
    </LinearLayout>


    <ImageView
        android:id="@+id/img_rank"
        android:layout_width="@dimen/item_wifi_iv_rank_width"
        android:layout_height="@dimen/item_wifi_iv_rank_height"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
        android:background="@drawable/ic_wifi_1" />

    <ImageView
        android:id="@+id/img_password"
        android:layout_width="@dimen/item_wifi_iv_rank_width"
        android:layout_height="@dimen/item_wifi_iv_rank_height"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/item_wifi_iv_password_margin_end"
        android:layout_toStartOf="@id/img_rank"
        android:background="@drawable/ic_password" />


    <ImageView
        android:id="@+id/img_more"
        android:layout_width="@dimen/item_wifi_iv_rank_width"
        android:layout_height="@dimen/item_wifi_iv_rank_height"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/item_wifi_iv_password_margin_end"
        android:layout_toStartOf="@id/img_password"
        android:background="@drawable/select_wifi_img_press_icon" />

    <View
        android:id="@+id/line"
        style="@style/About_Line"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="@dimen/item_wifi_line_margin_top" />

</RelativeLayout>