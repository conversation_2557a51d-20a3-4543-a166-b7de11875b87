package com.eeo.ota.server;

import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;

/**
 * 后续切换
 * 使用EEO自己的后台
 */
public class EEO extends Server {
    @Override
    public void checkVersion(CheckVersionCallback checkVersionCallback) {

    }

    @Override
    public void download(DownloadListener downloadListener) {

    }

    @Override
    public void stopDownload() {

    }
}
