package com.eeo.WhiteboardAccelerate.SerialPort

object DataUtils {
        const val SERIAL_PATH_OPS: String = "/dev/ttyS1"
        const val SERIAL_BAUDRATE_OPS: Int = 9600
        const val HDX_UART_PATH: String = "/dev/ttyS2"
        const val HDX_UART_BAUDRATE: Int = 115200
        const val HID_DEVICE_PATH: String = "/dev/otg_whiteboard_data"
        const val ALGORITHM_VER: Byte = 0x02 //定义当前算法的版本号

        const val FUNCTION_START_BYTE: Byte = 0xE0.toByte()
        const val FUNCTION_END_BYTE: Byte = 0xF0.toByte()
        const val FUNCTION_MSG_OBJECT_OPS: Byte = 0x01.toByte()
        const val FUNCTION_MSG_OBJECT_ANDROID: Byte = 0x80.toByte()
        /**************消息类型定义**********************/
        const val FUNCTION_MSG_ACK: Byte= 0x10.toByte()
        const val FUNCTION_MSG_NOTIFY: Byte= 0x20.toByte()
        const val FUNCTION_MSG_REQUEST: Byte= 0x30.toByte()
        const val FUNCTION_MSG_REPEAT: Byte= 0x40.toByte()
        /**************命名名称定义**********************/
        const val COMMAND_COMMUNICATION_VALID: Int= 0x0001
        const val COMMAND_WRITE_ACCELERATE: Int=0x0002
        const val COMMAND_OPS_RESOLUTION: Int=0x0003
        const val COMMAND_WRITE_ACCELERATE_RANGE: Int=0x0004
        const val COMMAND_WRITE_ACCELERATE_RANGE_LIMIT: Int=0x0005
        const val COMMAND_STROKE_WIDTH: Int=0x0006
        const val COMMAND_STROKE_COLOR: Int=0x0007
        const val COMMAND_STROKE_SHAPE: Int=0x0008
        const val COMMAND_CLASSIN_FPS: Int=0x0009
        const val COMMAND_CLASSIN_ERASER: Int=0x0010
        const val COMMAND_CLASSIN_APP: Int=0x0011
        const val COMMAND_CLASSIN_DICETOOL: Int=0x0012
        const val COMMAND_CLASSIN_CHRONOTOOL: Int=0x0013
        const val COMMAND_CLASSIN_TIMERTOOL: Int=0x0014
        const val COMMAND_CLASSIN_RULERTOOL: Int=0x0015
        const val COMMAND_ALGORITHM_VER: Int=0x0016
        const val COMMAND_DOUBLE_SCREEN_SWITCH: Int=0x0017
        const val COMMAND_CENTRAL_CONTROL: Int=0x0018
}