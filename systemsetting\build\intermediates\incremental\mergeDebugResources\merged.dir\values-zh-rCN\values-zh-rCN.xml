<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <string-array name="languages">
        <item>简体中文</item>
        <item>English</item>
    </string-array>
    <string-array name="startup_channels">
        <item>上次关机信号源</item>
        <item>内置电脑</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>
    <string-array name="startup_channels_windows_disabled">
        <item>上次关机信号源</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>
    <dimen name="adb_btn_height">28dp</dimen>
    <dimen name="adb_btn_margin_bottom">12dp</dimen>
    <dimen name="adb_btn_margin_start">9dp</dimen>
    <dimen name="adb_btn_text_size">11sp</dimen>
    <dimen name="adb_btn_width">70dp</dimen>
    <dimen name="adb_height">384dp</dimen>
    <dimen name="adb_item_adb_margin_top">13dp</dimen>
    <dimen name="adb_item_height">29dp</dimen>
    <dimen name="adb_item_margin_end">16dp</dimen>
    <dimen name="adb_item_margin_start">21dp</dimen>
    <dimen name="adb_item_margin_top">3dp</dimen>
    <dimen name="adb_item_radius">3dp</dimen>
    <dimen name="adb_item_width">232dp</dimen>
    <dimen name="adb_iv_back_height">25dp</dimen>
    <dimen name="adb_iv_back_margin_start">20dp</dimen>
    <dimen name="adb_iv_back_margin_top">20dp</dimen>
    <dimen name="adb_iv_back_width">25dp</dimen>
    <dimen name="adb_iv_close_margin_end">16dp</dimen>
    <dimen name="adb_iv_close_margin_top">11dp</dimen>
    <dimen name="adb_iv_reset_height">20dp</dimen>
    <dimen name="adb_iv_reset_margin_start">220dp</dimen>
    <dimen name="adb_iv_reset_width">20dp</dimen>
    <dimen name="adb_line_margin_top">42dp</dimen>
    <dimen name="adb_scrollview_height">298dp</dimen>
    <dimen name="adb_sw_height">18dp</dimen>
    <dimen name="adb_sw_margin_end">13dp</dimen>
    <dimen name="adb_sw_width">36dp</dimen>
    <dimen name="adb_tv_margin_start">13dp</dimen>
    <dimen name="adb_tv_text_size">9sp</dimen>
    <dimen name="adb_width">264dp</dimen>
    <dimen name="bg_radius">8dp</dimen>
    <dimen name="dialog_factory_reset_btn_cancel_margin_top">24dp</dimen>
    <dimen name="dialog_factory_reset_content_margin_top">16dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_height">20dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_width">20dp</dimen>
    <dimen name="dialog_factory_reset_title_margin_start">8dp</dimen>
    <dimen name="dialog_factory_reset_title_margin_top">25dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_bottom">21dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_start">21dp</dimen>
    <dimen name="dialog_have_update_content_margin_top">8dp</dimen>
    <dimen name="dialog_have_update_content_text_size">9sp</dimen>
    <dimen name="dialog_have_update_height">148dp</dimen>
    <dimen name="dialog_have_update_title_margin_top">19dp</dimen>
    <dimen name="dialog_have_update_title_text_size">9sp</dimen>
    <dimen name="dialog_have_update_width">240dp</dimen>
    <dimen name="dialog_install_fail_tv_title_drawable_padding">8dp</dimen>
    <dimen name="dialog_install_fail_tv_title_margin_top">32dp</dimen>
    <dimen name="dialog_installing_pb_height">21dp</dimen>
    <dimen name="dialog_installing_pb_width">21dp</dimen>
    <dimen name="dialog_installing_text_size">16sp</dimen>
    <dimen name="dialog_installing_tv_margin_start">13dp</dimen>
    <dimen name="dialog_network_auto_manual_height">59dp</dimen>
    <dimen name="dialog_network_auto_manual_item_height">29dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_height">20dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_margin_start">29dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_width">20dp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_left">11dp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_right">11dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_margin_start">3dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_text_size">9sp</dimen>
    <dimen name="dialog_network_auto_manual_width">120dp</dimen>
    <dimen name="dialog_shutdown_content_margin_top">19dp</dimen>
    <dimen name="dialog_shutdown_countdown_content_margin_top">15dp</dimen>
    <dimen name="dialog_shutdown_title_margin_top">23dp</dimen>
    <dimen name="dialog_volume_height">74dp</dimen>
    <dimen name="dialog_volume_iv_margin_start">21dp</dimen>
    <dimen name="dialog_volume_margin_bottom">228dp</dimen>
    <dimen name="dialog_volume_margin_end">80dp</dimen>
    <dimen name="dialog_volume_sb_margin_start">46dp</dimen>
    <dimen name="dialog_volume_tv_text_size">11sp</dimen>
    <dimen name="dialog_volume_width">250dp</dimen>
    <dimen name="fl_desktop_padding_vertical">6dp</dimen>
    <dimen name="fragment_about_content_height">13dp</dimen>
    <dimen name="fragment_about_content_width">160dp</dimen>
    <dimen name="fragment_about_device_name_margin_top">29dp</dimen>
    <dimen name="fragment_about_iv_windows_host_margin_start">121dp</dimen>
    <dimen name="fragment_about_line_height">0.3dp</dimen>
    <dimen name="fragment_about_line_margin_start">21dp</dimen>
    <dimen name="fragment_about_line_margin_top">33dp</dimen>
    <dimen name="fragment_about_padding_bottom">5dp</dimen>
    <dimen name="fragment_about_padding_view_height">52dp</dimen>
    <dimen name="fragment_about_scrollbar_size">4dp</dimen>
    <dimen name="fragment_about_text_size">9sp</dimen>
    <dimen name="fragment_about_title_height">13dp</dimen>
    <dimen name="fragment_about_title_margin_start">21dp</dimen>
    <dimen name="fragment_about_title_margin_top">27dp</dimen>
    <dimen name="fragment_about_title_width">60dp</dimen>
    <dimen name="fragment_about_tv_system_version_margin_start">1dp</dimen>
    <dimen name="fragment_about_windows_host_width">80dp</dimen>
    <dimen name="fragment_extra_item_margin_top">20dp</dimen>
    <dimen name="fragment_extra_sw_height">20dp</dimen>
    <dimen name="fragment_extra_sw_margin_end">21dp</dimen>
    <dimen name="fragment_extra_sw_width">40dp</dimen>
    <dimen name="fragment_extra_tv_margin_start">21dp</dimen>
    <dimen name="fragment_extra_tv_text_size">9sp</dimen>
    <dimen name="fragment_extra_wireless_screen_margin_top">26dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_end">21dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_top">26dp</dimen>
    <dimen name="fragment_locale_spinner_width">120dp</dimen>
    <dimen name="fragment_locale_tv_language_height">20dp</dimen>
    <dimen name="fragment_locale_tv_language_margin_top">26dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_start">29dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_top">12dp</dimen>
    <dimen name="fragment_network_content_margin_start">21dp</dimen>
    <dimen name="fragment_network_content_width">160dp</dimen>
    <dimen name="fragment_network_dns_content_margin_start">23dp</dimen>
    <dimen name="fragment_network_dns_title_width">50dp</dimen>
    <dimen name="fragment_network_et_height">29dp</dimen>
    <dimen name="fragment_network_et_margin_start">8dp</dimen>
    <dimen name="fragment_network_et_padding_end">7dp</dimen>
    <dimen name="fragment_network_et_text_size">9sp</dimen>
    <dimen name="fragment_network_et_width">140dp</dimen>
    <dimen name="fragment_network_item_margin_top">23dp</dimen>
    <dimen name="fragment_network_iv_ip_height">13dp</dimen>
    <dimen name="fragment_network_iv_ip_margin_start">33dp</dimen>
    <dimen name="fragment_network_iv_ip_width">13dp</dimen>
    <dimen name="fragment_network_ll_ip_margin_top">15dp</dimen>
    <dimen name="fragment_network_ll_mask_margin_top">11dp</dimen>
    <dimen name="fragment_network_network_margin_top">26dp</dimen>
    <dimen name="fragment_network_sw_height">20dp</dimen>
    <dimen name="fragment_network_sw_margin_start">141dp</dimen>
    <dimen name="fragment_network_sw_width">40dp</dimen>
    <dimen name="fragment_network_text_size">9sp</dimen>
    <dimen name="fragment_network_title_margin_start">21dp</dimen>
    <dimen name="fragment_network_title_width">40dp</dimen>
    <dimen name="fragment_network_tv_ip_setting_width">140dp</dimen>
    <dimen name="fragment_update_btn_cancel_margin_top">59dp</dimen>
    <dimen name="fragment_update_btn_install_margin_top">43dp</dimen>
    <dimen name="fragment_update_btn_retry_margin_top">19dp</dimen>
    <dimen name="fragment_update_btn_right_update_gone_margin_top">20dp</dimen>
    <dimen name="fragment_update_btn_right_update_margin_top">121dp</dimen>
    <dimen name="fragment_update_line_margin_top">30dp</dimen>
    <dimen name="fragment_update_pb_check_update_margin_top">77dp</dimen>
    <dimen name="fragment_update_pb_download_margin_end">21dp</dimen>
    <dimen name="fragment_update_pb_download_margin_top">32dp</dimen>
    <dimen name="fragment_update_pb_horizontal_max_height">5dp</dimen>
    <dimen name="fragment_update_pb_horizontal_min_height">5dp</dimen>
    <dimen name="fragment_update_tv_check_network_margin_top">27dp</dimen>
    <dimen name="fragment_update_tv_checking_margin_top">11dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_start">21dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_top">33dp</dimen>
    <dimen name="fragment_update_tv_update_description_height">81dp</dimen>
    <dimen name="fragment_update_tv_update_description_margin_top">13dp</dimen>
    <dimen name="fragment_update_tv_update_description_max_height">80dp</dimen>
    <dimen name="fragment_update_tv_update_description_width">221dp</dimen>
    <dimen name="fragment_update_tv_version_margin_top">33dp</dimen>
    <dimen name="fragment_update_tv_version_text_size">9sp</dimen>
    <dimen name="fragment_wifi_line1_margin_end">0dp</dimen>
    <dimen name="fragment_wifi_line1_margin_start">0dp</dimen>
    <dimen name="fragment_wifi_line1_margin_top">19dp</dimen>
    <dimen name="fragment_wifi_ll_network_gone_margin_top">23dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_start">21dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_top">33dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_height">12dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_margin_start">8dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_width">12dp</dimen>
    <dimen name="fragment_wifi_rv_connected_margin_top">10dp</dimen>
    <dimen name="fragment_wifi_rv_wifi_list_margin_top">13dp</dimen>
    <dimen name="fragment_wifi_sw_margin_start">134dp</dimen>
    <dimen name="fragment_wifi_title_wifi_width">47dp</dimen>
    <dimen name="fragment_wifi_tv_other_network_text_size">9sp</dimen>
    <dimen name="inter_touch_lock_height">98dp</dimen>
    <dimen name="inter_touch_lock_margin_start">8dp</dimen>
    <dimen name="inter_touch_lock_margin_top">8dp</dimen>
    <dimen name="inter_touch_lock_width">220dp</dimen>
    <dimen name="item_wifi_connect_height">25dp</dimen>
    <dimen name="item_wifi_connect_iv_check_height">17dp</dimen>
    <dimen name="item_wifi_connect_iv_check_width">17dp</dimen>
    <dimen name="item_wifi_connect_iv_password_margin_start">182dp</dimen>
    <dimen name="item_wifi_connect_margin_top">18dp</dimen>
    <dimen name="item_wifi_height">41dp</dimen>
    <dimen name="item_wifi_iv_password_margin_end">4dp</dimen>
    <dimen name="item_wifi_iv_rank_height">20dp</dimen>
    <dimen name="item_wifi_iv_rank_margin_end">21dp</dimen>
    <dimen name="item_wifi_iv_rank_width">20dp</dimen>
    <dimen name="item_wifi_line_margin_top">0dp</dimen>
    <dimen name="item_wifi_more_drawable_padding">8dp</dimen>
    <dimen name="item_wifi_more_et_address_margin_top">6dp</dimen>
    <dimen name="item_wifi_more_et_subnet_mask_margin_top">11dp</dimen>
    <dimen name="item_wifi_more_fl_disconnect_margin_top">21dp</dimen>
    <dimen name="item_wifi_more_ip_address_margin_top">8dp</dimen>
    <dimen name="item_wifi_more_item_margin_end">35dp</dimen>
    <dimen name="item_wifi_more_item_margin_top">26dp</dimen>
    <dimen name="item_wifi_more_iv_arrow_margin_top">23dp</dimen>
    <dimen name="item_wifi_more_iv_disconnect_margin_start">21dp</dimen>
    <dimen name="item_wifi_more_iv_ip_marin_end">8dp</dimen>
    <dimen name="item_wifi_more_iv_manual_margin_end">15dp</dimen>
    <dimen name="item_wifi_more_line1_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_line_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_rl_manual_margin_top">18dp</dimen>
    <dimen name="item_wifi_more_title_margin_start">107dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_height">29dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_margin_start">49dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_padding_left">21dp</dimen>
    <dimen name="item_wifi_more_tv_dns_width">47dp</dimen>
    <dimen name="item_wifi_more_tv_ip_setting_width">147dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_bottom">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_end">21dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_start">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_tv_save_text_size">9sp</dimen>
    <dimen name="item_wifi_tv_state_margin_top">1dp</dimen>
    <dimen name="item_wifi_tv_state_text_size">8sp</dimen>
    <dimen name="item_wifi_tv_wifi_height">26dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_height">13dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_max_width">100dp</dimen>
    <dimen name="iv_back_height">20dp</dimen>
    <dimen name="iv_back_margin_start">13dp</dimen>
    <dimen name="iv_back_margin_top">11dp</dimen>
    <dimen name="iv_back_width">20dp</dimen>
    <dimen name="iv_bright_height">20dp</dimen>
    <dimen name="iv_bright_margin_start">24dp</dimen>
    <dimen name="iv_bright_margin_top">60dp</dimen>
    <dimen name="iv_bright_width">20dp</dimen>
    <dimen name="iv_screen_height">20dp</dimen>
    <dimen name="iv_screen_margin_top">33dp</dimen>
    <dimen name="iv_screen_width">20dp</dimen>
    <dimen name="iv_shutdown_height">27dp</dimen>
    <dimen name="iv_shutdown_margin_left">103dp</dimen>
    <dimen name="iv_shutdown_margin_top">134dp</dimen>
    <dimen name="iv_shutdown_width">27dp</dimen>
    <dimen name="iv_voice_margin_top">24dp</dimen>
    <dimen name="launcher_main_btn_start_height">40dp</dimen>
    <dimen name="launcher_main_btn_start_margin_top">56dp</dimen>
    <dimen name="launcher_main_btn_start_width">160dp</dimen>
    <dimen name="launcher_main_iv_no_signal_height">304dp</dimen>
    <dimen name="launcher_main_iv_no_signal_margin_top">202dp</dimen>
    <dimen name="launcher_main_iv_no_signal_width">304dp</dimen>
    <dimen name="launcher_main_iv_privacy_height">27dp</dimen>
    <dimen name="launcher_main_iv_privacy_width">27dp</dimen>
    <dimen name="launcher_main_tv_current_signal_margin_top">10dp</dimen>
    <dimen name="launcher_main_tv_current_signal_text_size">12sp</dimen>
    <dimen name="launcher_main_tv_hint_height">37dp</dimen>
    <dimen name="launcher_main_tv_hint_text_size">9sp</dimen>
    <dimen name="launcher_main_tv_hint_width">160dp</dimen>
    <dimen name="launcher_main_tv_hotline_height">40dp</dimen>
    <dimen name="launcher_main_tv_hotline_margin_top">16dp</dimen>
    <dimen name="launcher_main_tv_hotline_width">200dp</dimen>
    <dimen name="launcher_main_tv_no_signal_margin_top">467dp</dimen>
    <dimen name="launcher_main_tv_no_signal_text_size">16sp</dimen>
    <dimen name="launcher_main_tv_privacy_margin_top">30dp</dimen>
    <dimen name="launcher_main_tv_privacy_text_size">18sp</dimen>
    <dimen name="ll_select_width">120dp</dimen>
    <dimen name="ll_touch_lock_margin_bottom">37dp</dimen>
    <dimen name="ll_touch_lock_width">56dp</dimen>
    <dimen name="main_bg_radius">16dp</dimen>
    <dimen name="main_bg_stroke">0.5dp</dimen>
    <dimen name="main_cv_height">394dp</dimen>
    <dimen name="main_cv_width">394dp</dimen>
    <dimen name="main_height">384dp</dimen>
    <dimen name="main_line1_height">0.3dp</dimen>
    <dimen name="main_line1_margin_top">25dp</dimen>
    <dimen name="main_tv_title_margin_top">27dp</dimen>
    <dimen name="main_width">384dp</dimen>
    <dimen name="privacy_height">294dp</dimen>
    <dimen name="privacy_margin_top">21dp</dimen>
    <dimen name="privacy_width">337dp</dimen>
    <dimen name="rgb_btn_reset_width">232dp</dimen>
    <dimen name="rgb_et_height">20dp</dimen>
    <dimen name="rgb_et_width">40dp</dimen>
    <dimen name="rgb_green_gain_margin_top">24dp</dimen>
    <dimen name="rgb_line2_margin_top">25dp</dimen>
    <dimen name="rgb_red_gain_margin_top">23dp</dimen>
    <dimen name="rgb_reset_margin_bottom">12dp</dimen>
    <dimen name="rgb_sb_height">7dp</dimen>
    <dimen name="rgb_sb_margin_start">9dp</dimen>
    <dimen name="rgb_sb_margin_top">-4dp</dimen>
    <dimen name="rgb_sb_padding_end">7dp</dimen>
    <dimen name="rgb_sb_padding_start">7dp</dimen>
    <dimen name="rgb_sb_thumb_height">23dp</dimen>
    <dimen name="rgb_sb_width">195dp</dimen>
    <dimen name="rgb_tv_color_temperature_cold_margin_top">-5dp</dimen>
    <dimen name="rgb_tv_color_temperature_margin_top">19dp</dimen>
    <dimen name="rgb_tv_margin_start">16dp</dimen>
    <dimen name="sb_bright_height">20dp</dimen>
    <dimen name="sb_bright_margin_start">49dp</dimen>
    <dimen name="sb_bright_margin_top">60dp</dimen>
    <dimen name="sb_bright_max_height">40dp</dimen>
    <dimen name="sb_bright_padding_end">0dp</dimen>
    <dimen name="sb_bright_padding_start">0dp</dimen>
    <dimen name="sb_bright_width">151dp</dimen>
    <dimen name="sb_voice_margin_top">24dp</dimen>
    <dimen name="screen_dialog_content_text_size">9sp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_height">75dp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_margin_top">-1dp</dimen>
    <dimen name="screen_dialog_cv_wireless_screen_height">318dp</dimen>
    <dimen name="screen_dialog_iv_back_height">20dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_start">18dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_top">23dp</dimen>
    <dimen name="screen_dialog_iv_back_width">20dp</dimen>
    <dimen name="screen_dialog_iv_code_height">73dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_end">1dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_top">29dp</dimen>
    <dimen name="screen_dialog_iv_code_width">73dp</dimen>
    <dimen name="screen_dialog_iv_step_1_height">15dp</dimen>
    <dimen name="screen_dialog_iv_step_1_margin_top">26dp</dimen>
    <dimen name="screen_dialog_iv_step_1_width">15dp</dimen>
    <dimen name="screen_dialog_iv_step_2_margin_top">12dp</dimen>
    <dimen name="screen_dialog_iv_step_3_margin_top">39dp</dimen>
    <dimen name="screen_dialog_line2_margin_top">8dp</dimen>
    <dimen name="screen_dialog_rl_sub_wired_screen_margin_top">25dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_margin_top">8dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_width">341dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_height">65dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_margin_top">11dp</dimen>
    <dimen name="screen_dialog_rl_wireless_screen_height">308dp</dimen>
    <dimen name="screen_dialog_switch_enable_pin_code_margin_end">1dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_height">20dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_end">23dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_top">14dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_width">40dp</dimen>
    <dimen name="screen_dialog_title_text_size">12sp</dimen>
    <dimen name="screen_dialog_tv_code_line_height">13dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_end">-2dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_top">5dp</dimen>
    <dimen name="screen_dialog_tv_code_text_size">9sp</dimen>
    <dimen name="screen_dialog_tv_enable_pin_code_margin_top">8dp</dimen>
    <dimen name="screen_dialog_tv_margin_start">21dp</dimen>
    <dimen name="screen_dialog_tv_one_margin_start">25dp</dimen>
    <dimen name="screen_dialog_tv_order_height">21dp</dimen>
    <dimen name="screen_dialog_tv_order_width">21dp</dimen>
    <dimen name="screen_dialog_tv_pin_code_margin_top">4dp</dimen>
    <dimen name="screen_dialog_tv_signal_margin_top">11dp</dimen>
    <dimen name="screen_dialog_tv_step_1_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_line_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_start">6dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_top">1dp</dimen>
    <dimen name="screen_dialog_tv_title_margin_top">25dp</dimen>
    <dimen name="screen_offset_tv_margin_bottom">10dp</dimen>
    <dimen name="screen_offset_tv_margin_horizontal">40dp</dimen>
    <dimen name="screen_offset_tv_margin_top">240dp</dimen>
    <dimen name="screen_offset_tv_text_size">12sp</dimen>
    <dimen name="setting_line1_margin_top">11dp</dimen>
    <dimen name="setting_tv_title_margin_top">13dp</dimen>
    <dimen name="shutdown_btn_cancel_margin_left">93dp</dimen>
    <dimen name="shutdown_btn_confirm_height">28dp</dimen>
    <dimen name="shutdown_btn_confirm_margin_left">203dp</dimen>
    <dimen name="shutdown_btn_confirm_margin_top">37dp</dimen>
    <dimen name="shutdown_btn_confirm_text_size">11sp</dimen>
    <dimen name="shutdown_btn_confirm_width">88dp</dimen>
    <dimen name="shutdown_content_margin_top">11dp</dimen>
    <dimen name="shutdown_content_text_size">9sp</dimen>
    <dimen name="shutdown_tittle_margin_left">11dp</dimen>
    <dimen name="shutdown_tittle_margin_top">139dp</dimen>
    <dimen name="shutdown_tittle_text_size">12sp</dimen>
    <dimen name="signal_item_margin_top">11dp</dimen>
    <dimen name="signal_iv_margin_start">139dp</dimen>
    <dimen name="signal_tv_drawable_padding">17dp</dimen>
    <dimen name="signal_tv_height">48dp</dimen>
    <dimen name="signal_tv_margin_start">169dp</dimen>
    <dimen name="signal_tv_text_size">9sp</dimen>
    <dimen name="signal_tv_width">341dp</dimen>
    <dimen name="signal_tv_window_margin_top">21dp</dimen>
    <dimen name="sl_lock_height">37dp</dimen>
    <dimen name="sl_lock_width">37dp</dimen>
    <dimen name="sl_network_height">29dp</dimen>
    <dimen name="sl_network_margin_start">28dp</dimen>
    <dimen name="sl_network_margin_top">21dp</dimen>
    <dimen name="sl_network_shadow_offset_x">0dp</dimen>
    <dimen name="sl_network_shadow_offset_y">1dp</dimen>
    <dimen name="sl_screen_corner_radius">8dp</dimen>
    <dimen name="sl_screen_elevation">5dp</dimen>
    <dimen name="sl_screen_height">114dp</dimen>
    <dimen name="sl_screen_margin_start">16dp</dimen>
    <dimen name="sl_screen_margin_top">64dp</dimen>
    <dimen name="sl_screen_shadow_limit">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_x">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_y">0dp</dimen>
    <dimen name="sl_screen_stroke_width">0.2dp</dimen>
    <dimen name="sl_screen_width">114dp</dimen>
    <dimen name="sl_setting_margin_start">7dp</dimen>
    <dimen name="sl_setting_width">234dp</dimen>
    <dimen name="sl_wifi_margin_top">11dp</dimen>
    <dimen name="sl_write_margin_top">3dp</dimen>
    <dimen name="small_window_iv_home_height">13dp</dimen>
    <dimen name="small_window_iv_home_width">13dp</dimen>
    <dimen name="small_window_iv_volume_height">15dp</dimen>
    <dimen name="small_window_iv_volume_margin_end">5dp</dimen>
    <dimen name="small_window_iv_volume_width">15dp</dimen>
    <dimen name="small_window_shortcut_drawable_padding">3dp</dimen>
    <dimen name="small_window_shortcut_height">29dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_gone_margin_start">131dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_margin_end">21dp</dimen>
    <dimen name="small_window_shortcut_text_size">9sp</dimen>
    <dimen name="small_window_shortcut_tv_desktop_margin_start">11dp</dimen>
    <dimen name="small_window_shortcut_width">427dp</dimen>
    <dimen name="small_window_sv_volume_height">13dp</dimen>
    <dimen name="small_window_sv_volume_margin_end">11dp</dimen>
    <dimen name="small_window_sv_volume_padding_end">0dp</dimen>
    <dimen name="small_window_sv_volume_padding_start">0dp</dimen>
    <dimen name="small_window_sv_volume_width">157dp</dimen>
    <dimen name="small_window_tv_home_margin_start">16dp</dimen>
    <dimen name="spinner_dropdown_item_iv_margin_vertical">5dp</dimen>
    <dimen name="spinner_dropdown_item_tv_margin_start">3dp</dimen>
    <dimen name="toast_height">53dp</dimen>
    <dimen name="toast_msg_margin_start">8dp</dimen>
    <dimen name="toast_msg_text_size">12sp</dimen>
    <dimen name="toast_padding_horizontal">30dp</dimen>
    <dimen name="toast_padding_vertical">19dp</dimen>
    <dimen name="toast_resolution_corner_radius">8dp</dimen>
    <dimen name="toast_resolution_height">53dp</dimen>
    <dimen name="toast_resolution_margin_start">13dp</dimen>
    <dimen name="toast_resolution_tv_text_size">12sp</dimen>
    <dimen name="toast_resolution_width">83dp</dimen>
    <dimen name="toast_shutting_down_ops_height">65dp</dimen>
    <dimen name="toast_shutting_down_ops_iv_margin_top">10dp</dimen>
    <dimen name="toast_shutting_down_ops_tv_margin_top">5dp</dimen>
    <dimen name="toast_shutting_down_ops_width">120dp</dimen>
    <dimen name="toast_uhd_height">77dp</dimen>
    <dimen name="toast_uhd_iv_height">29dp</dimen>
    <dimen name="toast_uhd_iv_margin_start">13dp</dimen>
    <dimen name="toast_uhd_iv_width">59dp</dimen>
    <dimen name="toast_uhd_margin_bottom">94dp</dimen>
    <dimen name="toast_uhd_tv_martin_start">7dp</dimen>
    <dimen name="toast_uhd_tv_text_size">12sp</dimen>
    <dimen name="toast_uhd_width">240dp</dimen>
    <dimen name="toast_width">160dp</dimen>
    <dimen name="tv_desktop_margin_bottom">17dp</dimen>
    <dimen name="tv_desktop_margin_start">36dp</dimen>
    <dimen name="tv_desktop_margin_top">23dp</dimen>
    <dimen name="tv_desktop_text_size">10sp</dimen>
    <dimen name="tv_eye_margin_start">165dp</dimen>
    <dimen name="tv_lock_margin_top">5dp</dimen>
    <dimen name="tv_network_margin_start">55dp</dimen>
    <dimen name="tv_network_text_size">9sp</dimen>
    <dimen name="tv_rest_margin_desktop">14dp</dimen>
    <dimen name="tv_rest_margin_start">15dp</dimen>
    <dimen name="tv_restart_margin_start">44dp</dimen>
    <dimen name="tv_screen_drawable_padding">5dp</dimen>
    <dimen name="tv_screen_height">13dp</dimen>
    <dimen name="tv_screen_margin_top">58dp</dimen>
    <dimen name="tv_screen_text_size">9sp</dimen>
    <dimen name="tv_screen_width">50dp</dimen>
    <dimen name="tv_setting_margin_start">9dp</dimen>
    <dimen name="tv_setting_margin_top">25dp</dimen>
    <dimen name="tv_shutdown_width">50dp</dimen>
    <dimen name="tv_signal_margin_start">61dp</dimen>
    <dimen name="tv_touch_lock_margin_start">113dp</dimen>
    <dimen name="tv_touch_lock_width">80dp</dimen>
    <dimen name="wifi_dialog_input_password_btn_margin_top">20dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_height">29dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_margin_top">11dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_end">30dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_width">197dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_height">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_start">191dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_top">15dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_width">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_wifi_margin_start">21dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_wifi_margin_top">21dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_top">25dp</dimen>
    <dimen name="wifi_dialog_input_password_title_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_height">13dp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_width">100dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_start">24dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_top">8dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_text_size">8sp</dimen>
    <dimen name="window_ruler_iv_ruler_height">572dp</dimen>
    <dimen name="window_ruler_iv_ruler_width">572dp</dimen>
    <string name="DNS">DNS服务器</string>
    <string name="DNS1">DNS 1</string>
    <string name="DNS2">DNS 2</string>
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"转到首页"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"转到上一层级"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"更多选项"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"完成"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"查看全部"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"选择应用"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"关闭"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"开启"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt+"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Ctrl+"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"Delete 键"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"Enter 键"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Fn+"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta+"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Shift+"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"空格键"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym+"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menu+"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"搜索…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"清除查询"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"搜索查询"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"搜索"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"提交查询"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"语音搜索"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"分享对象"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"与<ns1:g id="APPLICATION_NAME">%s</ns1:g>分享"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"收起"</string>
    <string name="about">关于本机</string>
    <string name="adb">adb开关</string>
    <string name="adb_title">隐藏设置</string>
    <string name="android_version">安卓版本</string>
    <string name="annotate">批注</string>
    <string name="app_name">SystemSetting</string>
    <string name="app_name_ota">Ota</string>
    <string name="auto">自动</string>
    <string name="auto_update">自动更新</string>
    <string name="behind_hdmi1">HDMI 1</string>
    <string name="behind_hdmi2">HDMI 2</string>
    <string name="breath_led_on">Logo灯常亮</string>
    <string name="cancel">取消</string>
    <string name="cancel_download">取消下载</string>
    <string name="cancel_update_fail">取消</string>
    <string name="change_to_pc">切换到内置电脑</string>
    <string name="check_network">请检查大屏有线网络或Wi-Fi的连接情况</string>
    <string name="check_update">检查更新</string>
    <string name="checking_update">正在检查更新</string>
    <string name="click_lock">触控已锁定</string>
    <string name="color_temperature">色温</string>
    <string name="color_temperature_adjust">色温调节</string>
    <string name="color_temperature_cold">冷</string>
    <string name="color_temperature_eye_care">处于护眼模式</string>
    <string name="color_temperature_value_wrong">色温值为%d~%d</string>
    <string name="color_temperature_warm">暖</string>
    <string name="company">制造商</string>
    <string name="company_name">北京翼鸥教育科技有限公司</string>
    <string name="confirm">确认</string>
    <string name="confirm_update_fail">立即重启</string>
    <string name="confirm_update_success">我知道了</string>
    <string name="connect_fail">连接服务器失败，请检查网络后重试</string>
    <string name="current_sign">当前通道 [内置电脑]</string>
    <string name="current_sign_hdmi">当前通道 [HDMI]</string>
    <string name="current_sign_hdmi_1">当前通道 [HDMI 1]</string>
    <string name="current_sign_hdmi_2">当前通道 [HDMI 2]</string>
    <string name="current_sign_typec">当前通道 [USB C]</string>
    <string name="current_sign_windows_disabled">当前通道 [内置电脑]（禁用）</string>
    <string name="debug_menu">调试菜单</string>
    <string name="desktop">桌面</string>
    <string name="device_name">设备名称</string>
    <string name="disconnect">未连接</string>
    <string name="disconnect_network">断开此网络</string>
    <string name="download">正在下载，请稍后...</string>
    <string name="dynrec_fail">动态注册失败</string>
    <string name="eeo_screen_move_txt">点击上方或上滑恢复全屏</string>
    <string name="email"><EMAIL></string>
    <string name="english">English</string>
    <string name="error_md5">文件MD5不一致，请重新下载</string>
    <string name="extra">更多</string>
    <string name="eye">护眼</string>
    <string name="factory_menu">工厂菜单</string>
    <string name="factory_reset">恢复出厂</string>
    <string name="file_not_exist">更新包不存在，请重新下载</string>
    <string name="finish_lock">已解锁</string>
    <string name="forget_network">移除此网络</string>
    <string name="front_typec">USB C</string>
    <string name="gateway">网关</string>
    <string name="hardware_self_test">硬件一键自检</string>
    <string name="hardware_self_test_CPU">CPU</string>
    <string name="hardware_self_test_ethernet">有线网络</string>
    <string name="hardware_self_test_fail">失败</string>
    <string name="hardware_self_test_hard_disk">硬盘</string>
    <string name="hardware_self_test_memory">内存</string>
    <string name="hardware_self_test_mic">麦克风</string>
    <string name="hardware_self_test_no_test">未检测</string>
    <string name="hardware_self_test_success">正常</string>
    <string name="hardware_self_test_testing">检测中...</string>
    <string name="hardware_self_test_touch">触摸框</string>
    <string name="hardware_self_test_wifi">无线网络</string>
    <string name="have_update">大屏系统有新版本可以更新</string>
    <string name="have_update_hint">-系统更新需要重新启动\n-本次更新修复了一些重大问题，为保障您的正\n常使用，建议您立即更新</string>
    <string name="help">指南</string>
    <string name="hint_no_control">当前信号源暂时无法小屏控制</string>
    <string name="home">首页</string>
    <string name="hotline">400-077-1585</string>
    <string name="install_fail">系统安装失败</string>
    <string name="install_fail_reinstall">本次安装失败，您可尝试重新安装</string>
    <string name="install_ing">正在安装中(%d%%)，请稍后...</string>
    <string name="install_msg">系统安装需要几分钟时间，完成安装前一体机暂时无\n法使用。</string>
    <string name="install_right">立即安装</string>
    <string name="install_title">最新版本系统已下载，点击“立即安装”继续安装</string>
    <string name="ip_address">IP地址</string>
    <string name="ip_setting">IP设置</string>
    <string name="join">加入</string>
    <string name="language_and_locale">语言</string>
    <string name="language_change">语言切换</string>
    <string name="lastest_version">已经是最新版本</string>
    <string name="lock">触控锁</string>
    <string name="login">登录</string>
    <string name="login_cancel">取消</string>
    <string name="login_title">需要登录</string>
    <string name="mac_address">MAC地址</string>
    <string name="manual">手动</string>
    <string name="maximize">最大化</string>
    <string name="network">有线网络</string>
    <string name="network_error">网络异常，请检查大屏有线网络或Wi-Fi的连接情况</string>
    <string name="network_wifi_status_authenticating">正在进行身份验证…</string>
    <string name="network_wifi_status_blocked">已停用</string>
    <string name="network_wifi_status_connected">已连接</string>
    <string name="network_wifi_status_connected_no_internet">已连接，但无法访问互联网</string>
    <string name="network_wifi_status_connecting">正在连接…</string>
    <string name="network_wifi_status_disabled">"已停用"</string>
    <string name="network_wifi_status_disconnected">已断开连接</string>
    <string name="network_wifi_status_disconnecting">正在断开连接…</string>
    <string name="network_wifi_status_failed">失败</string>
    <string name="network_wifi_status_idle"/>
    <string name="network_wifi_status_network_failure">"IP 配置失败"</string>
    <string name="network_wifi_status_obtaining_ip_address">正在获取IP地址…</string>
    <string name="network_wifi_status_password_failure">"身份验证出现问题"</string>
    <string name="network_wifi_status_saved">已保存</string>
    <string name="network_wifi_status_scanning">正在扫描…</string>
    <string name="network_wifi_status_suspended">已暂停</string>
    <string name="network_wifi_status_verifying_poor_link">暂时关闭（网络状况不佳）</string>
    <string name="network_wifi_status_wifi_failure">"WLAN 连接失败"</string>
    <string name="no_network">没有网络</string>
    <string name="no_pc_1">未检测到电脑模块，请关机后确保电脑模\n块正确安装，然后尝试重启大屏(错误 01）\n如需帮助，请拨打服务热线：400-077-1585</string>
    <string name="no_pc_2">未检测到电脑模块，请关机后确保电脑模\n块正确安装，然后尝试重启大屏(错误 02）\n如需帮助，请拨打服务热线：400-077-1585</string>
    <string name="no_sign">无信号</string>
    <string name="no_sign_hdmi">未识别到信号，请检查信号线是否连接正常\n如需帮助，请拨打服务热线：400-077-1585</string>
    <string name="no_sign_msg">未识别到信号，可以尝试启动内置电脑\n如需帮助，请拨打服务热线：400-077-1585</string>
    <string name="no_sign_windows_disabled">内置电脑通道已禁用，可以在“设置-更多”启用\n如需帮助，请拨打服务热线：400-077-1585</string>
    <string name="not_found_new_version">未发现新版本</string>
    <string name="not_network">网络未连通</string>
    <string name="password">密码</string>
    <string name="password_error">* 密码错误</string>
    <string name="privacy">隐私保护中</string>
    <string name="privacy_policy">个人信息保护政策&amp;隐私协议</string>
    <string name="projection">投屏</string>
    <string name="r30_write_speed">R30书写预测</string>
    <string name="ready_install">正在准备安装，请稍候...</string>
    <string name="reinstall">重新安装</string>
    <string name="remaining">    剩余: </string>
    <string name="report_version_timeout">上报版本超时</string>
    <string name="reset_ops">本次还原大约需要5分钟，在此期间请勿断电</string>
    <string name="resolution">屏幕分辨率</string>
    <string name="restart">重启</string>
    <string name="restart_content">重启将会结束所有正在运行的应用程序</string>
    <string name="restart_countdown_confirm">重启(%ds)</string>
    <string name="restart_title">确认重启大屏？</string>
    <string name="resting">息屏</string>
    <string name="retry">重试</string>
    <string name="rgb_blue_gain">B-蓝色增益</string>
    <string name="rgb_blue_gain_value_wrong">B值为0~255</string>
    <string name="rgb_green_gain">G-绿色增益</string>
    <string name="rgb_green_gain_value_wrong">G值为0~255</string>
    <string name="rgb_red_gain">R-红色增益</string>
    <string name="rgb_red_gain_value_wrong">R值为0~255</string>
    <string name="rgb_reset">恢复默认参数</string>
    <string name="right_update">立即更新</string>
    <string name="running_memory">运行内存</string>
    <string name="save">存储</string>
    <string name="saved_network">已保存网络</string>
    <string name="screen_activation_status_activated">已激活</string>
    <string name="screen_activation_status_getting">读取中...</string>
    <string name="screen_activation_status_nonactivated">未激活</string>
    <string name="screen_air_play">AirPlay</string>
    <string name="screen_msg1">扫码或浏览器输入\ntranscreen.app下\n载客户端</string>
    <string name="screen_name">手机/电脑投屏</string>
    <string name="screen_permission_refuse">拒绝</string>
    <string name="screen_pin_code">投屏码：%s</string>
    <string name="screen_pin_code2">投屏码</string>
    <string name="screen_step_1">下载投屏客户端Transcreen</string>
    <string name="screen_step_2">连接设备热点\n名称：%s\n密码：%s</string>
    <string name="screen_step_3">客户端投屏\n打开Transcreen\n选择【%s】投屏</string>
    <string name="screen_text">投屏</string>
    <string name="screen_title">大屏控制</string>
    <string name="screen_wifi_connect">连接以下Wi-Fi：</string>
    <string name="screen_wifi_eeo_guest"> EEO-Guest</string>
    <string name="screen_wifi_eeo_password"> eeoguest123</string>
    <string name="screen_wifi_name">名称：</string>
    <string name="screen_wifi_password">密码：</string>
    <string msgid="6264217191555673260" name="search_menu_title">"搜索"</string>
    <string name="select_network">选择网络</string>
    <string name="serial_number">序列号</string>
    <string name="service_email">服务邮箱</string>
    <string name="service_hotline">服务热线</string>
    <string name="setting">设置</string>
    <string name="shutdown">关机</string>
    <string name="shutdown_content">关机将会结束所有正在运行的应用程序</string>
    <string name="shutdown_countdown_cancel">取消</string>
    <string name="shutdown_countdown_confirm">关机(%ds)</string>
    <string name="shutdown_countdown_content">检测到Windows 电脑未正常关闭，请确认\nWindows 电脑是否需要强制关机？</string>
    <string name="shutdown_countdown_title">关机确认</string>
    <string name="shutdown_title">确认关闭大屏</string>
    <string name="sign">信号源</string>
    <string name="simplified_chinese">简体中文</string>
    <string name="start_computer">启动内置电脑</string>
    <string name="startup_channel">开机信号源</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
    <string name="store_memory">存储内存</string>
    <string name="stroke_algorithm">笔锋算法</string>
    <string name="sub_device_update_fail">更新失败</string>
    <string name="sub_device_update_success">更新成功</string>
    <string name="subnet_mask">子网掩码</string>
    <string name="switch_wifi">Wi-Fi开关</string>
    <string name="system_lastest_version">系统版本已经是最新版本</string>
    <string name="system_version">系统版本</string>
    <string name="toast_fail">网络连接失败</string>
    <string name="toast_resolution">%s   %dX%d @ %dHz</string>
    <string name="toast_set_ip_fail">设置失败</string>
    <string name="toast_shutting_down_ops">内置电脑关机中</string>
    <string name="toast_success">网络连接成功</string>
    <string name="total">总共: </string>
    <string name="touch_calibration">触摸框校准</string>
    <string name="touch_slider">半屏滑条</string>
    <string name="touch_unplugged">未识别到触摸框</string>
    <string name="touch_version">触摸框版本</string>
    <string name="uhd_hdmi">请选择 HDMI 2.0 以上符合\n“超高清”信号的线材。</string>
    <string name="uhd_type_c">请选择 全功能Type-C 符合\n“超高清”信号的线材。</string>
    <string name="unlock">点击解锁</string>
    <string name="updatable">可更新至：</string>
    <string name="update_description">更新内容:</string>
    <string name="update_fail">系统更新失败</string>
    <string name="update_fail_content">建议您立即重启。如触控失效，您可以长按\n关机键5s，待大屏关机后再重启</string>
    <string name="update_msg">系统更新需要几分钟时间，更新过程中一体机暂时无\n法使用。</string>
    <string name="update_success">系统更新成功</string>
    <string name="update_success_content">已为您升级到最新版本</string>
    <string name="updating">正在更新中，请稍候…</string>
    <string name="updating_touch">触摸框升级中，请不要关机！</string>
    <string name="wifi">Wi—Fi</string>
    <string name="wifi_behind_str">“的密码</string>
    <string name="wifi_device_error">无线网络设备工作异常，请联系售后</string>
    <string name="wifi_front_str">请输入“</string>
    <string name="wifi_more">Wi-Fi详情</string>
    <string name="wifi_network">网络</string>
    <string name="window_factory_reset_content">恢复出厂设置将清除android的所有数据。</string>
    <string name="window_factory_reset_title">确认要恢复出厂设置吗？</string>
    <string name="window_host">Windows 系统还原</string>
    <string name="window_host_content">还原Windows系统将清除电脑C盘的所有数据。</string>
    <string name="window_host_title">确认要还原Windows系统吗？</string>
    <string name="windows">内置电脑</string>
    <string name="windows_disable">内置电脑通道禁用</string>
    <string name="windows_disabled">内置电脑（禁用）</string>
    <string name="windows_task_manager">Windows任务管理器</string>
    <string name="wired_screen_name">有线投屏</string>
    <string name="wireless_screen">无线投屏</string>
    <string name="wireless_screen_disabled">无线投屏（已禁用）</string>
    <string name="wireless_screen_disabled_toast">无线投屏已禁用，如需使用请前往设置开启</string>
    <string name="wireless_screen_enable">开启无线投屏</string>
    <string name="wireless_screen_enable_pin_code">启用投屏码</string>
    <string name="wireless_screen_name">无线投屏</string>
    <string name="write_acceleration">板书加速</string>
    <string name="write_text">批注</string>
    <string name="write_without_screen_on">落笔直接写</string>
</resources>