<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.eeo.ota"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:supportsRtl="true">
        <receiver
            android:name=".receiver.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="2147483647">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name=".service.SubDeviceUpdateService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.eeo.ota.action.SubDeviceUpdateService" />
            </intent-filter>
        </service>

        <service
            android:name=".service.AutoUpdateService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.eeo.ota.action.AutoUpdateService" />
            </intent-filter>
        </service>

        <service
            android:name=".arraymic.ArrayMicUpdateService"
            android:exported="false" />
    </application>

</manifest>
