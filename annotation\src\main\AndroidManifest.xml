<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.eeo.annotation"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.MANAGE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:allowBackup="true"
        android:icon="@drawable/pen_eraser"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.Annotation"
        tools:targetApi="31">
        <activity
            android:name=".ScreenCaptureActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name=".AnnotationService"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="mediaProjection">

            <intent-filter>
                <action android:name="com.eeo.action.Annotation" />
            </intent-filter>
        </service>
    </application>

</manifest>