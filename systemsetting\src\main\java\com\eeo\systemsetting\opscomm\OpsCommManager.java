package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.util.Log;

/**
 * Windows串口通信管理器
 * 负责处理Windows发送的MAC地址获取指令
 * 采用与Rs232Manager一致的架构模式
 * 
 * 当前阶段：专注于MAC地址获取功能
 * 后续阶段：扩展关机控制和信号源切换功能
 * 
 * 实现方式：使用JNI原生代码直接操作串口，确保字节级数据完整性
 */
public class OpsCommManager {
    private static final String TAG = "OpsCommManager";
    private Context mContext;
    private static OpsCommManager mOpsCommManager = null;

    // 核心组件
    private JniSerialPortManager mSerialPortManager;  // JNI版本串口管理器
    private ProtocolHandler mProtocolHandler;
    private MacAddressHandler mMacAddressHandler;
    private PowerControlHandler mPowerControlHandler;
    private SignalSwitchHandler mSignalSwitchHandler;

    // 运行状态
    private volatile boolean mIsRunning = false;

    // 统计信息
    private long mCommandCount = 0;
    private long mSuccessCount = 0;
    private long mErrorCount = 0;

    /**
     * 私有构造函数，单例模式
     */
    private OpsCommManager(Context context) {
        mContext = context.getApplicationContext();
        init();
    }

    /**
     * 获取单例实例
     */
    public static OpsCommManager getInstance(Context context) {
        if (mOpsCommManager == null) {
            synchronized (OpsCommManager.class) {
                if (mOpsCommManager == null) {
                    mOpsCommManager = new OpsCommManager(context);
                }
            }
        }
        return mOpsCommManager;
    }

    /**
     * 初始化各组件
     */
    private void init() {
        Log.d(TAG, "OpsCommManager initializing with JNI implementation...");
        
        try {
            // 初始化MAC地址处理器
            mMacAddressHandler = new MacAddressHandler(mContext);
            
            // 初始化关机控制处理器
            mPowerControlHandler = new PowerControlHandler(mContext);
            
            // 初始化信号源切换处理器
            mSignalSwitchHandler = new SignalSwitchHandler(mContext);
            
            // 初始化协议处理器（内部会创建PowerControlHandler，这里我们用自己的实例）
            mProtocolHandler = new ProtocolHandler(mContext, mMacAddressHandler);
            
            // 初始化JNI串口管理器
            mSerialPortManager = new JniSerialPortManager(mContext, mProtocolHandler);
            
            // 设置组件间的引用关系
            mProtocolHandler.setOpsCommManager(this);
            mProtocolHandler.setJniSerialPortManager(mSerialPortManager);
            
            Log.d(TAG, "OpsCommManager initialized successfully with JNI implementation, PowerControl and SignalSwitch support");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize OpsCommManager", e);
        }
    }

    /**
     * 启动串口通信服务
     */
    public synchronized boolean start() {
        if (mIsRunning) {
            Log.w(TAG, "OpsCommManager is already running");
            return true;
        }

        Log.d(TAG, "Starting OpsCommManager with JNI implementation...");
        
        try {
            if (mSerialPortManager != null && mSerialPortManager.start()) {
                mIsRunning = true;
                Log.d(TAG, "OpsCommManager started successfully");
                return true;
            } else {
                Log.e(TAG, "Failed to start JNI SerialPortManager");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "Exception while starting OpsCommManager", e);
            return false;
        }
    }

    /**
     * 停止串口通信服务
     */
    public synchronized void stop() {
        if (!mIsRunning) {
            Log.w(TAG, "OpsCommManager is not running");
            return;
        }

        Log.d(TAG, "Stopping OpsCommManager...");
        
        try {
            if (mSerialPortManager != null) {
                mSerialPortManager.stop();
            }
            mIsRunning = false;
            Log.d(TAG, "OpsCommManager stopped successfully");
        } catch (Exception e) {
            Log.e(TAG, "Exception while stopping OpsCommManager", e);
        }
    }

    /**
     * 检查运行状态
     */
    public boolean isRunning() {
        return mIsRunning;
    }

    /**
     * 手动获取以太网MAC地址（用于测试）
     */
    public String getEthMacAddress() {
        if (mMacAddressHandler != null) {
            return mMacAddressHandler.getEthMacAddress();
        }
        return null;
    }

    /**
     * 获取关机控制处理器（用于外部访问）
     */
    public PowerControlHandler getPowerControlHandler() {
        return mPowerControlHandler;
    }

    /**
     * 获取信号源切换处理器（用于外部访问）
     */
    public SignalSwitchHandler getSignalSwitchHandler() {
        return mSignalSwitchHandler;
    }

    /**
     * 获取当前使用的实现类型
     */
    public String getCurrentImplementation() {
        return "JNI (Native)";
    }

    /**
     * 获取详细统计信息
     */
    public String getDetailedStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== OpsCommManager Statistics ===\n");
        sb.append("Implementation: JNI (Native)\n");
        sb.append("Running: ").append(isRunning()).append("\n");
        sb.append("Commands: ").append(mCommandCount).append("\n");
        sb.append("Success: ").append(mSuccessCount).append("\n");
        sb.append("Errors: ").append(mErrorCount).append("\n");
        sb.append("Success Rate: ").append(String.format("%.2f%%", 
                mCommandCount > 0 ? (mSuccessCount * 100.0 / mCommandCount) : 0.0)).append("\n");
        
        // 添加JNI串口管理器统计信息
        if (mSerialPortManager != null) {
            sb.append("JNI Statistics: ").append(mSerialPortManager.getStatistics()).append("\n");
        }
        
        return sb.toString();
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("Commands: %d, Success: %d, Errors: %d, Success Rate: %.2f%%",
                mCommandCount, mSuccessCount, mErrorCount,
                mCommandCount > 0 ? (mSuccessCount * 100.0 / mCommandCount) : 0.0);
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        mCommandCount = 0;
        mSuccessCount = 0;
        mErrorCount = 0;
        Log.d(TAG, "Statistics reset");
    }

    /**
     * 内部方法：增加指令计数
     */
    void incrementCommandCount() {
        mCommandCount++;
    }

    /**
     * 内部方法：增加成功计数
     */
    void incrementSuccessCount() {
        mSuccessCount++;
    }

    /**
     * 内部方法：增加错误计数
     */
    void incrementErrorCount() {
        mErrorCount++;
    }



    /**
     * 销毁资源（在服务销毁时调用）
     */
    public void destroy() {
        Log.d(TAG, "Destroying OpsCommManager...");
        stop();
        mOpsCommManager = null;
    }
} 