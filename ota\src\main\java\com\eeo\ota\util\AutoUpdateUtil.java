package com.eeo.ota.util;

import android.util.Log;

import com.eeo.ota.bean.Constant;

import java.util.Calendar;

public class AutoUpdateUtil {
    private static final String TAG = "ota-AutoUpdateUtil";

    /**
     * 夜晚更新
     * 2:00～6:00
     */
    public static boolean isNight() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour < 2 || hour >= 6) {
            Log.d(TAG, "isNight: false");
            return false;
        } else {
            Log.d(TAG, "isNight: true");
            return true;
        }
    }

    /**
     * 中午仅下载
     * 12:10～13:30
     */
    public static boolean isNoon() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int min = calendar.get(Calendar.MINUTE);
        if (hour == 12 && min >= 10) {
            Log.d(TAG, "isNoon: true");
            return true;
        } else if (hour == 13 && min <= 30) {
            Log.d(TAG, "isNoon: true");
            return true;
        } else {
            Log.d(TAG, "isNoon: false");
            return false;
        }
    }

    public static boolean isAutoUpdate() {
        //目前只有CXB01是自动更新的
        if (Constant.IS_CXB01) {
            Log.d(TAG, "isAutoUpdate: true");
            return true;
        }
        Log.d(TAG, "isAutoUpdate: false");
        return false;
    }

}
