package com.eeo.annotation.view;

import static com.eeo.annotation.bean.Constant.ENTER_ANNOTATION_WRITE;
import static com.eeo.annotation.bean.Constant.ENTER_SLEEP_WAIT;
import static com.eeo.annotation.bean.Constant.ENTER_SLEEP_WRITE;
import static com.eeo.annotation.bean.Constant.EXIT_ANNOTATION;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.PixelFormat;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.droidlogic.app.SystemControlManager;
import com.eeo.WhiteboardAccelerate.IDrawNoteInterface;
import com.eeo.annotation.AnnotationService;
import com.eeo.annotation.R;
import com.eeo.annotation.bean.Constant;
import com.eeo.annotation.util.Util;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;

/**
 * 批注window：由三个view组成
 * ①批注View：画板
 * ②笔盘View：选择画笔、橡皮擦、退出
 * ③清空View：二次点击橡皮擦后才显示
 */
public class AnnotationWindow implements View.OnClickListener, View.OnTouchListener {
    private final static String TAG = "AnnotationWindow";

    public static final int ID_PEN_RED = 0;
    public static final int ID_PEN_YELLOW = 1;
    public static final int ID_PEN_BLUE = 2;
    public static final int ID_ERASER = 3;
    public static final int ID_EXIT = 4;
    public static final int ID_CLEAR_ALL = 5;

    private final Context mContext;

    private WindowManager mWindowManager;

    /**
     * 批注View
     */
    private View mAnnotationView;
    private WindowManager.LayoutParams mAnnotationLayoutParams;

    /**
     * 笔盘View
     */
    private View mMenuView;
    private WindowManager.LayoutParams mMenuLayoutParams;

    /**
     * 3种画笔
     */
    private ImageView[] mImageViews = new ImageView[6];
    private TextView mClearAllTv;

    /**
     * 是否选中橡皮擦
     * 选中橡皮擦后再次点击橡皮擦显示清空
     */
    private boolean mIsButtonEraser;

    /**
     * 清空View
     */
    private View mClearAllView;
    private WindowManager.LayoutParams mClearAllLayoutParams;
    private boolean mHasClearAllViewShown;

    /**
     * 画板view
     */
    private DrawingView mDrawingView;
    private View mSleepWriteView;
    private ImageButton mEnterScreenButton;

    private int lastAnnotationStatus;
    private boolean mHasShown;

    private int mPenId;  //记录之前的画笔，不记录橡皮，用来恢复上一次画笔
    private int mPenColor;
    private int mPenSize = Constant.SIZE_PEN;
    private int mEraserSize = Constant.BUTTON_ERASER_SIZE;
    private int mPenBackground;

    /**
     * 屏幕尺寸
     */
    private int mScreenWidth;
    private int mScreenHeight;

    /**
     * 召唤批注笔盘的位置
     */
    private int mX;
    private int mY;

    /**
     * 笔盘拖动
     */
    private int mTouchStartX;
    private int mTouchStartY;
    private int mTouchCurrentX;
    private int mTouchCurrentY;
    private int mMoveX;
    private int mMoveY;

    private Udi mUdi;

    public AnnotationWindow(Context context, int screenWidth, int screenHeight) {
        mContext = context;
        mScreenWidth = screenWidth;
        mScreenHeight = screenHeight;
        mUdi = new Udi(mContext, UdiConstant.TOKEN_ANNOTATION);
        initPenSize();
    }

    private void initPenSize() {
        if (Constant.IS_BS65A) {
            mPenSize = Constant.SIZE_PEN_BS65A;
        } else if (Constant.IS_BS75A || Constant.IS_BSP75A) {
            mPenSize = Constant.SIZE_PEN_BS75A;
        } else if (Constant.IS_BS86A || Constant.IS_BSP86A) {
            mPenSize = Constant.SIZE_PEN_BS86A;
        } else if (Constant.IS_BS110A || Constant.IS_BSP110A) {
            mPenSize = Constant.SIZE_PEN_BS110A;
        }
    }

    private void initDrawingView() {
        mAnnotationView = LayoutInflater.from(mContext).inflate(R.layout.window_annotation, null);
        mDrawingView = (DrawingView) mAnnotationView.findViewById(R.id.drawing_view);
        mSleepWriteView = mAnnotationView.findViewById(R.id.sleep_write_view);
        mEnterScreenButton = mAnnotationView.findViewById(R.id.enter_screen);
        mEnterScreenButton.setOnClickListener(this);
        mSleepWriteView.setVisibility(View.GONE);
        mDrawingView.setScreenSize(mScreenWidth, mScreenHeight);
        mSleepWriteView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    setAnnotationWriteStatus(ENTER_SLEEP_WRITE);
                }
                return false;
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initMenuView() {
        mMenuView = LayoutInflater.from(mContext).inflate(R.layout.window_menu, null);
        mImageViews[ID_PEN_RED] = (ImageView) mMenuView.findViewById(R.id.pen_red);
        mImageViews[ID_PEN_YELLOW] = (ImageView) mMenuView.findViewById(R.id.pen_yellow);
        mImageViews[ID_PEN_BLUE] = (ImageView) mMenuView.findViewById(R.id.pen_blue);
        mImageViews[ID_ERASER] = (ImageView) mMenuView.findViewById(R.id.pen_eraser);
        mImageViews[ID_EXIT] = (ImageView) mMenuView.findViewById(R.id.exit);
        mImageViews[ID_CLEAR_ALL] = (ImageView) mMenuView.findViewById(R.id.pen_clear_all);
        for (ImageView imageView : mImageViews) {
            imageView.setOnClickListener(this);
            //imageView.setOnTouchListener(this);
        }
        mMenuView.setVisibility(View.GONE);
        mMenuView.setOnTouchListener(this);
    }

    private void initClearAllView() {
        mClearAllView = LayoutInflater.from(mContext).inflate(R.layout.window_clear_all, null);
        mClearAllTv = mClearAllView.findViewById(R.id.tv_clear_all);
        mClearAllTv.setOnClickListener(this);
        mClearAllView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_OUTSIDE) {
                    removeClearAllView();
                }
                return false;
            }
        });
    }

    public void showAnnotationWindow(int x, int y, int annotationStatus) {
        mX = x;
        mY = y;
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        }
        addDrawingView();
        addMenuView();
        mHasShown = true;
        setPen(ID_PEN_RED);
        Util.setAnnotation(mContext, annotationStatus == ENTER_ANNOTATION_WRITE ? 1 : 2);
        Udi.sendOpsTouchEnableBroadcast(mContext, false);
    }

    public void setAnnotationWriteStatus(int status) {
        switch (status) {
            case ENTER_ANNOTATION_WRITE:
                mSleepWriteView.setVisibility(View.GONE);
                mMenuView.setVisibility(View.VISIBLE);
                mDrawingView.setBackgroundBitmap(null);
                break;
            case EXIT_ANNOTATION:
                exit();
                if (lastAnnotationStatus == ENTER_SLEEP_WAIT || lastAnnotationStatus == ENTER_SLEEP_WRITE) {
                    SystemControlManager.getInstance().setBreathLedStatus(1);//如果上次是熄屏写状态，则设置呼吸灯唤醒
                }
                break;
            case ENTER_SLEEP_WRITE:
                mSleepWriteView.animate().alpha(0.0f).setDuration(500).withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        mSleepWriteView.setVisibility(View.GONE);
                        mMenuView.setVisibility(View.VISIBLE);
                    }
                });
                break;
            case ENTER_SLEEP_WAIT:
                mSleepWriteView.setVisibility(View.VISIBLE);
                mMenuView.setVisibility(View.GONE);
                mDrawingView.setBackgroundBitmap(BitmapFactory.decodeResource(mContext.getResources(), R.drawable.pen_sleep_write_bg));
                SystemControlManager.getInstance().setBreathLedStatus(2);//进入呼吸状态
                break;
        }
        lastAnnotationStatus = status;
    }

    private void addDrawingView() {
        if (mAnnotationView == null) {
            initDrawingView();
        }
        mAnnotationLayoutParams = new WindowManager.LayoutParams();
        mAnnotationLayoutParams.format = PixelFormat.RGBA_8888;
        mAnnotationLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
//        mAnnotationLayoutParams.setTitle("projection");    //虚显过滤标识判断
        if (mHasShown) {
            mWindowManager.updateViewLayout(mAnnotationView, mAnnotationLayoutParams);
        } else {
            mWindowManager.addView(mAnnotationView, mAnnotationLayoutParams);
        }
    }

    private void addMenuView() {
        if (mMenuView == null) {
            initMenuView();
        }
        mMenuLayoutParams = new WindowManager.LayoutParams();
        mMenuLayoutParams.format = PixelFormat.RGBA_8888;
        mMenuLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        mMenuLayoutParams.setTitle("projection");    //虚显过滤标识判断
        mMenuLayoutParams.gravity = Gravity.START | Gravity.TOP;
        mMenuLayoutParams.width = Constant.MENU_WIDTH;
        mMenuLayoutParams.height = Constant.MENU_HEIGHT;
        mMenuLayoutParams.x = mX - mMenuLayoutParams.width / 2;
        mMenuLayoutParams.y = mY - mMenuLayoutParams.height / 2;
        if (mMenuLayoutParams.x < Constant.MARGIN) {
            //超出屏幕左边缘
            mMenuLayoutParams.x = Constant.MARGIN;
        } else if (mMenuLayoutParams.x + mMenuLayoutParams.width + Constant.MARGIN > mScreenWidth) {
            //超出屏幕右边缘
            mMenuLayoutParams.x = mScreenWidth - mMenuLayoutParams.width - Constant.MARGIN;
        }
        if (mMenuLayoutParams.y < Constant.MARGIN) {
            mMenuLayoutParams.y = Constant.MARGIN;
        } else if (mMenuLayoutParams.y + mMenuLayoutParams.height + Constant.MARGIN > mScreenHeight) {
            mMenuLayoutParams.y = mScreenHeight - mMenuLayoutParams.height - Constant.MARGIN;
        }
        mMenuLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL /*|
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE*/;
        if (mHasShown) {
            mWindowManager.updateViewLayout(mMenuView, mMenuLayoutParams);
        } else {
            mWindowManager.addView(mMenuView, mMenuLayoutParams);
        }
    }

    private void addClearAllView() {
        if (mClearAllView == null) {
            initClearAllView();
        }
        mClearAllLayoutParams = new WindowManager.LayoutParams();
        mClearAllLayoutParams.format = PixelFormat.RGBA_8888;
        mClearAllLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        mClearAllLayoutParams.setTitle("projection");    //虚显过滤标识判断
        mClearAllLayoutParams.gravity = Gravity.START | Gravity.TOP;
        mClearAllLayoutParams.width = Constant.CLEAR_ALL_WIDTH;
        mClearAllLayoutParams.height = Constant.CLEAR_ALL_HEIGHT;
        mClearAllLayoutParams.x = mMenuLayoutParams.x + Constant.CLEAR_ALL_MARGIN_LEFT;
        //笔盘位于屏幕上1/3时，清空view出现在下方，否则出现在上方
        if (mMenuLayoutParams.y < mScreenHeight / 3) {
            mClearAllLayoutParams.y = mMenuLayoutParams.y + mMenuLayoutParams.height + Constant.CLEAR_ALL_MARGIN_BOTTOM;
        } else {
            mClearAllLayoutParams.y = mMenuLayoutParams.y - Constant.CLEAR_ALL_HEIGHT - Constant.CLEAR_ALL_MARGIN_BOTTOM;
        }
        mClearAllLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL /*|
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE*/
                | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
        if (mHasClearAllViewShown) {
            mWindowManager.updateViewLayout(mClearAllView, mClearAllLayoutParams);
        } else {
            mWindowManager.addView(mClearAllView, mClearAllLayoutParams);
        }
        mHasClearAllViewShown = true;
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View v) {
        mDrawingView.setClearAccelerateCanvas();
        switch (v.getId()) {
            case R.id.pen_red:
                setPen(ID_PEN_RED);
                break;
            case R.id.pen_yellow:
                setPen(ID_PEN_YELLOW);
                break;
            case R.id.pen_blue:
                setPen(ID_PEN_BLUE);
                break;
            case R.id.pen_eraser:
                setPen(ID_ERASER);
                break;
            case R.id.exit:
            case R.id.enter_screen:
                setAnnotationWriteStatus(EXIT_ANNOTATION);
                break;
            /*case R.id.tv_clear_all:
                mDrawingView.clear();
                //恢复上次的画笔
                setPen(mPenId);
                break;*/
            case R.id.pen_clear_all:
                mDrawingView.clear();
                //恢复上次的画笔
                setPen(mPenId);
                break;
            default:
                break;
        }
    }

    /**
     * 笔盘任意位置可以拖动
     */
    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN ||
                event.getAction() == MotionEvent.ACTION_UP) {
            mTouchStartX = (int) event.getRawX();
            mTouchStartY = (int) event.getRawY();
            mMoveX = 0;
            mMoveY = 0;
            mDrawingView.setClearAccelerateCanvas();
        } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
            mTouchCurrentX = (int) event.getRawX();
            mTouchCurrentY = (int) event.getRawY();
            mMoveX = mTouchCurrentX - mTouchStartX;
            mMoveY = mTouchCurrentY - mTouchStartY;
            mTouchStartX = mTouchCurrentX;
            mTouchStartY = mTouchCurrentY;
            mMenuLayoutParams.x += mMoveX;
            mMenuLayoutParams.y += mMoveY;
            if (mMenuLayoutParams.x < Constant.MARGIN) {
                //超出屏幕左边缘
                mMenuLayoutParams.x = Constant.MARGIN;
            } else if (mMenuLayoutParams.x + mMenuLayoutParams.width + Constant.MARGIN > mScreenWidth) {
                //超出屏幕右边缘
                mMenuLayoutParams.x = mScreenWidth - mMenuLayoutParams.width - Constant.MARGIN;
            }
            if (mMenuLayoutParams.y < Constant.MARGIN) {
                mMenuLayoutParams.y = Constant.MARGIN;
            } else if (mMenuLayoutParams.y + mMenuLayoutParams.height + Constant.MARGIN > mScreenHeight) {
                mMenuLayoutParams.y = mScreenHeight - mMenuLayoutParams.height - Constant.MARGIN;
            }
            mWindowManager.updateViewLayout(mMenuView, mMenuLayoutParams);

            if (mHasClearAllViewShown) {
                mClearAllLayoutParams.x = mMenuLayoutParams.x + Constant.CLEAR_ALL_MARGIN_LEFT;
                //笔盘位于屏幕上1/3时，清空view出现在下方，否则出现在上方
                if (mMenuLayoutParams.y < mScreenHeight / 3) {
                    mClearAllLayoutParams.y = mMenuLayoutParams.y + mMenuLayoutParams.height + Constant.CLEAR_ALL_MARGIN_BOTTOM;
                } else {
                    mClearAllLayoutParams.y = mMenuLayoutParams.y - Constant.CLEAR_ALL_HEIGHT - Constant.CLEAR_ALL_MARGIN_BOTTOM;
                }
                mWindowManager.updateViewLayout(mClearAllView, mClearAllLayoutParams);
            }
        }
        return false;
    }

    /**
     * 设置画笔
     */
    public void setPen(int id) {
        switch (id) {
            case ID_PEN_RED:
                mPenId = ID_PEN_RED;
                mPenColor = mContext.getResources().getColor(R.color.pen_red);
                mPenBackground = R.drawable.pen_red_bg;
                break;
            case ID_PEN_YELLOW:
                mPenId = ID_PEN_YELLOW;
                mPenColor = mContext.getResources().getColor(R.color.pen_yellow);
                mPenBackground = R.drawable.pen_yellow_bg;
                break;
            case ID_PEN_BLUE:
                mPenId = ID_PEN_BLUE;
                mPenColor = mContext.getResources().getColor(R.color.pen_blue);
                mPenBackground = R.drawable.pen_blue_bg;
                break;
            case ID_ERASER:
                break;
        }
        if (id == ID_ERASER) {
            mDrawingView.setBrush(true, mPenColor, mEraserSize);
            /*if (mIsButtonEraser) {
                addClearAllView();
            }*/
            mIsButtonEraser = true;
        } else {
            mDrawingView.setBrush(false, mPenColor, mPenSize);
            /*removeClearAllView();*/
            mIsButtonEraser = false;
        }
        setSelectedBackground(id);
    }

    /**
     * 设置画笔、橡皮擦选中状态背景
     */
    @SuppressLint("UseCompatLoadingForDrawables")
    private void setSelectedBackground(int id) {
        for (int i = 0; i < 3; i++) {
            if (i == id) {
                mImageViews[i].setBackgroundResource(mPenBackground);
            } else {
                mImageViews[i].setBackground(null);
            }
        }
        if (ID_ERASER == id) {
            mImageViews[ID_ERASER].setImageDrawable(mContext.getResources().getDrawable(R.drawable.pen_eraser_selected, null));
        } else {
            mImageViews[ID_ERASER].setImageDrawable(mContext.getResources().getDrawable(R.drawable.pen_eraser, null));
        }
        if (ID_CLEAR_ALL == id) {
            mImageViews[ID_CLEAR_ALL].setImageDrawable(mContext.getResources().getDrawable(R.drawable.clear_all_selected, null));
        } else {
            mImageViews[ID_CLEAR_ALL].setImageDrawable(mContext.getResources().getDrawable(R.drawable.clear_all, null));
        }
    }

    /**
     * 设置批注背景：截图bitmap
     */
    public void setBackgroundBitmap(Bitmap bitmap) {
        mDrawingView.setBackgroundBitmap(bitmap);
    }

    public void dismissAnnotationWindow() {
        if (mWindowManager != null) {
            Log.e(TAG, "dismissAnnotationWindow : removeView");
            if (mAnnotationView != null && mAnnotationView.isAttachedToWindow()) {
                mWindowManager.removeView(mAnnotationView);
            }
            if (mMenuView != null && mMenuView.isAttachedToWindow()) {
                mWindowManager.removeView(mMenuView);
            }
            if (mClearAllView != null && mClearAllView.isAttachedToWindow()) {
                mWindowManager.removeView(mClearAllView);
            }
            mHasShown = false;
            mHasClearAllViewShown = false;
        }
        Util.setAnnotation(mContext, 0);
        Udi.sendOpsTouchEnableBroadcast(mContext, true);
    }

    public void removeClearAllView() {
        if (mWindowManager != null) {
            if (mClearAllView != null && mClearAllView.isAttachedToWindow()) {
                Log.e(TAG, "removeClearAllView");
                mWindowManager.removeView(mClearAllView);
            }
        }
        mHasClearAllViewShown = false;
    }

    public void exit() {
        dismissAnnotationWindow();
        Intent intent = new Intent(mContext, AnnotationService.class);
        mContext.stopService(intent);
    }

    public int getMenuX() {
        return mX;
    }

    public int getMenuY() {
        return mY;
    }


    public void onAccelerateServiceConnected(IDrawNoteInterface iDrawNoteInterface) {
        if (mDrawingView != null) {
            mDrawingView.onAccelerateServiceConnected(iDrawNoteInterface);
            mDrawingView.setBrush(mIsButtonEraser, mPenColor, mPenSize);
        }
    }

    public void onAccelerateServiceDisconnected() {
        if (mDrawingView != null) {
            mDrawingView.onAccelerateServiceDisconnected();
        }
    }
}
