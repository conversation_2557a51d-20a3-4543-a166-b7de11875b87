package com.eeo.systemsetting.launcher;

import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.res.Configuration;
import android.graphics.PixelFormat;
import android.media.tv.TvContract;
import android.media.tv.TvView;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.cvte.tv.api.TvApiSDKManager;
import com.cvte.tv.api.aidl.EntityInputSource;
import com.cvte.tv.api.aidl.EntityResolution;
import com.cvte.tv.api.aidl.EnumInputSourceCategory;
import com.cvte.tv.api.aidl.EnumInputSourceId;
import com.cvte.tv.api.aidl.EnumSignalStatus;
import com.cvte.tv.api.aidl.ITVApiScreenFreezeAidl;
import com.cvte.tv.api.aidl.ITVApiScreenWindowAidl;
import com.cvte.tv.api.aidl.ITVApiSystemBacklightAidl;
import com.cvte.tv.api.aidl.ITVApiSystemCecAidl;
import com.cvte.tv.api.aidl.ITVApiSystemInputSourceAidl;
import com.cvte.tv.api.aidl.ITVApiSystemPropertiesAidl;
import com.cvte.tv.api.aidl.ITVApiSystemSignalAidl;
import com.cvte.tv.api.aidl.ITVApiTvAntennaTypeAidl;
import com.cvte.tv.api.aidl.ITVApiTvAtscAidl;
import com.cvte.tv.api.aidl.ITVApiTvAtvAidl;
import com.cvte.tv.api.aidl.ITVApiTvChannelsAidl;
import com.cvte.tv.api.aidl.ITVApiTvDtmbAidl;
import com.cvte.tv.api.aidl.ITVApiTvDvbcAidl;
import com.cvte.tv.api.aidl.ITVApiTvDvbtAidl;
import com.cvte.tv.api.aidl.ITVApiTvTeletextAidl;
import com.cvte.tv.api.aidl.ITvApiManager;
import com.cvte.tv.api.aidl.ITvNotifyListener;
import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.bean.AvailableSourceBean;
import com.eeo.systemsetting.projection.GestureDetectorService;
import com.eeo.systemsetting.projection.GestureListener;
import com.eeo.systemsetting.projection.Projection;
import com.eeo.systemsetting.screenoffset.ScreenMoveView;
import com.eeo.systemsetting.screenoffset.TouchSliderThread;
import com.eeo.systemsetting.utils.CLog;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.source.SourceChangeListener;
import com.google.gson.Gson;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;


public class TifPlayerActivity extends Activity {
    //TIF-player
    private static TvView mTvPlayer;
    public static Handler mMHandler;
    private final static String TAG = "TifPlayerActivity===";
    private static final String TIF_CLASS_NAME = "com.cvte.tv.api.ctif.TvPlayer.TifPlayerActivity";
    //TIF-player

    //接受处理的广播
    private static final String ACTION_GET_CLOSE_FRAGMENT = "com.cvte.intent.action.CLOSE_FRAGMENT";
    private static final String ACTION_GET_TV_SETTING_SUICIDE = "com.cvte.intent.action.suicide";
    private static final String ACTION_GET_KEY_GET = "seewo.intent.action.KEY_GET";  //from hotkey-service
    private static final String ACTION_GET_SYS_SHUTDOWN = "android.intent.action.ACTION_SHUTDOWN"; //from system-framework
    private static final String ACTION_SEND_FINNISH_TIF = "com.cvte.intent.action.FINNISH_TIF";
    private static final String ACTION_SEND_SIGNAL_CHANGE = "com.cvte.intent.action.SIGNAL_Change";
    private static final String ACTION_CONTROL_NO_SIGNAL = "com.cvte.intent.action_CONTROL_NO_SIGNAL";//工厂自动测试发，打白场时需隐藏无信号窗口，否则影响测试

    public static final String ACTION_RETUNE_TVVIEW = "com.eeo.action.retune.tvview"; //重新抢占tvview，如画中画释放后
    public static final String ACTION_SHOW_RESOLUTION = "com.eeo.action.SHOW.RESOLUTION"; //显示信源分辨率弹窗

    //发出的广播
    private static final String ACTION_SEND_CLOSE_SHORTCUT = "com.cvte.intent.action.CLOSE_SHORTCUT";
    private static final String ACTION_SEND_GO_TO_ANDROID = "com.cvte.intent.action.GO_TO_ANDROID"; //to OsService
    private static final String ACTION_SEND_SWITCH_TOUCH = "com.cvte.intent.action.switch.touch";
    private static final String ACTION_SEND_TO_MCU = "com.seewo.TvSettingA2Mcu";
    //property
    private static final String PROPERTY_UWSD_STOP_TV = "ctm.uwsd.stoptv";
    public static final String PROPERTY_LAST_TV_SOURCE_ID = "ctm.last.tv.source"; //仅用于tvsetting通道恢复
    private static final String PRO_COMPLETE_SOURCE_ID = "sys.complete.source_id";
    private static final String KEY_CONTROL = "key_no_signal";
    public static final String SHUTDOWN_ACTION_PROPERTY = "sys.shutdown.requested";
    //handler msg 句柄
    private final static int MSG_INIT_OK = 2;
    private final static int MSG_INIT_INFO = 3;
    private final static int MSG_TUNE_TVVIEW = 4;
    private final static int MSG_SHOW_NO_SIGNAL = 5;
    private final static int MSG_HIDE_NO_SIGNAL = 6;
    //移除掉无信号页面tag
    private final static int MSG_REMOVE_NO_SIGNAL = 7;
    //显示出无信号页面tag
    private final static int MSG_DISPLAY_NO_SIGNAL = 8;
    //空切时候立刻显示无信号页面
    private final static int MSG_DISPLAY_NO_SIGNAL_RIGHT_AWAY = 14;
    private final static int MSG_UPDATE_NO_SIGNAL_VIEW = 9;
    private final static int MSG_SHOW_PROJECTION = 10;
    private final static int MSG_DISMISS_PROJECTION = 11;
    //显示半屏模式UI界面
    public final static int MSG_SHOW_SCREEN_OFFSET_UI_LEFT = 12;
    public final static int MSG_SHOW_SCREEN_OFFSET_UI_RIGHT = 16;
    public final static int MSG_HIDE_SCREEN_OFFSET_UI = 13;

    //切换PC通道后，无信号界面切换按钮延迟一会再可点击，避免频繁点击重启
    private final static int MSG_ENABLE_BTN_START = 15;
    private final static int ENABLE_BTN_START_DELAY = 10000;

    private final static int MSG_START_OPS = 17;

    private final static int MSG_SHOW_RESOLUTION = 18;

    private final static int MSG_SHOW_UHD = 19;
    private final static int MSG_DISMISS_UHD = 20;

    private final static int MSG_HANDLE_BOOT_SOURCE = 21;
    private final static int MSG_HANDLE_AUTO_SWITCH_CHANNEL = 22;
    private final static int MSG_ENABLE_OPS = 23;

    /**
     * 刚开机时再延迟3s显示，避免显示不出来
     */
    private boolean mIsUhdMsgFirstGet = true;

    private static final String IR_KEY = "ir_keypad";  //遥控器设备

    private static final String PROP_PIXELPICTURE = "persist.ctm.pixelpicture";

    private TvSettingReceiverAsUser mTvSettingReceiverAsUser;

    private static boolean isNoSignalAlive = false;
    private static boolean isUnsupportlAlive = false;
    private static int mSigLostCount = 0;
    private static boolean isLauncherGoToTv = false;

    private static ITvNotifyListener listener = null;
    private ITvApiManager iTvApiManager;
    private ITVApiTvTeletextAidl mTtxApi;
    private ITVApiSystemCecAidl mCecApi;
    private ITVApiSystemSignalAidl mSignalApi;
    private ITVApiScreenWindowAidl mWindowApi;
    private ITVApiSystemInputSourceAidl mSourceApi;
    private ITVApiTvChannelsAidl mChannelsApi;
    private ITVApiTvAntennaTypeAidl mAntennaTypeApi;
    private ITVApiTvAtvAidl mAtvApi;
    private ITVApiTvAtscAidl mAtscApi;
    private ITVApiTvDtmbAidl mDtmbApi;
    private ITVApiTvDvbtAidl mDvbtApi;
    private ITVApiTvDvbcAidl mDvbcApi;
    private ITVApiScreenFreezeAidl mFreezeApi;
    private ITVApiSystemPropertiesAidl mPropApi;
    private ITVApiSystemBacklightAidl mBackligntApi;

    // public static boolean isOnPauseBefore = false;
    private static boolean isListenerEnable = false;

    private static final String KEY_INPUT_SOURCE_ID = "inputSourceId";
    private EnumInputSourceId mCurInputSourceId = EnumInputSourceId.SOURCE_ID_MAX;
    private boolean mLastPcAndHasSignal = false;  //ops从有信号到无信号后关机
    private long mLastPcAndHasSignalTime = 0;
    private boolean enable_nosignal_tag = true;
    private static final int SHOW_NO_SIGNAL_DELAY = 2000;
    private static final Object mNosignalMutex = new Object();
    private static final Object mTvInfoMutex = new Object();
    private long mFirstTuneTime = 0;
    private static final String PROP_PIP = "persist.sys.pip.source";
    public static final String TPV_ACTION = "com.tpv.action.TV_LIFECYCLE_CHANGED";
    private RelativeLayout rlMain;
    private RelativeLayout rlNoSign;
    private TextView mNoSignTv;
    private TextView txtMsg;
    private Button btnStart;
    private TextView txtMsgPhone;
    private TextView txtNoPc;

    private boolean mShouldRetune = false;

    /**
     * 开机时ops有信号次数
     * 第二次有信号的时候才切换到目标通道，确保ops edid信息握手成功
     */
    private int mBootOpsHasSignalCount = 0;

    private int mStartOpsRetryTime;
    private boolean mStartOpsTimeout = false;

    /**
     * 第一次开启ops时，ops要连续两次有信号，否则下次切ops时重新拉一下hpd，避免ops颜色格式异常
     * 避免*********-异常场景：部分ops有第二次信号前就切到了其它通道，ops颜色格式异常
     */
    private boolean mIsOpsFinished = false;
    private boolean mShouldOpsHpd = false;
    private int mOpsHasSignalCount = 0;

    private boolean mHasAutoSwitchChannelHandled = false;

    /**
     * 避免刚开机时Udi服务还未连接成功
     * 导致ota成功/失败弹窗禁透传失败
     */
    private boolean mEnableOsdInit = false;

    private boolean mIsBootAnimExit = false;

    private List<AvailableSourceBean.SourcesBean> mAvailableSourceList = new ArrayList<>();

    /**
     * 分辨率信息相关
     */
    private Toast mResolutionToast;
    private TextView mResolutionTv;
    private EntityResolution mEntityResolution;

    /**
     * 画中画相关
     */
    private RelativeLayout mProjectionLayout;
    private TextView mHintTv;
    private Projection mProjection;
    private TouchSliderThread mTouchSlider;
    private ScreenMoveView mScreenMoveView;
    private int mX;
    private int mY;
    private GestureDetectorService.MyBinder mBinder;
    private ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mBinder = (GestureDetectorService.MyBinder) service;
            if (mBinder != null) {
                mBinder.getService().setGestureListener(new GestureListener() {
                    @Override
                    public void showProjection(int x, int y) {
                        Log.d(TAG, "showProjection: (" + x + "," + y + ")");
                        mX = x;
                        mY = y;
                        if (!mProjection.isShown()) {
                            mMHandler.sendEmptyMessage(MSG_SHOW_PROJECTION);
                        }
                    }

                    @Override
                    public void dismissProjection() {
                        Log.d(TAG, "dismissProjection: ");
                        mMHandler.sendEmptyMessage(MSG_DISMISS_PROJECTION);
                    }
                });
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected: ");
            mBinder = null;
        }
    };

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate: ");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.launcher_main);
        CommonUtils.updateDensity(this);
        mMHandler = new MHandler(this);

        mTvPlayer = (TvView) findViewById(R.id.tv_player);
        mTvPlayer.setCallback(new TifTvViewInputListener());
        rlMain = (RelativeLayout) findViewById(R.id.rl_main);
        mProjectionLayout = (RelativeLayout) findViewById(R.id.rl_tvview);
        mHintTv = (TextView) findViewById(R.id.tv_hint);
        setPlayerLocation(0, 0, 0, 0);
        getTvApi();
        mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
        if (EeoApplication.mShouldHandleBootSource) {
            mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_BOOT_SOURCE, 30000); //time out
        }
        mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 60000); //time out

        rlNoSign = findViewById(R.id.rl_no_sign);
        mNoSignTv = findViewById(R.id.txt_no_sign);
        txtMsg = findViewById(R.id.txt_msg);
        btnStart = findViewById(R.id.btn_start);
        txtNoPc = findViewById(R.id.txt_no_pc);
        txtMsgPhone = findViewById(R.id.txt_msg_phone);
        bindGestureService();
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        mProjection = new Projection(this, displayMetrics.widthPixels, displayMetrics.heightPixels, mProjectionLayout,
                mTvPlayer, mHintTv, rlMain, mAvailableSourceList);
        if (Constant.HAS_TOUCH_SLIDER) {
            mScreenMoveView = new ScreenMoveView(this);
            mTouchSlider = new TouchSliderThread(this, mMHandler);
            mTouchSlider.start();
        }
        register();

        btnStart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (CommonUtils.isNoSignalFastClick()) {
                    return;
                }
                if (isInPCSource()) {
                    Log.i(TAG, "onClick: current is pc");
                    if (CommonUtils.isOpsInserted()) {
                        mStartOpsRetryTime = 0;
                        mMHandler.removeMessages(MSG_START_OPS);
                        mMHandler.sendEmptyMessage(MSG_START_OPS);
                        mMHandler.sendEmptyMessageDelayed(MSG_ENABLE_OPS, 15000); //启动后一直无信号，用这种方法能出图，但解决不了分辨率、图标错乱
                    }
                } else {
                    Log.i(TAG, "onClick: current is hdmi");
                    btnStart.setEnabled(false);
                    mMHandler.sendEmptyMessageDelayed(MSG_ENABLE_BTN_START, ENABLE_BTN_START_DELAY);
                    EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
                    if (mSourceApi != null) {
                        Observable.create(new ObservableOnSubscribe<Boolean>() {
                                    @Override
                                    public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                                        boolean isSuccess = mSourceApi.eventSystemInputSourceSetInputSource(EnumInputSourceId.SOURCE_ID_PC.ordinal());
                                        emitter.onNext(isSuccess);
                                    }
                                }).observeOn(Schedulers.io())
                                .subscribeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Consumer<Boolean>() {
                                    @Override
                                    public void accept(Boolean aBoolean) throws Exception {
                                        if (aBoolean) {
                                            mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
                                        }

                                    }
                                });
//                            setTifInputSource(inputSource, mTvPlayer);
                    }
                }
                //无信号界面立刻消失，等信号状态变化太慢
                if (rlNoSign.getVisibility() == View.VISIBLE && CommonUtils.isOpsInserted()/*isSourceEnable(UdiConstant.SOURCE_PC)*/) {
                    rlNoSign.setVisibility(View.GONE);
                    mMHandler.removeMessages(MSG_DISPLAY_NO_SIGNAL);
                    mMHandler.sendEmptyMessageDelayed(MSG_DISPLAY_NO_SIGNAL, 20000);
                }
            }
        });
    }

    @Override
    protected void onStart() {
        Log.i(TAG, "onStart: ");
        super.onStart();

        if (iTvApiManager == null) {
            getTvApi();
        }

        /*if (mSourceApi != null) {
            TvSourceRestart();
        }
*/
        synchronized (mNosignalMutex) {
            if (!mMHandler.hasCallbacks(updateNoSignal)) {
                mMHandler.post(updateNoSignal);
            }
        }
        EeoApplication.isTifPlayerActivityStopped = false;
        CommonUtils.enableOsd(this, false);
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume: ");
        isLauncherGoToTv = this.getIntent().getBooleanExtra("isLauncherGoToTv", false);

        if (SystemProperties.getBoolean(PROP_PIP, false)) {
            if (mMHandler == null) {
                mMHandler = new MHandler(this);
            }
            Log.i(TAG, "onResume: pip onResume msg_tune_tvview");
            mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
        }
        super.onResume();
    }

    @Override
    protected void onPause() {
        Log.i(TAG, "onPause: ");
        // isOnPauseBefore = true;
        mSigLostCount = 0;
        super.onPause();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        Log.i(TAG, "onNewIntent: ");
        super.onNewIntent(intent);
        setIntent(intent);//刷新intent
        if (mMHandler == null) {
            mMHandler = new MHandler(this);
        }
        if (mTvPlayer == null) {
            mTvPlayer = (TvView) findViewById(R.id.tv_player);
            mTvPlayer.setCallback(new TifTvViewInputListener());
        }
        setPlayerLocation(0, 0, 0, 0);
        if (iTvApiManager == null) {
            getTvApi();
        }
        mShouldRetune = true;
        mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
    }

    /**
     * 需要tvsetting配置成透明them  , 否则将引起TifPlayer-onstop。播放器被退出
     */
    @Override
    protected void onStop() {
        Log.i(TAG, "onStop: ");
//        TvSourceStop();
        synchronized (mNosignalMutex) {
            if (mMHandler.hasCallbacks(updateNoSignal)) {
                mMHandler.removeCallbacks(updateNoSignal);
            }
        }

        isUnsupportlAlive = false;
        isNoSignalAlive = false;
        mSigLostCount = 0;
        super.onStop();
        EeoApplication.isTifPlayerActivityStopped = true;
        CommonUtils.enableOsd(this, true);
        if (SystemProperties.getBoolean("ro.config.low_ram", false)) {
            finish();
        }
    }

    @Override
    public void finish() {
        Log.i(TAG, "finish: ");
        mCurInputSourceId = EnumInputSourceId.SOURCE_ID_MAX;
        mTvPlayer.reset();
        super.finish();
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy: ");
        mCurInputSourceId = EnumInputSourceId.SOURCE_ID_MAX;
        unregister();
        mTvPlayer.reset();
        unbindGestureService();
        if (mTouchSlider != null) {
            mTouchSlider.setStop();
        }
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        CommonUtils.updateDensity(this);
        super.onConfigurationChanged(newConfig);
        mHintTv.setText(getResources().getString(R.string.hint_no_control));
        mNoSignTv.setText(getResources().getString(R.string.no_sign));
        updateNoSignalView();
        if (mScreenMoveView != null) {
            mScreenMoveView.onConfigurationChanged();
        }
        if (mProjection != null) {
            mProjection.onConfigurationChanged();
        }
        if (mUhdView != null) {
            dismissUhdView();
            mUhdView = null;
        }
    }

    /**
     * 内部消息处理
     */
    private class MHandler extends Handler {

        private final WeakReference<Activity> mTarget;

        private MHandler(Activity target) {
            mTarget = new WeakReference<Activity>(target);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            TifPlayerActivity mTifPlayerActivity = (TifPlayerActivity) mTarget.get();
            switch (msg.what) {
                case MSG_INIT_OK:
                    Log.i(TAG, "isLauncherGoToTv=" + isLauncherGoToTv);
                    if (isLauncherGoToTv) {
                        if (mWindowApi != null) {
                            try {
                                mWindowApi.eventScreenWindowMute(false);
                                mWindowApi.eventScreenWindowSetFull();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        mTifPlayerActivity.getIntent().putExtra("isLauncherGoToTv", false);
                    }
                    //cvte kongweijun close
                    //new Thread(mUpdateChannelInfo).start();
                    //cvte kongweijun close
                    break;
                case MSG_INIT_INFO:
                    Log.i(TAG, "handleMessage: MSG_INIT_INFO");
                    break;

                case MSG_REMOVE_NO_SIGNAL:
                    Log.i(TAG, ": MSG_REMOVE_NO_SIGNAL");
                    mMHandler.removeMessages(MSG_DISPLAY_NO_SIGNAL);
                    rlNoSign.setVisibility(View.GONE);
                    //增加延迟再结束开机动画，避免看到ops bios的画面
                    if (!mIsBootAnimExit) {
                        postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                exitBootAnim();
                            }
                        }, 3000);
                    }
                    CommonUtils.setNoSignal(TifPlayerActivity.this, false);
                    break;

                case MSG_DISPLAY_NO_SIGNAL:
                    if (PowerUtil.getInstance(TifPlayerActivity.this).isShuttingDown()) {
                        Log.e(TAG, "handleMessage: don't show NO_SIGNAL while shutting down.");
                        break;
                    }
                    //ops从有信号到无信号且当前是ops通道，直接关机
                    if (mIsOpsFinished && mLastPcAndHasSignal && mLastPcAndHasSignalTime > 0 && System.currentTimeMillis() - mLastPcAndHasSignalTime > 20000
                            && mCurInputSourceId == EnumInputSourceId.SOURCE_ID_PC && !EeoApplication.isResettingOps
                            && !CommonUtils.isOpsDisable()
                    ) {
                        Log.e(TAG, "ops is no on,shut down. ");
                        PowerUtil.getInstance(TifPlayerActivity.this).shutdownOrReboot(false, "",
                                PowerUtil.FLAG_SHUTDOWN_WEAK, false);
                        break;
                    }
                    if (rlNoSign.getVisibility() != View.VISIBLE) {
                        rlNoSign.setVisibility(View.VISIBLE);
                        rlNoSign.bringToFront();
                        exitBootAnim();
                        CommonUtils.setNoSignal(TifPlayerActivity.this, true);
                    }
                    btnStart.setEnabled(true);
                    mMHandler.removeMessages(MSG_ENABLE_BTN_START);
                    updateNoSignalState();
                    break;
                case MSG_DISPLAY_NO_SIGNAL_RIGHT_AWAY:
                    Log.d(TAG, "MSG_DISPLAY_NO_SIGNAL_RIGHT_AWAY");
                    if (mMHandler.hasCallbacks(updateNoSignal)) {
                        mMHandler.removeCallbacks(updateNoSignal);
                    }
                    if (rlNoSign.getVisibility() != View.VISIBLE) {
                        rlNoSign.setVisibility(View.VISIBLE);
                        rlNoSign.bringToFront();
                        exitBootAnim();
                        CommonUtils.setNoSignal(TifPlayerActivity.this, true);
                    }
                    updateNoSignalState();
                    mLastPcAndHasSignal = false;
                    break;
                case MSG_UPDATE_NO_SIGNAL_VIEW:
                    updateNoSignalState();
                    break;
                case MSG_TUNE_TVVIEW:
                    if (mFirstTuneTime == 0) {
                        mFirstTuneTime = System.currentTimeMillis();
                        Log.i(TAG, "handleMessage: mFirstTuneTime == 0");
                    }
                    long curTime = System.currentTimeMillis();
                    if ("0".equals(SystemProperties.get("service.bootvideo.exit")) ||
                            curTime - mFirstTuneTime >= 5000) {
                        if (mSourceApi != null) {
                            try {
                                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                                setTifInputSource(inputSource, mTvPlayer);
                                Log.i(TAG, "handleMessage: setTifInputSource");
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        //避免开机时禁透传失败，ota弹窗出现透传
                        if (!mEnableOsdInit) {
                            if (CommonUtils.isDialog(TifPlayerActivity.this)) {
                                if (CommonUtils.enableOsd(TifPlayerActivity.this, true)) {
                                    mEnableOsdInit = true;
                                }
                            } else {
                                mEnableOsdInit = true;
                            }
                        }
                    } else {
                        if (mMHandler.hasMessages(MSG_TUNE_TVVIEW)) {
                            mMHandler.removeMessages(MSG_TUNE_TVVIEW);
                        }
                        mMHandler.sendEmptyMessageDelayed(MSG_TUNE_TVVIEW, 200);
                    }
                    break;
                case MSG_SHOW_NO_SIGNAL:
                    Log.i(TAG, "handleMessage: msg show no signal");
                    synchronized (mNosignalMutex) {
                        enable_nosignal_tag = true;
                        if (!mMHandler.hasCallbacks(updateNoSignal)) {
                            mMHandler.post(updateNoSignal);
                        }
                    }
                    break;
                case MSG_HIDE_NO_SIGNAL:
                    Log.i(TAG, "handleMessage: msg hide no signal");
                    synchronized (mNosignalMutex) {
                        enable_nosignal_tag = false;
                        if (mMHandler.hasCallbacks(updateNoSignal)) {
                            mMHandler.removeCallbacks(updateNoSignal);
                        }
                    }
                    break;
                case MSG_SHOW_PROJECTION:
                    mProjection.show(mX, mY);
                    break;
                case MSG_DISMISS_PROJECTION:
                    mProjection.dismiss();
                    break;
                case MSG_SHOW_SCREEN_OFFSET_UI_LEFT:
                    if (rlNoSign.getVisibility() == View.GONE) {
                        if (mScreenMoveView != null) {
                            mScreenMoveView.show(true);
                        }
                    }
                    break;
                case MSG_SHOW_SCREEN_OFFSET_UI_RIGHT:
                    if (rlNoSign.getVisibility() == View.GONE) {
                        if (mScreenMoveView != null) {
                            mScreenMoveView.show(false);
                        }
                    }
                    break;
                case MSG_HIDE_SCREEN_OFFSET_UI:
                    if (mScreenMoveView != null) {
                        mScreenMoveView.hide();
                    }
                    break;
                case MSG_ENABLE_BTN_START:
                    if (btnStart != null) {
                        btnStart.setEnabled(true);
                    }
                    break;
                case MSG_START_OPS:
                    Log.d(TAG, "handleMessage: startOps");
                    if (mStartOpsRetryTime < 3) {
                        EeoApplication.udi.startOps();
                        mStartOpsRetryTime++;
                        sendEmptyMessageDelayed(MSG_START_OPS, 5000);
                    } else if (!UdiConstant.OPS_STATUS_ON.equals(EeoApplication.udi.getOpsStatus())) {
                        mStartOpsTimeout = true;
                        updateNoSignalView();
                    }
                    break;
                case MSG_SHOW_RESOLUTION:
                    try {
                        mEntityResolution = mSignalApi.eventSystemSignalGetResolution();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                    if (mEntityResolution == null || mEntityResolution.horizontal == 0 || mEntityResolution.vertical == 0 || mEntityResolution.frequency == 0) {
                        showResolutionToast(false);
                    } else {
                        showResolutionToast(true);
                    }
                    break;
                case MSG_SHOW_UHD:
                    if (mIsUhdMsgFirstGet) {
                        sendEmptyMessageDelayed(MSG_SHOW_UHD, 3000);
                        mIsUhdMsgFirstGet = false;
                        break;
                    }
                    if (mHdmiUhdFirstShowTime == 0) {
                        if (isInHDMISource() || isInTypeCSource()) {
                            try {
                                EntityResolution entityResolution = mSignalApi.eventSystemSignalGetResolution();
                                //这里分辨率接近4k、50hz的才弹提示
                                if (entityResolution != null && entityResolution.horizontal >= 3000
                                        && entityResolution.vertical >= 2000
                                        && entityResolution.frequency >= 50) {
                                    mHdmiUhdFirstShowTime = System.currentTimeMillis();
                                    showUhdView(isInHDMISource());
                                }
                            } catch (RemoteException e) {
                                Log.e(TAG, "handleMessage MSG_SHOW_UHD exception " + e);
                            }
                        }
                    }
                    break;
                case MSG_DISMISS_UHD:
                    dismissUhdView();
                    break;
                case MSG_HANDLE_BOOT_SOURCE:
                    removeMessages(MSG_HANDLE_BOOT_SOURCE);
                    handleBootSource();
                    //开机通道完成后再恢复通道插入自动跳转功能
                    sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 5000);
                    break;
                case MSG_HANDLE_AUTO_SWITCH_CHANNEL:
                    removeMessages(MSG_HANDLE_AUTO_SWITCH_CHANNEL);
                    handleAutoSwitchChannel();
                    break;
                case MSG_ENABLE_OPS:
                    removeMessages(MSG_ENABLE_OPS);
                    if (!mIsOpsFinished && mShouldOpsHpd && isInPCSource() && !CommonUtils.isOpsDisable()) {
                        enableOps();
                    }
                    break;
                default:
                    break;
            }
        }

        public void removeCallback(Runnable r) {
            removeCallbacks(r);
            if (r == updateNoSignal) {
                mSigLostCount = 0;
            }
        }
    }

    /**
     * 设置播放器布局的位置
     *
     * @param left   离边缘的位置
     * @param top    ～
     * @param right  ～
     * @param bottom ～
     * @return
     */
    private static boolean setPlayerLocation(int left, int top, int right, int bottom) {  // 0 , 0

        TvView tvPlayer = mTvPlayer;
        if (tvPlayer == null) {
            return false;
        }

        RelativeLayout.LayoutParams mTvPlayerLayoutParams = (RelativeLayout.LayoutParams) tvPlayer.getLayoutParams();

        mTvPlayerLayoutParams.leftMargin = left;
        mTvPlayerLayoutParams.topMargin = top;
        mTvPlayerLayoutParams.rightMargin = right;
        mTvPlayerLayoutParams.bottomMargin = bottom;

        tvPlayer.setLayoutParams(mTvPlayerLayoutParams);

        return true;
    }


    @Override
    public void onBackPressed() {

    }


    /**
     * Tif切通道,底层信号源切换
     *
     * @param inputSource
     * @param tvPlayer
     */
    public void setTifInputSource(EntityInputSource inputSource, TvView tvPlayer) {
        if (inputSource == null || tvPlayer == null || TextUtils.isEmpty(inputSource.inputId)) {
            Log.i(TAG, "error inputSource = " + inputSource + " tvPlayer = " + tvPlayer);
            return;
        }
        if (!mShouldRetune && mCurInputSourceId == inputSource.sourceId) {
            Log.i(TAG, "same inputid pass");
            if (!SystemProperties.getBoolean(PROP_PIP, false)) {
                return;
            }
        }

        synchronized (mNosignalMutex) {
            if (mMHandler.hasCallbacks(updateNoSignal)) {
                mMHandler.removeCallbacks(updateNoSignal);
            }
            mMHandler.post(updateNoSignal);
        }
        Uri uri = TvContract.buildChannelUriForPassthroughInput(inputSource.inputId);
        Log.i(TAG, "--->" + "sourceId= " + inputSource.sourceId + " inputId= " + inputSource.inputId + " uri= " + uri);
        if (!mShouldRetune || mCurInputSourceId != inputSource.sourceId) {
            tvPlayer.reset();
        }
        tvPlayer.tune(inputSource.inputId, uri);
        mShouldRetune = false;
        mCurInputSourceId = inputSource.sourceId;
        updateCurrentSource();
//		tvPlayer.tune("com.droidlogic.tvinput/.services.Hdmi4InputService/HW8", null);

        SystemProperties.set(PRO_COMPLETE_SOURCE_ID, String.valueOf(inputSource.sourceId.ordinal()));
        //空切时立刻显示无信号界面，避免黑屏
        checkSourceEnable(inputSource.sourceId);
    }

    /**
     * 获取TVAPI对象
     */
    private void getTvApi() {
        iTvApiManager = TvApiSDKManager.getInstance().getTvApi();
        if (iTvApiManager == null) {
            Log.i(TAG, "can not get tvapi");
            return;
        }

        try {
            mSourceApi = iTvApiManager.getTVApiSystemInputSource();
            mChannelsApi = iTvApiManager.getTVApiTvChannels();
            mAntennaTypeApi = iTvApiManager.getTVApiTvAntennaType();
            mAtvApi = iTvApiManager.getTVApiTvAtv();
            mAtscApi = iTvApiManager.getTVApiTvAtsc();
            mDtmbApi = iTvApiManager.getTVApiTvDtmb();
            mDvbtApi = iTvApiManager.getTVApiTvDvbt();
            mDvbcApi = iTvApiManager.getTVApiTvDvbc();
            mCecApi = iTvApiManager.getTVApiSystemCec();
            mSignalApi = iTvApiManager.getTVApiSystemSignal();
            mWindowApi = iTvApiManager.getTVApiScreenWindow();
            mTtxApi = iTvApiManager.getTVApiTvTeletext();
            mFreezeApi = iTvApiManager.getTVApiScreenFreeze();
            mPropApi = iTvApiManager.getTVApiSystemProperties();
            mBackligntApi = iTvApiManager.getTVApiSystemBacklight();
        } catch (RemoteException e) {
            e.printStackTrace();
        }

        mMHandler.removeMessages(MSG_INIT_OK);
        mMHandler.sendEmptyMessage(MSG_INIT_OK);
        /*synchronized (mTvInfoMutex) {
            mMHandler.removeMessages(MSG_INIT_INFO);
            mMHandler.sendEmptyMessage(MSG_INIT_INFO);
        }*/
        Log.i(TAG, "getTvApi oK");
    }

    /**
     * 清理TVAPI对象
     */
    private void releaseTvapi() {
        mSourceApi = null;
        mChannelsApi = null;
        mAntennaTypeApi = null;
        mAtvApi = null;
        mAtscApi = null;
        mDtmbApi = null;
        mDvbtApi = null;
        mDvbcApi = null;
        mCecApi = null;
        mSignalApi = null;
        mWindowApi = null;
        mTtxApi = null;
        mFreezeApi = null;
        mPropApi = null;
    }

    private void register() {
        isListenerEnable = true;
        unregisterNotifyListener();
        unregisterTvSettingReceiverAsUser();
        registerTvSettingReceiverAsUser();
        registerNotifyListener();
        registerSourceChangeListener();
    }

    private void unregister() {
        isListenerEnable = false;
        unregisterNotifyListener();
        unregisterTvSettingReceiverAsUser();
        unregisterSourceChangeListener();
    }

    /**
     * 注册全局TVAPI回调监听器
     */
    private void registerNotifyListener() {
        listener = new ITvNotifyListener() {
            @Override
            public void onTvNotify(String action, Bundle bundle) {
                executeNotifyAction(action, bundle);
            }
        };
        TvApiSDKManager.getInstance().addNotifyHandle(listener);
        Log.i(TAG, "registerNotifyListener: ok");
    }

    /**
     * 注销删除全局TVAPI回调监听器
     */
    private void unregisterNotifyListener() {
        if (listener != null) {
            TvApiSDKManager.getInstance().removeNotifyHandle(listener);
            listener = null;
        }
    }

    /**
     * 广播接收器
     */
    public class TvSettingReceiverAsUser extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action.equals(ACTION_GET_CLOSE_FRAGMENT)) {
                if (isNoSignalAlive) {
                    isNoSignalAlive = false;
                }
            } else if (action.equals(ACTION_GET_TV_SETTING_SUICIDE)) { //added by wangmingji
                exitToAndroid();
                Log.i(TAG, "onReceive go to android");
            } else if (action.equals(ACTION_GET_KEY_GET)) {
                //mHandler.sendEmptyMessage(MSG_INIT_INFO);
                Log.i(TAG, "onReceive go to ACTION_GET_KEY_GET");
            } else if (action.equals(ACTION_GET_SYS_SHUTDOWN)) {
                Log.i(TAG, "onReceive go to ACTION_GET_SYS_SHUTDOWN");
            } else if (action.equals(ACTION_SEND_FINNISH_TIF)) {
                Log.i(TAG, "BroadcastReceiver MSG_FINISH_TIF_ACTIVITY  : ");
                finish();
            } else if (action.equals(ACTION_CONTROL_NO_SIGNAL)) {
                if (intent.getBooleanExtra(KEY_CONTROL, true)) {
                    mMHandler.sendEmptyMessageDelayed(MSG_SHOW_NO_SIGNAL, SHOW_NO_SIGNAL_DELAY);
                    Log.i(TAG, "get ACTION_CONTROL_NO_SIGNAL show nosignal: ");
                } else {
                    mMHandler.sendEmptyMessage(MSG_HIDE_NO_SIGNAL);
                    Log.i(TAG, "get ACTION_CONTROL_NO_SIGNAL hideAllDialog: ");
                }
            } else if (action.equals(ACTION_RETUNE_TVVIEW)) {
                mShouldRetune = true;
                mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
            } else if (action.equals(ACTION_SHOW_RESOLUTION)) {
                mMHandler.sendEmptyMessage(MSG_SHOW_RESOLUTION);
            } else if (action.equals("android.media.VOLUME_CHANGED_ACTION") ||
                    intent.getAction().equals("android.media.STREAM_MUTE_CHANGED_ACTION")) {
                if (mProjection != null && mProjection.isShown()) {
                    mProjection.updateVolumeProgress();
                }
            }
        }
    }

    /**
     * 创建接收器对象，注册广播接受器
     */
    private void registerTvSettingReceiverAsUser() {
        mTvSettingReceiverAsUser = new TvSettingReceiverAsUser();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_GET_CLOSE_FRAGMENT);
        filter.addAction(ACTION_GET_TV_SETTING_SUICIDE);
        filter.addAction(ACTION_GET_KEY_GET);
        filter.addAction(ACTION_GET_SYS_SHUTDOWN);
        filter.addAction(ACTION_SEND_FINNISH_TIF);
        filter.addAction(ACTION_SEND_SIGNAL_CHANGE);
        filter.addAction(ACTION_CONTROL_NO_SIGNAL);
        filter.addAction(ACTION_RETUNE_TVVIEW);
        filter.addAction(ACTION_SHOW_RESOLUTION);
        filter.addAction("android.media.VOLUME_CHANGED_ACTION");
        filter.addAction("android.media.STREAM_MUTE_CHANGED_ACTION");
        registerReceiver(mTvSettingReceiverAsUser, filter, null, null);
        Log.i(TAG, "registerTvSettingReceiverAsUser ");
    }

    /**
     * 注销广播接收器，清空接收器对象
     */
    private void unregisterTvSettingReceiverAsUser() {
        if (mTvSettingReceiverAsUser == null) {
            return;
        }
        unregisterReceiver(mTvSettingReceiverAsUser);
        mTvSettingReceiverAsUser = null;
        Log.i(TAG, "unregisterTvSettingReceiverAsUser");
    }


    /**
     * 通道恢复，用于外部通道直接启动非tvsettings之外的activity.
     * onpause记录上次被中断的通道，onresume恢复
     */
    private void TvSourceRestart() {
        try {
            int lastSourceId = getLastSource();
            EntityInputSource cur_source = mSourceApi.eventSystemInputSourceGetInputSource();
            Log.i(TAG, "[TvSourceRestart]lastSourceId = " + lastSourceId);
            if (lastSourceId >= 0 && cur_source != null && cur_source.sourceId == EnumInputSourceId.SOURCE_ID_ANDROID) {
                Log.i(TAG, "check source last = " + lastSourceId + " cur_source = " + cur_source.sourceId.ordinal());
                mSourceApi.eventSystemInputSourceSetInputSource(lastSourceId);
            }
            clearLastSource();
        } catch (RemoteException e) {
            CLog.e("" + e);
        }
    }

    /**
     * 退回安卓通道
     */
    private void TvSourceStop() {
        try {
            EntityInputSource cur_source = mSourceApi.eventSystemInputSourceGetInputSource();
            if (cur_source != null && cur_source.sourceId != EnumInputSourceId.SOURCE_ID_ANDROID) {
                Log.i(TAG, "change source to android from " + cur_source.sourceId);
                saveLastSource(cur_source.sourceId.ordinal());

                mSourceApi.eventSystemInputSourceSetInputSource(EnumInputSourceId.SOURCE_ID_ANDROID.ordinal());
                if (mTvPlayer != null) {
                    mTvPlayer.reset();
                }
                mCurInputSourceId = EnumInputSourceId.SOURCE_ID_MAX;
            }
        } catch (RemoteException e) {
            CLog.e("" + e);
        }
    }

    private boolean isTvhardwareOK() {
        if (SystemProperties.get("ctm.mtk.tvhardware.done", "null").equals("true")) {
            return true;
        }
        return false;
    }

    /**
     * 刷新图像信号画面
     */
    private Thread updateNoSignal = new Thread() {
        int LostCount = 0;

        @Override
        public void run() {
            synchronized (mNosignalMutex) {
                EnumSignalStatus signalStatus = null;

                if (enable_nosignal_tag && mSignalApi != null && mSourceApi != null && mChannelsApi != null) {
//                    Log.i(TAG, "updateNoSignal mSigLostCount = " + mSigLostCount + " LostCount = " + LostCount);
//                    EnumInputStatus inputStatus = EnumInputStatus.INPUT_STATUS_CONNECTED;
                    try {
                        if (!mBackligntApi.eventSystemBacklightIsEnabled()) {
                            //背光关闭=休眠，重置计时器避免一唤醒就看到无信号界面
                            mSigLostCount = 0;
                        }
                        EntityResolution mER = mSignalApi.eventSystemSignalGetResolution();
                        signalStatus = mSignalApi.eventSystemSignalGetSignalState();
                        if (mER == null || (mER.vertical == 0 && mER.horizontal == 0 && mER.frequency == 0)) {
                            signalStatus = EnumSignalStatus.SIGNAL_LOST;
                        }

//                        EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
//                        if (inputSource != null) {
//                            inputStatus = inputSource.status;
//                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                        signalStatus = EnumSignalStatus.SIGNAL_LOST;
                    }

                    if (signalStatus != EnumSignalStatus.SIGNAL_NORMAL /*|| isAudioDataChannel() && !isPixelPictureOpen()*/) {//mTvFragmentManager.getBackStackEntryCount() == 0
                        if (isInPCSource()) {
                            LostCount = 20;
                        } else {
                            LostCount = 8;
                        }

                        mSigLostCount++;
                        if (mSigLostCount >= LostCount) {//T时间后显示无信号画面
                            mMHandler.sendEmptyMessage(MSG_DISPLAY_NO_SIGNAL);
                            return;
                        }
                    } else {
                        isUnsupportlAlive = false;
                        isNoSignalAlive = false;
                        mSigLostCount = 0;
                    }
                }
                mMHandler.postDelayed(this, 1000);
            }
        }
    };

    public static boolean isPixelPictureOpen() {
        Log.i(TAG, "isPixelPictureOpen:setPixelPicture PROP_PIXELPICTURE=" + SystemProperties.getBoolean(PROP_PIXELPICTURE, false));
        return SystemProperties.getBoolean(PROP_PIXELPICTURE, false);
    }


    /**
     * 全局消息处理函数，tvsetting-activity处于onpause时，依然可以接收到回调消息
     *
     * @param action
     * @param bundle
     */
    private void executeNotifyAction(String action, Bundle bundle) {
        Log.i(TAG, "executeNotifyAction11 action = " + action);
        if (isListenerEnable) {
            switch (action) {
                case "notifySystemSignalChange":
                    synchronized (mNosignalMutex) {
                        mMHandler.sendEmptyMessage(MSG_SHOW_RESOLUTION);
                        if (bundle != null && bundle.get("enumSignalStatus").equals("SIGNAL_NORMAL")) {
                            Log.i(TAG, "executeNotifyAction: bundle ! = null");
                            if (mMHandler.hasCallbacks(updateNoSignal)) {
                                mMHandler.removeCallbacks(updateNoSignal);
                            }
                            //移除无信号页面
                            mMHandler.sendEmptyMessage(MSG_REMOVE_NO_SIGNAL);

                            mMHandler.post(() -> {
                                getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                            });
                            mMHandler.removeMessages(MSG_START_OPS);
                            mMHandler.removeMessages(MSG_DISPLAY_NO_SIGNAL);
                            mMHandler.removeMessages(MSG_ENABLE_OPS);
                            mStartOpsTimeout = false;
                            //UHD连接保障提示
                            mMHandler.sendEmptyMessage(MSG_SHOW_UHD);
                            if (isInPCSource()) {
                                if (!mLastPcAndHasSignal) {
                                    mLastPcAndHasSignal = true;
                                    mLastPcAndHasSignalTime = System.currentTimeMillis();
                                }
                                if (PowerUtil.getInstance(TifPlayerActivity.this).isNoSignalShuttingDown()) {
                                    PowerUtil.getInstance(TifPlayerActivity.this).cancelNoSignalShutdown();
                                }
                            } else {
                                mLastPcAndHasSignal = false;
                            }
                            if (EeoApplication.mShouldHandleBootSource) {
                                mBootOpsHasSignalCount++;
                                if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_PC ||
                                        mCurInputSourceId == EnumInputSourceId.SOURCE_ID_MAX) {
                                    Log.d(TAG, "executeNotifyAction: mBootOpsHasSignalCount=" + mBootOpsHasSignalCount);
                                    if (mBootOpsHasSignalCount == 1) {
                                        mMHandler.removeMessages(MSG_HANDLE_BOOT_SOURCE);
                                        mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_BOOT_SOURCE, 30000); //time out
                                    } else if (mBootOpsHasSignalCount == 2) {
                                        mMHandler.removeMessages(MSG_HANDLE_BOOT_SOURCE);
                                        mMHandler.sendEmptyMessage(MSG_HANDLE_BOOT_SOURCE);
                                    }
                                } else {
                                    mMHandler.removeMessages(MSG_HANDLE_BOOT_SOURCE);
                                    EeoApplication.mShouldHandleBootSource = false;
                                }
                            }
                            checkOpsPullHpd();
                        } else {
                            Log.i(TAG, "executeNotifyAction: bundle == null");
                            if (!mMHandler.hasCallbacks(updateNoSignal)) {
                                mMHandler.post(updateNoSignal);
                                //显示无信号页面
                                mMHandler.sendEmptyMessage(MSG_DISPLAY_NO_SIGNAL);
                            }
                            mMHandler.post(() -> {
                                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                            });
                            handleSourceChange();
                            mShouldRetune = true;
                            if (!isInPCSource()) {
                                mLastPcAndHasSignal = false;
                                if (!mHasAutoSwitchChannelHandled) {
                                    mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 5000);
                                }
                            } else {
                                if (!mHasAutoSwitchChannelHandled && !CommonUtils.isOpsInserted()) {
                                    mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 5000);
                                }
                            }
                            if (!mIsOpsFinished && mOpsHasSignalCount == 1) {
                                //ops有信号过，但没有两次有信号
                                mShouldOpsHpd = true;
                                Log.d(TAG, "should pull ops hpd ..");
                            }
                            if (!mIsOpsFinished && mShouldOpsHpd && isInPCSource() && !CommonUtils.isOpsDisable()) {
                                mMHandler.removeMessages(MSG_ENABLE_OPS);
                                mMHandler.sendEmptyMessageDelayed(MSG_ENABLE_OPS, 3000);
                            }
                            break;
                        }
                    }
                case "notifySystemInputSourceChange":
                case "notifyTvChannelChange":
                    mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);
                    handleSourceChange();
                    //重置倒计时
                    mSigLostCount = 0;
                    /*synchronized (mTvInfoMutex) {
                        if (!mMHandler.hasMessages(MSG_INIT_INFO)) {
                            Log.i(TAG, "executeNotifyAction2: ");
                            mMHandler.sendEmptyMessage(MSG_INIT_INFO);
                        }
                    }*/
                    getAvailableSource();
                    break;

                case "notifySystemInputSourcePlugInOutStatus":
                    getAvailableSource();
                    break;
                case "notifySystemCecChange":
                    //FIXME ,this notification is not so suitable
                    break;
                case "notifyKeyPadLockedPopupMsg":
                    break;
                default:
                    break;
            }
        }
    }

    private void handleSourceChange() {
        if (!EeoApplication.isTifPlayerActivityStopped) {
            //切信号源时退出画中画、批注、控制界面、投屏引导界面等
            CommonUtils.exitAll(TifPlayerActivity.this);
            CommonUtils.finishMultiScreen(TifPlayerActivity.this);
        }
    }

    /**
     * 发送广播消息
     *
     * @param action
     * @param b
     */
    private void TsendBroadcast(final String action, final Bundle b) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent(action);
                if (b != null) {
                    intent.putExtras(b);
                }
                sendBroadcast(intent);
                Log.i(TAG, "sendBroadcastAsUser  action = " + action);
            }
        }).start();
    }

    /**
     * 发出切安卓通道的广播(应用使用)，然后自杀
     */
    private void exitToAndroid() {
        TsendBroadcast(ACTION_SEND_GO_TO_ANDROID, null);//老方案代码，切设备，可考虑与opensdk一起去掉。有别的事件
        super.onStop();
    }


    /**
     * 判断但前是否在TV类的通道搜台
     *
     * @return boolean
     */
    private boolean isScanning() {
        try {
            return (mAtvApi != null && mAtvApi.eventTvScanAtvIsScanning() ||
                    mAtscApi != null && mAtscApi.eventTvAtscIsScanning() ||
                    mDtmbApi != null && mDtmbApi.eventTvScanDtmbIsScanning() ||
                    mDvbtApi != null && mDvbtApi.eventTvScanDvbtIsScanning() ||
                    mDvbcApi != null && mDvbcApi.eventTvScanDvbcIsScanning());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 判断当前通道是否是TV类型的。包括DTV和ATV的
     *
     * @return boolean
     */
    private boolean isCurrentSourceTV() {
        if (!Constant.CVT_EN_TV_SOURCE) {
            return false;
        }

        if (mSourceApi != null && mChannelsApi != null) {
            EntityInputSource inputSource = null;
            try {
                inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            if (inputSource == null) {
                return false;
            }
            if ((inputSource.category == EnumInputSourceCategory.INPUTSOURCE_DTMB)
                    || (inputSource.category == EnumInputSourceCategory.INPUTSOURCE_ATV)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断当前是否在DTV类型的通道
     *
     * @return boolean
     */
    private boolean isInDTVSource() {
        if (!Constant.CVT_EN_TV_SOURCE) {
            return false;
        }

        if (mSourceApi != null && mChannelsApi != null) {
            EntityInputSource inputSource = null;
            try {
                inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            if (inputSource == null) {
                return false;
            }
            if (inputSource.category == EnumInputSourceCategory.INPUTSOURCE_DTMB) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断当前是否在ATV类型通道
     *
     * @return boolean
     */
    private boolean isInATVSource() {
        if (!Constant.CVT_EN_TV_SOURCE) {
            return false;
        }

        if (mSourceApi != null && mChannelsApi != null) {
            EntityInputSource inputSource = null;
            try {
                inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            if (inputSource == null) {
                return false;
            }
            if (inputSource.category == EnumInputSourceCategory.INPUTSOURCE_ATV) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断当前是否是PC通道
     *
     * @return boolean
     */
    private boolean isInPCSource() {
        return mCurInputSourceId == EnumInputSourceId.SOURCE_ID_PC;
        /*if (mSourceApi != null && mChannelsApi != null) {
            try {
                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                if (inputSource != null) {
                    if (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_PC) { //TvCommonManager.INPUT_SOURCE_HDMI, in 8386 it is PC Channel
                        return true;
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;*/
    }

    /**
     * 判断当前是否是HDMI通道
     *
     * @return
     */
    private boolean isInHDMISource() {
        return mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI1 || mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI2;
        /*if (mSourceApi != null && mChannelsApi != null) {
            try {
                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                if (inputSource != null) {
                    if ((inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI1) || (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI2) ||
                            (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI3) || (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI4) ||
                            (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI5)) { //TvCommonManager.INPUT_SOURCE_HDMI, in 8386 it is PC Channel
                        return true;
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;*/
    }

    private boolean isInHDMI1Source() {
        return mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI1;
        /*if (mSourceApi != null && mChannelsApi != null) {
            try {
                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                if (inputSource != null) {
                    if ((inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI1)) {
                        return true;
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;*/
    }

    private boolean isInHDMI2Source() {
        return mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI2;
       /* if (mSourceApi != null && mChannelsApi != null) {
            try {
                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                if (inputSource != null) {
                    if ((inputSource.sourceId == EnumInputSourceId.SOURCE_ID_HDMI2)) {
                        return true;
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;*/
    }

    /**
     * 判断当前是否是TypeC通道
     *
     * @return
     */
    private boolean isInTypeCSource() {
        return mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC1 || mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC2;
        /*if (mSourceApi != null && mChannelsApi != null) {
            try {
                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                if (inputSource != null) {
                    if ((inputSource.sourceId == EnumInputSourceId.SOURCE_ID_TYPEC1) || (inputSource.sourceId == EnumInputSourceId.SOURCE_ID_TYPEC2)) { //TvCommonManager.INPUT_SOURCE_HDMI, in 8386 it is PC Channel
                        return true;
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;*/
    }

    private void updateNoSignalView() {
        boolean isOpsDisable = CommonUtils.isOpsDisable();
        btnStart.setVisibility(isOpsDisable ? View.GONE : View.VISIBLE);
        txtNoPc.setVisibility(View.GONE);
        txtMsgPhone.setVisibility(View.VISIBLE);
        if (isInPCSource()) {
            txtMsg.setText(getString(isOpsDisable ? R.string.current_sign_windows_disabled : R.string.current_sign));
            if (!mStartOpsTimeout && CommonUtils.isOpsInserted()) {
                btnStart.setText(getString(R.string.start_computer));
                txtMsgPhone.setText(getString(isOpsDisable ? R.string.no_sign_windows_disabled : R.string.no_sign_msg));
            } else {
                btnStart.setVisibility(View.GONE);
                txtNoPc.setVisibility(View.VISIBLE);
                txtNoPc.setText(getString(CommonUtils.isOpsInserted() ? R.string.no_pc_2 : R.string.no_pc_1));
                txtMsgPhone.setVisibility(View.GONE);
            }
        } else if (isInHDMI1Source()) {
            txtMsg.setText(getString(R.string.current_sign_hdmi_1));
            btnStart.setText(getString(R.string.change_to_pc));
            txtMsgPhone.setText(getString(R.string.no_sign_hdmi));
        } else if (isInHDMI2Source()) {
            txtMsg.setText(getString(R.string.current_sign_hdmi_2));
            btnStart.setText(getString(R.string.change_to_pc));
            txtMsgPhone.setText(getString(R.string.no_sign_hdmi));
        } else if (isInHDMISource()) {
            txtMsg.setText(getString(R.string.current_sign_hdmi));
            btnStart.setText(getString(R.string.change_to_pc));
            txtMsgPhone.setText(getString(R.string.no_sign_hdmi));
        } else if (isInTypeCSource()) {
            txtMsg.setText(getString(R.string.current_sign_typec));
            btnStart.setText(getString(R.string.change_to_pc));
            txtMsgPhone.setText(getString(R.string.no_sign_hdmi));
        }
    }

    /**
     * 根据是否当前是pc还是HDMI无信号页面显示不同状态
     */
    private void updateNoSignalState() {
        updateNoSignalView();
        //通知退出半屏模式
        mMHandler.sendEmptyMessage(MSG_HIDE_SCREEN_OFFSET_UI);
    }

    /**
     * 保存当前通道号到sys.last.tv.source，但其他activity从外部通道直接启动，如侧边栏启动finder，
     * tvsetting的onpause-onstop被回调时使用
     */
    private void saveLastSource(int sourceId) {
        SystemProperties.set(PROPERTY_LAST_TV_SOURCE_ID, "" + sourceId);
    }

    /**
     * 清除sys.last.tv.source保存的上次通道。每次从别的activity－返回到tvsetting后，恢复上次通道后必须调用，如在onresume里面使用
     */
    private void clearLastSource() {
        SystemProperties.set(PROPERTY_LAST_TV_SOURCE_ID, "-1");
    }

    /**
     * 从属性sys.last.tv.source获取上次保存的通道号，用于tvsetting被别的Activity中断儿onpause-onstop的现场保存，一般在onresume里面调用
     *
     * @return int
     */
    private int getLastSource() {
        return SystemProperties.getInt(PROPERTY_LAST_TV_SOURCE_ID, -1);
    }

    private void sendTpvLifecycleChanged(String reason) {
        Intent intent = new Intent(TPV_ACTION);
        intent.putExtra("reason", reason);
        sendBroadcast(intent);
    }

    private void bindGestureService() {
        Log.d(TAG, "bindGestureService: ");
        Intent intent = new Intent(TifPlayerActivity.this, GestureDetectorService.class);
        bindService(intent, mServiceConnection, BIND_AUTO_CREATE);
    }

    private void unbindGestureService() {
        unbindService(mServiceConnection);
    }

    private SourceChangeListener mSourceChangeListener = null;

    private void registerSourceChangeListener() {
        if (mSourceChangeListener == null) {
            mSourceChangeListener = new SourceChangeListener() {
                @Override
                public void onSourceChange(String previousSource, String newSource, boolean isFinished) {
                    //显示分辨率信息
                    /*if (isFinished) {
                        mMHandler.sendEmptyMessage(MSG_SHOW_RESOLUTION);
                    }*/
                    //快速更新无信号界面
                    if (rlNoSign.getVisibility() == View.VISIBLE) {
                        if (UdiConstant.SOURCE_PC.equals(newSource) && CommonUtils.isOpsInserted()) {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    rlNoSign.setVisibility(View.GONE);
                                }
                            });
                            mMHandler.removeMessages(MSG_DISPLAY_NO_SIGNAL);
                            mMHandler.sendEmptyMessageDelayed(MSG_DISPLAY_NO_SIGNAL, 20000);
                        } else if (isFinished) {
                            if (!isSourceEnable(newSource)) {
                                //优化无信号界面当前通道信息更新不及时
                                mMHandler.sendEmptyMessage(MSG_UPDATE_NO_SIGNAL_VIEW);
                            }
                        }
                    }
                }
            };
        }
        EeoApplication.getApplication().registerSourceChangeListener(mSourceChangeListener);
    }

    private void unregisterSourceChangeListener() {
        if (mSourceChangeListener != null) {
            EeoApplication.getApplication().unregisterSourceChangeListener(mSourceChangeListener);
            mSourceChangeListener = null;
        }
    }

    private void getAvailableSource() {
        //获取当前信号哪些可用
        Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                        String availableSource = EeoApplication.udi.getAvailableSource();
                        if (availableSource != null) {
                            Gson gson = new Gson();
                            AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                            List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                            emitter.onNext(sources);
                        }
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
                    @Override
                    public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                        mAvailableSourceList.clear();
                        mAvailableSourceList.addAll(sourcesBeans);
                    }
                });
    }

    private void checkSourceEnable(EnumInputSourceId sourceId) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (!isSourceEnable(sourceId)) {
                    Log.d(TAG, sourceId + " is not enabled!");
                    mMHandler.sendEmptyMessage(MSG_DISPLAY_NO_SIGNAL_RIGHT_AWAY);
                }
            }
        }).start();
    }

    private boolean isSourceEnable(EnumInputSourceId sourceId) {
        if (sourceId == null) {
            return false;
        }
        String availableSource = EeoApplication.udi.getAvailableSource();
        Log.i(TAG, "isSourceEnable: availableSource = " + availableSource);
        if (availableSource == null) {
            return false;
        }
        Gson gson = new Gson();
        AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
        List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
        if (sourceId == EnumInputSourceId.SOURCE_ID_PC) {
//            for (AvailableSourceBean.SourcesBean sourcesBean : sources) {
//                if (sourcesBean.getSourceItem().equals(UdiConstant.SOURCE_PC)) {
//                    return sourcesBean.isHasSignal();
//                }
//            }
            //PC插着时直接返回true，避免开机时先出现无信号界面，再启动pc-ID1110480
            return CommonUtils.isOpsInserted() && !CommonUtils.isOpsDisable();
        } else if (sourceId == EnumInputSourceId.SOURCE_ID_TYPEC1) {
            for (AvailableSourceBean.SourcesBean sourcesBean : sources) {
                if (sourcesBean.getSourceItem().equals(UdiConstant.SOURCE_TYPE_C1)) {
                    return sourcesBean.isHasSignal();
                }
            }
        } else if (sourceId == EnumInputSourceId.SOURCE_ID_TYPEC2) {
            for (AvailableSourceBean.SourcesBean sourcesBean : sources) {
                if (sourcesBean.getSourceItem().equals(UdiConstant.SOURCE_TYPE_C2)) {
                    return sourcesBean.isHasSignal();
                }
            }
        } else if (sourceId == EnumInputSourceId.SOURCE_ID_HDMI1) {
            for (AvailableSourceBean.SourcesBean sourcesBean : sources) {
                if (sourcesBean.getSourceItem().equals(UdiConstant.SOURCE_HDMI1)) {
                    return sourcesBean.isHasSignal();
                }
            }
        } else if (sourceId == EnumInputSourceId.SOURCE_ID_HDMI2) {
            for (AvailableSourceBean.SourcesBean sourcesBean : sources) {
                if (sourcesBean.getSourceItem().equals(UdiConstant.SOURCE_HDMI2)) {
                    return sourcesBean.isHasSignal();
                }
            }
        }
        return false;
    }

    private boolean isSourceEnable(String source) {
        if (source == null || mAvailableSourceList.size() == 0) {
            return false;
        }
        for (AvailableSourceBean.SourcesBean sourcesBean : mAvailableSourceList) {
            if (sourcesBean.getSourceItem().equals(source)) {
                return sourcesBean.isHasSignal();
            }
        }
        return false;
    }

    private void updateCurrentSource() {
        if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_PC) {
            EeoApplication.mCurrentSource = UdiConstant.SOURCE_PC;
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC1) {
            EeoApplication.mCurrentSource = UdiConstant.SOURCE_TYPE_C1;
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC2) {
            EeoApplication.mCurrentSource = UdiConstant.SOURCE_TYPE_C2;
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI1) {
            EeoApplication.mCurrentSource = UdiConstant.SOURCE_HDMI1;
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI2) {
            EeoApplication.mCurrentSource = UdiConstant.SOURCE_HDMI2;
        }
    }

    public void showResolutionToast(boolean hasResolution) {
        if (mResolutionToast == null) {
            mResolutionToast = new Toast(this);
            LayoutInflater layoutInflater = (LayoutInflater) getSystemService(LAYOUT_INFLATER_SERVICE);
            View view = layoutInflater.inflate(R.layout.toast_resolution, null);
            mResolutionTv = view.findViewById(R.id.tv_resolution);
            mResolutionToast.setView(view);
            mResolutionToast.setDuration(Toast.LENGTH_LONG);
            mResolutionToast.setGravity(Gravity.TOP | Gravity.START, CommonUtils.dp2px(this, 13),
                    CommonUtils.dp2px(this, 14));
        }
        //通过tvView来修改Toast的宽度
        if (hasResolution) {
            mResolutionTv.setMinimumWidth(CommonUtils.dp2px(this, 195));
        } else {
            mResolutionTv.setMinimumWidth(CommonUtils.dp2px(this, 83));
        }
        if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_PC) {
            if (EeoApplication.mShouldHandleBootSource) {
                return;
            }
            mResolutionTv.setText(hasResolution ? String.format(getString(R.string.toast_resolution),
                    getString(R.string.windows), mEntityResolution.horizontal, mEntityResolution.vertical, mEntityResolution.frequency) :
                    getString(R.string.windows));
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC1 || mCurInputSourceId == EnumInputSourceId.SOURCE_ID_TYPEC2) {
            mResolutionTv.setText(hasResolution ? String.format(getString(R.string.toast_resolution),
                    getString(R.string.front_typec), mEntityResolution.horizontal, mEntityResolution.vertical, mEntityResolution.frequency) :
                    getString(R.string.front_typec));
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI1) {
            mResolutionTv.setText(hasResolution ? String.format(getString(R.string.toast_resolution),
                    getString(R.string.behind_hdmi1), mEntityResolution.horizontal, mEntityResolution.vertical, mEntityResolution.frequency) :
                    getString(R.string.behind_hdmi1));
        } else if (mCurInputSourceId == EnumInputSourceId.SOURCE_ID_HDMI2) {
            mResolutionTv.setText(hasResolution ? String.format(getString(R.string.toast_resolution),
                    getString(R.string.behind_hdmi2), mEntityResolution.horizontal, mEntityResolution.vertical, mEntityResolution.frequency) :
                    getString(R.string.behind_hdmi2));
        }
        mResolutionToast.show();
    }

    /**
     * HDMI和typeC插线连接保障提示
     * 开机
     */
    private View mUhdView;
    private WindowManager.LayoutParams mUhdLayoutParams;
    private boolean mHasUhdViewShown;
    private WindowManager mWindowManager;
    private long mHdmiUhdFirstShowTime = 0;  //HDMI和TypeC分辨率达到4k触发弹提示，每次开机仅提示一次

    private void showUhdView(boolean isHdmi) {
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        }
        if (mUhdView == null) {
            mUhdView = LayoutInflater.from(this).inflate(R.layout.toast_uhd, null);
            mUhdView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {

                    if (motionEvent.getAction() == MotionEvent.ACTION_OUTSIDE) {
                        Log.i(TAG, "outside");
                        dismissUhdView();
                    }
                    return false;
                }
            });
            mUhdLayoutParams = new WindowManager.LayoutParams();
            mUhdLayoutParams.format = PixelFormat.RGBA_8888;
            mUhdLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            mUhdLayoutParams.gravity = Gravity.START | Gravity.BOTTOM;
            mUhdLayoutParams.width = getResources().getDimensionPixelSize(R.dimen.toast_uhd_width)
                    + getResources().getDimensionPixelSize(R.dimen.toast_resolution_margin_start);
            mUhdLayoutParams.height = getResources().getDimensionPixelSize(R.dimen.toast_uhd_height);
            mUhdLayoutParams.x = 0;
            mUhdLayoutParams.y = getResources().getDimensionPixelSize(R.dimen.toast_uhd_margin_bottom);
            mUhdLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
        }
        ImageView uhdIv = mUhdView.findViewById(R.id.iv_uhd);
        TextView uhdTv = mUhdView.findViewById(R.id.tv_uhd);
        if (isHdmi) {
            uhdIv.setImageResource(R.drawable.uhd_hdmi);
            uhdTv.setText(R.string.uhd_hdmi);
        } else {
            uhdIv.setImageResource(R.drawable.uhd_type_c);
            uhdTv.setText(R.string.uhd_type_c);
        }
        if (mHasUhdViewShown) {
            mWindowManager.updateViewLayout(mUhdView, mUhdLayoutParams);
        } else {
            mWindowManager.addView(mUhdView, mUhdLayoutParams);
            ObjectAnimator animator = ObjectAnimator.ofFloat(mUhdView, "translationX", -mUhdLayoutParams.width, 0);
            animator.setDuration(600);
            animator.start();
        }
        mHasUhdViewShown = true;
        mMHandler.removeMessages(MSG_DISMISS_UHD);
        mMHandler.sendEmptyMessageDelayed(MSG_DISMISS_UHD, 5000);
    }

    public void dismissUhdView() {
        if (mWindowManager != null && mUhdView != null && mUhdView.isAttachedToWindow()) {
            Log.d(TAG, "dismissUhdView");
            mWindowManager.removeView(mUhdView);
        }
        mHasUhdViewShown = false;
        mMHandler.removeMessages(MSG_DISMISS_UHD);
    }

    private void exitBootAnim() {
        if (!mIsBootAnimExit && !EeoApplication.mShouldHandleBootSource) {
            SystemProperties.set("persist.sys.bootanim.exit", "1");
            mIsBootAnimExit = true;
        }
    }

    /**
     * 目标通道非ops且插了ops的，开机通道先设为ops，启动后再在TifPlayerActivity切换到目标通道
     * ①ops有信号后 ②无信号 ③超时
     */
    private void handleBootSource() {
        if (!EeoApplication.mShouldHandleBootSource) {
            return;
        }
        if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_PC)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI1)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI2)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI2);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_TYPEC)) {
            EeoApplication.udi.changeSource(Constant.SOURCE_TYPE_C);
        }
        EeoApplication.mShouldHandleBootSource = false;
    }

    /**
     * 开机过程、开机通道切换前先关闭插入跳转
     * 这里恢复之前的状态
     */
    public void handleAutoSwitchChannel() {
        if (!mHasAutoSwitchChannelHandled) {
            mHasAutoSwitchChannelHandled = true;
            //恢复之前的状态
            String autoSwitchChannel = CommonUtils.getAutoSwitchChannel(TifPlayerActivity.this);
            SystemProperties.set(Constant.PROP_AUTO_SWITCH_CHANNEL, autoSwitchChannel);
            Log.d(TAG, "handleAutoSwitchChannel: autoSwitchChannel=" + autoSwitchChannel);
        }
    }

    /**
     * 检查是否需要OPS拉一下HPD
     * 避免*********-异常场景：部分ops有第二次信号前就切到了其它通道，ops颜色格式异常
     */
    private void checkOpsPullHpd() {
        //避免ops颜色格式异常
        if (mIsOpsFinished) {
            return;
        }
        if (isInPCSource()) {
            if (mShouldOpsHpd) {
                if (!PowerUtil.getInstance(TifPlayerActivity.this).isShuttingDown()) {
                    pullHpd();
                }
            } else {
                mOpsHasSignalCount++;
                Log.d(TAG, "mOpsHasSignalCount=" + mOpsHasSignalCount);
                if (mOpsHasSignalCount >= 2) {
                    mIsOpsFinished = true;
                    if (!mHasAutoSwitchChannelHandled) {
                        mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 5000);
                    }
                }
            }
        } else {
            if (!mHasAutoSwitchChannelHandled) {
                mMHandler.sendEmptyMessageDelayed(MSG_HANDLE_AUTO_SWITCH_CHANNEL, 5000);
            }
            //ops没有两次有信号
            if (mOpsHasSignalCount <= 1) {
                mShouldOpsHpd = true;
                Log.d(TAG, "should pull ops hpd .");
            }
        }
    }

    private void pullHpd() {
        Log.e(TAG, "pull hpd");
        CommonUtils.resetHpd();
        mShouldOpsHpd = false;
        mIsOpsFinished = true;
    }

    /**
     * 开机ops未完全准备好切换了通道的
     * 禁用一下ops再启用
     * 避免切ops通道一直无信号
     */
    private void enableOps() {
        Log.d(TAG, "enableOps: ");
        mShouldOpsHpd = false;
        mIsOpsFinished = true;
        SystemControlManager.getInstance().disableOpsHdmi(1);
        mMHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                SystemControlManager.getInstance().disableOpsHdmi(0);
                //切一下其它信号源再切回来，才能快速显示ops画面
                EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
            }
        }, 500);
        mMHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                //切一下其它信号源再切回来，才能快速显示ops画面
                EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
            }
        }, 1000);
    }
}
