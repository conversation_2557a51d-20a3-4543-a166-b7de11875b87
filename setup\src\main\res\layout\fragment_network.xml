<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scrollview"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_bg"
    android:overScrollMode="never">

    <RelativeLayout
        android:layout_width="331dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="221dp">

        <ImageView
            android:id="@+id/back_ic"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_arrow_left_w">

        </ImageView>

        <TextView
            android:id="@+id/txt_ethernet_network"
            android:layout_width="wrap_content"
            android:layout_height="21dp"
            android:layout_centerHorizontal="true"
            android:text="@string/network_settings"
            android:textColor="@color/white_100"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/txt_wifi_settings"
            style="@style/NetWork_TextView"
            android:layout_below="@id/back_ic"
            android:layout_marginStart="10dp"
            android:layout_marginTop="38dp"
            android:text="@string/wifi_settings" />

        <Switch
            android:id="@+id/sw_wifi_settings"
            style="@style/NetWork_Switch"
            android:layout_below="@id/back_ic"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="35dp"
            android:layout_marginEnd="10dp"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

        <View
            android:id="@+id/line"
            style="@style/About_Line"
            android:layout_below="@+id/sw_wifi_settings"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp" />

        <RelativeLayout
            android:id="@+id/rl_sub_wifi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/line"
            android:visibility="gone">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_connected"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_wifi_list"
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:layout_below="@id/rv_connected"
                android:layout_gravity="center_vertical" />
        </RelativeLayout>

        <TextView
            style="@style/NetWork_TextView"
            android:layout_below="@id/rl_sub_wifi"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            android:text="@string/ethernet_settings" />

        <Switch
            android:id="@+id/sw_ethernet_settings"
            style="@style/NetWork_Switch"
            android:layout_below="@id/rl_sub_wifi"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="17dp"
            android:layout_marginEnd="10dp"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

        <RelativeLayout
            android:id="@+id/rl_sub_ethernet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/sw_ethernet_settings"
            android:paddingHorizontal="10dp"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/ll_mac_address"
                style="@style/NetWork_Linear"
                android:layout_height="29dp"
                android:layout_marginTop="25dp">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/mac_address" />

                <TextView
                    android:id="@+id/txt_mac_address"
                    style="@style/NetWork_TextView"
                    android:text="122:123:234:12"
                    android:textColor="@color/white_100" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_ip_mode"
                style="@style/NetWork_Linear"
                android:layout_height="29dp"
                android:layout_below="@id/ll_mac_address"
                android:layout_marginTop="11dp">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/ip_setting" />

                <TextView
                    android:id="@+id/txt_ip_setting"
                    style="@style/NetWork_TextView"
                    android:text="@string/manual"
                    android:textColor="@color/white_100" />

                <ImageView
                    android:id="@+id/img_arrow"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/ic_arrow_right_w" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_ip_setting"
                style="@style/NetWork_Linear"
                android:layout_width="match_parent"
                android:layout_height="29dp"
                android:layout_below="@id/ll_ip_mode"
                android:layout_marginTop="11dp">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:width="37dp"
                    android:text="@string/ip_address" />

                <TextView
                    android:id="@+id/txt_ip_address"
                    style="@style/NetWork_TextView"
                    android:text="**************"
                    android:textColor="@color/white_100" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_ip"
                style="@style/NetWork_Linear"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll_ip_setting"
                android:layout_marginTop="15dp"
                android:visibility="gone">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/ip_address" />

                <ImageView
                    android:id="@+id/img_ip"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:background="@drawable/ic_status_wrong"
                    android:visibility="invisible" />

                <EditText
                    android:id="@+id/edt_ip_address"
                    style="@style/Network_EditText"
                    android:background="@drawable/input_bg" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_mask"
                style="@style/NetWork_Linear"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll_ip"
                android:layout_marginTop="11dp"
                android:visibility="gone">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/subnet_mask" />

                <ImageView
                    android:id="@+id/img_mask"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:background="@drawable/ic_status_wrong"
                    android:visibility="invisible" />

                <EditText
                    android:id="@+id/edt_mask_address"
                    style="@style/Network_EditText"
                    android:background="@drawable/input_bg" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_gateway"
                style="@style/NetWork_Linear"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll_mask"
                android:layout_marginTop="11dp"
                android:visibility="gone">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/gateway" />

                <ImageView
                    android:id="@+id/img_gateway"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:background="@drawable/ic_status_wrong"
                    android:visibility="invisible" />

                <EditText
                    android:id="@+id/edt_gateway"
                    style="@style/Network_EditText"
                    android:background="@drawable/input_bg" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_dns"
                style="@style/NetWork_Linear"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll_gateway"
                android:layout_marginTop="11dp"
                android:visibility="gone">

                <TextView
                    style="@style/NetWork_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/DNS" />

                <ImageView
                    android:id="@+id/img_dns"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:background="@drawable/ic_status_wrong"
                    android:visibility="invisible" />

                <EditText
                    android:id="@+id/edt_dns"
                    style="@style/Network_EditText"
                    android:background="@drawable/input_bg" />
            </LinearLayout>
        </RelativeLayout>

        <Button
            android:id="@+id/btn_skip"
            style="@style/MyButtonStyle"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:layout_below="@id/rl_sub_ethernet"
            android:layout_marginStart="67dp"
            android:layout_marginTop="46dp"
            android:enabled="true"
            android:text="@string/skip" />

        <Button
            android:id="@+id/btn_confirm"
            style="@style/MyButtonStyle"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:layout_alignTop="@id/btn_skip"
            android:layout_marginStart="21dp"
            android:layout_toEndOf="@id/btn_skip"
            android:enabled="false"
            android:text="@string/confirm" />

    </RelativeLayout>

</androidx.core.widget.NestedScrollView>