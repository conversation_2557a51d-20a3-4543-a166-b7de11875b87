package cn.eeo.classin.setup.utils;


import static android.content.Context.LAYOUT_INFLATER_SERVICE;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityManagerNative;
import android.app.IActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Environment;
import android.os.RemoteException;
import android.os.StatFs;
import android.os.SystemProperties;
import android.provider.Settings;
import android.text.format.Formatter;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.eeo.classin.setup.MainActivity;
import cn.eeo.classin.setup.R;

public class CommonUtils {
    public static final String TAG = "CommonUtils===";
    private static final int FAST_CLICK_TIME_MIN = 300; // 两次点击间隔不能少于300ms
    private static final int FAST_CLICK_TIME_2_S = 2000;
    private static long lastClickTime;

    /**
     * 判断是否为合法IP
     *
     * @param ipAddress
     * @return
     */
    public static boolean isBooleanIp(String ipAddress) {
        String ip = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pattern = Pattern.compile(ip);
        Matcher matcher = pattern.matcher(ipAddress);
        return matcher.matches();
    }


    public static int dp2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, context.getResources().getDisplayMetrics());
    }


    public static int sp2px(Context context, float spValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, spValue, context.getResources().getDisplayMetrics());
    }

    /**
     * wifi详情页返回广播通知
     *
     * @param activity
     */
    public static void backByWiFiMore(Activity activity) {
        Intent intent = new Intent();
        intent.setAction(Constant.ACTION_OUT_WIFI_MORE_PAGE);
        activity.sendBroadcast(intent);
    }

    /**
     * 手机系统版本
     */
    public static String getSdkVersion() {
        return android.os.Build.VERSION.RELEASE;
    }


    /**
     * 当前的手机总存储内存大小
     *
     * @return xx GB
     */
    public static String getTotalInternalMemorySize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        return Formatter.formatFileSize(context, totalBlocks * blockSize);
    }


    /**
     * 当前手机可用存储内存大小
     *
     * @return xx GB
     */
    public static String getAvailableInternalMemorySize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long availableBlocks = stat.getAvailableBlocks();
        return Formatter.formatFileSize(context, availableBlocks * blockSize);
    }

    /**
     * 获取android总运行内存大小
     *
     * @param context
     */
    public static String getTotalMemory(Context context) {
        String str1 = "/proc/meminfo";// 系统内存信息文件
        String str2;
        String[] arrayOfString;
        long initial_memory = 0;
        try {
            FileReader localFileReader = new FileReader(str1);
            BufferedReader localBufferedReader = new BufferedReader(localFileReader, 8192);
            str2 = localBufferedReader.readLine();// 读取meminfo第一行，系统总内存大小
            arrayOfString = str2.split("\\s+");
            for (String num : arrayOfString) {
                Log.i(str2, num + "\t");
            }
            // 获得系统总内存，单位是KB
            int i = Integer.valueOf(arrayOfString[1]).intValue();
            //int值乘以1024转换为long类型
            initial_memory = new Long((long) i * 1024);
            localBufferedReader.close();
        } catch (IOException e) {
        }
        return Formatter.formatFileSize(context, initial_memory);// Byte转换为KB或者MB，内存大小规格化
    }

    /**
     * 是否有网络
     *
     * @param context
     * @return
     */
    public static boolean isNetSystemUsable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if (networkInfo != null) {
            boolean connectedOrConnecting = networkInfo.isConnectedOrConnecting();
            return connectedOrConnecting;
        } else {
            return false;
        }
    }

    /**
     * 获取是否有网络连接
     *
     * @param context
     * @return
     */
    public static boolean isNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return mNetworkInfo.isAvailable();
            }
        }
        return false;
    }

    /**
     * 系统是否装了某个应用
     */
    public static boolean isAppInstalled(Context context, String pkgName) {
        try {
            ApplicationInfo info = context.getPackageManager().getApplicationInfo(pkgName, PackageManager.GET_UNINSTALLED_PACKAGES);
            if (info != null) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String getTopActivity(Context context) {
        ActivityManager manager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(1);
        if (taskInfos != null && taskInfos.size() > 0) {
            String topActivityName = taskInfos.get(0).topActivity.getClassName();
            return topActivityName;
        }
        return "";
    }

    /**
     * 自定义toast
     *
     * @param context
     */
    public static void showSuccessToast(Context context) {
        Toast toast = new Toast(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View v = layoutInflater.inflate(R.layout.toast_success_bg, null);
        toast.setView(v);
        toast.setGravity(Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, 0, 400);
        toast.show();
    }

    public static void showFailToast(Context context) {
        Toast toast = new Toast(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View v = layoutInflater.inflate(R.layout.toast_fail_bg, null);
        toast.setView(v);
        toast.setGravity(Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, 0, 400);
        toast.show();
    }

    /**
     * 防止点击太快
     *
     * @return
     */
    public static boolean isFastClick() {
        boolean flag = true;
        long currentClickTime = System.currentTimeMillis();
        if ((currentClickTime - lastClickTime) >= FAST_CLICK_TIME_MIN) {
            flag = false;
        }
        lastClickTime = currentClickTime;
        return flag;
    }

    public static boolean isWiFiConnected(Context context) {
        ConnectivityManager connManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);

        return networkInfo != null && networkInfo.isConnected();
    }

    /**
     * 设置触摸框是否可以触摸
     *
     * @param isTouch true，可以触摸；false：不可以触摸
     */
    public static void setTouchState(Context context, boolean isTouch) {
        Intent intent = new Intent();

        intent.setAction(Constant.SET_USB_ENABLE_ACTION);
        //标记广播是系统设置页面来的
//        intent.putExtra(Constant.USB2_TOUCH_MODULE, Constant.MODULE_SETTING);
        intent.setComponent(new ComponentName("com.eeo.systemsetting",
                "com.eeo.systemsetting.broadcast.SystemSettingBroadcast"));
        if (isTouch) {
            intent.putExtra(Constant.USB2_TOUCH_KEY, Constant.TOUCH);
        } else {
            intent.putExtra(Constant.USB2_TOUCH_KEY, Constant.UN_TOUCH);

        }
        context.sendBroadcast(intent);
    }

    public static void setLanguage(Locale locale) {
        Log.d(TAG, "setLanguage: " + locale.toString());
        try {
            IActivityManager am = ActivityManagerNative.getDefault();
            Configuration config = am.getConfiguration();
            config.setLocale(locale);
            //设置开机动画：0-default,1-CN,2-EN
            SystemProperties.set("persist.sys.boot_animation", String.valueOf("zh".equals(locale.getLanguage()) ? 1 : 2));
            am.updatePersistentConfiguration(config);
            //EeoApplication.set
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public static void finishSetupWizard(Context context) {
        // Add a persistent setting to allow other apps to know the device has been provisioned.
        Settings.Global.putInt(context.getContentResolver(), Settings.Global.DEVICE_PROVISIONED, 1);

        // remove this activity from the package manager.
        PackageManager pm = context.getPackageManager();
        ComponentName mainActivity = new ComponentName(context, MainActivity.class);
        pm.setComponentEnabledSetting(mainActivity, PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
        //禁用依赖的ota库的receiver，避免和设置依赖的BootReceiver都开机自启，导致ota同时连接两个互踢
        ComponentName otaReceiver = new ComponentName("cn.eeo.classin.setup", "com.eeo.ota.receiver.BootReceiver");
        pm.setComponentEnabledSetting(otaReceiver, PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);
    }
}
