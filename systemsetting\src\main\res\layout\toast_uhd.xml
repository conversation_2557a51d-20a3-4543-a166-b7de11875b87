<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/toast_resolution_margin_start"
        android:background="@drawable/toast_resolution_bg"
        android:minWidth="@dimen/toast_uhd_width"
        android:minHeight="@dimen/toast_uhd_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:id="@+id/iv_uhd"
            android:layout_width="@dimen/toast_uhd_iv_width"
            android:layout_height="@dimen/toast_uhd_iv_height"
            android:layout_marginStart="@dimen/toast_uhd_iv_margin_start"
            android:scaleType="centerInside"
            android:src="@drawable/uhd_hdmi"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_uhd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/toast_uhd_tv_martin_start"
            android:text="@string/uhd_hdmi"
            android:textColor="@color/text_black_100"
            android:textSize="@dimen/toast_uhd_tv_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_uhd"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>