package com.eeo.systemsetting.projection;

public class ProjectionConstant {
    /**
     * 是否使用录屏
     * 还是使用TvView抢占通道方案
     */
    public static final boolean USE_RECORD = false;

    /**
     * 是否录屏
     */
    public final static String RECORD = "record";
    /**
     * 屏幕宽度
     */
    public final static String WIDTH = "width";
    public final static String HEIGHT = "height";
    public final static String DENSITY = "density";
    public final static String RESULT_CODE = "resultCode";

    /**
     * SystemProperties设置小窗口位置
     * 给InputDispatcher.cpp处理
     **/
    public final static String KEY_SMALL_WINDOW_LEFT = "eeo.small.window.left";
    public final static String KEY_SMALL_WINDOW_TOP = "eeo.small.window.top";
    public final static String KEY_SMALL_WINDOW_RIGHT = "eeo.small.window.right";
    public final static String KEY_SMALL_WINDOW_BOTTOM = "eeo.small.window.bottom";
    public final static String KEY_SMALL_WINDOW_SHOW = "eeo.small.window.show";

    /**
     * 请求录屏返回的intent
     */
    public final static String DATA = "data";

    /**
     * 画中画小窗口宽高
     * 单位dp
     * 整个画中画高度：小窗口+快捷栏+Margin
     */
    public final static int SMALL_WINDOW_WIDTH = 576;
    public final static int SMALL_WINDOW_HEIGHT = 324;
    public final static int SHORTCUT_VIEW_WIDTH = SMALL_WINDOW_WIDTH;
    public final static int SHORTCUT_VIEW_HEIGHT = 27;
    public final static int SHORTCUT_VIEW_MARGIN_TOP = 7;

    /**
     * 小窗口距离屏幕边缘的最小距离
     */
    public final static int MARGIN = 7;

    /**
     * 圆角背景的padding
     * 触控要算上
     */
    public final static int PADDING = 0;

}
