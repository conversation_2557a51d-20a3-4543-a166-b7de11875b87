package com.eeo.udisdk.voice;

import android.content.Context;
import android.util.Log;

import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 声音相关
 */
public class VoiceManager {
    private static final String TAG = "Udi-VoiceManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;

    public VoiceManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    public boolean isMute() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/voice/mute"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isMute:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isMute body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("isMute");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean setMute(boolean isMute) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("isMute", isMute);
            jsonObject.put("isShowUi", false); //不显示CVTE Ui
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/voice/mute"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setMute:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
