package com.eeo.ota.util;

import android.content.Context;
import android.content.SharedPreferences;

public class SharedPreferencesUtil {
    private static final String SP_NAME = "ota";

    /**
     * 设备密钥
     */
    private static final String KEY_DEVICE_PSK = "device_psk";
    /**
     * 是否有新版本
     *
     * @deprecated 使用report和showDialog两个flag来代替
     */
    private static final String KEY_HAVE_NEW_VERSION = "have_new_version";
    /**
     * 是否需要上报
     * android ota更新后需要上报成功
     */
    private static final String KEY_SHOULD_REPORT = "should_report";
    /**
     * 是否显示更新结果对话框
     * android ota完整流程更新后会显示一次
     */
    private static final String KEY_SHOW_UPDATE_DIALOG = "show_update_dialog";

    /**
     * 新版本号
     */
    private static final String KEY_NEW_VERSION = "new_version";
    /**
     * 更新包md5
     */
    private static final String KEY_MD5 = "md5";
    /**
     * 更新包下载存放路径
     */
    private static final String KEY_OUTPUT_FILE = "output_file";
    /**
     * 下载的更新包对应的版本号
     */
    private static final String KEY_OUTPUT_FILE_VERSION = "output_file_version";

    /**
     * 是否打开自动更新
     */
    private static final String KEY_AUTO_UPDATE = "auto_update";

    /**
     * Has the upgrade of the array microphone been completed
     * false: Incomplete or failed, upgrade service needs to be started
     * true: Completed successfully, no need to start upgrade service
     */
    private static final String KEY_ARRAY_MIC_UPDATE_COMPLETED = "array_mic_update_completed";

    private static final String KEY_AUTO_UPDATE_RETRY_TIMES = "auto_update_retry_times";


    public static String getDevicePsk(Context context) {
        return getString(context, KEY_DEVICE_PSK, null);
    }

    public static void setDevicePsk(Context context, String value) {
        setString(context, KEY_DEVICE_PSK, value);
    }

    public static boolean getHaveNewVersion(Context context) {
        return getBoolean(context, KEY_HAVE_NEW_VERSION, false);
    }

    public static void setHaveNewVersion(Context context, boolean value) {
        setBoolean(context, KEY_HAVE_NEW_VERSION, value);
    }

    public static boolean getShouldReport(Context context) {
        return getBoolean(context, KEY_SHOULD_REPORT, false);
    }

    public static void setShouldReport(Context context, boolean value) {
        setBoolean(context, KEY_SHOULD_REPORT, value);
    }

    public static boolean getShowUpdateDialog(Context context) {
        return getBoolean(context, KEY_SHOW_UPDATE_DIALOG, false);
    }

    public static void setShowUpdateDialog(Context context, boolean value) {
        setBoolean(context, KEY_SHOW_UPDATE_DIALOG, value);
    }

    public static String getNewVersion(Context context) {
        return getString(context, KEY_NEW_VERSION, null);
    }

    public static void setNewVersion(Context context, String value) {
        setString(context, KEY_NEW_VERSION, value);
    }

    public static String getMd5(Context context) {
        return getString(context, KEY_MD5, null);
    }

    public static void setMD5(Context context, String value) {
        setString(context, KEY_MD5, value);
    }

    public static String getOutputFile(Context context) {
        return getString(context, KEY_OUTPUT_FILE, null);
    }

    public static void setOutputFile(Context context, String value) {
        setString(context, KEY_OUTPUT_FILE, value);
    }

    public static String getOutputFileVersion(Context context) {
        return getString(context, KEY_OUTPUT_FILE_VERSION, null);
    }

    public static void setOutputFileVersion(Context context, String value) {
        setString(context, KEY_OUTPUT_FILE_VERSION, value);
    }

    public static int getAutoUpdateRetryTimes(Context context) {
        return getInt(context, KEY_AUTO_UPDATE_RETRY_TIMES, 0);
    }

    public static void setAutoUpdateRetryTimes(Context context, int value) {
        setInt(context, KEY_AUTO_UPDATE_RETRY_TIMES, value);
    }

    public static void saveIsAutoUpdate(Context context, Boolean isAutoUpdate) {
        setBoolean(context, KEY_AUTO_UPDATE, isAutoUpdate);
    }

    public static Boolean getIsAutoUpdate(Context context) {
        return getBoolean(context, KEY_AUTO_UPDATE, true);
    }

    public static boolean isArrayMicUpdateCompleted(Context context) {
        return getBoolean(context, KEY_ARRAY_MIC_UPDATE_COMPLETED, false);
    }

    public static void setArrayMicUpdateCompleted(Context context, boolean completed) {
        setBoolean(context, KEY_ARRAY_MIC_UPDATE_COMPLETED, completed);
    }

    private static String getString(Context context, String key, String def) {
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE).getString(key, def);
    }

    private static void setString(Context context, String key, String value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(key, value);
        editor.apply();
    }

    private static boolean getBoolean(Context context, String key, boolean def) {
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE).getBoolean(key, def);
    }

    private static void setBoolean(Context context, String key, boolean value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(key, value);
        editor.apply();
    }

    private static int getInt(Context context, String key, int def) {
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE).getInt(key, def);
    }

    private static void setInt(Context context, String key, int value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(key, value);
        editor.apply();
    }
}
