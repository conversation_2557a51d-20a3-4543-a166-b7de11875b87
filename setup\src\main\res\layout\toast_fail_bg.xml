<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="160dp"
    android:layout_height="53dp"
    android:background="@drawable/shape_network_toast_bg"
    android:paddingHorizontal="30dp"
    android:paddingVertical="19dp"
    android:gravity="center"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="@drawable/ic_failure" />

    <TextView
        android:id="@+id/txt_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/toast_fail"
        android:textColor="@color/black_100"
        android:textSize="12sp" />

</LinearLayout>