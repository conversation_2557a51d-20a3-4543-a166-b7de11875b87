package com.eeo.WhiteboardAccelerate.SerialPort

import android.os.Handler
import android.os.Message
import android.util.Log
import android.content.Context
import android.provider.Settings
import com.eeo.WhiteboardAccelerate.CommonUtils
import com.eeo.WhiteboardAccelerate.CommonUtils.TAG
import com.eeo.WhiteboardAccelerate.CommonUtils.byteToHexString
import com.eeo.WhiteboardAccelerate.CommonUtils.intHexString
import com.eeo.WhiteboardAccelerate.Service.AccelerateService
import com.eeo.WhiteboardAccelerate.Service.AccelerateService.Companion.MSG_CLEAR_CANVAS_DATA
import com.eeo.WhiteboardAccelerate.SkiaUtils
import kotlin.experimental.xor

class SerialPortData {
    private var mHandlerMsg: Handler?=null
    private var mContext: Context?=null
    private var revcData: MutableList<Byte> = mutableListOf()
    private var clssInfps:Int = 63//fps越高代表classin响应越好，越低代表classin越忙.正常值为63
    private var classInEraserStatus:Int = 2;//默认非板擦状态
    constructor(mHandler: Handler,mContext: Context){
        this.mHandlerMsg = mHandler
        this.mContext =  mContext
    }
    /**
     *串口数据格式定义
     *—————————————————————————————————————————————————————————————————————————————————————
     * 第1字节  第2字节   第3字节	第4字节  第5-6字节 第7字节  第7+1字节 第7+N字节 第N+8字节 第N+9字节
     * 起始位  消息发送方 消息接收方	消息类型  功能类型 数据长度N  数据1   数据N	 校验位	  结束位
     * —————————————————————————————————————————————————————————————————————————————————————
     */

    /**
     * 解析接收到的串口数据
     * @param data 接收的数据
     */
    fun parseSerialPortData(serialData: ByteArray,dataSize:Int){
        for (index in 0 until dataSize){
            revcData.add(serialData[index])
        }
        //Log.d(TAG,"parseSerialPortData:"+byteToHexString(revcData.toByteArray(),revcData.size))
        var handleIndex = 0
        while(handleIndex < revcData.size){
            //查找匹配协议报文头
            if (revcData[handleIndex] != DataUtils.FUNCTION_START_BYTE){
                handleIndex++
                continue
            }

            var dataLeng = if(revcData.size > 6+handleIndex) getByteToInt(revcData[handleIndex+6]) else break
            if(dataLeng>8){//协议数据部分目前最长8字节
                handleIndex++
                continue
            }
            var checkSum = if(revcData.size > 7+dataLeng+handleIndex)  revcData[7+dataLeng+handleIndex] else break
            var dataCheck= revcData[3+handleIndex]
            for (index in (handleIndex+4)..(6+dataLeng+handleIndex)){
                dataCheck = dataCheck.xor(revcData[index])
            }
            //验证校验位
            if(dataCheck != checkSum){
                handleIndex++
                continue
            }
            //验证协议报文结束位
            var endByte = if(revcData.size > handleIndex+dataLeng+8) revcData[handleIndex + dataLeng + 8] else break
            if ( endByte!= DataUtils.FUNCTION_END_BYTE) {
                handleIndex++
                continue
            }
            //报文验证成功，开始处理数据
            var protocolLeng = handleIndex+8+dataLeng
            handlerProtocolMsg(revcData.slice(handleIndex until protocolLeng))
            handleIndex = protocolLeng+1 //下一组协议数据index
        }
        //清除已经处理的协议数据。
        if(revcData.size == handleIndex) {
            revcData.clear()
        }
        else {
            for (index in 0 until  handleIndex)
                if(revcData.size >0) revcData.removeAt(0)
        }

//        if(revcData.size == 0){
//            Log.d(TAG,"handleIndex:"+handleIndex)
//        }
//        else{
//            Log.d(TAG,"revcData:"+byteToHexString(revcData.toByteArray(),revcData.size))
//        }
    }
    /**
     * 处理串口协议定义的消息
     * @param protocolData:串口协议数据
     */
    private fun handlerProtocolMsg(protocolData: List<Byte>){
        var msgType = protocolData!![3]
        var funcType: Int = getByteToInt(protocolData[4]).shl(8) or getByteToInt(protocolData[5])
        var dateLeng = getByteToInt(protocolData[6])
        when (msgType) {
            DataUtils.FUNCTION_MSG_ACK -> Log.d(TAG, "ACK MESSAGE")
            DataUtils.FUNCTION_MSG_NOTIFY -> replyAckMsg(protocolData.toByteArray())
            DataUtils.FUNCTION_MSG_REQUEST -> Log.d(TAG, "REQUEST MESSAGE")
            DataUtils.FUNCTION_MSG_REPEAT -> Log.d(TAG, "REPEAT MESSAGE")
        }
        when(funcType) {
            DataUtils.COMMAND_COMMUNICATION_VALID ->
                Log.d(TAG, "receive data command uart heartbeat")
            DataUtils.COMMAND_WRITE_ACCELERATE ->{
                var status = getByteToInt(protocolData[7])
                if(status == 0x01 && classInEraserStatus != 0x01 && !********************()){//唤出板擦时不允许书写
                    mHandlerMsg!!.removeMessages(MSG_CLEAR_CANVAS_DATA)
                    SkiaUtils.native_setOpenWriteAccelerate(true)
                }
                else if(status == 0x02 ){
                    SkiaUtils.native_setOpenWriteAccelerate(false)
                    sendClearCanvasMessage()
                }
                Log.d(TAG,"receive data command_write_accelerate:"+status)
            }
            DataUtils.COMMAND_OPS_RESOLUTION -> {
                if (dateLeng== 4) {
                    var HorizontalPixe = getByteToInt(protocolData[7]).shl(8) or getByteToInt(protocolData[8])
                    var VerticalPixe = getByteToInt(protocolData[9]).shl(8) or getByteToInt(protocolData[10])
                    SkiaUtils.native_setOpsResolution(HorizontalPixe,VerticalPixe)
                    Log.d(TAG, "receive data command_ops_resolution:$HorizontalPixe x $VerticalPixe")
                }
            }
            DataUtils.COMMAND_WRITE_ACCELERATE_RANGE -> {
                if (dateLeng == 8) {
                    var topLeftX = getByteToInt(protocolData[7]).shl(8) or getByteToInt(protocolData[8])
                    var topLeftY = getByteToInt(protocolData[9]).shl(8) or getByteToInt(protocolData[10])
                    var bottomRightX = getByteToInt(protocolData[11]).shl(8) or getByteToInt(protocolData[12])
                    var bottomRightY = getByteToInt(protocolData[13]).shl(8) or getByteToInt(protocolData[14])
                    SkiaUtils.native_setWriteRange(topLeftX,topLeftY,bottomRightX,bottomRightY)
                    Log.d(TAG, "receive data command_write_accelerate_range:" +
                            "($topLeftX ,$topLeftY ) ( $bottomRightX,$bottomRightY )")
                }
            }
            DataUtils.COMMAND_WRITE_ACCELERATE_RANGE_LIMIT -> {
                if (dateLeng == 8) {
                    var topLeftX = getByteToInt(protocolData[7]).shl(8) or getByteToInt(protocolData[8])
                    var topLeftY = getByteToInt(protocolData[9]).shl(8) or getByteToInt(protocolData[10])
                    var bottomRightX = getByteToInt(protocolData[11]).shl(8) or getByteToInt(protocolData[12])
                    var bottomRightY = getByteToInt(protocolData[13]).shl(8) or getByteToInt(protocolData[14])
                    SkiaUtils.native_setLimitWriteRange(0,0,0,0)//先清空旧区域
                    SkiaUtils.native_setLimitWriteRange(topLeftX,topLeftY,bottomRightX,bottomRightY)
                    Log.d(TAG, "receive data command_write_accelerate_range_limit:" +
                            "($topLeftX ,$topLeftY) ($bottomRightX,$bottomRightY )")
                }
            }
            DataUtils.COMMAND_STROKE_WIDTH -> {
                Log.d(TAG,"receive data command_stroke_width:"+getByteToInt(protocolData[7]))
                SkiaUtils.native_setPaintWidth(getByteToInt(protocolData[7]))
            }
            DataUtils.COMMAND_STROKE_COLOR -> {
                if(dateLeng == 4){
                    var a:Int=  getByteToInt(protocolData[7])
                    var r:Int = getByteToInt(protocolData[8])
                    var g:Int = getByteToInt(protocolData[9])
                    var b:Int = getByteToInt(protocolData[10])
                    Log.d(TAG, "receive data command_stroke_color:"+intHexString(a)+
                            intHexString(r)+intHexString(g)+intHexString(b))
                    SkiaUtils.native_setPaintColor(a,r,g,b)
                }
            }
            DataUtils.COMMAND_STROKE_SHAPE ->{
                Log.d(TAG,"receive data command_stroke_shape:"+protocolData[7].toString())
            }
            DataUtils.COMMAND_CLASSIN_FPS ->{
                if(dateLeng == 2){
                    var mFps = getByteToInt(protocolData[7]).shl(8) or getByteToInt(protocolData[8])
                    if(mFps >0){
                        clssInfps = mFps;
                        SkiaUtils.native_setClassInFps(clssInfps)
                        Log.d(TAG,"receive data COMMAND_CLASSIN_FPS:"+clssInfps)
                    }
                }
            }
            DataUtils.COMMAND_CLASSIN_ERASER ->{
                var status = getByteToInt(protocolData[7])
                Log.d(TAG,"receive data COMMAND_CLASSIN_ERASER:"+status)
                if(status == 0x01 || status == 0x03){//01:唤起板擦 03:清屏
                    mHandlerMsg!!.removeMessages(MSG_CLEAR_CANVAS_DATA)
                    mHandlerMsg!!.sendEmptyMessage(MSG_CLEAR_CANVAS_DATA)
                }
                classInEraserStatus = status;
            }
            DataUtils.COMMAND_ALGORITHM_VER ->{
                var comData = ByteArray(1)
                comData[0] = DataUtils.ALGORITHM_VER
                sendClassInMsg(DataUtils.FUNCTION_MSG_NOTIFY,DataUtils.COMMAND_ALGORITHM_VER ,comData)
            }
            else -> {
                Log.d(TAG,"receive data command error!!!")
            }
        }
    }

    /**
     * 回复通知类型消息的ACK
     * @param data:数据内容
     */
    private fun replyAckMsg(data: ByteArray){
        var ackData = ArrayList<Byte>()
        ackData.add(DataUtils.FUNCTION_START_BYTE)
        ackData.add(DataUtils.FUNCTION_MSG_OBJECT_ANDROID)
        ackData.add(DataUtils.FUNCTION_MSG_OBJECT_OPS)
        ackData.add(DataUtils.FUNCTION_MSG_ACK)
        ackData.add(data!![4])
        ackData.add(data!![5])
        var dataLeng =  getByteToInt(data[6])
        ackData.add(data!![6])
        for(index in 1..dataLeng) {
            ackData.add(data[index + 6])
        }
        var dataCheck= ackData[3]
        for (index in 4..6+dataLeng){
            dataCheck = dataCheck.xor(ackData[index])
        }
        ackData.add(dataCheck)
        ackData.add(DataUtils.FUNCTION_END_BYTE)

        Log.d(TAG,"replyAckMsg:${byteToHexString(ackData.toByteArray(),ackData.size)}")
        sendHandleMessage(ackData.toByteArray())
    }
    /**
     * 向OPS发送通知类型、请求类型、循环类型消息
     * @param msgType:消息类型
     * @param commandType:命令名称类型
     * @param commandDataLeng:命令数据内容
     */
    fun sendClassInMsg(msgType:Byte,commandType: Int,commandData: ByteArray){
        var notifyData = ArrayList<Byte>()
        notifyData.add(DataUtils.FUNCTION_START_BYTE)
        notifyData.add(DataUtils.FUNCTION_MSG_OBJECT_ANDROID)
        notifyData.add(DataUtils.FUNCTION_MSG_OBJECT_OPS)
        notifyData.add(msgType)
        notifyData.add((commandType.shr(8) and 0xFF).toByte())
        notifyData.add((commandType and 0xFF).toByte())
        notifyData.add((commandData.size and 0xFF).toByte())
        for(index in 0 until commandData.size) {
            notifyData.add(commandData[index])
        }
        var dataCheck= notifyData[3]
        for (index in 4..6+commandData.size){
            dataCheck = dataCheck.xor(notifyData[index])
        }
        notifyData.add(dataCheck)
        notifyData.add(DataUtils.FUNCTION_END_BYTE)

        /**1分钟没有收到ack回复，则重发消息**/
        Log.d(TAG,"sendOpsMsg=${byteToHexString(notifyData!!.toByteArray(),notifyData.size)}")
        sendHandleMessage(notifyData.toByteArray())
    }
    /**
     * Byte类型转换Int，防止出现负数
     * @param byte
     */
    private fun getByteToInt(byte:Byte):Int{
        return byte.toInt().and(0xFF)
    }
    private fun sendHandleMessage(data:ByteArray){
        val msg = Message()
        val bundle = msg.data
        bundle.putByteArray(AccelerateService.msg_ops_data, data)
        msg.data = bundle
        msg.what = AccelerateService.MSG_SEND_UART_DATA
        mHandlerMsg!!.sendMessage(msg)
    }

    private fun sendClearCanvasMessage(){
        mHandlerMsg!!.removeMessages(MSG_CLEAR_CANVAS_DATA)
        mHandlerMsg!!.let {
            when(clssInfps){
                in 20 .. 45 ->
                    it.sendEmptyMessageDelayed(MSG_CLEAR_CANVAS_DATA,(6300/clssInfps).toLong())
                in 10 until 20 ->
                    it.sendEmptyMessageDelayed(MSG_CLEAR_CANVAS_DATA,1000)
                in 1 until 10 ->
                    it.sendEmptyMessageDelayed(MSG_CLEAR_CANVAS_DATA,1500)
                else -> it.sendEmptyMessage(MSG_CLEAR_CANVAS_DATA)
            }
        }
    }
    private fun ********************():Boolean{
        var isProjection = Settings.Global.getInt(mContext!!.contentResolver, "is_projection", 0)
        var isOffsetScreen =  CommonUtils.getSystemProperty("vendor.screen.yoffset")
        Log.d(TAG, "isProjection=$isProjection,isOffsetScreen=$isOffsetScreen")
        return  isProjection == 1 || (isOffsetScreen != "0" && isOffsetScreen != "")
    }
}