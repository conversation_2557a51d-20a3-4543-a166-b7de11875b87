<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/FirstFragment">

    <fragment
        android:id="@+id/FirstFragment"
        android:name="cn.eeo.classin.setup.FirstFragment"
        android:label="@string/first_fragment_label"
        tools:layout="@layout/fragment_first">

        <action
            android:id="@+id/action_FirstFragment_to_SecondFragment"
            app:destination="@id/SecondFragment" />
        <action
            android:id="@+id/action_FirstFragment_to_UserGuideAnimationFragment"
            app:destination="@id/UserGuideAnimationFragment" />

    </fragment>
    <fragment
        android:id="@+id/SecondFragment"
        android:name="cn.eeo.classin.setup.SecondFragment"
        android:label="@string/second_fragment_label"
        tools:layout="@layout/fragment_second">

        <action
            android:id="@+id/action_SecondFragment_to_NetworkFragment"
            app:destination="@id/NetworkFragment" />
        <action
            android:id="@+id/action_SecondFragment_to_BasicSettingFragment"
            app:destination="@id/BasicSettingFragment" />
    </fragment>

    <fragment
        android:id="@+id/NetworkFragment"
        android:name="cn.eeo.classin.setup.NetworkFragment"
        android:label="fragment_network"
        tools:layout="@layout/fragment_network">
        <action
            android:id="@+id/action_NetworkFragment_to_BasicSettingFragment"
            app:destination="@id/BasicSettingFragment" />
        <action
            android:id="@+id/action_NetworkFragment_to_SecondFragment"
            app:destination="@id/SecondFragment" />

    </fragment>
    <fragment
        android:id="@+id/BasicSettingFragment"
        android:name="cn.eeo.classin.setup.BasicSettingFragment"
        android:label="BasicSettingFragment"
        tools:layout="@layout/basic_setting">
        <action
            android:id="@+id/action_BasicSettingFragment_to_UserAgreementFragment"
            app:destination="@id/UserAgreementFragment" />
    </fragment>
    <fragment
        android:id="@+id/UserAgreementFragment"
        android:name="cn.eeo.classin.setup.UserAgreementFragment"
        android:label="BasicSettingFragment"
        tools:layout="@layout/fragment_user_agreement">
        <action
            android:id="@+id/action_UserAgreementFragment_to_UserGuideAnimationFragment"
            app:destination="@id/UserGuideAnimationFragment" />
    </fragment>
    <fragment
        android:id="@+id/UserGuideAnimationFragment"
        android:name="cn.eeo.classin.setup.UserGuideAnimationFragment"
        android:label="UserGuideAnimationFragment"
        tools:layout="@layout/fragment_animation" />

</navigation>