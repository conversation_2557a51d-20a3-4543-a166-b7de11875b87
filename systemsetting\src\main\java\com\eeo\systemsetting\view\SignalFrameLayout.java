package com.eeo.systemsetting.view;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.bean.AvailableSourceBean;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.source.SourceChangeListener;
import com.google.gson.Gson;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class SignalFrameLayout extends FrameLayout implements View.OnClickListener {
    public static final String TAG = "SignalFrameLayout===";
    View rootView = null;
    Activity activity;

    private ImageView ivBack;

    private FrameLayout mWindowsLayout;
    private TextView mWindowsTv;
    private FrameLayout mTypeCLayout;
    private FrameLayout mBehindHdmi1Layout;
    private FrameLayout mBehindHdmi2Layout;

    private List<AvailableSourceBean.SourcesBean> sources = null;

    private SourceChangeListener mSourceChangeListener = null;

    private String mCurrentSource;

    public SignalFrameLayout(Activity activity, Context context, List<AvailableSourceBean.SourcesBean> sources) {
        super(context);
        this.activity = activity;
        this.sources = sources;
        if (rootView == null) {
            rootView = LayoutInflater.from(context).inflate(R.layout.activity_signal, null);
        }

        findView();
        addView(rootView);


        if (sources != null && sources.size() > 0) {
            Log.i(TAG, "SignalFrameLayout: sources != null");
            getAvailableSource(sources);
        } else {
            Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
                        @Override
                        public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                            String availableSource = EeoApplication.udi.getAvailableSource();
                            Log.i(TAG, "SignalFrameLayout: " + availableSource);
                            if (availableSource != null) {
                                Gson gson = new Gson();
                                AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                                List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                                emitter.onNext(sources);
                            }
                        }
                    }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
                        @Override
                        public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                            getAvailableSource(sourcesBeans);
                        }
                    });
        }

        if (EeoApplication.mCurrentSource != null) {
            getCurrentSource(EeoApplication.mCurrentSource);
        } else {
            Observable.create(new ObservableOnSubscribe<String>() {
                        @Override
                        public void subscribe(ObservableEmitter<String> emitter) throws Exception {
                            String currentSource = EeoApplication.udi.getCurrentSource();
                            emitter.onNext(currentSource);
                        }
                    }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<String>() {
                        @Override
                        public void accept(String s) throws Exception {
                            getCurrentSource(s);
                        }
                    });
        }

        mSourceChangeListener = new SourceChangeListener() {
            @Override
            public void onSourceChange(String previousSource, String newSource, boolean isFinished) {
                if (isFinished) {
                    activity.finish();
                }
            }
        };
        EeoApplication.getApplication().registerSourceChangeListener(mSourceChangeListener);
    }


    /**
     * 获取当前信号
     */
    private void getCurrentSource(String currentSource) {
        Log.i(TAG, "SignalFrameLayout: currentSource : " + currentSource);
        mCurrentSource = currentSource;
        switch (currentSource) {
            case UdiConstant.SOURCE_PC:
                mWindowsLayout.setSelected(true);
                break;

            case UdiConstant.SOURCE_TYPE_C1:
            case UdiConstant.SOURCE_TYPE_C2:
                mTypeCLayout.setSelected(true);
                break;

            case UdiConstant.SOURCE_HDMI1:
                mBehindHdmi1Layout.setSelected(true);
                break;

            case UdiConstant.SOURCE_HDMI2:
                mBehindHdmi2Layout.setSelected(true);
                break;
        }
    }

    public void getAvailableSource(List<AvailableSourceBean.SourcesBean> sources) {
        if (sources != null) {
            for (int i = 0; i < sources.size(); i++) {
                if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_PC)) {
                    enableWindows(sources.get(i).isHasSignal());
                }

                if (sources.get(i).getSourceItem().equals(Constant.SOURCE_TYPE_C)) {
                    enableItem(mTypeCLayout, sources.get(i).isHasSignal());
                }

                if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_HDMI1)) {
                    enableItem(mBehindHdmi1Layout, sources.get(i).isHasSignal());
                }

                if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_HDMI2)) {
                    enableItem(mBehindHdmi2Layout, sources.get(i).isHasSignal());
                }
            }
        }
    }

    private void findView() {
        ivBack = rootView.findViewById(R.id.iv_back);
        ivBack.setOnClickListener(this);

        mWindowsLayout = rootView.findViewById(R.id.fl_windows);
        mWindowsLayout.setOnClickListener(this);
        mWindowsTv = rootView.findViewById(R.id.txt_windows);
        enableWindows(false);

        mTypeCLayout = rootView.findViewById(R.id.fl_typec);
        mTypeCLayout.setOnClickListener(this);
        enableItem(mTypeCLayout, false);

        mBehindHdmi1Layout = rootView.findViewById(R.id.fl_behind_hdmi1);
        mBehindHdmi1Layout.setOnClickListener(this);
        enableItem(mBehindHdmi1Layout, false);


        mBehindHdmi2Layout = rootView.findViewById(R.id.fl_behind_hdmi2);
        mBehindHdmi2Layout.setOnClickListener(this);
        enableItem(mBehindHdmi2Layout, false);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                CommonUtils.back(activity);
                break;

            case R.id.fl_windows:
                handleChangeSource(UdiConstant.SOURCE_PC);
                break;

            case R.id.fl_typec:
                handleChangeSource(Constant.SOURCE_TYPE_C);
                break;

            case R.id.fl_behind_hdmi1:
                handleChangeSource(UdiConstant.SOURCE_HDMI1);
                break;

            case R.id.fl_behind_hdmi2:
                handleChangeSource(UdiConstant.SOURCE_HDMI2);
                break;
            default:
                break;
        }
    }

    public void handleChangeSource(String newSource) {
        //点击当前通道也显示信源分辨率弹窗
        if (newSource.equals(mCurrentSource)) {
            CommonUtils.sendBroadcastToShowResolution(getContext());
        }
        EeoApplication.udi.changeSource(newSource);
        if (CommonUtils.isMultiScreen(getContext())) {
            CommonUtils.finishMultiScreen(getContext());
            activity.finish();
        }
    }

    private void enableItem(FrameLayout item, boolean enable) {
        if (item == null) {
            return;
        }
        if (enable) {
//            item.setEnabled(true);
            item.setAlpha(1.0f);
        } else {
//            item.setEnabled(false);
            item.setAlpha(0.3f);
        }
    }

    /**
     * 由于加了禁用内置电脑功能
     * 内置电脑通道item单独处理
     */
    private void enableWindows(boolean enable) {
        if (mWindowsLayout == null) {
            return;
        }
        if (CommonUtils.isOpsDisable()) {
            mWindowsTv.setText(getContext().getString(R.string.windows_disabled));
            mWindowsLayout.setAlpha(0.3f);
            mWindowsLayout.setClickable(false);
        } else {
            if (enable) {
//            item.setEnabled(true);
                mWindowsLayout.setAlpha(1.0f);
            } else {
//            item.setEnabled(false);
                mWindowsLayout.setAlpha(0.3f);
            }
            mWindowsTv.setText(getContext().getString(R.string.windows));
            mWindowsLayout.setClickable(true);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mSourceChangeListener != null) {
            EeoApplication.getApplication().unregisterSourceChangeListener(mSourceChangeListener);
        }
    }
}
