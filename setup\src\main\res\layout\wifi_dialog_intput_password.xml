<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="240dp"
    android:layout_height="148dp"
    android:layout_marginLeft="520dp"
    android:layout_marginTop="286dp"
    android:background="@drawable/shape_windows_host_bg">

    <ImageView
        android:id="@+id/img_wifi"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="21dp"
        android:layout_marginTop="21dp"
        android:background="@drawable/set_ic_wifi_n" />

    <TextView
        android:id="@+id/txt_wifi_front_str"
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/img_wifi"
        android:layout_marginStart="8dp"
        android:text="@string/wifi_front_str"/>

    <TextView
        android:id="@+id/txt_wifi_name"
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/txt_wifi_front_str"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:maxHeight="13dp"
        android:singleLine="true"
        android:text="HDEW506"
        android:textColor="@color/black_100"
        android:textSize="9sp" />

    <TextView
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/txt_wifi_name"
        android:text="@string/wifi_behind_str"/>

    <EditText
        android:id="@+id/edt_password"
        android:layout_width="197dp"
        android:layout_height="29dp"
        android:layout_below="@id/txt_wifi_name"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="11dp"
        android:background="@drawable/wifi_edt_password"
        android:hint="@string/password"
        android:paddingStart="8dp"
        android:paddingEnd="30dp"
        android:textColor="@color/black_100"
        android:textColorHint="@color/line1"
        android:textSize="9sp"
        android:inputType="textPassword"
        android:textCursorDrawable="@drawable/shape_network_edt_cursor"/>

    <ImageView
        android:id="@+id/img_eye"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="@drawable/ic_eye_off"
        android:layout_below="@id/txt_wifi_name"
        android:layout_marginTop="15dp"
        android:layout_marginStart="191dp"/>

    <TextView
        android:id="@+id/txt_password_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/img_eye"
        android:textSize="8sp"
        android:textColor="@color/error_red"
        android:text="@string/password_error"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>

    <Button
        android:id="@+id/btn_cancel"
        style="@style/SystemSetting_PopWindow_Host_Btn"
        android:background="@drawable/shape_shutdown_btn_white"
        android:text="@string/cancel"
        android:layout_marginStart="21dp"
        android:layout_below="@id/edt_password"
        android:layout_marginTop="20dp"
        />

    <Button
        android:id="@+id/btn_confirm"
        style="@style/SystemSetting_PopWindow_Host_Btn"
        android:layout_below="@id/edt_password"
        android:layout_marginStart="21dp"
        android:layout_marginTop="20dp"
        android:layout_toEndOf="@id/btn_cancel"
        android:background="@drawable/btn_un_click_green"
        android:text="@string/join"
        android:textColor="@color/white_100"
        android:enabled="false"/>

</RelativeLayout>