<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/select_wifi_item_press_color"
    android:paddingHorizontal="10dp"
    >

    <LinearLayout

        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/txt_wifi_name"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:paddingTop="14dp"
            android:layout_centerVertical="true"
            android:layout_gravity="fill_vertical"
            android:ellipsize="end"
            android:maxWidth="100dp"
            android:singleLine="true"
            android:text="HDEW506"
            android:textColor="@color/white_100"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/txt_state"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginLeft="4dp"
            android:text="低安全性"
            android:paddingTop="14dp"
            android:textColor="@color/white_100"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:textSize="8sp"
            android:visibility="gone" />
    </LinearLayout>


    <ImageView
        android:id="@+id/img_rank"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:background="@drawable/ic_wifi_1" />

    <ImageView
        android:id="@+id/img_password"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="4dp"
        android:layout_toStartOf="@id/img_rank"
        android:background="@drawable/ic_password" />


    <ImageView
        android:id="@+id/img_more"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="4dp"
        android:layout_toStartOf="@id/img_password"
        android:background="@drawable/select_wifi_img_press_icon" />

    <View
        android:id="@+id/line"
        style="@style/About_Line"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="0dp" />


</RelativeLayout>