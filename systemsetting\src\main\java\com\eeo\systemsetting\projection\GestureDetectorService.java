package com.eeo.systemsetting.projection;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;
import android.view.EventListener;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.WindowManagerGlobal;

import androidx.annotation.Nullable;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;

import java.util.Arrays;

public class GestureDetectorService extends Service {
    private static final String TAG = "GestureDetectorService";

    private EventListener mEventListener;

    /**
     * 聚拢手指数
     */
    private static final int NUM_POINTER = 5;
    /**
     * 收拢的距离阈值
     *
     * 65、75、86、110物理尺寸
     * 1428.48 (H)x803.52(V)  65（0.75）
     * <p>
     * 1650.24(H)x928.26(V)  75（0.87）
     * <p>
     * 1895.04(H)×1065.96(V)  86（1）
     * <p>
     * 2436.48(H) * 1370.52(V)  110（1.28）
     * <p>
     */
    private static final int THRESHOLD = 35;
    private static final int THRESHOLD_BS65A = 35;
    private static final int THRESHOLD_BS75A = 28;
    private static final int THRESHOLD_BS86A = 25;
    private static final int THRESHOLD_BS110A = 20;

    private int mThreshold = THRESHOLD;

    /**
     * 手指按下的坐标：(fingerDownPosition[i][0],fingerDownPosition[i][1])
     */
    private final float[][] fingerDownPosition = new float[NUM_POINTER][2];

    /**
     * 当前手指的坐标：(fingerPosition[i][0],fingerPosition[i][1])
     */
    private final float[][] fingerPosition = new float[NUM_POINTER][2];

    /**
     * 手指按下时，每两根手指的距离(这里用距离的平方：dx²+dy²）
     */
    private double[][] fingerDownDistance = new double[NUM_POINTER][NUM_POINTER];

    private long mLastFingerCloseTime;

    /**
     * 五指聚拢时小窗口的坐标
     */
    private int mX;
    private int mY;

    private GestureListener mGestureListener;

    private boolean isSmallWindowDisabled;

    private boolean hasFiveFingerHandled;

    /**
     * 物理尺规相关
     */
    private static final int RULER_NUM_POINTER = 3;
    private static final boolean RULER_ENABLE = false;
    private float mRulerCenterX;
    private float mRulerCenterY;
    private final float[][] mRulerFingerPosition = new float[RULER_NUM_POINTER][2];
    private double[] mRulerFingerDistance = new double[NUM_POINTER]; //三条边的长度
    /**
     * 物理尺柜三角形三条边边长都在范围内，就认为是物理尺柜
     */
    private boolean mRulerEdge1Ok;
    private boolean mRulerEdge2Ok;
    private boolean mRulerEdge3Ok;

    private int mRulerFingerDistance1Min;
    private int mRulerFingerDistance1Max;
    private int mRulerFingerDistance2Min;
    private int mRulerFingerDistance2Max;
    private int mRulerFingerDistance3Min;
    private int mRulerFingerDistance3Max;

    private static final int MSG_SHOW_RULER = 0x001;
    private static final int MSG_DISMISS_RULER = 0x002;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_SHOW_RULER:
                    showRulerView(msg.arg1, msg.arg2);
                    break;
                case MSG_DISMISS_RULER:
                    dismissRulerView();
                    break;
            }
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return new MyBinder();
    }

    public class MyBinder extends Binder {
        public GestureDetectorService getService() {
            return GestureDetectorService.this;
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "onCreate: ");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel mChannel = new NotificationChannel("channel_projection", "projectionChannel", NotificationManager.IMPORTANCE_NONE);
            notificationManager.createNotificationChannel(mChannel);
            Notification notification = new Notification.Builder(this, "channel_projection").build();
            startForeground(1, notification);
        }
        registerGestureDetector();
        init();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            int status = intent.getIntExtra("ProjectionStatus", 1);  //1启动画中画，2关闭画中画。
            Log.e(TAG, "onStartCommand: intent=" + intent + " ,ProjectionStatus=" + status);
            if (status == 2) {
                if (mGestureListener != null) {
                    mGestureListener.dismissProjection();
                }
                return Service.START_STICKY;
            }
        } else {
            registerGestureDetector();
        }
        return Service.START_STICKY;
    }

    /**
     * 无view的手势需要系统实现
     */
    private void registerGestureDetector() {
        if (mEventListener == null) {
            mEventListener = new EventListener.Stub() {
                @Override
                public void onKeyEvent(MotionEvent motionEvent) throws RemoteException {
                    switch (motionEvent.getAction() & MotionEvent.ACTION_MASK) {
                        case MotionEvent.ACTION_POINTER_DOWN:
                            if (motionEvent.getPointerCount() == NUM_POINTER) {
                                isSmallWindowDisabled = ProjectionUtil.isSmallWindowDisabled(GestureDetectorService.this);
                                if (isSmallWindowDisabled) {
                                    break;
                                }
                                fingerDownDistance = new double[NUM_POINTER][NUM_POINTER];
                                for (int i = 0; i < NUM_POINTER; i++) {
                                    //记录手指按下时的位置
                                    //fingerDownPosition[i][0] = motionEvent.getX(i);
                                    //fingerDownPosition[i][1] = motionEvent.getY(i);
//                                    Log.d(TAG, "ACTION_POINTER_DOWN " + i + " (" + fingerDownPosition[i][0] + "," + fingerDownPosition[i][1] + ")");
                                    //记录手指按下时，与其它手指的距离
                                    for (int j = i + 1; j < NUM_POINTER; j++) {
                                        fingerDownDistance[i][j] = Math.sqrt(Math.pow(motionEvent.getX(i) - motionEvent.getX(j), 2) + Math.pow(motionEvent.getY(i) - motionEvent.getY(j), 2));
                                    }
                                }
                            } else if (motionEvent.getPointerCount() == RULER_NUM_POINTER) {
                                handleThreeFingers(motionEvent);
                            }
                            break;
                        case MotionEvent.ACTION_MOVE:
                            if (motionEvent.getPointerCount() == NUM_POINTER) {
                                if (isSmallWindowDisabled || hasFiveFingerHandled) {
                                    break;
                                }
                                for (int i = 0; i < NUM_POINTER; i++) {
                                    fingerPosition[i][0] = motionEvent.getX(i);
                                    fingerPosition[i][1] = motionEvent.getY(i);
//                                    Log.d(TAG, "ACTION_MOVE " + i + " (" + fingerPosition[i][0] + "," + fingerPosition[i][1] + ")");
                                }
                                if (isFingersClosed()) {
                                    long currentTime = System.currentTimeMillis();
                                    if (currentTime - mLastFingerCloseTime < 500) {
                                        return;
                                    }
                                    mLastFingerCloseTime = System.currentTimeMillis();
                                    if (ProjectionUtil.isStarting()) {
                                        return;
                                    }
                                    Log.d(TAG, "isFingersClosed: true");
                                    //取五指的平均位置
                                    mX = mY = 0;
                                    for (int i = 0; i < fingerPosition.length; i++) {
                                        mX = mX + (int) fingerPosition[i][0];
                                        mY = mY + (int) fingerPosition[i][1];
                                    }
                                    mX = mX / fingerPosition.length;
                                    mY = mY / fingerPosition.length;
                                    if (mGestureListener != null) {
                                        mGestureListener.showProjection(mX, mY);
                                    }
                                    hasFiveFingerHandled = true;
                                }
                            } else if (motionEvent.getPointerCount() == RULER_NUM_POINTER) {
                                handleThreeFingers(motionEvent);
                            }
                            break;
                        case MotionEvent.ACTION_UP:
                            hasFiveFingerHandled = false;
                            EeoApplication.mIsFingerDown = false;
                            if (EeoApplication.mShouldSetTouchEnable) {
                                EeoApplication.mShouldSetTouchEnable = false;
                                CommonUtils.setTouchState(GestureDetectorService.this, true);//屏幕移动结束，待手指也抬起后才恢复windows触摸
                            }
                            if (mHasRulerViewShown) {
                                mHandler.removeMessages(MSG_SHOW_RULER);
                                mHandler.removeMessages(MSG_DISMISS_RULER);
                                mHandler.sendEmptyMessage(MSG_DISMISS_RULER);
                            }
                            break;
                        case MotionEvent.ACTION_DOWN:
                            EeoApplication.mIsFingerDown = true;
                            break;
                        case MotionEvent.ACTION_CANCEL:
                        default:
                            break;
                    }
                }
            };
        }
        try {
            WindowManagerGlobal.getWindowManagerService().registerGestureDetector(mEventListener);
        } catch (RemoteException e) {
            Log.e(TAG, "registerGestureDetector: exception:" + e);
            e.printStackTrace();
        }
    }

    /**
     * 手指聚拢
     * 判断方法：是否每两个手指的距离都变小
     */
    private boolean isFingersClosed() {
        int count = 0;
        int countNotClosed = 0;
        for (int i = 0; i < NUM_POINTER; i++) {
            for (int j = i + 1; j < NUM_POINTER; j++) {
                double distance = Math.sqrt(Math.pow(fingerPosition[i][0] - fingerPosition[j][0], 2) + Math.pow(fingerPosition[i][1] - fingerPosition[j][1], 2));
                double change = fingerDownDistance[i][j] - distance;
//                Log.d(TAG, "isFingersClosed: " + i + "与" + j + "距离：" + fingerDownDistance[i][j] + " ->" + distance + "  , change=" + change);
                //距离变小且要达到阈值
                if (change < -10) {
                    return false;
                } else if (change < 0) {
                    countNotClosed++;
                    //有2组手指距离小于0的认为不是聚拢
                    if (countNotClosed >= 2) {
                        return false;
                    }
                } else if (change < mThreshold) {
                    count++;
                    //5组手指距离未达到阈值才认为不是聚拢
                    if (count >= 5) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private void unregisterGestureDetector() {
        if (mEventListener != null) {
            try {
                WindowManagerGlobal.getWindowManagerService().unregisterGestureDetector(mEventListener);
                mEventListener = null;
            } catch (RemoteException e) {
                Log.e(TAG, "registerGestureDetector: exception:" + e);
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "onDestroy: ");
        unregisterGestureDetector();
    }

    public void setGestureListener(GestureListener listener) {
        mGestureListener = listener;
    }

    private View mRulerView;
    private WindowManager.LayoutParams mRulerLayoutParams;
    private boolean mHasRulerViewShown;
    private WindowManager mWindowManager;

    /**
     * @param x
     * @param y 图片的中心(x,y)
     */
    private void showRulerView(int x, int y) {
        if (mRulerView == null) {
            mRulerView = LayoutInflater.from(this).inflate(R.layout.window_ruler, null);
        }
        mRulerLayoutParams = new WindowManager.LayoutParams();
        mRulerLayoutParams.format = PixelFormat.RGBA_8888;
        mRulerLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        mRulerLayoutParams.gravity = Gravity.START | Gravity.TOP;
        mRulerLayoutParams.width = 576;
        mRulerLayoutParams.height = 312;
        mRulerLayoutParams.x = x - mRulerLayoutParams.width / 2;
        mRulerLayoutParams.y = y - mRulerLayoutParams.height / 2;
        mRulerLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL /*|
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE*/;
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        }
        if (mHasRulerViewShown) {
            mWindowManager.updateViewLayout(mRulerView, mRulerLayoutParams);
        } else {
            mWindowManager.addView(mRulerView, mRulerLayoutParams);
        }
        mHasRulerViewShown = true;
    }

    public void dismissRulerView() {
        if (mWindowManager != null) {
            mHasRulerViewShown = false;
            if (mRulerView != null && mRulerView.isAttachedToWindow()) {
                mWindowManager.removeView(mRulerView);
            }
        }
    }

    /**
     * 不同尺寸大屏对应的数值不一样
     */
    private void init() {
        if (Constant.IS_BS65A) {
            mThreshold = THRESHOLD_BS65A;
        } else if (Constant.IS_75) {
            mThreshold = THRESHOLD_BS75A;
        } else if (Constant.IS_86) {
            mThreshold = THRESHOLD_BS86A;
            mRulerFingerDistance1Min = 80;
            mRulerFingerDistance1Max = 86;
            mRulerFingerDistance2Min = 152;
            mRulerFingerDistance2Max = 162;
            mRulerFingerDistance3Min = 155;
            mRulerFingerDistance3Max = 165;
        } else if (Constant.IS_110) {
            mThreshold = THRESHOLD_BS110A;
            mRulerFingerDistance1Min = 60;
            mRulerFingerDistance1Max = 66;
            mRulerFingerDistance2Min = 122;
            mRulerFingerDistance2Max = 129;
            mRulerFingerDistance3Min = 124;
            mRulerFingerDistance3Max = 129;
        }
    }

    private void handleThreeFingers(MotionEvent motionEvent) {
        if (RULER_ENABLE) {
            mRulerCenterX = mRulerCenterY = 0;
            mRulerEdge1Ok = mRulerEdge2Ok = mRulerEdge3Ok = false;
            for (int i = 0; i < RULER_NUM_POINTER; i++) {
                mRulerFingerPosition[i][0] = motionEvent.getX(i);
                mRulerFingerPosition[i][1] = motionEvent.getY(i);
                mRulerCenterX = mRulerCenterX + motionEvent.getX(i);
                mRulerCenterY = mRulerCenterY + motionEvent.getY(i);
            }
            mRulerFingerDistance[0] = Math.sqrt(Math.pow(mRulerFingerPosition[0][0] - mRulerFingerPosition[1][0], 2) +
                    Math.pow(mRulerFingerPosition[0][1] - mRulerFingerPosition[1][1], 2));
            mRulerFingerDistance[1] = Math.sqrt(Math.pow(mRulerFingerPosition[0][0] - mRulerFingerPosition[2][0], 2) +
                    Math.pow(mRulerFingerPosition[0][1] - mRulerFingerPosition[2][1], 2));
            mRulerFingerDistance[2] = Math.sqrt(Math.pow(mRulerFingerPosition[1][0] - mRulerFingerPosition[2][0], 2) +
                    Math.pow(mRulerFingerPosition[1][1] - mRulerFingerPosition[2][1], 2));
            Arrays.sort(mRulerFingerDistance);
            if (mRulerFingerDistance[0] > mRulerFingerDistance1Min && mRulerFingerDistance[0] < mRulerFingerDistance1Max) {
                mRulerEdge1Ok = true;
            }
            if (mRulerFingerDistance[1] > mRulerFingerDistance2Min && mRulerFingerDistance[1] < mRulerFingerDistance2Max) {
                mRulerEdge2Ok = true;
            }
            if (mRulerFingerDistance[2] > mRulerFingerDistance3Min && mRulerFingerDistance[2] < mRulerFingerDistance3Max) {
                mRulerEdge3Ok = true;
            }
            //三条边长度都在范围内
            if (mRulerEdge1Ok && mRulerEdge2Ok && mRulerEdge3Ok) {
                mRulerCenterX = mRulerCenterX / RULER_NUM_POINTER;
                mRulerCenterY = mRulerCenterY / RULER_NUM_POINTER;
                mHandler.removeMessages(MSG_SHOW_RULER);
                mHandler.sendMessage(mHandler.obtainMessage(MSG_SHOW_RULER, (int) mRulerCenterX, (int) mRulerCenterY));
            }/* else {
                Log.e("TAG", "out :" + mRulerFingerDistance[0] + "," + mRulerFingerDistance[1] + "," + mRulerFingerDistance[2]);
            }*/
        }
    }
}
