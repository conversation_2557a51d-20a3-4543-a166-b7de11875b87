package cn.eeo.classin.setup.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import cn.eeo.classin.setup.NetworkFragment;
import cn.eeo.classin.setup.R;
import cn.eeo.classin.setup.wifi.AccessPoint;

public class WiFiScanListAdapter extends RecyclerView.Adapter<WiFiScanListAdapter.WifiViewHolder> {
    public static final String TAG = "WifiListAdapter===";
    private List<AccessPoint> list;
    private OnItemClickListener itemClickListener;

    private NetworkFragment fragment;

    public WiFiScanListAdapter(NetworkFragment fragment){
        this.fragment = fragment;
    }

    public interface OnItemClickListener {
        void onClick(AccessPoint accessPoint);
    }

    public void setAccessPoints(List<AccessPoint> list) {
        this.list = list;
        notifyDataSetChanged();
    }

    public void setItemClickListener(OnItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    @NonNull
    @Override
    public WifiViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new WifiViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull WifiViewHolder holder, int position) {
        AccessPoint accessPoint = list.get(position);
        holder.txtWifiName.setText(accessPoint.ssid);
        if (accessPoint.isSaved()) {
            holder.tvState.setVisibility(View.VISIBLE);
            holder.tvState.setText(accessPoint.getWifiStatusMsg(accessPoint.getStatuesCode()));
            holder.imgMore.setVisibility(View.VISIBLE);
            holder.imgMore.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (fragment != null) {
                        //fragment.addWiFiMoreFrameLayout(accessPoint.wifiInfo);
                    }
                    //Toast.makeText(view.getContext(), "点击了详情",Toast.LENGTH_LONG).show();
                }
            });

        } else {
            holder.tvState.setVisibility(View.GONE);
            holder.imgMore.setVisibility(View.GONE);
        }

        if (accessPoint.isSecured) {
            holder.imgPassword.setVisibility(View.VISIBLE);
        } else {
            holder.imgPassword.setVisibility(View.GONE);
        }

        //信号强度
        if (accessPoint.getSignalLevel() <= 1){
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_1);
        }else if (accessPoint.getSignalLevel() == 2){
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_2);
        }else if (accessPoint.getSignalLevel() == 3){
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_3);
        }else {
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_1);
        }

        holder.itemView.setOnClickListener(v -> {
            if (itemClickListener != null) {
                itemClickListener.onClick(accessPoint);
            }
        });
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    static class WifiViewHolder extends RecyclerView.ViewHolder {

        TextView txtWifiName;
        ImageView imgPassword;
        ImageView imgRank;
        ImageView imgMore;
        TextView tvState;

        public WifiViewHolder(View itemView) {
            super(itemView);
            txtWifiName = itemView.findViewById(R.id.txt_wifi_name);
            imgPassword = itemView.findViewById(R.id.img_password);
            imgRank = itemView.findViewById(R.id.img_rank);
            imgMore = itemView.findViewById(R.id.img_more);
            tvState = itemView.findViewById(R.id.txt_state);
        }
    }
}