<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_have_update_width"
    android:layout_height="@dimen/dialog_have_update_height"
    android:layout_gravity="center"
    android:background="@drawable/bg_window">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_have_update_title_margin_top"
        android:gravity="center_vertical"
        android:text="@string/have_update"
        android:textColor="@color/black"
        android:textSize="@dimen/dialog_have_update_title_text_size" />

    <TextView
        android:id="@+id/txt_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_have_update_content_margin_top"
        android:gravity="start"
        android:text="@string/have_update_hint"
        android:textColor="@color/text_black_100"
        android:textSize="@dimen/dialog_have_update_content_text_size" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dialog_have_update_btn_cancel_margin_start"
        android:layout_marginBottom="@dimen/dialog_have_update_btn_cancel_margin_bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/SystemSetting_PopWindow_Host_Btn"
            android:layout_width="@dimen/shutdown_btn_confirm_width"
            android:layout_height="@dimen/shutdown_btn_confirm_height"
            android:background="@drawable/shape_shutdown_btn_white"
            android:text="@string/cancel"
            android:textColor="@color/text_black_100"
            android:textSize="@dimen/shutdown_btn_confirm_text_size" />

        <Button
            android:id="@+id/btn_reinstall"
            style="@style/SystemSetting_PopWindow_Host_Btn"
            android:layout_width="@dimen/shutdown_btn_confirm_width"
            android:layout_height="@dimen/shutdown_btn_confirm_height"
            android:layout_marginStart="@dimen/dialog_have_update_btn_cancel_margin_start"
            android:background="@drawable/shape_shutdown_btn_green"
            android:text="@string/right_update"
            android:textColor="@color/white"
            android:textSize="@dimen/shutdown_btn_confirm_text_size" />

    </LinearLayout>

</RelativeLayout>