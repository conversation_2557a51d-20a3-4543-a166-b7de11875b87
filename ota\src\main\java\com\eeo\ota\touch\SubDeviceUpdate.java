package com.eeo.ota.touch;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.util.Log;

import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.dialog.UpdateDialog;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;

import java.util.HashMap;

public class SubDeviceUpdate {
    public final static String TAG = "SubDeviceUpdate";
    /**
     * 众远触摸框对应id
     */
    public final static int VID_TOUCH_ZHONGYUAN = 8183;   //0x1FF7
    public final static int PID_TOUCH_ZHONGYUAN = 3890; //0x0F32 正常
    public final static int PID_TOUCH_ZHONGYUAN_UPDATE_FAIL = 3850; //0x0F0A 固件升级失败

    private Context mContext;
    private Touch mTouch;
    private int mTouchVendorId;

    private boolean mIsUpdateDialogShowing = false;

    /**
     * 是否有子设备更新失败
     */
    private boolean mHasUpdateFailed = false;

    /**
     * Only when the touch box upgrade is successful, it needs to be restarted.
     * Do not restart when it fails or does not require an upgrade
     */
    private boolean mShouldRebootAfterArrayMicService = false;

    /**
     * Array mic upgrade success flag
     */
    private boolean mArrayMicUpdateSuccess = false;

    private final SubDeviceUpdateCallback mSubDeviceUpdateCallback;
    private SubDeviceUpdateCallback mTouchUpdateCallback = new SubDeviceUpdateCallback() {
        @Override
        public void onUpdateSuccess() {
            Log.e(TAG, "onUpdateSuccess");
            if (mSubDeviceUpdateCallback != null) {
                mSubDeviceUpdateCallback.onUpdateSuccess();
            }
            checkAllSubDeviceUpdateStatus();
        }

        @Override
        public void onUpdateFail(String errMsg) {
            Log.e(TAG, "onUpdateFail: " + errMsg);
            mHasUpdateFailed = true;
            if (mSubDeviceUpdateCallback != null) {
                mSubDeviceUpdateCallback.onUpdateFail(errMsg);
            }
            checkAllSubDeviceUpdateStatus();
        }

        @Override
        public void onAllUpdateFinish() {
            Log.e(TAG, "onAllUpdateFinish: ");
        }

        @Override
        public void onUpdateProgressChanged(int progress) {
            Log.e(TAG, "onUpdateProgressChanged: " + progress);
            if (mSubDeviceUpdateCallback != null) {
                mSubDeviceUpdateCallback.onUpdateProgressChanged(progress);
            }
        }
    };

    public SubDeviceUpdate(Context context, SubDeviceUpdateCallback callback) {
        mContext = context;
        mSubDeviceUpdateCallback = callback;
        initTouch();
    }

    /**
     * 触摸框
     */
    private void initTouch() {
        //识别插入的触摸框厂家
        mTouchVendorId = getTouchVendorId(mContext);
        Log.d(TAG, "touch vid = " + mTouchVendorId);
        if (mTouchVendorId == VID_TOUCH_ZHONGYUAN) {
            mTouch = new TouchZY(mContext, mTouchUpdateCallback);
        }
        if (mTouch != null) {
            mTouch.parseVersionInfo();
        }
    }

    /**
     * 检测是否有新版本
     */
    public boolean checkUpdate() {
        return mTouch != null && mTouch.checkVersion();
    }

    /**
     * 检测并更新所有子设备
     */
    public boolean update() {
        boolean hasUpdate = false;
        //更新触摸框
        if (mTouch != null && mTouch.checkVersion()) {
            Log.d(TAG, "update touch");
            hasUpdate = true;
            showUpdatingDialog();
            mTouch.update();
        }
        return hasUpdate;
    }


    /**
     * 通过VID区分插入的触摸框的厂家
     */
    public static int getTouchVendorId(Context context) {
        UsbManager usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        HashMap<String, UsbDevice> deviceHashMap = usbManager.getDeviceList();
        for (UsbDevice usbDevice : deviceHashMap.values()) {
            Log.d(TAG, "VID=" + usbDevice.getVendorId() + "(" + (Integer.toHexString(usbDevice.getVendorId())) + ") ,PID="
                    + usbDevice.getProductId() + "(" + (Integer.toHexString(usbDevice.getProductId())) + ")");
            if (usbDevice.getVendorId() == VID_TOUCH_ZHONGYUAN) {
                return VID_TOUCH_ZHONGYUAN;
            }
        }
        return -1;
    }

    public void release() {
        if (mTouch != null) {
            mTouch.release();
        }
    }

    /**
     * 子设备更新结束之后check一下
     * 所有都结束了之后回调onAllUpdateFinish
     */
    private void checkAllSubDeviceUpdateStatus() {
        if (mTouch != null && mTouch.isUpdating()) {
            return;
        }
        //all sub devices update finish
        if (mSubDeviceUpdateCallback != null) {
            mSubDeviceUpdateCallback.onAllUpdateFinish();
        }
        mIsUpdateDialogShowing = false;
        boolean showSystemUpdateDialog = Util.shouldShowUpdateDialog(mContext);
        if (mHasUpdateFailed) {
            UpdateDialog.dismissUpdatingDialog();
            UpdateDialog.showUpdateFailDialog(mContext, showSystemUpdateDialog);
            // Touch upgrade failed, no need to restart
            mShouldRebootAfterArrayMicService = false;
            Log.d(TAG, "ArrayMicOTA: Touch update failed, no reboot needed");
        } else {
            // The touch box upgrade was successful and requires a restart, 
            //but it will be delayed until the array microphone service is completed
            //触摸框升级完后重启，不然书写加速枚举触摸框设备
            mShouldRebootAfterArrayMicService = true;
            Log.d(TAG, "ArrayMicOTA: Touch update success, reboot will be delayed until array mic service completion");
//            if (showSystemUpdateDialog) {
//                SharedPreferencesUtil.setShowUpdateDialog(mContext, false);
//            }
//            UpdateDialog.showUpdateSuccessDialog(mContext, showSystemUpdateDialog);
        }
    }

    private void showUpdatingDialog() {
        if (mIsUpdateDialogShowing) {
            return;
        }
        mIsUpdateDialogShowing = true;
        UpdateDialog.showUpdatingDialog(mContext);
    }

    /**
     * Get comprehensive reboot flag based on all upgrade results
     */
    public boolean shouldRebootAfterAllUpdates() {
        // Touch upgrade success OR array mic upgrade success = need reboot
        boolean shouldReboot = mShouldRebootAfterArrayMicService || mArrayMicUpdateSuccess;
        Log.d(TAG, "ArrayMicOTA: shouldRebootAfterAllUpdates - touch success: " + mShouldRebootAfterArrayMicService +
                   ", array mic success: " + mArrayMicUpdateSuccess + ", result: " + shouldReboot);
        return shouldReboot;
    }

    /**
     * Set array mic upgrade success flag
     */
    public void setArrayMicUpdateSuccess(boolean success) {
        mArrayMicUpdateSuccess = success;
        Log.d(TAG, "ArrayMicOTA: Array mic update success flag set to: " + success);
    }

    /**
     * Call this method after the array microphone service is completed,
     * and decide whether to restart according to the upgrade result of the touch box
     */
    public void executeRebootAfterArrayMicService() {
        if (mShouldRebootAfterArrayMicService) {
            Log.d(TAG, "ArrayMicOTA: Executing delayed reboot after array mic service completion");
            Util.reboot(mContext, true);
        } else {
            Log.d(TAG, "ArrayMicOTA: No reboot needed after array mic service completion");
        }
    }

    /**
     * 检查是否需要在阵列麦服务完成后重启
     * @return true if reboot is needed after array mic service completion
     */
    public boolean shouldRebootAfterArrayMicService() {
        return mShouldRebootAfterArrayMicService;
    }

}
