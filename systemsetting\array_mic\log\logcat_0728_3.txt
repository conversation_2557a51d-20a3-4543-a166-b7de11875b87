2025-07-28 13:59:01.857   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-28 13:59:01.857   859-859   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-07-28 13:59:01.892   859-1970  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-07-28 13:59:06.225   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-07-28 13:59:11.042   859-859   ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-07-28 13:59:11.042   859-859   ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-07-28 13:59:11.042   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-07-28 13:59:14.091   859-859   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-07-28 13:59:14.091   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-07-28 13:59:14.094   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-07-28 13:59:14.139   859-859   ArrayMicOTA             com.eeo.systemsetting                E  Failed to compare versions: QH303_QSOUND_20231110001 vs A013
                                                                                                    java.lang.NumberFormatException: For input string: "30320231110001"
                                                                                                    	at java.lang.Integer.parseInt(Integer.java:618)
                                                                                                    	at java.lang.Integer.parseInt(Integer.java:650)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater.isVersionLower(ArrayMicFirmwareUpdater.java:232)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater.checkVersionAndDecide(ArrayMicFirmwareUpdater.java:255)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater.executeNextStep(ArrayMicFirmwareUpdater.java:133)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater.lambda$executeNextStep$5$com-eeo-ota-arraymic-ArrayMicFirmwareUpdater(ArrayMicFirmwareUpdater.java:127)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater$$ExternalSyntheticLambda11.run(Unknown Source:2)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicFirmwareUpdater$7.run(ArrayMicFirmwareUpdater.java:393)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loop(Looper.java:223)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7680)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:952)
2025-07-28 13:59:14.140   859-859   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-07-28 13:59:14.140   859-859   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-07-28 13:59:14.140   859-859   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-07-28 13:59:14.140   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-07-28 13:59:14.142   859-2418  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh
2025-07-28 13:59:14.206   859-2420  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: qpid ==>1412
2025-07-28 13:59:14.214   859-2418  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh] finished with exit code: 0
2025-07-28 13:59:16.216   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-07-28 13:59:16.222   859-2434  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-07-28 13:59:16.265   859-2434  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /mnt/UDISK/*] finished with exit code: 0
2025-07-28 13:59:18.266   859-859   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-07-28 13:59:18.267   859-2441  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device push system/ota/QH303_V197_20240712.swu /mnt/UDISK/