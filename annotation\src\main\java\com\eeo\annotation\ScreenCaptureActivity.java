package com.eeo.annotation;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.ColorDrawable;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.eeo.annotation.bean.Constant;

/**
 * 纯Android的截屏才用到
 * 双系统的截屏用udi的
 */
public class ScreenCaptureActivity extends Activity {
    private final static String TAG = "ScreenCaptureActivity";
    //请求码
    private final static int REQUEST_CODE = 101;
    //权限请求码
    private final static int PERMISSION_REQUEST_CODE = 1101;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.e(TAG, "onCreate: ");
        //如下代码 只是想 启动一个透明的Activity 而上一个activity又不被pause
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
        getWindow().setDimAmount(0f);
        if (!Constant.CAPTURE_ENABLE || Constant.UDI_ENABLE) {
            startAnnotationService(-1, null);
        } else {
            checkPermission();
        }
    }

    //权限检查，连接截屏服务
    public void checkPermission() {
        //调用检查权限接口进行权限检查
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED)) {
            //如果没有权限，获取权限
            //调用请求权限接口进行权限申请
            ActivityCompat.requestPermissions(this, new String[]{
                    Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.RECORD_AUDIO}, PERMISSION_REQUEST_CODE);
        } else {
            requestScreenCapture();
        }
    }

    //没有权限，去请求权限后，需要判断用户是否同意权限请求
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            //请求码相同
            if (grantResults.length > 0 &&
                    (grantResults[0] != PackageManager.PERMISSION_GRANTED ||
                            grantResults[1] != PackageManager.PERMISSION_GRANTED)) {
                //如果结果都存在，但是至少一个没请求成功，弹出提示
                Toast.makeText(this, "请同意必须的应用权限，否则无法正常使用该功能！", Toast.LENGTH_SHORT).show();
            } else if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                //如果结果都存在，两个权限都申请成功，连接服务，启动截屏
                Toast.makeText(this, "权限申请成功，用户同意！", Toast.LENGTH_SHORT).show();
                requestScreenCapture();
            }
        }
    }

    private void requestScreenCapture() {
        Log.e(TAG, "requestScreenCapture: ");
        MediaProjectionManager mediaProjectionManager = (MediaProjectionManager) getSystemService(MEDIA_PROJECTION_SERVICE);
        //截屏请求
        Intent intent = mediaProjectionManager.createScreenCaptureIntent();
        startActivityForResult(intent, REQUEST_CODE);
    }

    @Override
    //返回方法，获取返回的信息
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //首先判断请求码是否一致，结果是否ok
        if (requestCode == REQUEST_CODE && resultCode == RESULT_OK) {
            startAnnotationService(resultCode, data);
        }
    }

    private void startAnnotationService(int resultCode, Intent data) {
        Intent startServiceIntent = new Intent(this, AnnotationService.class);
        startServiceIntent.putExtra(Constant.CAPTURE, true);
        if (resultCode != -1) {
            startServiceIntent.putExtra(Constant.RESULT_CODE, resultCode);
        }
        if (data != null) {
            startServiceIntent.putExtra(Constant.DATA, data);
        }
        startServiceIntent.putExtra(Constant.X, getIntent().getIntExtra(Constant.X, Constant.DEFAULT_X));
        startServiceIntent.putExtra(Constant.Y, getIntent().getIntExtra(Constant.Y, Constant.DEFAULT_Y));
        startService(startServiceIntent);
        finish();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.e(TAG, "onPause: ");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "onDestroy: ");
    }
}
