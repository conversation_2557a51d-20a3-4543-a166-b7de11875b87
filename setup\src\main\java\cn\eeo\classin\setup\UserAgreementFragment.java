package cn.eeo.classin.setup;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;

import com.elvishew.xlog.XLog;

import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;
import cn.eeo.classin.setup.base.BaseFragment;

public class UserAgreementFragment extends BaseFragment implements View.OnClickListener{
    @BindView(R.id.back_ic)
    ImageView iv_back;
    @BindView(R.id.btnConfirm)
    Button bt_confirm;
    @BindView(R.id.user_agreement_txt)
    NestedScrollView nestedScrollView;
    @BindView(R.id.webView)
    WebView webView;

    private NavController navController;
    private final String TAG = "UserAgreementFragment";

    public UserAgreementFragment() {
        // Required empty public constructor
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public int getLayout() {
        XLog.d("getLayout");
        return R.layout.fragment_user_agreement;
    }

    @Override
    public void initDate() {
        XLog.d("initDate");
        nestedScrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                // 判断是否滑动到底部
                if (isNestedScrollViewAtBottom(v)) {
                    // 滑动到底部，使按钮可点击并更改颜色
                    XLog.d("滑到底部");
                    bt_confirm.setEnabled(true);
                } else {
                    // 未滑动到底部，禁用按钮并使用不可点击颜色
                    XLog.d("没有滑动到底部");
                }
            }
        });
        webView.setBackgroundColor(0);
        WebSettings webSettings = webView.getSettings();

        // 设置支持缩放
        //webSettings.setSupportZoom(true);
        //webSettings.setBuiltInZoomControls(true);
        String htmlContent = "<html><head><style>" +
                "body {" +
                "   color: #FFFFFF; /* 设置文字颜色为白色 */" +
                "   font-size: 14dp; /* 设置字体大小为9sp */" +
                "}" +
                "</style></head></html>";
        webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null);
        // 设置初始缩放级别
        webView.setInitialScale(100); // 设置为50%，可以根据需要调整
        Log.d(TAG,"Locale.getDefault():"+Locale.getDefault());
        if (Locale.CHINA.equals(Locale.getDefault())){
            Log.d(TAG,"user_agreement");
            webView.loadUrl("file:///android_asset/user_agreement.html");
        }else {
            Log.d(TAG,"user_agreement_english");
            webView.loadUrl("file:///android_asset/user_agreement_english.html");
        }

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        XLog.d("onViewCreated");
        navController = Navigation.findNavController(view);

    }

    @OnClick({R.id.back_ic,R.id.btnConfirm})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.back_ic:
                if (navController!=null){
                    navController.navigateUp();
                }else {
                    XLog.d("navController is null");
                }
                break;
            case R.id.btnConfirm:
                NavHostFragment.findNavController(UserAgreementFragment.this)
                        .navigate(R.id.action_UserAgreementFragment_to_UserGuideAnimationFragment);
                break;
            default:
                break;
        }

    }
    // 判断NestedScrollView是否滑动到底部
    private boolean isNestedScrollViewAtBottom(NestedScrollView scrollView) {
        return scrollView.getChildAt(0).getBottom() <= (scrollView.getHeight() + scrollView.getScrollY());
    }

}