// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WiFiFragment_ViewBinding implements Unbinder {
  private WiFiFragment target;

  @UiThread
  public WiFiFragment_ViewBinding(WiFiFragment target, View source) {
    this.target = target;

    target.rvWiFiConnected = Utils.findRequiredViewAsType(source, R.id.rv_connected, "field 'rvWiFiConnected'", RecyclerView.class);
    target.rvWifiList = Utils.findRequiredViewAsType(source, R.id.rv_wifi_list, "field 'rvWifiList'", RecyclerView.class);
    target.swNetwork = Utils.findRequiredViewAsType(source, R.id.sw_network, "field 'swNetwork'", Switch.class);
    target.progressbarCheckUpdate = Utils.findRequiredViewAsType(source, R.id.progressbar_check_update, "field 'progressbarCheckUpdate'", ProgressBar.class);
    target.line = Utils.findRequiredView(source, R.id.line1, "field 'line'");
    target.txtOtherNetwork = Utils.findRequiredViewAsType(source, R.id.txt_other_network, "field 'txtOtherNetwork'", TextView.class);
    target.llNetwork = Utils.findRequiredViewAsType(source, R.id.ll_network, "field 'llNetwork'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    WiFiFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.rvWiFiConnected = null;
    target.rvWifiList = null;
    target.swNetwork = null;
    target.progressbarCheckUpdate = null;
    target.line = null;
    target.txtOtherNetwork = null;
    target.llNetwork = null;
  }
}
