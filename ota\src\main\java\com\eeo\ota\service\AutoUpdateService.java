package com.eeo.ota.service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import com.eeo.ota.Ota;
import com.eeo.ota.bean.VersionInfo;
import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;
import com.eeo.ota.callback.InstallListener;
import com.eeo.ota.util.AutoUpdateUtil;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;

import java.util.Calendar;
import java.util.Locale;


public class AutoUpdateService extends Service {
    public static final String TAG = "ota-AutoUpdateService";
    private static final String CHANNEL_ID = "eeo_ota_AutoUpdateService_channel_id";

    private Ota mOta;

    private AlarmManager mAlarmManager;
    private Calendar mCheckUpdateCalendar;
    private PendingIntent mCheckUpdatePendingIntent;

    /**
     * 无网络时隔一分钟重试
     * 最多5次
     */
    private int mRetryTimes = 0;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel mChannel = new NotificationChannel(CHANNEL_ID, "OtaChannel", NotificationManager.IMPORTANCE_NONE);
            notificationManager.createNotificationChannel(mChannel);
            Notification notification = new Notification.Builder(this, CHANNEL_ID).build();
            startForeground(1, notification);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand");
        if (!SharedPreferencesUtil.getIsAutoUpdate(this)) {
            //没有打开自动更新开关
            stopSelf();
            return super.onStartCommand(intent, flags, startId);
        }
        if (!Util.isNetworkConnect(this)) {
            //无网络1分钟后重试
            setNextAlarm(true);
            stopSelf();
            return super.onStartCommand(intent, flags, startId);
        }
        setNextAlarm(false);
        if (AutoUpdateUtil.isNoon() || AutoUpdateUtil.isNight()) {
            mOta = Ota.getInstance(this);
            checkVersion();
        } else {
            stopSelf();
        }
        return super.onStartCommand(intent, flags, startId);
    }

    /**
     * 12:10~13:30 仅下载
     * 02:00~6:00 下载 +安装
     * 无网络的1min后重试
     * 最多重试5次
     */
    private void setNextAlarm(boolean isRetry) {
        if (mAlarmManager == null) {
            mAlarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        }
        mCheckUpdateCalendar = Calendar.getInstance(Locale.CHINA);
        mRetryTimes = SharedPreferencesUtil.getAutoUpdateRetryTimes(this);
        if (isRetry && mRetryTimes < 5) {
            //1分钟之后
            mCheckUpdateCalendar.add(Calendar.MINUTE, 1);
            Log.d(TAG, "setNextAlarm: " + mCheckUpdateCalendar.getTime() + ",mRetryTimes=" + mRetryTimes);
            mRetryTimes++;
        } else {
            int hour = mCheckUpdateCalendar.get(Calendar.HOUR_OF_DAY);
            int min = mCheckUpdateCalendar.get(Calendar.MINUTE);
            if (hour < 2) {
                //2:00
                mCheckUpdateCalendar.set(Calendar.HOUR_OF_DAY, 2);
                mCheckUpdateCalendar.set(Calendar.MINUTE, 0);
            } else if (hour < 12 || (hour == 12 && min < 10)) {
                //12:10
                mCheckUpdateCalendar.set(Calendar.HOUR_OF_DAY, 12);
                mCheckUpdateCalendar.set(Calendar.MINUTE, 10);
            } else {
                //第二天的2:00
                mCheckUpdateCalendar.add(Calendar.DAY_OF_YEAR, 1);
                mCheckUpdateCalendar.set(Calendar.HOUR_OF_DAY, 2);
                mCheckUpdateCalendar.set(Calendar.MINUTE, 0);
            }
            mCheckUpdateCalendar.set(Calendar.SECOND, 0);
            mCheckUpdateCalendar.set(Calendar.MILLISECOND, 0);
            Log.d(TAG, "setNextAlarm: " + mCheckUpdateCalendar.getTime());
            mRetryTimes = 0;
        }
        SharedPreferencesUtil.setAutoUpdateRetryTimes(this, mRetryTimes);
        Intent intent = new Intent("com.eeo.ota.action.AutoUpdateService");
        intent.setPackage(getPackageName());
        mCheckUpdatePendingIntent = PendingIntent.getService(this, 0, intent, PendingIntent.FLAG_CANCEL_CURRENT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mAlarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, mCheckUpdateCalendar.getTimeInMillis(), mCheckUpdatePendingIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            mAlarmManager.setExact(AlarmManager.RTC_WAKEUP, mCheckUpdateCalendar.getTimeInMillis(), mCheckUpdatePendingIntent);
        } else {
            mAlarmManager.set(AlarmManager.RTC_WAKEUP, mCheckUpdateCalendar.getTimeInMillis(), mCheckUpdatePendingIntent);
        }
    }

    /**
     * 检测更新
     */
    private void checkVersion() {
        Log.d(TAG, "checkVersion");
        mOta.checkVersion(new CheckVersionCallback() {
            @Override
            public void onCheckSuccess(VersionInfo versionInfo) {
                Log.d(TAG, "onCheckSuccess:" + versionInfo.toString());
                download();
            }

            @Override
            public void onCheckFail(int errCode, String reason) {
                Log.e(TAG, "onCheckFail:errCode=" + errCode + ",reason=" + reason);
                stopSelf();
            }
        });
    }

    private void download() {
        Log.d(TAG, "download");
        mOta.download(new DownloadListener() {
            @Override
            public void onDownloadProgress(int progress) {
                Log.d(TAG, "onDownloadProgress:" + progress);
            }

            @Override
            public void onDownloadCompleted(String outputFile) {
                Log.e(TAG, "onDownloadCompleted:" + outputFile);
                if (AutoUpdateUtil.isNight()) {
                    update();
                }
            }

            @Override
            public void onDownloadFailure(int errCode) {
                Log.e(TAG, "onDownloadFailure:" + errCode);
                stopSelf();
            }
        });
    }

    private void stopDownload() {
        mOta.stopDownload();
    }

    private void update() {
        Log.d(TAG, "update");
        mOta.installPackage(new InstallListener() {
            @Override
            public void onInstallSuccess() {
                Log.d(TAG, "onInstallSuccess");
                //成功之后直接重启升级了，这里不做处理
            }

            @Override
            public void onInstallFail(String errMsg) {
                Log.e(TAG, "onInstallFail:" + errMsg);
                stopSelf();
            }

            @Override
            public void onInstallProgress(float progress) {
                Log.d(TAG, "onInstallProgress: " + progress);
            }
        });
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        super.onDestroy();
    }
}
