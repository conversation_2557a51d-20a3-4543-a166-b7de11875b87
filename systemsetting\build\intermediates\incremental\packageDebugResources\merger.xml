<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res"/><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\build\generated\res\rs\debug"/><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res"><file name="adb_btn_press_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\adb_btn_press_bg.xml" qualifiers="" type="color"/><file name="btn_press_bg_green" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\btn_press_bg_green.xml" qualifiers="" type="color"/><file name="btn_press_bg_white" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\btn_press_bg_white.xml" qualifiers="" type="color"/><file name="color_network_edit" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\color_network_edit.xml" qualifiers="" type="color"/><file name="main_screen_text_color" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\main_screen_text_color.xml" qualifiers="" type="color"/><file name="main_shutdown_text_color" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\main_shutdown_text_color.xml" qualifiers="" type="color"/><file name="main_text_color" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\main_text_color.xml" qualifiers="" type="color"/><file name="text_enable_green" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\text_enable_green.xml" qualifiers="" type="color"/><file name="text_enable_white" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\text_enable_white.xml" qualifiers="" type="color"/><file name="text_press_green_black" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\color\text_press_green_black.xml" qualifiers="" type="color"/><file name="annotate" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\annotate.xml" qualifiers="" type="drawable"/><file name="bg_adb_item" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\bg_adb_item.xml" qualifiers="" type="drawable"/><file name="bg_projection_shortcut" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\bg_projection_shortcut.xml" qualifiers="" type="drawable"/><file name="bg_seek_bar" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\bg_seek_bar.xml" qualifiers="" type="drawable"/><file name="bg_window" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\bg_window.xml" qualifiers="" type="drawable"/><file name="btn_un_click_green" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\btn_un_click_green.xml" qualifiers="" type="drawable"/><file name="hint_no_control_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\hint_no_control_bg.xml" qualifiers="" type="drawable"/><file name="home" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\home.xml" qualifiers="" type="drawable"/><file name="icon_wifi_signal_lock_level" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\icon_wifi_signal_lock_level.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="network_btn_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\network_btn_bg.xml" qualifiers="" type="drawable"/><file name="network_edit_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\network_edit_bg.xml" qualifiers="" type="drawable"/><file name="network_switch_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\network_switch_bg.xml" qualifiers="" type="drawable"/><file name="progress_indeterminate_horizontal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_indeterminate_horizontal.xml" qualifiers="" type="drawable"/><file name="progress_projection_shape" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_projection_shape.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape1.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape10" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape10.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape2" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape2.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape3" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape3.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape4" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape4.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape5" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape5.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape6" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape6.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape7" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape7.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape8" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape8.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape9" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape9.xml" qualifiers="" type="drawable"/><file name="progress_vertical_gradient_simple_shape_rgb" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\progress_vertical_gradient_simple_shape_rgb.xml" qualifiers="" type="drawable"/><file name="rgb_edit_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\rgb_edit_bg.xml" qualifiers="" type="drawable"/><file name="screen" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\screen.xml" qualifiers="" type="drawable"/><file name="select_about_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_about_icon.xml" qualifiers="" type="drawable"/><file name="select_check_update_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_check_update_icon.xml" qualifiers="" type="drawable"/><file name="select_close_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_close_icon.xml" qualifiers="" type="drawable"/><file name="select_disconnect_network_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_disconnect_network_icon.xml" qualifiers="" type="drawable"/><file name="select_down_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_down_icon.xml" qualifiers="" type="drawable"/><file name="select_extra_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_extra_icon.xml" qualifiers="" type="drawable"/><file name="select_forget_network_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_forget_network_icon.xml" qualifiers="" type="drawable"/><file name="select_left_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_left_icon.xml" qualifiers="" type="drawable"/><file name="select_locale_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_locale_icon.xml" qualifiers="" type="drawable"/><file name="select_main_eye_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_eye_icon.xml" qualifiers="" type="drawable"/><file name="select_main_help_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_help_icon.xml" qualifiers="" type="drawable"/><file name="select_main_lock_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_lock_icon.xml" qualifiers="" type="drawable"/><file name="select_main_reset_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_reset_icon.xml" qualifiers="" type="drawable"/><file name="select_main_restart_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_restart_icon.xml" qualifiers="" type="drawable"/><file name="select_main_screen_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_screen_icon.xml" qualifiers="" type="drawable"/><file name="select_main_setting_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_setting_icon.xml" qualifiers="" type="drawable"/><file name="select_main_shutdown_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_shutdown_icon.xml" qualifiers="" type="drawable"/><file name="select_main_sign_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_sign_icon.xml" qualifiers="" type="drawable"/><file name="select_main_write_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_write_icon.xml" qualifiers="" type="drawable"/><file name="select_network_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_network_icon.xml" qualifiers="" type="drawable"/><file name="select_rgb_left_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_rgb_left_icon.xml" qualifiers="" type="drawable"/><file name="select_right_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_right_icon.xml" qualifiers="" type="drawable"/><file name="select_signal_hdmi_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_signal_hdmi_icon.xml" qualifiers="" type="drawable"/><file name="select_signal_typec_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_signal_typec_icon.xml" qualifiers="" type="drawable"/><file name="select_signal_windows_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_signal_windows_icon.xml" qualifiers="" type="drawable"/><file name="select_up_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_up_icon.xml" qualifiers="" type="drawable"/><file name="select_wifi_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_wifi_icon.xml" qualifiers="" type="drawable"/><file name="select_wifi_img_press_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_wifi_img_press_icon.xml" qualifiers="" type="drawable"/><file name="select_wifi_item_press_color" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_wifi_item_press_color.xml" qualifiers="" type="drawable"/><file name="sel_shortcut" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\sel_shortcut.xml" qualifiers="" type="drawable"/><file name="shape_adb_btn" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_adb_btn.xml" qualifiers="" type="drawable"/><file name="shape_adb_dialog" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_adb_dialog.xml" qualifiers="" type="drawable"/><file name="shape_bg_lock" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_bg_lock.xml" qualifiers="" type="drawable"/><file name="shape_btn_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_btn_bg.xml" qualifiers="" type="drawable"/><file name="shape_main_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_main_bg.xml" qualifiers="" type="drawable"/><file name="shape_main_progress_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_main_progress_bg.xml" qualifiers="" type="drawable"/><file name="shape_main_unclick_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_main_unclick_bg.xml" qualifiers="" type="drawable"/><file name="shape_network_auto_manual" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_auto_manual.xml" qualifiers="" type="drawable"/><file name="shape_network_btn_click" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_btn_click.xml" qualifiers="" type="drawable"/><file name="shape_network_btn_unclick" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_btn_unclick.xml" qualifiers="" type="drawable"/><file name="shape_network_edit_focused" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_edit_focused.xml" qualifiers="" type="drawable"/><file name="shape_network_edit_normal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_edit_normal.xml" qualifiers="" type="drawable"/><file name="shape_network_edt_cursor" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_edt_cursor.xml" qualifiers="" type="drawable"/><file name="shape_network_scrollview" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_scrollview.xml" qualifiers="" type="drawable"/><file name="shape_network_toast_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_network_toast_bg.xml" qualifiers="" type="drawable"/><file name="shape_no_sign_btn_green" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_no_sign_btn_green.xml" qualifiers="" type="drawable"/><file name="shape_no_sign_btn_white" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_no_sign_btn_white.xml" qualifiers="" type="drawable"/><file name="shape_rgb_edit_focused" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_rgb_edit_focused.xml" qualifiers="" type="drawable"/><file name="shape_rgb_edit_normal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_rgb_edit_normal.xml" qualifiers="" type="drawable"/><file name="shape_screen_dialog" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_screen_dialog.xml" qualifiers="" type="drawable"/><file name="shape_screen_dialog_wireless_disabled" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_screen_dialog_wireless_disabled.xml" qualifiers="" type="drawable"/><file name="shape_shadow_progress" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_shadow_progress.xml" qualifiers="" type="drawable"/><file name="shape_shutdown_btn_green" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_shutdown_btn_green.xml" qualifiers="" type="drawable"/><file name="shape_shutdown_btn_white" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_shutdown_btn_white.xml" qualifiers="" type="drawable"/><file name="shape_signal_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_signal_bg.xml" qualifiers="" type="drawable"/><file name="shape_tvview_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_tvview_bg.xml" qualifiers="" type="drawable"/><file name="shape_windows_host_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\shape_windows_host_bg.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="sw_adb_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\sw_adb_bg.xml" qualifiers="" type="drawable"/><file name="toast_resolution_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\toast_resolution_bg.xml" qualifiers="" type="drawable"/><file name="tv_text_color" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\tv_text_color.xml" qualifiers="" type="drawable"/><file name="wifi_edt_password" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\wifi_edt_password.xml" qualifiers="" type="drawable"/><file name="guide_network_error" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-en-nodpi\guide_network_error.png" qualifiers="en-nodpi-v4" type="drawable"/><file name="bg_no_signal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\bg_no_signal.png" qualifiers="nodpi-v4" type="drawable"/><file name="bg_projection" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\bg_projection.jpg" qualifiers="nodpi-v4" type="drawable"/><file name="brightness" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\brightness.png" qualifiers="nodpi-v4" type="drawable"/><file name="bt_switch_off" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\bt_switch_off.png" qualifiers="nodpi-v4" type="drawable"/><file name="bt_switch_open" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\bt_switch_open.png" qualifiers="nodpi-v4" type="drawable"/><file name="close" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\close.png" qualifiers="nodpi-v4" type="drawable"/><file name="guide_network_error" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\guide_network_error.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_down" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_down.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_down_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_down_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_left" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_left.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_left_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_left_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_left_rgb" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_left_rgb.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_right" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_right.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_right_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_right_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_right_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_right_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_up" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_up.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_arrow_up_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_arrow_up_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_bright" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_bright.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_bright_01" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_bright_01.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_bright_02" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_bright_02.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_bright_03" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_bright_03.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_close" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_close.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_close_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_close_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_eye_off" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_eye_off.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_eye_on" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_eye_on.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_failure" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_failure.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_help_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_help_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_help_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_help_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_help_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_help_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_home_s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_home_s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_home_s_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_home_s_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_more_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_more_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_more_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_more_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_no_signal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_no_signal.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_password" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_password.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_restart_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_restart_bg.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_restart_disable" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_restart_disable.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_restart_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_restart_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_restart_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_restart_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_resting_screen_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_resting_screen_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_resting_screen_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_resting_screen_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_resting_screen_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_resting_screen_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_h1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_h1.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_screen_image_s_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_screen_image_s_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_shutdown_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_shutdown_bg.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_shutdown_disable" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_shutdown_disable.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_shutdown_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_shutdown_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_shutdown_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_shutdown_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_hdmi_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_hdmi_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_hdmi_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_hdmi_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_type_c_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_type_c_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_type_c_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_type_c_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_win_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_win_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_signal_win_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_signal_win_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_status_wrong" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_status_wrong.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_succeed" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_succeed.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_00" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_00.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_00s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_00s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_01" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_01.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_01s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_01s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_02" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_02.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_02s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_02s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_03" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_03.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_voice_03s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_voice_03s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_warn" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_warn.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_wifi_1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_wifi_1.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_wifi_2" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_wifi_2.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_wifi_3" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_wifi_3.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_write_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_write_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_write_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_write_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_write_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_write_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_write_s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_write_s.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_write_s_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_write_s_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="privacy" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\privacy.png" qualifiers="nodpi-v4" type="drawable"/><file name="ruler" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ruler.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_1.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_2" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_2.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_3" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_3.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_4" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_4.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_5" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_5.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_6" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_6.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_7" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_7.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_8" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_8.png" qualifiers="nodpi-v4" type="drawable"/><file name="searching_animation" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\searching_animation.xml" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_about_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_about_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_about_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_about_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_checkbox" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_checkbox.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_extra_set_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_extra_set_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_extra_set_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_extra_set_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_eye_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_eye_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_eye_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_eye_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_eye_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_eye_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_locale_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_locale_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_locale_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_locale_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_lock_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_lock_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_lock_s" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_lock_s.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_network_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_network_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_network_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_network_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_setting_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_setting_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_setting_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_setting_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_setting_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_setting_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_signal_d" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_signal_d.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_signal_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_signal_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_signal_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_signal_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_update_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_update_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_update_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_update_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_add" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_add.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_add_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_add_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_delet" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_delet.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_delet_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_delet_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_disconnect" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_disconnect.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_disconnect_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_disconnect_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="set_ic_wifi_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\set_ic_wifi_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="shutdown" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\shutdown.png" qualifiers="nodpi-v4" type="drawable"/><file name="step_1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\step_1.png" qualifiers="nodpi-v4" type="drawable"/><file name="step_2" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\step_2.png" qualifiers="nodpi-v4" type="drawable"/><file name="step_3" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\step_3.png" qualifiers="nodpi-v4" type="drawable"/><file name="sw_adb_off" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\sw_adb_off.png" qualifiers="nodpi-v4" type="drawable"/><file name="sw_adb_on" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\sw_adb_on.png" qualifiers="nodpi-v4" type="drawable"/><file name="thumb_rgb" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\thumb_rgb.png" qualifiers="nodpi-v4" type="drawable"/><file name="transcreen_app" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\transcreen_app.png" qualifiers="nodpi-v4" type="drawable"/><file name="uhd_hdmi" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\uhd_hdmi.png" qualifiers="nodpi-v4" type="drawable"/><file name="uhd_type_c" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\uhd_type_c.png" qualifiers="nodpi-v4" type="drawable"/><file name="volume_0" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\volume_0.png" qualifiers="nodpi-v4" type="drawable"/><file name="volume_1" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\volume_1.png" qualifiers="nodpi-v4" type="drawable"/><file name="volume_2" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\volume_2.png" qualifiers="nodpi-v4" type="drawable"/><file name="volume_3" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\volume_3.png" qualifiers="nodpi-v4" type="drawable"/><file name="window_arror" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\window_arror.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_blur" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_blur.xml" qualifiers="" type="layout"/><file name="activity_hardware_self_test" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_hardware_self_test.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_privacy" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_privacy.xml" qualifiers="" type="layout"/><file name="activity_restart" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_restart.xml" qualifiers="" type="layout"/><file name="activity_setting" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_setting.xml" qualifiers="" type="layout"/><file name="activity_shutdown" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_shutdown.xml" qualifiers="" type="layout"/><file name="activity_signal" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_signal.xml" qualifiers="" type="layout"/><file name="dialog_adb" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_adb.xml" qualifiers="" type="layout"/><file name="dialog_factory_reset" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_factory_reset.xml" qualifiers="" type="layout"/><file name="dialog_have_update" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_have_update.xml" qualifiers="" type="layout"/><file name="dialog_installing" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_installing.xml" qualifiers="" type="layout"/><file name="dialog_install_fail" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_install_fail.xml" qualifiers="" type="layout"/><file name="dialog_network_auto_manual" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_network_auto_manual.xml" qualifiers="" type="layout"/><file name="dialog_reset_ops" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_reset_ops.xml" qualifiers="" type="layout"/><file name="dialog_restart" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_restart.xml" qualifiers="" type="layout"/><file name="dialog_rgb" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_rgb.xml" qualifiers="" type="layout"/><file name="dialog_shutdown" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_shutdown.xml" qualifiers="" type="layout"/><file name="dialog_shutdown_countdown" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_shutdown_countdown.xml" qualifiers="" type="layout"/><file name="dialog_systemsetting_window_host" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_systemsetting_window_host.xml" qualifiers="" type="layout"/><file name="dialog_touch_lock" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_touch_lock.xml" qualifiers="" type="layout"/><file name="dialog_volume" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\dialog_volume.xml" qualifiers="" type="layout"/><file name="fragment_about" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_about.xml" qualifiers="" type="layout"/><file name="fragment_extra" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_extra.xml" qualifiers="" type="layout"/><file name="fragment_locale" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_locale.xml" qualifiers="" type="layout"/><file name="fragment_network" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_network.xml" qualifiers="" type="layout"/><file name="fragment_update" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_update.xml" qualifiers="" type="layout"/><file name="fragment_wifi" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\fragment_wifi.xml" qualifiers="" type="layout"/><file name="item_wifi" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\item_wifi.xml" qualifiers="" type="layout"/><file name="item_wifi_connect" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\item_wifi_connect.xml" qualifiers="" type="layout"/><file name="item_wifi_more" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\item_wifi_more.xml" qualifiers="" type="layout"/><file name="launcher_main" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\launcher_main.xml" qualifiers="" type="layout"/><file name="screen_dialog" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\screen_dialog.xml" qualifiers="" type="layout"/><file name="screen_offset_view" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\screen_offset_view.xml" qualifiers="" type="layout"/><file name="small_window_shortcut" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\small_window_shortcut.xml" qualifiers="" type="layout"/><file name="spinner_dropdown_item" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\spinner_dropdown_item.xml" qualifiers="" type="layout"/><file name="spinner_item" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\spinner_item.xml" qualifiers="" type="layout"/><file name="toast_fail_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\toast_fail_bg.xml" qualifiers="" type="layout"/><file name="toast_resolution" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\toast_resolution.xml" qualifiers="" type="layout"/><file name="toast_shutting_down_ops" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\toast_shutting_down_ops.xml" qualifiers="" type="layout"/><file name="toast_success_bg" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\toast_success_bg.xml" qualifiers="" type="layout"/><file name="toast_uhd" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\toast_uhd.xml" qualifiers="" type="layout"/><file name="wifi_dialog_input_password" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\wifi_dialog_input_password.xml" qualifiers="" type="layout"/><file name="window_ruler" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\window_ruler.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF02BA8E</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="white_100">#FFFFFFFF</color><color name="white_95">#F2FFFFFF</color><color name="white_90">#E6FFFFFF</color><color name="white_85">#D9FFFFFF</color><color name="white_80">#CCFFFFFF</color><color name="white_75">#BFFFFFFF</color><color name="white_70">#B3FFFFFF</color><color name="white_65">#A6FFFFFF</color><color name="white_60">#99FFFFFF</color><color name="white_55">#8CFFFFFF</color><color name="white_50">#80FFFFFF</color><color name="white_45">#73FFFFFF</color><color name="white_40">#66FFFFFF</color><color name="white_35">#59FFFFFF</color><color name="white_30">#4DFFFFFF</color><color name="white_25">#40FFFFFF</color><color name="white_20">#33FFFFFF</color><color name="white_15">#26FFFFFF</color><color name="white_10">#1AFFFFFF</color><color name="white_5">#0DFFFFFF</color><color name="white_0">#00000000</color><color name="gray_100">#ffd8d8d8</color><color name="gray_90">#E62F2F2F</color><color name="gray_85">#d92f2f2f</color><color name="gray_80">#CC323232</color><color name="gray_60">#992f2f2f</color><color name="gray_50">#D3D1D1</color><color name="gray_10">#1A525D6D</color><color name="green_100">#FF009265</color><color name="black_100">#FF1C1F25</color><color name="black_80">#CC1C1F25</color><color name="black_70">#B31C1F25</color><color name="text_black_100">#FF525D6D</color><color name="text_lock">#FF666666</color><color name="main_line">#FFC4CCCE</color><color name="main_shadow_color">#4DA4AFC0</color><color name="shadow_color">#33525D6D</color><color name="shadow_color_middle">#4D525D6D</color><color name="press_color">#FF009265</color><color name="btn_default_bg_green">#FF009265</color><color name="btn_press_color_green">#FF007F58</color><color name="btn_dis_click_bg_green">#8002BA8E</color><color name="btn_default_bg_white">#FFFFFFFF</color><color name="btn_press_color_white">#FFDADADA</color><color name="btn_dis_click_bg_white">#FFDADEE4</color><color name="line1">#ffb2bdc0</color><color name="line2">#80b2bdc0</color><color name="line_50">#33525d6d</color><color name="black_check">#FF696969</color><color name="grey_10">#1A525D6D</color><color name="error_red">#FFED4747</color><color name="tv_text_normal">#FF525D6D</color><color name="tv_text_pressed">#FF02BA8E</color><color name="progress_bar_background">#FFCBCED3</color><color name="progress_bar_projection_background">#FFCBCED3</color><color name="progress_bar_rgb_background">#801C1F25</color><color name="scrollbar_track_background">#FFBBC8CC</color><color name="adb_btn_default">#991C1F25</color><color name="adb_btn_press">#FF1C1F25</color><color name="adb_bg">#FF525D6D</color><color name="adb_item_bg">#331C1F25</color><color name="text_70">#B31C1F25</color></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values\dimens.xml" qualifiers=""><dimen name="bg_radius">8dp</dimen><dimen name="main_cv_width">394dp</dimen><dimen name="main_cv_height">394dp</dimen><dimen name="main_width">384dp</dimen><dimen name="main_height">384dp</dimen><dimen name="main_bg_radius">16dp</dimen><dimen name="main_bg_stroke">0.5dp</dimen><dimen name="main_tv_title_margin_top">27dp</dimen><dimen name="sl_screen_width">114dp</dimen><dimen name="sl_screen_height">114dp</dimen><dimen name="sl_screen_margin_start">16dp</dimen><dimen name="sl_screen_margin_top">64dp</dimen><dimen name="sl_screen_corner_radius">8dp</dimen><dimen name="sl_screen_elevation">5dp</dimen><dimen name="sl_screen_shadow_limit">0dp</dimen><dimen name="sl_screen_shadow_offset_x">0dp</dimen><dimen name="sl_screen_shadow_offset_y">0dp</dimen><dimen name="sl_screen_stroke_width">0.2dp</dimen><dimen name="iv_screen_width">20dp</dimen><dimen name="iv_screen_height">20dp</dimen><dimen name="tv_screen_width">50dp</dimen><dimen name="tv_screen_height">13dp</dimen><dimen name="tv_screen_text_size">9sp</dimen><dimen name="iv_screen_margin_top">33dp</dimen><dimen name="tv_screen_margin_top">58dp</dimen><dimen name="tv_shutdown_width">50dp</dimen><dimen name="tv_setting_margin_top">25dp</dimen><dimen name="tv_screen_drawable_padding">5dp</dimen><dimen name="sl_write_margin_top">3dp</dimen><dimen name="sl_setting_width">234dp</dimen><dimen name="sl_setting_margin_start">7dp</dimen><dimen name="tv_setting_margin_start">9dp</dimen><dimen name="tv_signal_margin_start">61dp</dimen><dimen name="tv_touch_lock_width">80dp</dimen><dimen name="tv_touch_lock_margin_start">113dp</dimen><dimen name="tv_eye_margin_start">165dp</dimen><dimen name="iv_bright_width">20dp</dimen><dimen name="iv_bright_height">20dp</dimen><dimen name="iv_bright_margin_start">24dp</dimen><dimen name="iv_bright_margin_top">60dp</dimen><dimen name="iv_voice_margin_top">24dp</dimen><dimen name="sb_bright_width">151dp</dimen><dimen name="sb_bright_height">20dp</dimen><dimen name="sb_bright_max_height">40dp</dimen><dimen name="sb_bright_padding_start">0dp</dimen><dimen name="sb_bright_padding_end">0dp</dimen><dimen name="sb_bright_margin_start">49dp</dimen><dimen name="sb_bright_margin_top">60dp</dimen><dimen name="sb_voice_margin_top">24dp</dimen><dimen name="tv_rest_margin_start">15dp</dimen><dimen name="tv_restart_margin_start">44dp</dimen><dimen name="main_line1_height">0.3dp</dimen><dimen name="main_line1_margin_top">25dp</dimen><dimen name="ll_touch_lock_width">56dp</dimen><dimen name="ll_touch_lock_margin_bottom">37dp</dimen><dimen name="sl_lock_width">37dp</dimen><dimen name="sl_lock_height">37dp</dimen><dimen name="tv_lock_margin_top">5dp</dimen><dimen name="iv_lock_padding">9dp</dimen><dimen name="inter_touch_lock_width">220dp</dimen><dimen name="inter_touch_lock_height">98dp</dimen><dimen name="inter_touch_lock_margin_start">8dp</dimen><dimen name="inter_touch_lock_margin_top">8dp</dimen><dimen name="iv_back_margin_top">11dp</dimen><dimen name="iv_back_width">20dp</dimen><dimen name="iv_back_height">20dp</dimen><dimen name="iv_back_margin_start">13dp</dimen><dimen name="setting_tv_title_margin_top">13dp</dimen><dimen name="setting_line1_margin_top">11dp</dimen><dimen name="ll_select_width">120dp</dimen><dimen name="sl_network_height">29dp</dimen><dimen name="sl_network_margin_start">28dp</dimen><dimen name="sl_network_margin_top">21dp</dimen><dimen name="tv_network_margin_start">55dp</dimen><dimen name="tv_network_text_size">9sp</dimen><dimen name="sl_network_shadow_offset_x">0dp</dimen><dimen name="sl_network_shadow_offset_y">1dp</dimen><dimen name="sl_wifi_margin_top">11dp</dimen><dimen name="iv_shutdown_width">27dp</dimen><dimen name="iv_shutdown_height">27dp</dimen><dimen name="iv_shutdown_margin_top">134dp</dimen><dimen name="iv_shutdown_margin_left">103dp</dimen><dimen name="shutdown_tittle_text_size">12sp</dimen><dimen name="shutdown_tittle_margin_top">139dp</dimen><dimen name="shutdown_tittle_margin_left">11dp</dimen><dimen name="shutdown_content_text_size">9sp</dimen><dimen name="shutdown_content_margin_top">11dp</dimen><dimen name="shutdown_btn_confirm_width">88dp</dimen><dimen name="shutdown_btn_confirm_height">28dp</dimen><dimen name="shutdown_btn_confirm_text_size">11sp</dimen><dimen name="shutdown_btn_confirm_margin_top">37dp</dimen><dimen name="shutdown_btn_confirm_margin_left">203dp</dimen><dimen name="shutdown_btn_cancel_margin_left">93dp</dimen><dimen name="signal_tv_width">341dp</dimen><dimen name="signal_tv_height">48dp</dimen><dimen name="signal_tv_text_size">9sp</dimen><dimen name="signal_tv_drawable_padding">17dp</dimen><dimen name="signal_tv_margin_start">169dp</dimen><dimen name="signal_tv_window_margin_top">21dp</dimen><dimen name="signal_iv_margin_start">139dp</dimen><dimen name="signal_item_margin_top">11dp</dimen><dimen name="adb_width">264dp</dimen><dimen name="adb_height">384dp</dimen><dimen name="adb_scrollview_height">298dp</dimen><dimen name="adb_iv_back_width">25dp</dimen><dimen name="adb_iv_back_height">25dp</dimen><dimen name="adb_iv_back_margin_start">20dp</dimen><dimen name="adb_iv_back_margin_top">20dp</dimen><dimen name="adb_iv_close_margin_top">11dp</dimen><dimen name="adb_iv_close_margin_end">16dp</dimen><dimen name="adb_line_margin_top">42dp</dimen><dimen name="adb_item_width">232dp</dimen><dimen name="adb_item_height">29dp</dimen><dimen name="adb_item_radius">3dp</dimen><dimen name="adb_item_margin_top">3dp</dimen><dimen name="adb_item_adb_margin_top">13dp</dimen><dimen name="adb_item_margin_start">21dp</dimen><dimen name="adb_item_margin_end">16dp</dimen><dimen name="adb_sw_width">36dp</dimen><dimen name="adb_sw_height">18dp</dimen><dimen name="adb_sw_margin_end">13dp</dimen><dimen name="adb_iv_reset_width">20dp</dimen><dimen name="adb_iv_reset_height">20dp</dimen><dimen name="adb_iv_reset_margin_start">220dp</dimen><dimen name="adb_tv_margin_start">13dp</dimen><dimen name="adb_tv_text_size">9sp</dimen><dimen name="adb_btn_text_size">9sp</dimen><dimen name="adb_btn_width">70dp</dimen><dimen name="adb_btn_height">28dp</dimen><dimen name="adb_btn_margin_start">9dp</dimen><dimen name="adb_btn_margin_bottom">12dp</dimen><dimen name="rgb_tv_margin_start">16dp</dimen><dimen name="rgb_tv_color_temperature_margin_top">19dp</dimen><dimen name="rgb_sb_width">195dp</dimen><dimen name="rgb_sb_height">7dp</dimen><dimen name="rgb_sb_padding_start">7dp</dimen><dimen name="rgb_sb_padding_end">7dp</dimen><dimen name="rgb_sb_margin_start">9dp</dimen><dimen name="rgb_sb_thumb_height">23dp</dimen><dimen name="rgb_et_width">40dp</dimen><dimen name="rgb_et_height">20dp</dimen><dimen name="rgb_sb_margin_top">-4dp</dimen><dimen name="rgb_tv_color_temperature_cold_margin_top">-5dp</dimen><dimen name="rgb_line2_margin_top">25dp</dimen><dimen name="rgb_red_gain_margin_top">23dp</dimen><dimen name="rgb_green_gain_margin_top">24dp</dimen><dimen name="rgb_reset_margin_bottom">12dp</dimen><dimen name="rgb_btn_reset_width">232dp</dimen><dimen name="dialog_have_update_width">240dp</dimen><dimen name="dialog_have_update_height">148dp</dimen><dimen name="dialog_have_update_title_margin_top">19dp</dimen><dimen name="dialog_have_update_title_text_size">9sp</dimen><dimen name="dialog_have_update_content_text_size">9sp</dimen><dimen name="dialog_have_update_content_margin_top">8dp</dimen><dimen name="dialog_have_update_btn_cancel_margin_start">21dp</dimen><dimen name="dialog_have_update_btn_cancel_margin_bottom">21dp</dimen><dimen name="dialog_shutdown_countdown_content_margin_top">15dp</dimen><dimen name="dialog_shutdown_title_margin_top">23dp</dimen><dimen name="dialog_shutdown_content_margin_top">19dp</dimen><dimen name="dialog_factory_reset_title_margin_top">25dp</dimen><dimen name="dialog_factory_reset_iv_warn_width">20dp</dimen><dimen name="dialog_factory_reset_iv_warn_height">20dp</dimen><dimen name="dialog_factory_reset_content_margin_top">16dp</dimen><dimen name="dialog_factory_reset_title_margin_start">8dp</dimen><dimen name="dialog_factory_reset_btn_cancel_margin_top">24dp</dimen><dimen name="dialog_installing_pb_width">21dp</dimen><dimen name="dialog_installing_pb_height">21dp</dimen><dimen name="dialog_installing_tv_margin_start">13dp</dimen><dimen name="dialog_installing_text_size">16sp</dimen><dimen name="dialog_install_fail_tv_title_margin_top">32dp</dimen><dimen name="dialog_install_fail_tv_title_drawable_padding">8dp</dimen><dimen name="dialog_network_auto_manual_width">120dp</dimen><dimen name="dialog_network_auto_manual_height">59dp</dimen><dimen name="dialog_network_auto_manual_item_height">29dp</dimen><dimen name="dialog_network_auto_manual_iv_width">20dp</dimen><dimen name="dialog_network_auto_manual_iv_height">20dp</dimen><dimen name="dialog_network_auto_manual_iv_margin_start">29dp</dimen><dimen name="dialog_network_auto_manual_tv_margin_start">3dp</dimen><dimen name="dialog_network_auto_manual_tv_text_size">9sp</dimen><dimen name="dialog_network_auto_manual_line_margin_left">11dp</dimen><dimen name="dialog_network_auto_manual_line_margin_right">11dp</dimen><dimen name="fragment_about_scrollbar_size">4dp</dimen><dimen name="fragment_about_padding_bottom">5dp</dimen><dimen name="fragment_about_device_name_margin_top">29dp</dimen><dimen name="fragment_about_text_size">9sp</dimen><dimen name="fragment_about_title_width">80dp</dimen><dimen name="fragment_about_title_height">13dp</dimen><dimen name="fragment_about_title_margin_top">27dp</dimen><dimen name="fragment_about_title_margin_start">21dp</dimen><dimen name="fragment_about_content_width">160dp</dimen><dimen name="fragment_about_content_height">13dp</dimen><dimen name="fragment_about_line_height">0.3dp</dimen><dimen name="fragment_about_line_margin_start">21dp</dimen><dimen name="fragment_about_line_margin_top">33dp</dimen><dimen name="fragment_about_tv_system_version_margin_start">-19dp</dimen><dimen name="fragment_about_windows_host_width">120dp</dimen><dimen name="fragment_about_iv_windows_host_margin_start">81dp</dimen><dimen name="fragment_about_padding_view_height">52dp</dimen><dimen name="fragment_extra_wireless_screen_margin_top">26dp</dimen><dimen name="fragment_extra_item_margin_top">20dp</dimen><dimen name="fragment_extra_sw_width">40dp</dimen><dimen name="fragment_extra_sw_height">20dp</dimen><dimen name="fragment_extra_sw_margin_end">21dp</dimen><dimen name="fragment_extra_tv_margin_start">21dp</dimen><dimen name="fragment_extra_tv_text_size">9sp</dimen><dimen name="fragment_network_network_margin_top">26dp</dimen><dimen name="fragment_network_sw_margin_start">121dp</dimen><dimen name="fragment_network_text_size">9sp</dimen><dimen name="fragment_network_item_margin_top">23dp</dimen><dimen name="fragment_network_title_width">60dp</dimen><dimen name="fragment_network_title_margin_start">21dp</dimen><dimen name="fragment_network_content_width">160dp</dimen><dimen name="fragment_network_content_margin_start">1dp</dimen><dimen name="fragment_network_tv_ip_setting_width">140dp</dimen><dimen name="fragment_network_ll_ip_margin_top">15dp</dimen><dimen name="fragment_network_iv_ip_width">13dp</dimen><dimen name="fragment_network_iv_ip_height">13dp</dimen><dimen name="fragment_network_iv_ip_margin_start">13dp</dimen><dimen name="fragment_network_ll_mask_margin_top">11dp</dimen><dimen name="fragment_network_dns_title_width">60dp</dimen><dimen name="fragment_network_dns_content_margin_start">13dp</dimen><dimen name="fragment_network_btn_confirm_margin_start">29dp</dimen><dimen name="fragment_network_btn_confirm_margin_top">12dp</dimen><dimen name="fragment_network_sw_width">40dp</dimen><dimen name="fragment_network_sw_height">20dp</dimen><dimen name="fragment_network_et_width">140dp</dimen><dimen name="fragment_network_et_height">29dp</dimen><dimen name="fragment_network_et_text_size">9sp</dimen><dimen name="fragment_network_et_margin_start">8dp</dimen><dimen name="fragment_network_et_padding_end">7dp</dimen><dimen name="fragment_wifi_title_wifi_width">60dp</dimen><dimen name="fragment_wifi_sw_margin_start">121dp</dimen><dimen name="fragment_wifi_rv_connected_margin_top">10dp</dimen><dimen name="fragment_wifi_line1_margin_top">19dp</dimen><dimen name="fragment_wifi_line1_margin_start">0dp</dimen><dimen name="fragment_wifi_line1_margin_end">0dp</dimen><dimen name="fragment_wifi_ll_network_margin_start">21dp</dimen><dimen name="fragment_wifi_ll_network_margin_top">33dp</dimen><dimen name="fragment_wifi_ll_network_gone_margin_top">23dp</dimen><dimen name="fragment_wifi_tv_other_network_text_size">9sp</dimen><dimen name="fragment_wifi_pb_check_update_width">12dp</dimen><dimen name="fragment_wifi_pb_check_update_height">12dp</dimen><dimen name="fragment_wifi_pb_check_update_margin_start">8dp</dimen><dimen name="fragment_wifi_rv_wifi_list_margin_top">13dp</dimen><dimen name="fragment_update_line_margin_top">30dp</dimen><dimen name="fragment_update_tv_no_network_margin_start">21dp</dimen><dimen name="fragment_update_tv_no_network_margin_top">33dp</dimen><dimen name="fragment_update_tv_check_network_margin_top">27dp</dimen><dimen name="fragment_update_btn_retry_margin_top">19dp</dimen><dimen name="fragment_update_tv_version_margin_top">33dp</dimen><dimen name="fragment_update_pb_check_update_margin_top">77dp</dimen><dimen name="fragment_update_tv_checking_margin_top">11dp</dimen><dimen name="fragment_update_tv_version_text_size">9sp</dimen><dimen name="fragment_update_btn_right_update_margin_top">121dp</dimen><dimen name="fragment_update_btn_right_update_gone_margin_top">20dp</dimen><dimen name="fragment_update_btn_install_margin_top">43dp</dimen><dimen name="fragment_update_pb_download_margin_top">32dp</dimen><dimen name="fragment_update_pb_download_margin_end">21dp</dimen><dimen name="fragment_update_pb_horizontal_min_height">5dp</dimen><dimen name="fragment_update_pb_horizontal_max_height">5dp</dimen><dimen name="fragment_update_btn_cancel_margin_top">59dp</dimen><dimen name="fragment_update_tv_update_description_width">221dp</dimen><dimen name="fragment_update_tv_update_description_height">81dp</dimen><dimen name="fragment_update_tv_update_description_margin_top">13dp</dimen><dimen name="fragment_update_tv_update_description_max_height">80dp</dimen><dimen name="fragment_locale_tv_language_margin_top">26dp</dimen><dimen name="fragment_locale_iv_language_margin_top">26dp</dimen><dimen name="fragment_locale_iv_language_margin_end">21dp</dimen><dimen name="fragment_locale_tv_language_height">20dp</dimen><dimen name="fragment_locale_spinner_width">120dp</dimen><dimen name="item_wifi_height">41dp</dimen><dimen name="item_wifi_tv_wifi_height">26dp</dimen><dimen name="item_wifi_tv_wifi_margin_top">7dp</dimen><dimen name="item_wifi_tv_wifi_name_height">13dp</dimen><dimen name="item_wifi_tv_wifi_name_max_width">100dp</dimen><dimen name="item_wifi_tv_state_margin_top">1dp</dimen><dimen name="item_wifi_tv_state_text_size">8sp</dimen><dimen name="item_wifi_iv_rank_width">20dp</dimen><dimen name="item_wifi_iv_rank_height">20dp</dimen><dimen name="item_wifi_iv_rank_margin_end">21dp</dimen><dimen name="item_wifi_iv_password_margin_end">4dp</dimen><dimen name="item_wifi_line_margin_top">0dp</dimen><dimen name="item_wifi_connect_height">25dp</dimen><dimen name="item_wifi_connect_margin_top">18dp</dimen><dimen name="item_wifi_connect_iv_check_width">17dp</dimen><dimen name="item_wifi_connect_iv_check_height">17dp</dimen><dimen name="item_wifi_connect_iv_password_margin_start">182dp</dimen><dimen name="item_wifi_more_title_margin_start">107dp</dimen><dimen name="item_wifi_more_tv_save_margin_start">18dp</dimen><dimen name="item_wifi_more_tv_save_margin_top">14dp</dimen><dimen name="item_wifi_more_tv_save_margin_end">21dp</dimen><dimen name="item_wifi_more_tv_save_margin_bottom">18dp</dimen><dimen name="item_wifi_more_tv_save_text_size">9sp</dimen><dimen name="item_wifi_more_line_margin_top">14dp</dimen><dimen name="item_wifi_more_drawable_padding">8dp</dimen><dimen name="item_wifi_more_line1_margin_top">14dp</dimen><dimen name="item_wifi_more_item_margin_top">26dp</dimen><dimen name="item_wifi_more_tv_ip_setting_width">147dp</dimen><dimen name="item_wifi_more_item_margin_end">35dp</dimen><dimen name="item_wifi_more_iv_arrow_margin_top">23dp</dimen><dimen name="item_wifi_more_rl_manual_margin_top">18dp</dimen><dimen name="item_wifi_more_iv_manual_margin_end">15dp</dimen><dimen name="item_wifi_more_ip_address_margin_top">8dp</dimen><dimen name="item_wifi_more_et_address_margin_top">6dp</dimen><dimen name="item_wifi_more_iv_ip_marin_end">8dp</dimen><dimen name="item_wifi_more_et_subnet_mask_margin_top">11dp</dimen><dimen name="item_wifi_more_tv_dns_width">47dp</dimen><dimen name="item_wifi_more_fl_disconnect_margin_top">21dp</dimen><dimen name="item_wifi_more_tv_disconnect_height">29dp</dimen><dimen name="item_wifi_more_tv_disconnect_margin_start">49dp</dimen><dimen name="item_wifi_more_tv_disconnect_padding_left">21dp</dimen><dimen name="item_wifi_more_iv_disconnect_margin_start">21dp</dimen><dimen name="launcher_main_iv_privacy_width">27dp</dimen><dimen name="launcher_main_iv_privacy_height">27dp</dimen><dimen name="launcher_main_tv_privacy_margin_top">30dp</dimen><dimen name="launcher_main_tv_privacy_text_size">18sp</dimen><dimen name="launcher_main_tv_hint_width">160dp</dimen><dimen name="launcher_main_tv_hint_height">37dp</dimen><dimen name="launcher_main_tv_hint_text_size">9sp</dimen><dimen name="launcher_main_iv_no_signal_width">304dp</dimen><dimen name="launcher_main_iv_no_signal_height">304dp</dimen><dimen name="launcher_main_iv_no_signal_margin_top">202dp</dimen><dimen name="launcher_main_tv_no_signal_margin_top">467dp</dimen><dimen name="launcher_main_tv_no_signal_text_size">16sp</dimen><dimen name="launcher_main_tv_current_signal_margin_top">10dp</dimen><dimen name="launcher_main_tv_current_signal_text_size">12sp</dimen><dimen name="launcher_main_btn_start_width">160dp</dimen><dimen name="launcher_main_btn_start_height">40dp</dimen><dimen name="launcher_main_btn_start_margin_top">56dp</dimen><dimen name="launcher_main_tv_hotline_width">200dp</dimen><dimen name="launcher_main_tv_hotline_height">40dp</dimen><dimen name="launcher_main_tv_hotline_margin_top">16dp</dimen><dimen name="screen_dialog_cv_wireless_screen_height">318dp</dimen><dimen name="screen_dialog_rl_wireless_screen_height">308dp</dimen><dimen name="screen_dialog_iv_back_width">20dp</dimen><dimen name="screen_dialog_iv_back_height">20dp</dimen><dimen name="screen_dialog_iv_back_margin_start">18dp</dimen><dimen name="screen_dialog_iv_back_margin_top">23dp</dimen><dimen name="screen_dialog_tv_title_margin_top">25dp</dimen><dimen name="screen_dialog_title_text_size">12sp</dimen><dimen name="screen_dialog_content_text_size">9sp</dimen><dimen name="screen_dialog_tv_margin_start">21dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_width">40dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_height">20dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_margin_top">14dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_margin_end">23dp</dimen><dimen name="screen_dialog_line2_margin_top">8dp</dimen><dimen name="screen_dialog_rl_sub_wireless_screen_width">341dp</dimen><dimen name="screen_dialog_rl_sub_wireless_screen_margin_top">8dp</dimen><dimen name="screen_dialog_tv_enable_pin_code_margin_top">8dp</dimen><dimen name="screen_dialog_switch_enable_pin_code_margin_end">1dp</dimen><dimen name="screen_dialog_tv_order_width">21dp</dimen><dimen name="screen_dialog_tv_order_height">21dp</dimen><dimen name="screen_dialog_tv_one_margin_start">25dp</dimen><dimen name="screen_dialog_iv_step_1_width">15dp</dimen><dimen name="screen_dialog_iv_step_1_height">15dp</dimen><dimen name="screen_dialog_iv_step_1_margin_top">26dp</dimen><dimen name="screen_dialog_tv_step_1_height">13dp</dimen><dimen name="screen_dialog_tv_step_1_line_height">13dp</dimen><dimen name="screen_dialog_tv_step_1_margin_start">6dp</dimen><dimen name="screen_dialog_tv_step_1_margin_top">1dp</dimen><dimen name="screen_dialog_iv_step_2_margin_top">12dp</dimen><dimen name="screen_dialog_iv_step_3_margin_top">39dp</dimen><dimen name="screen_dialog_tv_pin_code_margin_top">4dp</dimen><dimen name="screen_dialog_iv_code_width">73dp</dimen><dimen name="screen_dialog_iv_code_height">73dp</dimen><dimen name="screen_dialog_iv_code_margin_top">26dp</dimen><dimen name="screen_dialog_iv_code_margin_end">2dp</dimen><dimen name="screen_dialog_tv_code_margin_top">5dp</dimen><dimen name="screen_dialog_tv_code_margin_end">-1dp</dimen><dimen name="screen_dialog_tv_code_text_size">8sp</dimen><dimen name="screen_dialog_tv_code_line_height">12dp</dimen><dimen name="screen_dialog_cv_wired_screen_margin_top">-1dp</dimen><dimen name="screen_dialog_cv_wired_screen_height">75dp</dimen><dimen name="screen_dialog_rl_wired_screen_height">65dp</dimen><dimen name="screen_dialog_rl_wired_screen_margin_top">11dp</dimen><dimen name="screen_dialog_rl_sub_wired_screen_margin_top">25dp</dimen><dimen name="screen_dialog_tv_signal_margin_top">11dp</dimen><dimen name="screen_offset_tv_text_size">12sp</dimen><dimen name="screen_offset_tv_margin_top">240dp</dimen><dimen name="screen_offset_tv_margin_horizontal">40dp</dimen><dimen name="screen_offset_tv_margin_bottom">10dp</dimen><dimen name="small_window_shortcut_width">427dp</dimen><dimen name="small_window_shortcut_height">29dp</dimen><dimen name="small_window_shortcut_iv_screen_margin_end">21dp</dimen><dimen name="small_window_shortcut_iv_screen_gone_margin_start">111dp</dimen><dimen name="small_window_iv_home_width">13dp</dimen><dimen name="small_window_iv_home_height">13dp</dimen><dimen name="small_window_iv_volume_width">15dp</dimen><dimen name="small_window_iv_volume_height">15dp</dimen><dimen name="small_window_iv_volume_margin_end">5dp</dimen><dimen name="small_window_sv_volume_width">157dp</dimen><dimen name="small_window_sv_volume_height">13dp</dimen><dimen name="small_window_sv_volume_margin_end">11dp</dimen><dimen name="small_window_sv_volume_padding_start">0dp</dimen><dimen name="small_window_sv_volume_padding_end">0dp</dimen><dimen name="small_window_shortcut_text_size">9sp</dimen><dimen name="small_window_shortcut_drawable_padding">3dp</dimen><dimen name="small_window_tv_home_margin_start">16dp</dimen><dimen name="dialog_volume_width">250dp</dimen><dimen name="dialog_volume_height">74dp</dimen><dimen name="dialog_volume_margin_end">80dp</dimen><dimen name="dialog_volume_margin_bottom">228dp</dimen><dimen name="dialog_volume_iv_margin_start">21dp</dimen><dimen name="dialog_volume_sb_margin_start">46dp</dimen><dimen name="dialog_volume_tv_text_size">11sp</dimen><dimen name="toast_width">160dp</dimen><dimen name="toast_height">53dp</dimen><dimen name="toast_padding_horizontal">30dp</dimen><dimen name="toast_padding_vertical">19dp</dimen><dimen name="toast_msg_margin_start">8dp</dimen><dimen name="toast_msg_text_size">12sp</dimen><dimen name="wifi_dialog_input_password_iv_wifi_margin_start">21dp</dimen><dimen name="wifi_dialog_input_password_iv_wifi_margin_top">21dp</dimen><dimen name="wifi_dialog_input_password_title_margin_start">8dp</dimen><dimen name="wifi_dialog_input_password_title_margin_top">25dp</dimen><dimen name="wifi_dialog_input_password_title_text_size">9sp</dimen><dimen name="wifi_dialog_input_password_title_wifi_name_max_width">100dp</dimen><dimen name="wifi_dialog_input_password_title_wifi_name_max_height">13dp</dimen><dimen name="wifi_dialog_input_password_et_password_width">197dp</dimen><dimen name="wifi_dialog_input_password_et_password_height">29dp</dimen><dimen name="wifi_dialog_input_password_et_password_margin_top">11dp</dimen><dimen name="wifi_dialog_input_password_et_password_padding_start">8dp</dimen><dimen name="wifi_dialog_input_password_et_password_padding_end">30dp</dimen><dimen name="wifi_dialog_input_password_et_password_text_size">9sp</dimen><dimen name="wifi_dialog_input_password_iv_eye_width">20dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_height">20dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_margin_top">15dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_margin_start">191dp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_text_size">8sp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_margin_start">24dp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_margin_top">8dp</dimen><dimen name="wifi_dialog_input_password_btn_margin_top">20dp</dimen><dimen name="window_ruler_iv_ruler_width">572dp</dimen><dimen name="window_ruler_iv_ruler_height">572dp</dimen><dimen name="spinner_dropdown_item_tv_margin_start">3dp</dimen><dimen name="spinner_dropdown_item_iv_margin_vertical">5dp</dimen><dimen name="privacy_width">337dp</dimen><dimen name="privacy_height">294dp</dimen><dimen name="privacy_margin_top">17dp</dimen><dimen name="toast_resolution_width">83dp</dimen><dimen name="toast_resolution_height">53dp</dimen><dimen name="toast_resolution_margin_start">13dp</dimen><dimen name="toast_resolution_corner_radius">8dp</dimen><dimen name="toast_resolution_tv_text_size">12sp</dimen><dimen name="toast_uhd_width">314dp</dimen><dimen name="toast_uhd_height">77dp</dimen><dimen name="toast_uhd_margin_bottom">94dp</dimen><dimen name="toast_uhd_iv_width">59dp</dimen><dimen name="toast_uhd_iv_height">29dp</dimen><dimen name="toast_uhd_iv_margin_start">13dp</dimen><dimen name="toast_uhd_tv_martin_start">7dp</dimen><dimen name="toast_uhd_tv_text_size">12sp</dimen><dimen name="toast_shutting_down_ops_width">170dp</dimen><dimen name="toast_shutting_down_ops_height">65dp</dimen><dimen name="toast_shutting_down_ops_iv_margin_top">10dp</dimen><dimen name="toast_shutting_down_ops_tv_margin_top">5dp</dimen><dimen name="tv_rest_margin_desktop">14dp</dimen><dimen name="tv_desktop_margin_start">36dp</dimen><dimen name="tv_desktop_text_size">10sp</dimen><dimen name="small_window_shortcut_tv_desktop_margin_start">11dp</dimen><dimen name="tv_desktop_margin_top">23dp</dimen><dimen name="tv_desktop_margin_bottom">17dp</dimen><dimen name="fl_desktop_padding_vertical">6dp</dimen></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SystemSetting</string><string name="screen_title">Control Panel</string><string name="screen_text">Mirroring</string><string name="write_text">Note</string><string name="setting">Settings</string><string name="sign">Source</string><string name="lock">Touch Lock</string><string name="unlock">Unlock</string><string name="click_lock">Locked</string><string name="eye">Eye Care</string><string name="shutdown">Shut Down</string><string name="restart">Restart</string><string name="resting">Sleep</string><string name="help">Guide</string><string name="shutdown_title">Confirm to shut down?</string><string name="restart_title">Confirm to restart?</string><string name="shutdown_content">Once shut down, all running applications will end</string><string name="restart_content">Once restarted, all running applications will end</string><string name="restart_countdown_confirm">Restart(%ds)</string><string name="cancel">Cancel</string><string name="shutdown_countdown_title">Prompt</string><string name="shutdown_countdown_content">Your Windows has not been shut down properly. \nDo you want to shut it down mandatorily?</string><string name="shutdown_countdown_confirm">Shut down(%ds)</string><string name="shutdown_countdown_cancel">Cancel</string><string name="windows">Windows</string><string name="windows_disabled">Windows (Disabled)</string><string name="front_typec">USB C</string><string name="behind_hdmi1">HDMI 1</string><string name="behind_hdmi2">HDMI 2</string><string name="network">Ethernet</string><string name="wifi">Wi—Fi</string><string name="switch_wifi">Wi-Fi</string><string name="wifi_more">Wi-Fi Details</string><string name="about">About</string><string name="check_update">Update</string><string name="mac_address">MAC Address</string><string name="ip_setting">IP Setting</string><string name="ip_address">IP</string><string name="manual">Manual</string><string name="subnet_mask">Subnet Mask</string><string name="gateway">Gateway</string><string name="DNS">DNS Server</string><string name="DNS1">DNS 1</string><string name="DNS2">DNS 2</string><string name="confirm">OK</string><string name="auto">Auto</string><string name="toast_success">Network connected</string><string name="toast_fail">Network connection failed</string><string name="toast_set_ip_fail">Failed to set</string><string name="device_name">Device Name</string><string name="serial_number">Serial Number</string><string name="resolution">Screen Resolution</string><string name="running_memory">Running Memory</string><string name="store_memory">Storage Memory</string><string name="total">in total: </string><string name="remaining">    left: </string><string name="android_version">Android Version</string><string name="system_version">System Version</string><string name="touch_version">Touch Version</string><string name="touch_unplugged">Touch not recognized</string><string name="company">Manufacturer</string><string name="company_name">Beijing EEO Education Technology \nCo., Ltd.</string><string name="service_email">Email</string><string name="email"><EMAIL></string><string name="service_hotline">Phone</string><string name="hotline">************</string><string name="window_host">System Restore</string><string name="window_host_title">Confirm to restore the Windows system?</string><string name="window_host_content">Once restored, all data in C Drive will be deleted.</string><string name="privacy_policy">Data Protection and Privacy Policy</string><string name="factory_reset">Reset</string><string name="window_factory_reset_title">Confirm to reset the system?</string><string name="window_factory_reset_content">Once reset, all data in Android will be deleted.</string><string name="auto_update">Auto update</string><string name="network_error">Network error, \nplease check the cable network or Wi-Fi connection. </string><string name="check_network">Please check the cable network or Wi-Fi connection of the large screen</string><string name="retry">Retry</string><string name="checking_update">Checking for updates</string><string name="update_description">Update Description:</string><string name="update_msg">System update will take a few minutes. \nIt will be temporarily unavailable during the update process.</string><string name="right_update">Update</string><string name="download">Downloading, please wait...</string><string name="cancel_download">Cancel</string><string name="install_title">The latest version is downloaded. Click "Install" to continue the installation.</string><string name="install_msg">System installation will take a few minutes. \nIt will be temporarily unavailable during the installation.</string><string name="install_right">Install</string><string name="wifi_network">Network</string><string name="finish_lock">Unlocked</string><string name="network_wifi_status_connected_no_internet">Connected but unable to access the internet</string><string name="network_wifi_status_saved">Saved</string><string name="network_wifi_status_idle"/><string name="network_wifi_status_disabled">Disactivated</string><string name="network_wifi_status_network_failure">IP confuguration failed</string><string name="network_wifi_status_wifi_failure">Wi-Fi connection failed</string><string name="network_wifi_status_password_failure">Authentication failed</string><string name="network_wifi_status_scanning">Scanning…</string><string name="network_wifi_status_connecting">Connecting…</string><string name="network_wifi_status_authenticating">Authentication in progress…</string><string name="network_wifi_status_obtaining_ip_address">Accessing IP address…</string><string name="network_wifi_status_connected">Connected</string><string name="network_wifi_status_suspended">Paused</string><string name="network_wifi_status_disconnecting">Disconnecting...</string><string name="network_wifi_status_disconnected">Disconnected</string><string name="network_wifi_status_failed">Failed</string><string name="network_wifi_status_blocked">Blocked</string><string name="network_wifi_status_verifying_poor_link">Temporarily closed (poor network)</string><string name="password">Password</string><string name="join">Join</string><string name="wifi_front_str">Please enter the password of \"</string><string name="wifi_behind_str">\"</string><string name="password_error">* Wrong password</string><string name="saved_network">Network saved</string><string name="select_network">Select Wi-Fi</string><string name="wifi_device_error">The Wi-Fi device is abnormal, please contact customer service for assistance. </string><string name="save">Save</string><string name="disconnect_network">Disconnect this network</string><string name="forget_network">Remove this network</string><string name="screen_name">Phone/computer mirroring</string><string name="wireless_screen_name">Wireless mirroring</string><string name="wireless_screen_disabled">Wireless mirroring (Disactivated)</string><string name="wireless_screen_disabled_toast">Wireless mirroring is disactivated. \nPlease activate it in Settings if you need.</string><string name="wireless_screen_enable">Enable wireless mirroring</string><string name="wireless_screen_enable_pin_code">Enable mirroring code</string><string name="wired_screen_name">Wired mirroring</string><string name="screen_wifi_connect">Connect the below Wi-Fi:</string><string name="screen_wifi_name">Name: </string><string name="screen_wifi_eeo_guest"> EEO-Guest</string><string name="screen_wifi_password">Password: </string><string name="screen_wifi_eeo_password"> eeoguest123</string><string name="screen_step_1">Download the Transcreen APP</string><string name="screen_step_2">Connect hotspot\nName：%s\nPassword：%s</string><string name="screen_step_3">Mirror to the board\nOpen Transcreen\nSelect【%s】to mirror</string><string name="screen_msg1">Scan the QR code or\nenter transcreen.app\nto download the app</string><string name="screen_pin_code">PIN Code：%s</string><string name="screen_pin_code2">PIN Code</string><string name="screen_permission_refuse">REFUSE</string><string name="screen_air_play">AirPlay</string><string name="not_network">The network is not connected</string><string name="disconnect">Not connected</string><string name="no_sign">No signal</string><string name="current_sign">Current Channel [Windows]</string><string name="current_sign_windows_disabled">Current Channel [Windows](Disabled)</string><string name="current_sign_hdmi">Current Channel [HDMI]</string><string name="current_sign_hdmi_1">Current Channel [HDMI 1]</string><string name="current_sign_hdmi_2">Current Channel [HDMI 2]</string><string name="current_sign_typec">Current Channel [USB C]</string><string name="change_to_pc">Switch to Windows</string><string name="start_computer">Start Windows</string><string name="no_sign_msg">No signal is recognized, please try to restart</string><string name="no_sign_windows_disabled">Windows is disabled, you can enable it in "Settings-More"</string><string name="no_sign_hdmi">No signal is recognized, please check \nwhether the signal cable is connected properly</string><string name="no_pc_1">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:01)</string><string name="no_pc_2">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:02)</string><string name="home">Home</string><string name="annotate">Note</string><string name="projection">Mirroring</string><string name="hint_no_control">You cannot controll under the small screen for the current signal source.</string><string name="privacy">Privacy is under protection</string><string name="ready_install">Preparing for installation, please wait...</string><string name="install_ing">Installing(%d%%), please wait...</string><string name="install_fail">Installation failed</string><string name="install_fail_reinstall">Installation failed, please try again</string><string name="reinstall">Reinstall</string><string name="have_update">New version found</string><string name="have_update_hint">-System update requires a restart\n-This update fixes some major issues. \nPlease update for your normal use.</string><string name="lastest_version">. It is the latest version</string><string name="system_lastest_version">Your version is the latest version</string><string name="updatable">Available version:</string><string name="reset_ops">This restoration will take about 5 minutes. Please do not cut off the power during the restoration.</string><string name="eeo_screen_move_txt">Click above or swipe up to restore full screen</string><string name="extra">More</string><string name="wireless_screen">Wireless mirroring</string><string name="write_without_screen_on">Write without screen on</string><string name="breath_led_on">Logo LED always on</string><string name="touch_slider">Half-Screen Slider</string><string name="windows_disable">Windows Disable</string><string name="windows_task_manager">Windows Task Manager</string><string name="factory_menu">Factory Menu</string><string name="debug_menu">Debug Menu</string><string name="touch_calibration">Touch Calibration</string><string name="login_title">Login is required</string><string name="login">Log in</string><string name="login_cancel">Cancel</string><string name="adb_title">Hidden Secrets</string><string name="adb">Adb Switch</string><string name="stroke_algorithm">Stroke Algorithm</string><string name="write_acceleration">Write Acceleration</string><string name="r30_write_speed">R30 Write Speed</string><string name="screen_activation_status_getting">getting...</string><string name="screen_activation_status_activated">activated</string><string name="screen_activation_status_nonactivated">nonactivated</string><string name="language_and_locale">Language</string><string name="language_change">Languages</string><string name="simplified_chinese">简体中文</string><string name="english">English</string><string-array name="languages">
        <item>简体中文</item>
        <item>English</item>
    </string-array><string name="startup_channel">Startup Source</string><string-array name="startup_channels">
        <item>Last Source</item>
        <item>Windows</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array><string-array name="startup_channels_windows_disabled">
        <item>Last Source</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array><string name="toast_resolution">%s   %dX%d @ %dHz</string><string name="uhd_hdmi">Please use HDMI 2.0 or higher cables \nthat meet the "UHD" signal specifications.</string><string name="uhd_type_c">Please use fully-equipped Type-C cables \nthat meet the "UHD" signal specifications.</string><string name="toast_shutting_down_ops">Windows is shutting down.</string><string name="color_temperature_adjust">Color Temperature Adjusting</string><string name="color_temperature">Color Temperature</string><string name="color_temperature_cold">Cold</string><string name="color_temperature_warm">Warm</string><string name="rgb_red_gain">Red Gain</string><string name="rgb_green_gain">Green Gain</string><string name="rgb_blue_gain">Blue Gain</string><string name="rgb_reset">Reset</string><string name="color_temperature_value_wrong">The value should be %d to %d.</string><string name="rgb_red_gain_value_wrong">The value should be 0 to 255.</string><string name="rgb_green_gain_value_wrong">The value should be 0 to 255.</string><string name="rgb_blue_gain_value_wrong">The value should be 0 to 255.</string><string name="color_temperature_eye_care">In Eye Care mode</string><string name="hardware_self_test">Hardware self test</string><string name="hardware_self_test_no_test">No Test</string><string name="hardware_self_test_testing">Testing...</string><string name="hardware_self_test_success">Success</string><string name="hardware_self_test_fail">Fail</string><string name="hardware_self_test_CPU">CPU</string><string name="hardware_self_test_memory">Memory</string><string name="hardware_self_test_hard_disk">Hard Disk</string><string name="hardware_self_test_touch">Touch</string><string name="hardware_self_test_ethernet">Ethernet</string><string name="hardware_self_test_wifi">Wifi</string><string name="hardware_self_test_mic">Mic</string><string name="desktop">Desktop</string><string name="maximize">Maximize</string></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values\style.xml" qualifiers=""><style name="EeoDialogStyle" parent="@style/Base.Theme.AppCompat.Dialog">

        <item name="android:colorBackground">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="windowNoTitle">true</item>
        <item name="android:dialogTitle">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        
        
        <item name="android:windowContentTransitions">true</item>

        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>

    </style><style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent
        </item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style><style name="Dialog_NO_DARK" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@android:color/transparent
        </item>
        <item name="android:backgroundDimEnabled">false</item>
    </style><style name="Main_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style><style name="Main_ShadowLayout_Circle_Img">
        <item name="android:layout_width">@dimen/sl_lock_width</item>
        <item name="android:layout_height">@dimen/sl_lock_height</item>
        <item name="android:layout_gravity">center</item>
    </style><style name="Main_ShadowLayout_Circle_Text">
        <item name="android:layout_width">@dimen/tv_screen_width</item>
        <item name="android:layout_height">@dimen/tv_screen_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:textSize">@dimen/tv_screen_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
    </style><style name="Main_Shutdown_TextView">
        <item name="android:layout_width">@dimen/tv_shutdown_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">start</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:textSize">@dimen/tv_desktop_text_size</item>
    </style><style name="Main_YcCardView">
        <item name="ycCardBackgroundColor">@color/white_95</item>
        <item name="ycCardCornerRadius">@dimen/main_bg_radius</item>
        <item name="ycCardElevation">@dimen/sl_screen_elevation</item>
        <item name="ycCardPreventCornerOverlap">false</item>
        <item name="ycStartShadowColor">@color/shadow_color</item>
    </style><style name="SeekBar_Light">
        <item name="android:layout_width">@dimen/sb_bright_width</item>
        <item name="android:layout_height">@dimen/sb_bright_height</item>
        <item name="android:maxWidth">@dimen/sb_bright_width</item>
        <item name="android:maxHeight">@dimen/sb_bright_max_height</item>
        <item name="android:paddingStart">@dimen/sb_bright_padding_start</item>
        <item name="android:paddingEnd">@dimen/sb_bright_padding_start</item>
        <item name="android:progressDrawable">@drawable/progress_vertical_gradient_simple_shape
        </item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@null</item>
        <item name="android:max">113</item>
    </style><style name="SeekBar_Rgb">
        <item name="android:layout_width">@dimen/rgb_sb_width</item>
        <item name="android:layout_height">@dimen/rgb_sb_thumb_height</item>
        <item name="android:maxWidth">@dimen/rgb_sb_width</item>
        <item name="android:maxHeight">@dimen/rgb_sb_height</item>
        <item name="android:paddingStart">@dimen/rgb_sb_padding_start</item>
        <item name="android:paddingEnd">@dimen/rgb_sb_padding_end</item>
        <item name="android:layout_marginStart">@dimen/rgb_sb_margin_start</item>
        <item name="android:layout_marginTop">@dimen/rgb_sb_margin_top</item>
        <item name="android:progressDrawable">
            @drawable/progress_vertical_gradient_simple_shape_rgb
        </item>
        <item name="android:splitTrack">false</item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@drawable/thumb_rgb</item>
        <item name="android:max">255</item>
    </style><style name="Shutdown_Icon">
        <item name="android:layout_width">@dimen/iv_shutdown_width</item>
        <item name="android:layout_height">@dimen/iv_shutdown_height</item>
        <item name="android:layout_marginTop">@dimen/iv_shutdown_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/iv_shutdown_margin_left</item>
    </style><style name="Shutdown_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_tittle_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_tittle_margin_left</item>
    </style><style name="Shutdown_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_content_text_size</item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_content_margin_top</item>
        <item name="android:layout_centerHorizontal">true</item>

    </style><style name="Shutdown_Btn_Confirm">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_confirm_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style><style name="Shutdown_Btn_Cancel">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_white</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_cancel_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style><style name="Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:layout_centerHorizontal">true</item>
    </style><style name="Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/main_line1_height</item>
        <item name="android:background">@color/main_line</item>
    </style><style name="Signal_FrameLayout">
        <item name="android:layout_width">@dimen/signal_tv_width</item>
        <item name="android:layout_height">@dimen/signal_tv_height</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:background">@drawable/shape_signal_bg</item>
    </style><style name="Signal_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:layout_marginStart">@dimen/signal_iv_margin_start</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:scaleType">fitCenter</item>
    </style><style name="Signal_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/signal_tv_margin_start</item>
        <item name="android:textSize">@dimen/signal_tv_text_size</item>
        <item name="android:textColor">@color/main_text_color</item>
    </style><style name="Setting_ShadowLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/sl_network_height</item>
        <item name="hl_layoutBackground_true">@color/press_color</item>
        <item name="hl_layoutBackground">@color/white_0</item>
        <item name="hl_shadowColor">@color/shadow_color</item>
        <item name="hl_shapeMode">selected</item>
        <item name="hl_shadowOffsetX">@dimen/sl_network_shadow_offset_x</item>
        <item name="hl_shadowOffsetY">@dimen/sl_network_shadow_offset_y</item>
        <item name="hl_textColor">@color/text_black_100</item>
        <item name="hl_textColor_true">@color/white_100</item>

    </style><style name="Setting_ImageView">
        <item name="android:layout_width">@dimen/iv_bright_width</item>
        <item name="android:layout_height">@dimen/iv_bright_height</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/sl_network_margin_start</item>
    </style><style name="Setting_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/tv_network_margin_start</item>
        <item name="android:textSize">@dimen/tv_network_text_size</item>
    </style><style name="NetWork_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>

    </style><style name="NetWork_Switch">
        <item name="android:layout_width">@dimen/fragment_network_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_network_sw_height</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style><style name="NetWork_TextView">
        <item name="android:layout_width">@dimen/fragment_network_title_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_title_margin_start</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_network_text_size</item>
    </style><style name="Network_EditText">
        <item name="android:layout_width">@dimen/fragment_network_et_width</item>
        <item name="android:layout_height">@dimen/fragment_network_et_height</item>
        <item name="android:textCursorDrawable">@drawable/shape_network_edt_cursor</item>
        <item name="android:textSize">@dimen/fragment_network_et_text_size</item>
        <item name="android:textColor">@color/color_network_edit</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_et_margin_start</item>
        <item name="android:paddingEnd">@dimen/fragment_network_et_padding_end</item>
        <item name="android:gravity">center_vertical|end</item>
    </style><style name="IP_Dialog_Select_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_item_height</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style><style name="IP_Dialog_Select_Image">
        <item name="android:layout_width">@dimen/dialog_network_auto_manual_iv_width</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_iv_height</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_iv_margin_start
        </item>
    </style><style name="IP_Dialog_Select_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_tv_margin_start
        </item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:textSize">@dimen/dialog_network_auto_manual_tv_text_size</item>
    </style><style name="About_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/fragment_about_title_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_title_margin_start</item>
    </style><style name="About_Text_Content">
        <item name="android:layout_width">@dimen/fragment_about_content_width</item>
        <item name="android:layout_height">@dimen/fragment_about_content_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_about_title_margin_start</item>
        <item name="android:gravity">end</item>
    </style><style name="About_Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/fragment_about_line_height</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_line_margin_start</item>
        <item name="android:layout_marginRight">@dimen/fragment_about_line_margin_start</item>
        <item name="android:background">@color/line_50</item>
    </style><style name="Extra_Switch">
        <item name="android:layout_width">@dimen/fragment_extra_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_extra_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_extra_sw_margin_end</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style><style name="Extra_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_extra_tv_margin_start</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_extra_tv_text_size</item>
    </style><style name="SystemSetting_PopWindow_Host_Btn">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="textAllCaps">false</item>
        <item name="android:outlineProvider">none</item>
    </style><style name="Update_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_update_tv_version_text_size</item>
    </style><style name="Update_Button">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:minWidth">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style><style name="update_progress_horizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        
        <item name="android:progressDrawable">@drawable/progress_indeterminate_horizontal</item>
        
        <item name="android:minHeight">@dimen/fragment_update_pb_horizontal_min_height</item>
        
        <item name="android:maxHeight">@dimen/fragment_update_pb_horizontal_max_height</item>
    </style><style name="Wifi_Text_NAME">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/wifi_dialog_input_password_title_margin_top
        </item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/wifi_dialog_input_password_title_text_size</item>
    </style><style name="WiFi_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/item_wifi_more_tv_disconnect_height</item>
        <item name="android:layout_marginStart">@dimen/item_wifi_more_tv_disconnect_margin_start
        </item>
        <item name="android:textSize">@dimen/item_wifi_more_tv_save_text_size</item>
        <item name="android:textColor">@color/text_press_green_black</item>
        <item name="android:gravity">center_vertical</item>
    </style><style name="Projection_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/small_window_tv_home_margin_start</item>
        <item name="android:textSize">@dimen/small_window_shortcut_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:gravity">center_vertical</item>
    </style><style name="Projection_ImageView">
        <item name="android:layout_width">@dimen/small_window_iv_home_width</item>
        <item name="android:layout_height">@dimen/small_window_iv_home_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style><style name="Adb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/adb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_centerVertical">true</item>
    </style><style name="Rgb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/rgb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Adb_Switch">
        <item name="android:layout_width">@dimen/adb_sw_width</item>
        <item name="android:layout_height">@dimen/adb_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/adb_sw_margin_end</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/sw_adb_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style><style name="Adb_Button">
        <item name="android:layout_width">@dimen/adb_btn_width</item>
        <item name="android:minWidth">@dimen/adb_btn_width</item>
        <item name="android:layout_height">@dimen/adb_btn_height</item>
        <item name="android:minHeight">@dimen/adb_btn_height</item>
        <item name="android:background">@drawable/shape_adb_btn</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/adb_btn_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.SystemSetting" parent="Theme.AppCompat.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorControlNormal">@color/white</item>
        
        <item name="colorButtonNormal">@color/teal_200</item>
        <item name="colorSwitchThumbNormal">@color/teal_700</item>
        <item name="colorBackgroundFloating">@color/black</item>
        

        
    </style></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values-zh-rCN\dimens.xml" qualifiers="zh-rCN"><dimen name="bg_radius">8dp</dimen><dimen name="main_cv_width">394dp</dimen><dimen name="main_cv_height">394dp</dimen><dimen name="main_width">384dp</dimen><dimen name="main_height">384dp</dimen><dimen name="main_bg_radius">16dp</dimen><dimen name="main_bg_stroke">0.5dp</dimen><dimen name="main_tv_title_margin_top">27dp</dimen><dimen name="sl_screen_width">114dp</dimen><dimen name="sl_screen_height">114dp</dimen><dimen name="sl_screen_margin_start">16dp</dimen><dimen name="sl_screen_margin_top">64dp</dimen><dimen name="sl_screen_corner_radius">8dp</dimen><dimen name="sl_screen_elevation">5dp</dimen><dimen name="sl_screen_shadow_limit">0dp</dimen><dimen name="sl_screen_shadow_offset_x">0dp</dimen><dimen name="sl_screen_shadow_offset_y">0dp</dimen><dimen name="sl_screen_stroke_width">0.2dp</dimen><dimen name="iv_screen_width">20dp</dimen><dimen name="iv_screen_height">20dp</dimen><dimen name="tv_screen_width">50dp</dimen><dimen name="tv_screen_height">13dp</dimen><dimen name="tv_screen_text_size">9sp</dimen><dimen name="iv_screen_margin_top">33dp</dimen><dimen name="tv_screen_margin_top">58dp</dimen><dimen name="tv_shutdown_width">50dp</dimen><dimen name="tv_setting_margin_top">25dp</dimen><dimen name="tv_screen_drawable_padding">5dp</dimen><dimen name="sl_write_margin_top">3dp</dimen><dimen name="sl_setting_width">234dp</dimen><dimen name="sl_setting_margin_start">7dp</dimen><dimen name="tv_setting_margin_start">9dp</dimen><dimen name="tv_signal_margin_start">61dp</dimen><dimen name="tv_touch_lock_width">80dp</dimen><dimen name="tv_touch_lock_margin_start">113dp</dimen><dimen name="tv_eye_margin_start">165dp</dimen><dimen name="iv_bright_width">20dp</dimen><dimen name="iv_bright_height">20dp</dimen><dimen name="iv_bright_margin_start">24dp</dimen><dimen name="iv_bright_margin_top">60dp</dimen><dimen name="iv_voice_margin_top">24dp</dimen><dimen name="sb_bright_width">151dp</dimen><dimen name="sb_bright_height">20dp</dimen><dimen name="sb_bright_max_height">40dp</dimen><dimen name="sb_bright_padding_start">0dp</dimen><dimen name="sb_bright_padding_end">0dp</dimen><dimen name="sb_bright_margin_start">49dp</dimen><dimen name="sb_bright_margin_top">60dp</dimen><dimen name="sb_voice_margin_top">24dp</dimen><dimen name="tv_rest_margin_start">15dp</dimen><dimen name="tv_restart_margin_start">44dp</dimen><dimen name="main_line1_height">0.3dp</dimen><dimen name="main_line1_margin_top">25dp</dimen><dimen name="ll_touch_lock_width">56dp</dimen><dimen name="ll_touch_lock_margin_bottom">37dp</dimen><dimen name="sl_lock_width">37dp</dimen><dimen name="sl_lock_height">37dp</dimen><dimen name="tv_lock_margin_top">5dp</dimen><dimen name="inter_touch_lock_width">220dp</dimen><dimen name="inter_touch_lock_height">98dp</dimen><dimen name="inter_touch_lock_margin_start">8dp</dimen><dimen name="inter_touch_lock_margin_top">8dp</dimen><dimen name="iv_back_margin_top">11dp</dimen><dimen name="iv_back_width">20dp</dimen><dimen name="iv_back_height">20dp</dimen><dimen name="iv_back_margin_start">13dp</dimen><dimen name="setting_tv_title_margin_top">13dp</dimen><dimen name="setting_line1_margin_top">11dp</dimen><dimen name="ll_select_width">120dp</dimen><dimen name="sl_network_height">29dp</dimen><dimen name="sl_network_margin_start">28dp</dimen><dimen name="sl_network_margin_top">21dp</dimen><dimen name="tv_network_margin_start">55dp</dimen><dimen name="tv_network_text_size">9sp</dimen><dimen name="sl_network_shadow_offset_x">0dp</dimen><dimen name="sl_network_shadow_offset_y">1dp</dimen><dimen name="sl_wifi_margin_top">11dp</dimen><dimen name="iv_shutdown_width">27dp</dimen><dimen name="iv_shutdown_height">27dp</dimen><dimen name="iv_shutdown_margin_top">134dp</dimen><dimen name="iv_shutdown_margin_left">103dp</dimen><dimen name="shutdown_tittle_text_size">12sp</dimen><dimen name="shutdown_tittle_margin_top">139dp</dimen><dimen name="shutdown_tittle_margin_left">11dp</dimen><dimen name="shutdown_content_text_size">9sp</dimen><dimen name="shutdown_content_margin_top">11dp</dimen><dimen name="shutdown_btn_confirm_width">88dp</dimen><dimen name="shutdown_btn_confirm_height">28dp</dimen><dimen name="shutdown_btn_confirm_text_size">11sp</dimen><dimen name="shutdown_btn_confirm_margin_top">37dp</dimen><dimen name="shutdown_btn_confirm_margin_left">203dp</dimen><dimen name="shutdown_btn_cancel_margin_left">93dp</dimen><dimen name="signal_tv_width">341dp</dimen><dimen name="signal_tv_height">48dp</dimen><dimen name="signal_tv_text_size">9sp</dimen><dimen name="signal_tv_drawable_padding">17dp</dimen><dimen name="signal_tv_margin_start">169dp</dimen><dimen name="signal_tv_window_margin_top">21dp</dimen><dimen name="signal_iv_margin_start">139dp</dimen><dimen name="signal_item_margin_top">11dp</dimen><dimen name="adb_width">264dp</dimen><dimen name="adb_height">384dp</dimen><dimen name="adb_scrollview_height">298dp</dimen><dimen name="adb_iv_back_width">25dp</dimen><dimen name="adb_iv_back_height">25dp</dimen><dimen name="adb_iv_back_margin_start">20dp</dimen><dimen name="adb_iv_back_margin_top">20dp</dimen><dimen name="adb_iv_close_margin_top">11dp</dimen><dimen name="adb_iv_close_margin_end">16dp</dimen><dimen name="adb_line_margin_top">42dp</dimen><dimen name="adb_item_width">232dp</dimen><dimen name="adb_item_height">29dp</dimen><dimen name="adb_item_radius">3dp</dimen><dimen name="adb_item_margin_top">3dp</dimen><dimen name="adb_item_adb_margin_top">13dp</dimen><dimen name="adb_item_margin_start">21dp</dimen><dimen name="adb_item_margin_end">16dp</dimen><dimen name="adb_sw_width">36dp</dimen><dimen name="adb_sw_height">18dp</dimen><dimen name="adb_sw_margin_end">13dp</dimen><dimen name="adb_iv_reset_width">20dp</dimen><dimen name="adb_iv_reset_height">20dp</dimen><dimen name="adb_iv_reset_margin_start">220dp</dimen><dimen name="adb_tv_margin_start">13dp</dimen><dimen name="adb_tv_text_size">9sp</dimen><dimen name="adb_btn_text_size">11sp</dimen><dimen name="adb_btn_width">70dp</dimen><dimen name="adb_btn_height">28dp</dimen><dimen name="adb_btn_margin_start">9dp</dimen><dimen name="adb_btn_margin_bottom">12dp</dimen><dimen name="rgb_tv_margin_start">16dp</dimen><dimen name="rgb_tv_color_temperature_margin_top">19dp</dimen><dimen name="rgb_sb_width">195dp</dimen><dimen name="rgb_sb_height">7dp</dimen><dimen name="rgb_sb_padding_start">7dp</dimen><dimen name="rgb_sb_padding_end">7dp</dimen><dimen name="rgb_sb_margin_start">9dp</dimen><dimen name="rgb_sb_thumb_height">23dp</dimen><dimen name="rgb_et_width">40dp</dimen><dimen name="rgb_et_height">20dp</dimen><dimen name="rgb_sb_margin_top">-4dp</dimen><dimen name="rgb_tv_color_temperature_cold_margin_top">-5dp</dimen><dimen name="rgb_line2_margin_top">25dp</dimen><dimen name="rgb_red_gain_margin_top">23dp</dimen><dimen name="rgb_green_gain_margin_top">24dp</dimen><dimen name="rgb_reset_margin_bottom">12dp</dimen><dimen name="rgb_btn_reset_width">232dp</dimen><dimen name="dialog_have_update_width">240dp</dimen><dimen name="dialog_have_update_height">148dp</dimen><dimen name="dialog_have_update_title_margin_top">19dp</dimen><dimen name="dialog_have_update_title_text_size">9sp</dimen><dimen name="dialog_have_update_content_text_size">9sp</dimen><dimen name="dialog_have_update_content_margin_top">8dp</dimen><dimen name="dialog_have_update_btn_cancel_margin_start">21dp</dimen><dimen name="dialog_have_update_btn_cancel_margin_bottom">21dp</dimen><dimen name="dialog_shutdown_countdown_content_margin_top">15dp</dimen><dimen name="dialog_shutdown_title_margin_top">23dp</dimen><dimen name="dialog_shutdown_content_margin_top">19dp</dimen><dimen name="dialog_factory_reset_title_margin_top">25dp</dimen><dimen name="dialog_factory_reset_iv_warn_width">20dp</dimen><dimen name="dialog_factory_reset_iv_warn_height">20dp</dimen><dimen name="dialog_factory_reset_content_margin_top">16dp</dimen><dimen name="dialog_factory_reset_title_margin_start">8dp</dimen><dimen name="dialog_factory_reset_btn_cancel_margin_top">24dp</dimen><dimen name="dialog_installing_pb_width">21dp</dimen><dimen name="dialog_installing_pb_height">21dp</dimen><dimen name="dialog_installing_tv_margin_start">13dp</dimen><dimen name="dialog_installing_text_size">16sp</dimen><dimen name="dialog_install_fail_tv_title_margin_top">32dp</dimen><dimen name="dialog_install_fail_tv_title_drawable_padding">8dp</dimen><dimen name="dialog_network_auto_manual_width">120dp</dimen><dimen name="dialog_network_auto_manual_height">59dp</dimen><dimen name="dialog_network_auto_manual_item_height">29dp</dimen><dimen name="dialog_network_auto_manual_iv_width">20dp</dimen><dimen name="dialog_network_auto_manual_iv_height">20dp</dimen><dimen name="dialog_network_auto_manual_iv_margin_start">29dp</dimen><dimen name="dialog_network_auto_manual_tv_margin_start">3dp</dimen><dimen name="dialog_network_auto_manual_tv_text_size">9sp</dimen><dimen name="dialog_network_auto_manual_line_margin_left">11dp</dimen><dimen name="dialog_network_auto_manual_line_margin_right">11dp</dimen><dimen name="fragment_about_scrollbar_size">4dp</dimen><dimen name="fragment_about_padding_bottom">5dp</dimen><dimen name="fragment_about_device_name_margin_top">29dp</dimen><dimen name="fragment_about_text_size">9sp</dimen><dimen name="fragment_about_title_width">60dp</dimen><dimen name="fragment_about_title_height">13dp</dimen><dimen name="fragment_about_title_margin_top">27dp</dimen><dimen name="fragment_about_title_margin_start">21dp</dimen><dimen name="fragment_about_content_width">160dp</dimen><dimen name="fragment_about_content_height">13dp</dimen><dimen name="fragment_about_line_height">0.3dp</dimen><dimen name="fragment_about_line_margin_start">21dp</dimen><dimen name="fragment_about_line_margin_top">33dp</dimen><dimen name="fragment_about_tv_system_version_margin_start">1dp</dimen><dimen name="fragment_about_windows_host_width">80dp</dimen><dimen name="fragment_about_iv_windows_host_margin_start">121dp</dimen><dimen name="fragment_about_padding_view_height">52dp</dimen><dimen name="fragment_extra_wireless_screen_margin_top">26dp</dimen><dimen name="fragment_extra_item_margin_top">20dp</dimen><dimen name="fragment_extra_sw_width">40dp</dimen><dimen name="fragment_extra_sw_height">20dp</dimen><dimen name="fragment_extra_sw_margin_end">21dp</dimen><dimen name="fragment_extra_tv_margin_start">21dp</dimen><dimen name="fragment_extra_tv_text_size">9sp</dimen><dimen name="fragment_network_network_margin_top">26dp</dimen><dimen name="fragment_network_sw_margin_start">141dp</dimen><dimen name="fragment_network_text_size">9sp</dimen><dimen name="fragment_network_item_margin_top">23dp</dimen><dimen name="fragment_network_title_width">40dp</dimen><dimen name="fragment_network_title_margin_start">21dp</dimen><dimen name="fragment_network_content_width">160dp</dimen><dimen name="fragment_network_content_margin_start">21dp</dimen><dimen name="fragment_network_tv_ip_setting_width">140dp</dimen><dimen name="fragment_network_ll_ip_margin_top">15dp</dimen><dimen name="fragment_network_iv_ip_width">13dp</dimen><dimen name="fragment_network_iv_ip_height">13dp</dimen><dimen name="fragment_network_iv_ip_margin_start">33dp</dimen><dimen name="fragment_network_ll_mask_margin_top">11dp</dimen><dimen name="fragment_network_dns_title_width">50dp</dimen><dimen name="fragment_network_dns_content_margin_start">23dp</dimen><dimen name="fragment_network_btn_confirm_margin_start">29dp</dimen><dimen name="fragment_network_btn_confirm_margin_top">12dp</dimen><dimen name="fragment_network_sw_width">40dp</dimen><dimen name="fragment_network_sw_height">20dp</dimen><dimen name="fragment_network_et_width">140dp</dimen><dimen name="fragment_network_et_height">29dp</dimen><dimen name="fragment_network_et_text_size">9sp</dimen><dimen name="fragment_network_et_margin_start">8dp</dimen><dimen name="fragment_network_et_padding_end">7dp</dimen><dimen name="fragment_wifi_title_wifi_width">47dp</dimen><dimen name="fragment_wifi_sw_margin_start">134dp</dimen><dimen name="fragment_wifi_rv_connected_margin_top">10dp</dimen><dimen name="fragment_wifi_line1_margin_top">19dp</dimen><dimen name="fragment_wifi_line1_margin_start">0dp</dimen><dimen name="fragment_wifi_line1_margin_end">0dp</dimen><dimen name="fragment_wifi_ll_network_margin_start">21dp</dimen><dimen name="fragment_wifi_ll_network_margin_top">33dp</dimen><dimen name="fragment_wifi_ll_network_gone_margin_top">23dp</dimen><dimen name="fragment_wifi_tv_other_network_text_size">9sp</dimen><dimen name="fragment_wifi_pb_check_update_width">12dp</dimen><dimen name="fragment_wifi_pb_check_update_height">12dp</dimen><dimen name="fragment_wifi_pb_check_update_margin_start">8dp</dimen><dimen name="fragment_wifi_rv_wifi_list_margin_top">13dp</dimen><dimen name="fragment_update_line_margin_top">30dp</dimen><dimen name="fragment_update_tv_no_network_margin_start">21dp</dimen><dimen name="fragment_update_tv_no_network_margin_top">33dp</dimen><dimen name="fragment_update_tv_check_network_margin_top">27dp</dimen><dimen name="fragment_update_btn_retry_margin_top">19dp</dimen><dimen name="fragment_update_tv_version_margin_top">33dp</dimen><dimen name="fragment_update_pb_check_update_margin_top">77dp</dimen><dimen name="fragment_update_tv_checking_margin_top">11dp</dimen><dimen name="fragment_update_tv_version_text_size">9sp</dimen><dimen name="fragment_update_btn_right_update_margin_top">121dp</dimen><dimen name="fragment_update_btn_right_update_gone_margin_top">20dp</dimen><dimen name="fragment_update_btn_install_margin_top">43dp</dimen><dimen name="fragment_update_pb_download_margin_top">32dp</dimen><dimen name="fragment_update_pb_download_margin_end">21dp</dimen><dimen name="fragment_update_pb_horizontal_min_height">5dp</dimen><dimen name="fragment_update_pb_horizontal_max_height">5dp</dimen><dimen name="fragment_update_btn_cancel_margin_top">59dp</dimen><dimen name="fragment_update_tv_update_description_width">221dp</dimen><dimen name="fragment_update_tv_update_description_height">81dp</dimen><dimen name="fragment_update_tv_update_description_margin_top">13dp</dimen><dimen name="fragment_update_tv_update_description_max_height">80dp</dimen><dimen name="fragment_locale_tv_language_margin_top">26dp</dimen><dimen name="fragment_locale_iv_language_margin_top">26dp</dimen><dimen name="fragment_locale_iv_language_margin_end">21dp</dimen><dimen name="fragment_locale_tv_language_height">20dp</dimen><dimen name="fragment_locale_spinner_width">120dp</dimen><dimen name="item_wifi_height">41dp</dimen><dimen name="item_wifi_tv_wifi_height">26dp</dimen><dimen name="item_wifi_tv_wifi_name_height">13dp</dimen><dimen name="item_wifi_tv_wifi_name_max_width">100dp</dimen><dimen name="item_wifi_tv_state_margin_top">1dp</dimen><dimen name="item_wifi_tv_state_text_size">8sp</dimen><dimen name="item_wifi_iv_rank_width">20dp</dimen><dimen name="item_wifi_iv_rank_height">20dp</dimen><dimen name="item_wifi_iv_rank_margin_end">21dp</dimen><dimen name="item_wifi_iv_password_margin_end">4dp</dimen><dimen name="item_wifi_line_margin_top">0dp</dimen><dimen name="item_wifi_connect_height">25dp</dimen><dimen name="item_wifi_connect_margin_top">18dp</dimen><dimen name="item_wifi_connect_iv_check_width">17dp</dimen><dimen name="item_wifi_connect_iv_check_height">17dp</dimen><dimen name="item_wifi_connect_iv_password_margin_start">182dp</dimen><dimen name="item_wifi_more_title_margin_start">107dp</dimen><dimen name="item_wifi_more_tv_save_margin_start">18dp</dimen><dimen name="item_wifi_more_tv_save_margin_top">14dp</dimen><dimen name="item_wifi_more_tv_save_margin_end">21dp</dimen><dimen name="item_wifi_more_tv_save_margin_bottom">18dp</dimen><dimen name="item_wifi_more_tv_save_text_size">9sp</dimen><dimen name="item_wifi_more_line_margin_top">14dp</dimen><dimen name="item_wifi_more_drawable_padding">8dp</dimen><dimen name="item_wifi_more_line1_margin_top">14dp</dimen><dimen name="item_wifi_more_item_margin_top">26dp</dimen><dimen name="item_wifi_more_tv_ip_setting_width">147dp</dimen><dimen name="item_wifi_more_item_margin_end">35dp</dimen><dimen name="item_wifi_more_iv_arrow_margin_top">23dp</dimen><dimen name="item_wifi_more_rl_manual_margin_top">18dp</dimen><dimen name="item_wifi_more_iv_manual_margin_end">15dp</dimen><dimen name="item_wifi_more_ip_address_margin_top">8dp</dimen><dimen name="item_wifi_more_et_address_margin_top">6dp</dimen><dimen name="item_wifi_more_iv_ip_marin_end">8dp</dimen><dimen name="item_wifi_more_et_subnet_mask_margin_top">11dp</dimen><dimen name="item_wifi_more_tv_dns_width">47dp</dimen><dimen name="item_wifi_more_fl_disconnect_margin_top">21dp</dimen><dimen name="item_wifi_more_tv_disconnect_height">29dp</dimen><dimen name="item_wifi_more_tv_disconnect_margin_start">49dp</dimen><dimen name="item_wifi_more_tv_disconnect_padding_left">21dp</dimen><dimen name="item_wifi_more_iv_disconnect_margin_start">21dp</dimen><dimen name="launcher_main_iv_privacy_width">27dp</dimen><dimen name="launcher_main_iv_privacy_height">27dp</dimen><dimen name="launcher_main_tv_privacy_margin_top">30dp</dimen><dimen name="launcher_main_tv_privacy_text_size">18sp</dimen><dimen name="launcher_main_tv_hint_width">160dp</dimen><dimen name="launcher_main_tv_hint_height">37dp</dimen><dimen name="launcher_main_tv_hint_text_size">9sp</dimen><dimen name="launcher_main_iv_no_signal_width">304dp</dimen><dimen name="launcher_main_iv_no_signal_height">304dp</dimen><dimen name="launcher_main_iv_no_signal_margin_top">202dp</dimen><dimen name="launcher_main_tv_no_signal_margin_top">467dp</dimen><dimen name="launcher_main_tv_no_signal_text_size">16sp</dimen><dimen name="launcher_main_tv_current_signal_margin_top">10dp</dimen><dimen name="launcher_main_tv_current_signal_text_size">12sp</dimen><dimen name="launcher_main_btn_start_width">160dp</dimen><dimen name="launcher_main_btn_start_height">40dp</dimen><dimen name="launcher_main_btn_start_margin_top">56dp</dimen><dimen name="launcher_main_tv_hotline_width">200dp</dimen><dimen name="launcher_main_tv_hotline_height">40dp</dimen><dimen name="launcher_main_tv_hotline_margin_top">16dp</dimen><dimen name="screen_dialog_cv_wireless_screen_height">318dp</dimen><dimen name="screen_dialog_rl_wireless_screen_height">308dp</dimen><dimen name="screen_dialog_iv_back_width">20dp</dimen><dimen name="screen_dialog_iv_back_height">20dp</dimen><dimen name="screen_dialog_iv_back_margin_start">18dp</dimen><dimen name="screen_dialog_iv_back_margin_top">23dp</dimen><dimen name="screen_dialog_tv_title_margin_top">25dp</dimen><dimen name="screen_dialog_title_text_size">12sp</dimen><dimen name="screen_dialog_content_text_size">9sp</dimen><dimen name="screen_dialog_tv_margin_start">21dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_width">40dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_height">20dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_margin_top">14dp</dimen><dimen name="screen_dialog_switch_enable_wireless_screen_margin_end">23dp</dimen><dimen name="screen_dialog_line2_margin_top">8dp</dimen><dimen name="screen_dialog_rl_sub_wireless_screen_width">341dp</dimen><dimen name="screen_dialog_rl_sub_wireless_screen_margin_top">8dp</dimen><dimen name="screen_dialog_tv_enable_pin_code_margin_top">8dp</dimen><dimen name="screen_dialog_switch_enable_pin_code_margin_end">1dp</dimen><dimen name="screen_dialog_tv_order_width">21dp</dimen><dimen name="screen_dialog_tv_order_height">21dp</dimen><dimen name="screen_dialog_tv_one_margin_start">25dp</dimen><dimen name="screen_dialog_iv_step_1_width">15dp</dimen><dimen name="screen_dialog_iv_step_1_height">15dp</dimen><dimen name="screen_dialog_iv_step_1_margin_top">26dp</dimen><dimen name="screen_dialog_tv_step_1_height">13dp</dimen><dimen name="screen_dialog_tv_step_1_line_height">13dp</dimen><dimen name="screen_dialog_tv_step_1_margin_start">6dp</dimen><dimen name="screen_dialog_tv_step_1_margin_top">1dp</dimen><dimen name="screen_dialog_iv_step_2_margin_top">12dp</dimen><dimen name="screen_dialog_iv_step_3_margin_top">39dp</dimen><dimen name="screen_dialog_tv_pin_code_margin_top">4dp</dimen><dimen name="screen_dialog_iv_code_width">73dp</dimen><dimen name="screen_dialog_iv_code_height">73dp</dimen><dimen name="screen_dialog_iv_code_margin_top">29dp</dimen><dimen name="screen_dialog_iv_code_margin_end">1dp</dimen><dimen name="screen_dialog_tv_code_margin_top">5dp</dimen><dimen name="screen_dialog_tv_code_margin_end">-2dp</dimen><dimen name="screen_dialog_tv_code_text_size">9sp</dimen><dimen name="screen_dialog_tv_code_line_height">13dp</dimen><dimen name="screen_dialog_cv_wired_screen_margin_top">-1dp</dimen><dimen name="screen_dialog_cv_wired_screen_height">75dp</dimen><dimen name="screen_dialog_rl_wired_screen_height">65dp</dimen><dimen name="screen_dialog_rl_wired_screen_margin_top">11dp</dimen><dimen name="screen_dialog_rl_sub_wired_screen_margin_top">25dp</dimen><dimen name="screen_dialog_tv_signal_margin_top">11dp</dimen><dimen name="screen_offset_tv_text_size">12sp</dimen><dimen name="screen_offset_tv_margin_top">240dp</dimen><dimen name="screen_offset_tv_margin_horizontal">40dp</dimen><dimen name="screen_offset_tv_margin_bottom">10dp</dimen><dimen name="small_window_shortcut_width">427dp</dimen><dimen name="small_window_shortcut_height">29dp</dimen><dimen name="small_window_shortcut_iv_screen_margin_end">21dp</dimen><dimen name="small_window_shortcut_iv_screen_gone_margin_start">131dp</dimen><dimen name="small_window_iv_home_width">13dp</dimen><dimen name="small_window_iv_home_height">13dp</dimen><dimen name="small_window_iv_volume_width">15dp</dimen><dimen name="small_window_iv_volume_height">15dp</dimen><dimen name="small_window_iv_volume_margin_end">5dp</dimen><dimen name="small_window_sv_volume_width">157dp</dimen><dimen name="small_window_sv_volume_height">13dp</dimen><dimen name="small_window_sv_volume_margin_end">11dp</dimen><dimen name="small_window_sv_volume_padding_start">0dp</dimen><dimen name="small_window_sv_volume_padding_end">0dp</dimen><dimen name="small_window_shortcut_text_size">9sp</dimen><dimen name="small_window_shortcut_drawable_padding">3dp</dimen><dimen name="small_window_tv_home_margin_start">16dp</dimen><dimen name="dialog_volume_width">250dp</dimen><dimen name="dialog_volume_height">74dp</dimen><dimen name="dialog_volume_margin_end">80dp</dimen><dimen name="dialog_volume_margin_bottom">228dp</dimen><dimen name="dialog_volume_iv_margin_start">21dp</dimen><dimen name="dialog_volume_sb_margin_start">46dp</dimen><dimen name="dialog_volume_tv_text_size">11sp</dimen><dimen name="toast_width">160dp</dimen><dimen name="toast_height">53dp</dimen><dimen name="toast_padding_horizontal">30dp</dimen><dimen name="toast_padding_vertical">19dp</dimen><dimen name="toast_msg_margin_start">8dp</dimen><dimen name="toast_msg_text_size">12sp</dimen><dimen name="wifi_dialog_input_password_iv_wifi_margin_start">21dp</dimen><dimen name="wifi_dialog_input_password_iv_wifi_margin_top">21dp</dimen><dimen name="wifi_dialog_input_password_title_margin_start">8dp</dimen><dimen name="wifi_dialog_input_password_title_margin_top">25dp</dimen><dimen name="wifi_dialog_input_password_title_text_size">9sp</dimen><dimen name="wifi_dialog_input_password_title_wifi_name_max_width">100dp</dimen><dimen name="wifi_dialog_input_password_title_wifi_name_max_height">13dp</dimen><dimen name="wifi_dialog_input_password_et_password_width">197dp</dimen><dimen name="wifi_dialog_input_password_et_password_height">29dp</dimen><dimen name="wifi_dialog_input_password_et_password_margin_top">11dp</dimen><dimen name="wifi_dialog_input_password_et_password_padding_start">8dp</dimen><dimen name="wifi_dialog_input_password_et_password_padding_end">30dp</dimen><dimen name="wifi_dialog_input_password_et_password_text_size">9sp</dimen><dimen name="wifi_dialog_input_password_iv_eye_width">20dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_height">20dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_margin_top">15dp</dimen><dimen name="wifi_dialog_input_password_iv_eye_margin_start">191dp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_text_size">8sp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_margin_start">24dp</dimen><dimen name="wifi_dialog_input_password_tv_password_error_margin_top">8dp</dimen><dimen name="wifi_dialog_input_password_btn_margin_top">20dp</dimen><dimen name="window_ruler_iv_ruler_width">572dp</dimen><dimen name="window_ruler_iv_ruler_height">572dp</dimen><dimen name="spinner_dropdown_item_tv_margin_start">3dp</dimen><dimen name="spinner_dropdown_item_iv_margin_vertical">5dp</dimen><dimen name="privacy_width">337dp</dimen><dimen name="privacy_height">294dp</dimen><dimen name="privacy_margin_top">21dp</dimen><dimen name="toast_resolution_width">83dp</dimen><dimen name="toast_resolution_height">53dp</dimen><dimen name="toast_resolution_margin_start">13dp</dimen><dimen name="toast_resolution_corner_radius">8dp</dimen><dimen name="toast_resolution_tv_text_size">12sp</dimen><dimen name="toast_uhd_width">240dp</dimen><dimen name="toast_uhd_height">77dp</dimen><dimen name="toast_uhd_margin_bottom">94dp</dimen><dimen name="toast_uhd_iv_width">59dp</dimen><dimen name="toast_uhd_iv_height">29dp</dimen><dimen name="toast_uhd_iv_margin_start">13dp</dimen><dimen name="toast_uhd_tv_martin_start">7dp</dimen><dimen name="toast_uhd_tv_text_size">12sp</dimen><dimen name="toast_shutting_down_ops_width">120dp</dimen><dimen name="toast_shutting_down_ops_height">65dp</dimen><dimen name="toast_shutting_down_ops_iv_margin_top">10dp</dimen><dimen name="toast_shutting_down_ops_tv_margin_top">5dp</dimen><dimen name="tv_desktop_margin_start">36dp</dimen><dimen name="tv_rest_margin_desktop">14dp</dimen><dimen name="tv_desktop_text_size">10sp</dimen><dimen name="fl_desktop_padding_vertical">6dp</dimen><dimen name="tv_desktop_margin_bottom">17dp</dimen><dimen name="small_window_shortcut_tv_desktop_margin_start">11dp</dimen><dimen name="tv_desktop_margin_top">23dp</dimen></file><file path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\values-zh-rCN\string.xml" qualifiers="zh-rCN"><string name="app_name">SystemSetting</string><string name="screen_title">大屏控制</string><string name="screen_text">投屏</string><string name="write_text">批注</string><string name="setting">设置</string><string name="sign">信号源</string><string name="lock">触控锁</string><string name="unlock">点击解锁</string><string name="click_lock">触控已锁定</string><string name="eye">护眼</string><string name="shutdown">关机</string><string name="restart">重启</string><string name="resting">息屏</string><string name="help">指南</string><string name="shutdown_title">确认关闭大屏</string><string name="restart_title">确认重启大屏？</string><string name="shutdown_content">关机将会结束所有正在运行的应用程序</string><string name="restart_content">重启将会结束所有正在运行的应用程序</string><string name="restart_countdown_confirm">重启(%ds)</string><string name="cancel">取消</string><string name="shutdown_countdown_title">关机确认</string><string name="shutdown_countdown_content">检测到Windows 电脑未正常关闭，请确认\nWindows 电脑是否需要强制关机？</string><string name="shutdown_countdown_confirm">关机(%ds)</string><string name="shutdown_countdown_cancel">取消</string><string name="windows">内置电脑</string><string name="windows_disabled">内置电脑（禁用）</string><string name="front_typec">USB C</string><string name="behind_hdmi1">HDMI 1</string><string name="behind_hdmi2">HDMI 2</string><string name="network">有线网络</string><string name="wifi">Wi—Fi</string><string name="switch_wifi">Wi-Fi开关</string><string name="wifi_more">Wi-Fi详情</string><string name="about">关于本机</string><string name="check_update">检查更新</string><string name="mac_address">MAC地址</string><string name="ip_setting">IP设置</string><string name="ip_address">IP地址</string><string name="manual">手动</string><string name="subnet_mask">子网掩码</string><string name="gateway">网关</string><string name="DNS">DNS服务器</string><string name="DNS1">DNS 1</string><string name="DNS2">DNS 2</string><string name="confirm">确认</string><string name="auto">自动</string><string name="toast_success">网络连接成功</string><string name="toast_fail">网络连接失败</string><string name="toast_set_ip_fail">设置失败</string><string name="device_name">设备名称</string><string name="serial_number">序列号</string><string name="resolution">屏幕分辨率</string><string name="running_memory">运行内存</string><string name="store_memory">存储内存</string><string name="total">总共: </string><string name="remaining">    剩余: </string><string name="android_version">安卓版本</string><string name="system_version">系统版本</string><string name="touch_version">触摸框版本</string><string name="touch_unplugged">未识别到触摸框</string><string name="company">制造商</string><string name="company_name">北京翼鸥教育科技有限公司</string><string name="service_email">服务邮箱</string><string name="email"><EMAIL></string><string name="service_hotline">服务热线</string><string name="hotline">************</string><string name="window_host">Windows 系统还原</string><string name="window_host_title">确认要还原Windows系统吗？</string><string name="window_host_content">还原Windows系统将清除电脑C盘的所有数据。</string><string name="privacy_policy">个人信息保护政策&amp;隐私协议</string><string name="factory_reset">恢复出厂</string><string name="window_factory_reset_title">确认要恢复出厂设置吗？</string><string name="window_factory_reset_content">恢复出厂设置将清除android的所有数据。</string><string name="auto_update">自动更新</string><string name="network_error">网络异常，请检查大屏有线网络或Wi-Fi的连接情况</string><string name="check_network">请检查大屏有线网络或Wi-Fi的连接情况</string><string name="retry">重试</string><string name="checking_update">正在检查更新</string><string name="update_description">更新内容:</string><string name="update_msg">系统更新需要几分钟时间，更新过程中一体机暂时无\n法使用。</string><string name="right_update">立即更新</string><string name="download">正在下载，请稍后...</string><string name="cancel_download">取消下载</string><string name="install_title">最新版本系统已下载，点击“立即安装”继续安装</string><string name="install_msg">系统安装需要几分钟时间，完成安装前一体机暂时无\n法使用。</string><string name="install_right">立即安装</string><string name="wifi_network">网络</string><string name="finish_lock">已解锁</string><string name="network_wifi_status_connected_no_internet">已连接，但无法访问互联网</string><string name="network_wifi_status_saved">已保存</string><string name="network_wifi_status_idle"/><string name="network_wifi_status_disabled">"已停用"</string><string name="network_wifi_status_network_failure">"IP 配置失败"</string><string name="network_wifi_status_wifi_failure">"WLAN 连接失败"</string><string name="network_wifi_status_password_failure">"身份验证出现问题"</string><string name="network_wifi_status_scanning">正在扫描…</string><string name="network_wifi_status_connecting">正在连接…</string><string name="network_wifi_status_authenticating">正在进行身份验证…</string><string name="network_wifi_status_obtaining_ip_address">正在获取IP地址…</string><string name="network_wifi_status_connected">已连接</string><string name="network_wifi_status_suspended">已暂停</string><string name="network_wifi_status_disconnecting">正在断开连接…</string><string name="network_wifi_status_disconnected">已断开连接</string><string name="network_wifi_status_failed">失败</string><string name="network_wifi_status_blocked">已停用</string><string name="network_wifi_status_verifying_poor_link">暂时关闭（网络状况不佳）</string><string name="password">密码</string><string name="join">加入</string><string name="wifi_front_str">请输入“</string><string name="wifi_behind_str">“的密码</string><string name="password_error">* 密码错误</string><string name="saved_network">已保存网络</string><string name="select_network">选择网络</string><string name="wifi_device_error">无线网络设备工作异常，请联系售后</string><string name="save">存储</string><string name="disconnect_network">断开此网络</string><string name="forget_network">移除此网络</string><string name="screen_name">手机/电脑投屏</string><string name="wireless_screen_name">无线投屏</string><string name="wireless_screen_disabled">无线投屏（已禁用）</string><string name="wireless_screen_disabled_toast">无线投屏已禁用，如需使用请前往设置开启</string><string name="wireless_screen_enable">开启无线投屏</string><string name="wireless_screen_enable_pin_code">启用投屏码</string><string name="wired_screen_name">有线投屏</string><string name="screen_wifi_connect">连接以下Wi-Fi：</string><string name="screen_wifi_name">名称：</string><string name="screen_wifi_eeo_guest"> EEO-Guest</string><string name="screen_wifi_password">密码：</string><string name="screen_wifi_eeo_password"> eeoguest123</string><string name="screen_step_1">下载投屏客户端Transcreen</string><string name="screen_step_2">连接设备热点\n名称：%s\n密码：%s</string><string name="screen_step_3">客户端投屏\n打开Transcreen\n选择【%s】投屏</string><string name="screen_msg1">扫码或浏览器输入\ntranscreen.app下\n载客户端</string><string name="screen_pin_code">投屏码：%s</string><string name="screen_pin_code2">投屏码</string><string name="screen_permission_refuse">拒绝</string><string name="screen_air_play">AirPlay</string><string name="not_network">网络未连通</string><string name="disconnect">未连接</string><string name="no_sign">无信号</string><string name="current_sign">当前通道 [内置电脑]</string><string name="current_sign_windows_disabled">当前通道 [内置电脑]（禁用）</string><string name="current_sign_hdmi">当前通道 [HDMI]</string><string name="current_sign_hdmi_1">当前通道 [HDMI 1]</string><string name="current_sign_hdmi_2">当前通道 [HDMI 2]</string><string name="current_sign_typec">当前通道 [USB C]</string><string name="change_to_pc">切换到内置电脑</string><string name="start_computer">启动内置电脑</string><string name="no_sign_msg">未识别到信号，可以尝试启动内置电脑\n如需帮助，请拨打服务热线：************</string><string name="no_sign_windows_disabled">内置电脑通道已禁用，可以在“设置-更多”启用\n如需帮助，请拨打服务热线：************</string><string name="no_sign_hdmi">未识别到信号，请检查信号线是否连接正常\n如需帮助，请拨打服务热线：************</string><string name="no_pc_1">未检测到电脑模块，请关机后确保电脑模\n块正确安装，然后尝试重启大屏(错误 01）\n如需帮助，请拨打服务热线：************</string><string name="no_pc_2">未检测到电脑模块，请关机后确保电脑模\n块正确安装，然后尝试重启大屏(错误 02）\n如需帮助，请拨打服务热线：************</string><string name="home">首页</string><string name="annotate">批注</string><string name="projection">投屏</string><string name="hint_no_control">当前信号源暂时无法小屏控制</string><string name="privacy">隐私保护中</string><string name="ready_install">正在准备安装，请稍候...</string><string name="install_ing">正在安装中(%d%%)，请稍后...</string><string name="install_fail">系统安装失败</string><string name="install_fail_reinstall">本次安装失败，您可尝试重新安装</string><string name="reinstall">重新安装</string><string name="have_update">大屏系统有新版本可以更新</string><string name="have_update_hint">-系统更新需要重新启动\n-本次更新修复了一些重大问题，为保障您的正\n常使用，建议您立即更新</string><string name="lastest_version">已经是最新版本</string><string name="system_lastest_version">系统版本已经是最新版本</string><string name="updatable">可更新至：</string><string name="reset_ops">本次还原大约需要5分钟，在此期间请勿断电</string><string name="eeo_screen_move_txt">点击上方或上滑恢复全屏</string><string name="extra">更多</string><string name="wireless_screen">无线投屏</string><string name="write_without_screen_on">落笔直接写</string><string name="breath_led_on">Logo灯常亮</string><string name="touch_slider">半屏滑条</string><string name="windows_disable">内置电脑通道禁用</string><string name="windows_task_manager">Windows任务管理器</string><string name="factory_menu">工厂菜单</string><string name="debug_menu">调试菜单</string><string name="touch_calibration">触摸框校准</string><string name="login_title">需要登录</string><string name="login">登录</string><string name="login_cancel">取消</string><string name="adb_title">隐藏设置</string><string name="adb">adb开关</string><string name="stroke_algorithm">笔锋算法</string><string name="write_acceleration">板书加速</string><string name="r30_write_speed">R30书写预测</string><string name="screen_activation_status_getting">读取中...</string><string name="screen_activation_status_activated">已激活</string><string name="screen_activation_status_nonactivated">未激活</string><string name="language_and_locale">语言</string><string name="language_change">语言切换</string><string name="simplified_chinese">简体中文</string><string name="english">English</string><string-array name="languages">
        <item>简体中文</item>
        <item>English</item>
    </string-array><string name="startup_channel">开机信号源</string><string-array name="startup_channels">
        <item>上次关机信号源</item>
        <item>内置电脑</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array><string-array name="startup_channels_windows_disabled">
        <item>上次关机信号源</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array><string name="toast_resolution">%s   %dX%d @ %dHz</string><string name="uhd_hdmi">请选择 HDMI 2.0 以上符合\n“超高清”信号的线材。</string><string name="uhd_type_c">请选择 全功能Type-C 符合\n“超高清”信号的线材。</string><string name="toast_shutting_down_ops">内置电脑关机中</string><string name="color_temperature_adjust">色温调节</string><string name="color_temperature">色温</string><string name="color_temperature_cold">冷</string><string name="color_temperature_warm">暖</string><string name="rgb_red_gain">R-红色增益</string><string name="rgb_green_gain">G-绿色增益</string><string name="rgb_blue_gain">B-蓝色增益</string><string name="rgb_reset">恢复默认参数</string><string name="color_temperature_value_wrong">色温值为%d~%d</string><string name="rgb_red_gain_value_wrong">R值为0~255</string><string name="rgb_green_gain_value_wrong">G值为0~255</string><string name="rgb_blue_gain_value_wrong">B值为0~255</string><string name="color_temperature_eye_care">处于护眼模式</string><string name="hardware_self_test">硬件一键自检</string><string name="hardware_self_test_no_test">未检测</string><string name="hardware_self_test_testing">检测中...</string><string name="hardware_self_test_success">正常</string><string name="hardware_self_test_fail">失败</string><string name="hardware_self_test_CPU">CPU</string><string name="hardware_self_test_memory">内存</string><string name="hardware_self_test_hard_disk">硬盘</string><string name="hardware_self_test_touch">触摸框</string><string name="hardware_self_test_ethernet">有线网络</string><string name="hardware_self_test_wifi">无线网络</string><string name="hardware_self_test_mic">麦克风</string><string name="desktop">桌面</string><string name="maximize">最大化</string></file><file name="accessibility_service_config" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="desktop" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\desktop.xml" qualifiers="" type="drawable"/><file name="maximize" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\maximize.xml" qualifiers="" type="drawable"/><file name="select_main_desktop_icon" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable\select_main_desktop_icon.xml" qualifiers="" type="drawable"/><file name="ic_desktop_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_desktop_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_desktop_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_desktop_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_desktop_s_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_desktop_s_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_desktop_s_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_desktop_s_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_maximize_s_h" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_maximize_s_h.png" qualifiers="nodpi-v4" type="drawable"/><file name="ic_maximize_s_n" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\drawable-nodpi\ic_maximize_s_n.png" qualifiers="nodpi-v4" type="drawable"/><file name="activity_main_desktop" path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\main\res\layout\activity_main_desktop.xml" qualifiers="" type="layout"/></source><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\build\generated\res\rs\debug"/><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ClassIn_Board_S_Pro\code\t982-app-for-2.0\systemsetting\src\debug\res"/></dataSet><mergedItems/></merger>