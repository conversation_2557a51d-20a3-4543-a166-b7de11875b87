<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_bg"
    tools:context=".UserAgreementFragment"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="485dp"
        android:layout_marginTop="221dp">

        <LinearLayout
            android:id="@+id/ll_user_agreement"
            android:layout_width="match_parent"
            android:layout_height="21dp"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            tools:ignore="MissingConstraints">

            <ImageView
                android:id="@+id/back_ic"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_arrow_left_w"
                android:visibility="visible">

            </ImageView>

            <TextView
                android:id="@+id/txt_wireless_network"
                android:layout_width="wrap_content"
                android:layout_height="21dp"
                android:layout_marginLeft="27dp"
                android:text="@string/user_agreement"
                android:textColor="@color/white_100"
                android:textSize="16sp"
                android:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            app:layout_constraintTop_toBottomOf="@+id/ll_user_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="37dp"
            android:orientation="vertical"
            tools:ignore="MissingConstraints">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/user_agreement_txt"
                android:scrollbarThumbVertical="@drawable/custom_scrollbar_thumb"
                android:layout_width="309dp"
                android:layout_height="153dp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"
                tools:ignore="MissingConstraints">
                <WebView
                    android:id="@+id/webView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="8dp"
                    />
                <!-- 用户协议内容 -->
                <!--<TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="9sp"
                    android:text="@string/user_agreement_content" />
-->            </androidx.core.widget.NestedScrollView>

            <!-- 确认按钮，位于滑动框的外部 -->
            <Button
                android:id="@+id/btnConfirm"
                style="@style/MyButtonStyle"
                android:layout_width="88dp"
                android:layout_height="28dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="111dp"
                android:layout_marginTop="37dp"
                android:enabled="false"
                android:text="@string/agree_txt"
                tools:ignore="MissingConstraints" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>