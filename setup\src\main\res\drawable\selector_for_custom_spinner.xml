<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下状态 -->
    <item  android:state_pressed="true" android:textSize="9sp" android:gravity="end">
        <shape android:shape="rectangle">
            <!-- 填充颜色 -->
            <solid android:color="#4DFFFFFF"/>
            <!-- 矩形的圆角半径 -->
            <corners android:radius="6dp" />
        </shape>

    </item>
    <!-- 普通状态 -->
    <item  android:state_focused="false" android:state_pressed="false" android:textSize="9sp" android:gravity="end">
        <shape android:shape="rectangle" android:width="237dp" android:end="0dp">
            <corners android:radius="8dp" />
            <solid android:color="#4DFFFFFF" />
        </shape>
    </item>



</selector>