# 子设备OTA升级统一管理实现计划

## 1. 项目背景

### 1.1 当前问题
- **USB Hub冲突**：触摸框和阵列麦克风升级并行执行，USB切换命令冲突导致升级失败
- **启动时机不合理**：阵列麦克风升级服务在每次应用启动时都会触发
- **缺乏统一管理**：两个升级流程独立运行，没有状态同步机制
- **用户界面问题**：阵列麦克风升级时未阻止用户引导界面

### 1.2 解决方案
采用**顺序执行 + 统一入口 + SP Key状态管理**的方案，实现触摸框和阵列麦克风升级的统一管理。

## 2. 核心设计原则

### 2.1 升级顺序
**触摸框升级 → 阵列麦克风升级**（顺序执行，避免USB Hub冲突）

### 2.2 启动入口
- **setup模块**：U盘刷机后第一次开机
- **systemsetting模块**：Android系统OTA后开机或普通开机

### 2.3 状态管理
通过SP Key记录阵列麦克风升级状态，控制后续开机是否需要启动升级服务。

## 3. SP Key设计

### 3.1 新增SP Key
参考现有代码中的boolean类型SP Key设计（如KEY_SHOW_UPDATE_DIALOG），采用boolean类型：
```java
// 在SharedPreferencesUtil.java中新增
private static final String KEY_ARRAY_MIC_UPDATE_COMPLETED = "array_mic_update_completed";

public static boolean isArrayMicUpdateCompleted(Context context) {
    return getBoolean(context, KEY_ARRAY_MIC_UPDATE_COMPLETED, false);
}

public static void setArrayMicUpdateCompleted(Context context, boolean completed) {
    setBoolean(context, KEY_ARRAY_MIC_UPDATE_COMPLETED, completed);
}
```

### 3.2 状态逻辑
- **false（默认值）**：U盘刷机后首次开机默认值，需要启动阵列麦升级服务
- **true**：阵列麦升级服务已成功完成（版本检查成功不需要升级，或升级成功），后续开机不启动服务
- **false（失败后设置）**：阵列麦升级服务失败（版本检查失败或升级失败），下次开机需要重试

### 3.3 SP Key在不同场景的行为
- **U盘刷机**：会清除所有SP数据，KEY_ARRAY_MIC_UPDATE_COMPLETED默认为false
- **在线OTA**：不会影响SP数据，KEY_ARRAY_MIC_UPDATE_COMPLETED保持原值
- **普通开机**：SP数据保持不变

## 4. 启动入口分析

### 4.1 setup模块（U盘刷机后第一次开机）

#### 4.1.1 触摸框不需要升级
**位置**：`setup/MainActivity.java` 第133-135行
```java
} else {
    mMainView.setVisibility(View.VISIBLE);  // 当前逻辑
}
```
**修改为**：
```java
} else {
    // 触摸框不需要升级，但需要启动阵列麦克风升级服务
    Log.d("ArrayMicOTA", "Touch update not needed, starting array mic update service");
    mMainView.setVisibility(View.GONE);  // 阻止进入用户引导界面
    startArrayMicUpdateService();
}
```

#### 4.1.2 触摸框升级成功回调
**位置**：`setup/MainActivity.java` 第84-88行
```java
@Override
public void onUpdateSuccess() {
    // 当前为空实现
}
```
**修改为**：
```java
@Override
public void onUpdateSuccess() {
    Log.d("ArrayMicOTA", "Touch update success, starting array mic update service");
    // 触摸框升级成功，启动阵列麦克风升级服务
    startArrayMicUpdateService();
}
```

#### 4.1.3 触摸框升级失败回调
**位置**：`setup/MainActivity.java` 第90-96行
```java
@Override
public void onUpdateFail(String errMsg) {
    unbindSubDeviceUpdateService();
    //更新失败或者没有更新：执行检查到有更新前的流程
    mMainView.setVisibility(View.VISIBLE);
}
```
**修改为**：
```java
@Override
public void onUpdateFail(String errMsg) {
    unbindSubDeviceUpdateService();
    Log.d("ArrayMicOTA", "Touch update failed, starting array mic update service");
    // 触摸框升级失败，仍需启动阵列麦克风升级服务
    startArrayMicUpdateService();
}
```

### 4.2 systemsetting模块（OTA后开机或普通开机）

#### 4.2.1 触摸框不需要升级
**位置**：`systemsetting/launcher/FallbackHomeActivity.java` 第121-124行
```java
} else {
    startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
    finish();
}
```
**修改为**：
```java
} else {
    // 触摸框不需要升级，检查SP Key决定是否启动阵列麦克风升级服务
    Log.d("ArrayMicOTA", "Touch update not needed, checking SP key for array mic update");
    if (shouldStartArrayMicUpdate()) {
        Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service");
        startArrayMicUpdateService();
    }
    // 不管是否启动阵列麦升级服务，都要正常进入后续流程
    Log.d("ArrayMicOTA", "Proceeding to TifPlayerActivity");
    startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
    finish();
}
```

#### 4.2.2 触摸框升级成功回调
**位置**：`systemsetting/launcher/FallbackHomeActivity.java` 第37-53行
**修改为**：
```java
@Override
public void onUpdateSuccess() {
    // 保持原有逻辑完全不变
    //恢复开机通道
    String bootForceSource = CommonUtils.getBootForceSource(FallbackHomeActivity.this);
    if (bootForceSource.equals(Constant.FORCE_SOURCE_NONE)) {
        //升级触摸框时切到了Android通道，这里记录回之前的信号源
        if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_PC)) {
            SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_PC);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI1)) {
            SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_HDMI1);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI2)) {
            SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_HDMI2);
        } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_TYPEC)) {
            SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_TYPEC);
        }
    }
    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);

    // 新增：触摸框升级成功，检查SP Key决定是否启动阵列麦克风升级服务
    Log.d("ArrayMicOTA", "Touch update success, checking SP key for array mic update");
    if (shouldStartArrayMicUpdate()) {
        Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service");
        startArrayMicUpdateService();
    } else {
        Log.d("ArrayMicOTA", "SP key indicates array mic update completed, no need to start service");
    }
}
```

#### 4.2.3 触摸框升级失败回调
**位置**：`systemsetting/launcher/FallbackHomeActivity.java` 第56-64行
**修改为**：
```java
@Override
public void onUpdateFail(String errMsg) {
    unbindSubDeviceUpdateService();

    // 新增：触摸框升级失败，检查SP Key决定是否启动阵列麦克风升级服务
    Log.d("ArrayMicOTA", "Touch update failed, checking SP key for array mic update");
    if (shouldStartArrayMicUpdate()) {
        Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service");
        startArrayMicUpdateService();
    } else {
        Log.d("ArrayMicOTA", "SP key indicates array mic update completed, no need to start service");
    }

    // 保持原有逻辑完全不变
    //更新失败或者没有更新：执行检查到有更新前的流程
    //前面临时开机通道切了android，这里要启动ops
    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
    EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
    startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
    finish();
}
```

## 5. 阵列麦克风升级服务集成

### 5.1 移除自动启动机制
- 移除`EeoApplication.onCreate()`中的广播发送
- 移除`ArrayMicUpdateCheckReceiver`的自动触发

### 5.2 阵列麦克风升级服务结果处理
在`ArrayMicFirmwareUpdater.java`中的关键位置添加SP Key设置，不改变原有逻辑：

```java
// 在ArrayMicFirmwareUpdater.java的validateFinalVersion()方法中添加
private void validateFinalVersion() {
    Log.d(TAG, "Validating final version...");
    String newVersion = mAdbHelper.executeShellCommandWithOutput("adb -s " + ADB_DEVICE_SERIAL + " shell cat /usr/bin/qdreamer/qsound/version.txt");

    if (newVersion != null && newVersion.trim().equals(mTargetVersion)) {
        Log.i(TAG, "Update successful! New version: " + newVersion.trim());
        // 新增：设置SP Key为成功状态
        Log.d(TAG, "ArrayMicOTA: Setting SP key to completed (success)");
        SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, true);
        // 保持原有逻辑
        if (mCallback != null) {
            mCallback.onUpdateSuccess();
        }
    } else {
        // 保持原有逻辑，在fail()方法中会设置SP Key
        fail("Final version validation failed. Expected " + mTargetVersion + ", but got " + (newVersion != null ? newVersion.trim() : "null"));
    }
    mCurrentState = UpdateState.CLEANUP;
    executeNextStep();
}

// 在ArrayMicFirmwareUpdater.java的checkVersionAndDecide()方法中添加
private void checkVersionAndDecide() {
    // ... 保持现有版本检查逻辑不变 ...

    if (isLower || isSpecificError) {
        Log.i(TAG, "Update required. Proceeding with update...");
        mCurrentState = UpdateState.STOPPING_SERVICE;
        executeNextStep();
    } else {
        Log.i(TAG, "No update required. Cleaning up...");
        // 新增：不需要升级，设置SP Key为成功状态
        Log.d(TAG, "ArrayMicOTA: Setting SP key to completed (no update needed)");
        SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, true);
        // 保持原有逻辑
        mCurrentState = UpdateState.CLEANUP;
        executeNextStep();
    }
}

// 在ArrayMicFirmwareUpdater.java的fail()方法中添加
private void fail(String message) {
    if (mCurrentState != UpdateState.CLEANUP) {
        Log.e(TAG, "Update failed: " + message);
        // 新增：升级失败，设置SP Key为失败状态
        Log.d(TAG, "ArrayMicOTA: Setting SP key to not completed (failed)");
        SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, false);
        // 保持原有逻辑
        if (mCallback != null) {
            mCallback.onUpdateFail(message);
        }
        mCurrentState = UpdateState.CLEANUP;
        executeNextStep();
    }
}
```

### 5.3 setup模块的阵列麦克风升级服务启动方法
```java
// 在setup/MainActivity.java中新增
private void startArrayMicUpdateService() {
    Log.d("ArrayMicOTA", "Starting array mic update service in setup module");
    Intent intent = new Intent(this, ArrayMicUpdateService.class);
    ArrayMicUpdateService.setExternalCallback(mArrayMicUpdateCallback);
    startService(intent);
}

private SubDeviceUpdateCallback mArrayMicUpdateCallback = new SubDeviceUpdateCallback() {
    @Override
    public void onUpdateSuccess() {
        Log.d("ArrayMicOTA", "Array mic update success in setup module");
        // 允许进入用户引导界面
        mMainView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onUpdateFail(String errMsg) {
        Log.e("ArrayMicOTA", "Array mic update failed in setup module: " + errMsg);
        // 允许进入用户引导界面（即使失败也要让用户继续使用）
        mMainView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onAllUpdateFinish() {
        Log.d("ArrayMicOTA", "Array mic update all finished in setup module");
        // 允许进入用户引导界面
        mMainView.setVisibility(View.VISIBLE);
    }
};
```

### 5.4 systemsetting模块的SP Key检查和服务启动逻辑
```java
// 在systemsetting/launcher/FallbackHomeActivity.java中新增
private boolean shouldStartArrayMicUpdate() {
    boolean completed = SharedPreferencesUtil.isArrayMicUpdateCompleted(this);
    Log.d("ArrayMicOTA", "Checking SP key: array mic update completed = " + completed);
    // 只有在未完成状态时才需要启动升级服务
    return !completed;
}

private void startArrayMicUpdateService() {
    Log.d("ArrayMicOTA", "Starting array mic update service in systemsetting module");
    Intent intent = new Intent(this, ArrayMicUpdateService.class);
    ArrayMicUpdateService.setExternalCallback(mArrayMicUpdateCallback);
    startService(intent);
}

private SubDeviceUpdateCallback mArrayMicUpdateCallback = new SubDeviceUpdateCallback() {
    @Override
    public void onUpdateSuccess() {
        Log.d("ArrayMicOTA", "Array mic update success in systemsetting module");
        // systemsetting模块中阵列麦升级完成后不需要特殊处理，系统会自然进入后续流程
    }

    @Override
    public void onUpdateFail(String errMsg) {
        Log.e("ArrayMicOTA", "Array mic update failed in systemsetting module: " + errMsg);
        // systemsetting模块中阵列麦升级失败后不需要特殊处理，系统会自然进入后续流程
    }

    @Override
    public void onAllUpdateFinish() {
        Log.d("ArrayMicOTA", "Array mic update all finished in systemsetting module");
        // systemsetting模块中阵列麦升级完成后不需要特殊处理，系统会自然进入后续流程
    }
};
```

## 6. 实现步骤

### 6.1 第一阶段：基础设施
1. 在`SharedPreferencesUtil.java`中新增阵列麦克风状态管理方法
2. 移除`EeoApplication.onCreate()`中的自动启动逻辑
3. 修改`ArrayMicFirmwareUpdater.java`中的状态回调，添加SP Key更新逻辑

### 6.2 第二阶段：setup模块改造
1. 修改`setup/MainActivity.java`的三个启动入口
2. 添加阵列麦克风升级服务启动方法
3. 添加阵列麦克风升级回调处理逻辑

### 6.3 第三阶段：systemsetting模块改造
1. 修改`systemsetting/launcher/FallbackHomeActivity.java`的三个检查入口
2. 添加SP Key检查逻辑
3. 添加阵列麦克风升级服务启动方法

### 6.4 第四阶段：测试验证
1. U盘刷机后第一次开机测试
2. Android系统OTA后开机测试
3. 普通开机测试
4. 各种升级成功/失败场景测试

## 7. 关键技术点

### 7.1 USB Hub冲突解决
通过顺序执行确保同一时间只有一个设备在进行升级，避免USB切换命令冲突。

### 7.2 用户界面控制
- **setup模块**：阵列麦升级期间阻止进入用户引导界面，升级完成后允许进入
- **systemsetting模块**：无需阻止用户界面，升级完成后直接进入TifPlayerActivity

### 7.3 状态持久化
使用SP Key记录升级状态，确保系统重启后仍能正确判断是否需要启动升级服务。

### 7.4 错误处理
即使升级失败，也要允许用户继续使用系统，但记录失败状态以便下次重试。

### 7.5 日志统一
所有新增代码使用统一的日志标识符"ArrayMicOTA"，便于日志分析和问题排查。

## 8. 预期效果

1. **解决USB冲突**：触摸框和阵列麦克风升级顺序执行，不再并行冲突
2. **优化启动时机**：只在必要时启动阵列麦克风升级服务
3. **统一状态管理**：通过SP Key实现升级状态的持久化管理
4. **改善用户体验**：升级期间正确控制用户界面，升级完成后正常进入系统
5. **便于维护**：统一的日志标识符和清晰的代码结构
