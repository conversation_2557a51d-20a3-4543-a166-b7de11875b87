package com.eeo.systemsetting.launcher;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.IBinder;
import android.os.SystemProperties;
import android.util.Log;

import com.eeo.ota.arraymic.ArrayMicUpdateService;
import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.dialog.UpdateDialog;
import com.eeo.ota.service.SubDeviceUpdateService;
import com.eeo.ota.touch.SubDeviceUpdate;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.udisdk.UdiConstant;

public class FallbackHomeActivity extends Activity {
    private static final String TAG = "FallbackHome";

    private boolean mHasBound;
    private int mUpdateProgress = -1;
    private SubDeviceUpdateService.MyBinder mBinder;
    private SubDeviceUpdateService mSubDeviceUpdateService;
    private ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mBinder = (SubDeviceUpdateService.MyBinder) service;
            if (mBinder != null) {
                mSubDeviceUpdateService = mBinder.getService();
                mSubDeviceUpdateService.setSubDeviceUpdateListener(new SubDeviceUpdateCallback() {
                    @Override
                    public void onUpdateSuccess() {
                        //恢复开机通道
                        String bootForceSource = CommonUtils.getBootForceSource(FallbackHomeActivity.this);
                        if (bootForceSource.equals(Constant.FORCE_SOURCE_NONE)) {
                            //升级触摸框时切到了Android通道，这里记录回之前的信号源
                            if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_PC)) {
                                SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_PC);
                            } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI1)) {
                                SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_HDMI1);
                            } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_HDMI2)) {
                                SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_HDMI2);
                            } else if (EeoApplication.mBootSource.equals(Constant.FORCE_SOURCE_TYPEC)) {
                                SystemProperties.set(Constant.PROP_LAST_SOURCE, Constant.SOURCE_ID_TYPEC);
                            }
                        }
                        SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);

                        // Touch upgrade success, reboot flag already set in SubDeviceUpdate
                        // Check SP Key to determine whether to start array microphone upgrade service
                        Log.d("ArrayMicOTA", "Touch update success, checking SP key for array mic update");
                        if (shouldStartArrayMicUpdate()) {
                            Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service and waiting for completion");
                            startArrayMicUpdateService();
                        } else {
                            Log.d("ArrayMicOTA", "SP key indicates array mic update completed, touch success requires reboot");
                            // Touch upgrade success but no array mic service needed, reboot directly
                            Util.reboot(FallbackHomeActivity.this, true);
                        }
                    }

                    @Override
                    public void onUpdateFail(String errMsg) {
                        unbindSubDeviceUpdateService();

                        // Touch upgrade failed, no reboot flag set
                        // Check SP Key to determine whether to start array microphone upgrade service
                        Log.d("ArrayMicOTA", "Touch update failed, checking SP key for array mic update");
                        if (shouldStartArrayMicUpdate()) {
                            Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service and waiting for completion");
                            startArrayMicUpdateService();
                        } else {
                            Log.d("ArrayMicOTA", "SP key indicates array mic update completed, proceeding to TifPlayerActivity");
                            startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
                            finish();
                        }

                        //更新失败或者没有更新：执行检查到有更新前的流程
                        //前面临时开机通道切了android，这里要启动ops
                        SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
                        EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
                        startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
                        finish();
                    }

                    @Override
                    public void onAllUpdateFinish() {

                    }

                    @Override
                    public void onUpdateProgressChanged(int progress) {
                        Log.d(TAG, "onUpdateProgressChanged: " + progress);
                        if (mUpdateProgress < 0) {
                            SystemProperties.set("persist.sys.bootanim.exit", "1");
                        }
                        mUpdateProgress = progress;
                    }
                });
                mSubDeviceUpdateService.updateSubDevice();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mBinder = null;
            mSubDeviceUpdateService = null;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        handleFromSetup();
        handleBootSource();
        handleAutoSwitchChannel();
        maybeFinish();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    public void maybeFinish() {
        //进入TifPlayerActivity前先检测是否需要升级触摸框等子设备，升级完才让用户使用
        if (Util.shouldShowUpdateDialog(this) || checkSubDeviceUpdate()) {
            SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_ANDROID);
            EeoApplication.mShouldHandleBootSource = true;
            bindSubDeviceUpdateService();
        } else {
            // The touch does not require an upgrade, check SP Key to determine whether to start the array microphone upgrade service
            Log.d("ArrayMicOTA", "Touch update not needed, checking SP key for array mic update");
            if (shouldStartArrayMicUpdate()) {
                Log.d("ArrayMicOTA", "SP key indicates array mic update needed, starting service and waiting for completion");
                startArrayMicUpdateService();
                // Do not immediately enter TifPlayerActivity and wait for the callback of array microphone upgrade completion
            } else {
                Log.d("ArrayMicOTA", "SP key indicates array mic update completed, proceeding to TifPlayerActivity");
                startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
                finish();
            }
        }
    }

    /**
     * 开机通道相关
     * 未插ops或者目标通道最终是ops的，在这里处理即可
     * 其它的需要在TifPlayerActivity待ops信号变化后再做处理
     */
    public void handleBootSource() {
        String bootForceSource = CommonUtils.getBootForceSource(this);
        String bootSourceId = SystemProperties.get(Constant.PROP_LAST_SOURCE);
        if (bootForceSource.equals(Constant.FORCE_SOURCE_PC) ||
                bootForceSource.equals(Constant.FORCE_SOURCE_ANDROID)) {
            //目标通道是ops的，直接开机通道设为ops即可
            SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
        } else if (bootForceSource.equals(Constant.FORCE_SOURCE_HDMI1) ||
                bootForceSource.equals(Constant.FORCE_SOURCE_HDMI2) ||
                bootForceSource.equals(Constant.FORCE_SOURCE_TYPEC)) {
            if (CommonUtils.isOpsInserted()) {
                //目标通道非ops且插了ops的，开机通道先设为ops，启动后再在TifPlayerActivity切换到目标通道
                SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
                EeoApplication.mShouldHandleBootSource = true;
            } else {
                //目标通道非ops且未插ops的，直接开机通道设为目标通道即可
                SystemProperties.set(Constant.PROP_FORCE_SOURCE, bootForceSource);
            }
            EeoApplication.mBootSource = bootForceSource;
        } else if (bootForceSource.equals(Constant.FORCE_SOURCE_NONE)) {
            if (bootSourceId.equals(Constant.SOURCE_ID_PC)) {
                //目标通道是ops的，直接开机通道设为ops即可
                SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
            } else if (bootSourceId.equals(Constant.SOURCE_ID_HDMI1)) {
                if (CommonUtils.isOpsInserted()) {
                    //目标通道非ops且插了ops的，开机通道先设为ops，启动后再在TifPlayerActivity切换到目标通道
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
                    EeoApplication.mShouldHandleBootSource = true;
                } else {
                    //目标通道非ops且未插ops的，直接开机通道设为目标通道即可
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_HDMI1);
                }
                EeoApplication.mBootSource = Constant.FORCE_SOURCE_HDMI1;
            } else if (bootSourceId.equals(Constant.SOURCE_ID_HDMI2)) {
                if (CommonUtils.isOpsInserted()) {
                    //目标通道非ops且插了ops的，开机通道先设为ops，启动后再在TifPlayerActivity切换到目标通道
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
                    EeoApplication.mShouldHandleBootSource = true;
                } else {
                    //目标通道非ops且未插ops的，直接开机通道设为目标通道即可
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_HDMI2);
                }
                EeoApplication.mBootSource = Constant.FORCE_SOURCE_HDMI2;
            } else if (bootSourceId.equals(Constant.SOURCE_ID_TYPEC)) {
                if (CommonUtils.isOpsInserted()) {
                    //目标通道非ops且插了ops的，开机通道先设为ops，启动后再在TifPlayerActivity切换到目标通道
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
                    EeoApplication.mShouldHandleBootSource = true;
                } else {
                    //目标通道非ops且未插ops的，直接开机通道设为目标通道即可
                    SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_TYPEC);
                }
                EeoApplication.mBootSource = Constant.FORCE_SOURCE_TYPEC;
            } else {
                SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
            }
        }
        Log.d(TAG, "handleBootSource: bootForceSource=" + bootForceSource + " ,bootSourceId=" + bootSourceId
                + " ,mBootSource=" + EeoApplication.mBootSource + " ,mShouldHandleBootSource=" + EeoApplication.mShouldHandleBootSource);
    }

    /**
     * 通道插入自动跳转
     * 开机过程、开机通道切换前先关闭插入跳转
     */
    public void handleAutoSwitchChannel() {
        //记录之前的状态
        String autoSwitchChannel = CommonUtils.getAutoSwitchChannel(this);
        //先关闭自动跳转
        SystemProperties.set(Constant.PROP_AUTO_SWITCH_CHANNEL, Constant.PROP_AUTO_SWITCH_CHANNEL_DISABLE);
        Log.d(TAG, "handleAutoSwitchChannel: autoSwitchChannel=" + autoSwitchChannel + "->" + Constant.PROP_AUTO_SWITCH_CHANNEL_DISABLE);
    }

    /**
     * 从开机向导跳转后，需要执行一些操作：如执行无线投屏相关操作
     */
    public void handleFromSetup() {
        if (getIntent() != null && "setup".equals(getIntent().getStringExtra("from"))) {
            CommonUtils.getAndSetActivatedData(this);
            CommonUtils.checkAndSetWirelessScreen(this);
            //第一次开机后在这里初始化开机通道，避免第一次开机启动ops声音过大
            SystemProperties.set(Constant.PROP_FORCE_SOURCE, Constant.FORCE_SOURCE_PC);
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
        }
    }


    private boolean checkSubDeviceUpdate() {
        SubDeviceUpdate subDeviceUpdate = new SubDeviceUpdate(this, null);
        return subDeviceUpdate.checkUpdate();
    }

    private void bindSubDeviceUpdateService() {
        if (mHasBound) {
            return;
        }
        Log.d(TAG, "bindSubDeviceUpdateService: ");
        Intent intent = new Intent(this, SubDeviceUpdateService.class);
        bindService(intent, mServiceConnection, BIND_AUTO_CREATE);
        mHasBound = true;
    }

    private void unbindSubDeviceUpdateService() {
        if (mHasBound) {
            unbindService(mServiceConnection);
            mHasBound = false;
        }
    }

    private boolean shouldStartArrayMicUpdate() {
        boolean completed = SharedPreferencesUtil.isArrayMicUpdateCompleted(this);
        Log.d("ArrayMicOTA", "Checking SP key: array mic update completed = " + completed);
        // Only need to start the upgrade service in the incomplete state
        return !completed;
    }

    private void startArrayMicUpdateService() {
        Log.d("ArrayMicOTA", "Starting array mic update service in systemsetting module");

        // Show updating dialog for array mic service
        UpdateDialog.showUpdatingDialog(this);
        Log.d("ArrayMicOTA", "Showing update dialog for array mic service");

        Intent intent = new Intent(this, ArrayMicUpdateService.class);
        ArrayMicUpdateService.setExternalCallback(mArrayMicUpdateCallback);
        startService(intent);
    }

    private SubDeviceUpdateCallback mArrayMicUpdateCallback = new SubDeviceUpdateCallback() {
        @Override
        public void onUpdateSuccess() {
            Log.d("ArrayMicOTA", "Array mic update success in systemsetting module");
            // Set array mic upgrade success flag
            if (mSubDeviceUpdateService != null) {
                SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
                if (subDeviceUpdate != null) {
                    subDeviceUpdate.setArrayMicUpdateSuccess(true);
                }
            }
            // Close update dialog and execute reboot check or proceed
            closeUpdateDialogAndExecuteRebootOrProceed();
        }

        @Override
        public void onUpdateFail(String errMsg) {
            Log.e("ArrayMicOTA", "Array mic update failed in systemsetting module: " + errMsg);
            // Array mic upgrade failed, do not set success flag
            // Close update dialog and execute reboot check or proceed
            closeUpdateDialogAndExecuteRebootOrProceed();
        }

        @Override
        public void onAllUpdateFinish() {
            Log.d("ArrayMicOTA", "Array mic update all finished in systemsetting module");
            // Close update dialog and execute reboot check or proceed
            closeUpdateDialogAndExecuteRebootOrProceed();
        }
    };

    private void closeUpdateDialogAndExecuteRebootOrProceed() {
        // Close update dialog
        UpdateDialog.dismissUpdatingDialog();
        Log.d("ArrayMicOTA", "Update dialog dismissed after array mic service completion");

        // Execute reboot check or proceed to TifPlayerActivity
        executeRebootOrProceed();
    }

    private void executeRebootOrProceed() {
        if (mSubDeviceUpdateService != null) {
            SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
            if (subDeviceUpdate != null) {
                if (subDeviceUpdate.shouldRebootAfterAllUpdates()) {
                    Log.d("ArrayMicOTA", "Rebooting due to successful upgrade (touch or array mic)");
                    Util.reboot(this, true);
                } else {
                    Log.d("ArrayMicOTA", "No successful upgrades, proceeding to TifPlayerActivity");
                    proceedToTifPlayerActivity();
                }
            } else {
                Log.w("ArrayMicOTA", "SubDeviceUpdate instance is null, proceeding to TifPlayerActivity");
                proceedToTifPlayerActivity();
            }
        } else {
            Log.w("ArrayMicOTA", "SubDeviceUpdateService is null, proceeding to TifPlayerActivity");
            proceedToTifPlayerActivity();
        }
    }

    /**
     * Determine whether to restart based on the upgrade result of the touch
     */
    private void executeDelayedRebootAfterArrayMicService() {
        if (mSubDeviceUpdateService != null) {
            SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
            if (subDeviceUpdate != null) {
                Log.d("ArrayMicOTA", "Executing delayed reboot check after array mic service completion");
                subDeviceUpdate.executeRebootAfterArrayMicService();
            } else {
                Log.w("ArrayMicOTA", "SubDeviceUpdate instance is null, cannot execute delayed reboot");
            }
        } else {
            Log.w("ArrayMicOTA", "SubDeviceUpdateService is null, cannot execute delayed reboot");
        }
    }

    /**
     * After the array microphone upgrade is completed, enter TifPlayerActivity
     */
    private void proceedToTifPlayerActivity() {
        Log.d("ArrayMicOTA", "Proceeding to TifPlayerActivity after array mic service completion");
        startActivity(new Intent(getApplicationContext(), TifPlayerActivity.class));
        finish();
    }
}
