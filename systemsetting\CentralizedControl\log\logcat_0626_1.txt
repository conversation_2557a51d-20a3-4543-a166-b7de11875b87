2025-06-26 10:41:21.022   867-1274  SerialManager           com.eeo.systemsetting                I  === Windows Data Received ===
2025-06-26 10:41:21.022   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: Length=3 bytes
2025-06-26 10:41:21.024   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: HEX=FF FF 0A
2025-06-26 10:41:21.024   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: Raw bytes=[-1, -1, 10]
2025-06-26 10:41:21.024   867-1274  SerialManager           com.eeo.systemsetting                I  ==============================
2025-06-26 10:41:21.025   867-1274  OpsCommManager          com.eeo.systemsetting                D  onDataReceived: Received data, length=3
2025-06-26 10:41:21.025   867-1274  CommandParser           com.eeo.systemsetting                I  === Starting Command Parse ===
2025-06-26 10:41:21.025   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input data length=3
2025-06-26 10:41:21.028   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input HEX=FF FF 0A
2025-06-26 10:41:21.028   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input raw=[-1, -1, 10]
2025-06-26 10:41:21.028   867-1274  CommandParser           com.eeo.systemsetting                I  ==============================
2025-06-26 10:41:21.028   867-1274  CommandParser           com.eeo.systemsetting                E  parseCommand: Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.028   867-1274  CommandParser           com.eeo.systemsetting                W  createErrorResponse: Creating error response, command type: UNKNOWN, error: Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.028   867-1274  OpsCommManager          com.eeo.systemsetting                E  processReceivedData: Command parse failed=Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.028   867-1274  OpsCommManager          com.eeo.systemsetting                W  sendErrorResponse: Sending error response
2025-06-26 10:41:21.030   867-1274  ResponseBuilder         com.eeo.systemsetting                W  buildErrorResponse: Building error response, subFunction=0x04, errorCode=0x00
2025-06-26 10:41:21.030   867-1274  SerialManager           com.eeo.systemsetting                I  === Sending Data to Windows ===
2025-06-26 10:41:21.030   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Length=13 bytes
2025-06-26 10:41:21.039   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: HEX=7F 0B 99 A2 B3 C4 02 FF F3 04 00 CF 00
2025-06-26 10:41:21.040   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Raw bytes=[127, 11, -103, -94, -77, -60, 2, -1, -13, 4, 0, -49, 0]
2025-06-26 10:41:21.040   867-1274  SerialManager           com.eeo.systemsetting                I  ===============================
2025-06-26 10:41:21.040   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Data sent successfully to Windows
2025-06-26 10:41:21.050   867-1274  SerialManager           com.eeo.systemsetting                I  === Windows Data Received ===
2025-06-26 10:41:21.051   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: Length=7 bytes
2025-06-26 10:41:21.055   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: HEX=99 A2 B3 C4 02 FF F1
2025-06-26 10:41:21.056   867-1274  SerialManager           com.eeo.systemsetting                I  receiveData: Raw bytes=[-103, -94, -77, -60, 2, -1, -15]
2025-06-26 10:41:21.056   867-1274  SerialManager           com.eeo.systemsetting                I  ==============================
2025-06-26 10:41:21.056   867-1274  OpsCommManager          com.eeo.systemsetting                D  onDataReceived: Received data, length=7
2025-06-26 10:41:21.056   867-1274  CommandParser           com.eeo.systemsetting                I  === Starting Command Parse ===
2025-06-26 10:41:21.056   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input data length=7
2025-06-26 10:41:21.062   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input HEX=99 A2 B3 C4 02 FF F1
2025-06-26 10:41:21.062   867-1274  CommandParser           com.eeo.systemsetting                I  parseCommand: Input raw=[-103, -94, -77, -60, 2, -1, -15]
2025-06-26 10:41:21.063   867-1274  CommandParser           com.eeo.systemsetting                I  ==============================
2025-06-26 10:41:21.063   867-1274  CommandParser           com.eeo.systemsetting                E  parseCommand: Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.063   867-1274  CommandParser           com.eeo.systemsetting                W  createErrorResponse: Creating error response, command type: UNKNOWN, error: Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.063   867-1274  OpsCommManager          com.eeo.systemsetting                E  processReceivedData: Command parse failed=Data is null or too short, expected at least 9 bytes
2025-06-26 10:41:21.063   867-1274  OpsCommManager          com.eeo.systemsetting                W  sendErrorResponse: Sending error response
2025-06-26 10:41:21.067   867-1274  ResponseBuilder         com.eeo.systemsetting                W  buildErrorResponse: Building error response, subFunction=0x04, errorCode=0x00
2025-06-26 10:41:21.067   867-1274  SerialManager           com.eeo.systemsetting                I  === Sending Data to Windows ===
2025-06-26 10:41:21.067   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Length=13 bytes
2025-06-26 10:41:21.076   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: HEX=7F 0B 99 A2 B3 C4 02 FF F3 04 00 CF 00
2025-06-26 10:41:21.076   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Raw bytes=[127, 11, -103, -94, -77, -60, 2, -1, -13, 4, 0, -49, 0]
2025-06-26 10:41:21.076   867-1274  SerialManager           com.eeo.systemsetting                I  ===============================
2025-06-26 10:41:21.076   867-1274  SerialManager           com.eeo.systemsetting                I  sendData: Data sent successfully to Windows
