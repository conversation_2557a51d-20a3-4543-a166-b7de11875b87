package com.eeo.systemsetting.utils;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityManagerNative;
import android.app.IActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Environment;
import android.os.RemoteException;
import android.os.StatFs;
import android.os.SystemProperties;
import android.provider.Settings;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;

import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.activity.FinishMultiScreenActivity;
import com.eeo.systemsetting.activity.MainActivity;
import com.eeo.systemsetting.launcher.TifPlayerActivity;
import com.eeo.systemsetting.screen.ScreenManager;
import com.eeo.systemsetting.screen.ScreenUtil;
import com.eeo.systemsetting.screenoffset.ScreenMoveView;
import com.eeo.systemsetting.service.PopupAlertService;
import com.eeo.udisdk.UdiConstant;

import org.json.JSONArray;
import org.json.JSONException;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtils {
    public static final String TAG = "CommonUtils===";
    private static final int FAST_CLICK_TIME_MIN = 300; // 两次点击间隔不能少于300ms
    private static final int FAST_CLICK_TIME_2_S = 2000;
    private static long lastClickTime;

    /**
     * 这些Flag控制是否禁止五指聚拢召唤画中画
     */
    public static final String KEY_IS_ANNOTATION = "is_annotation";  //批注状态
    public static final String KEY_IS_LOCKED = "is_locked";  //触控锁
    public static final String KEY_NO_SIGNAL = "no_signal"; //无信号
    public static final String KEY_IS_DIALOG = "is_dialog"; //密码输入、windows还原、系统升级等提示框显示时
    public static final String KEY_IS_PROJECTION = "is_projection";  //画中画

    /**
     * 全局无线投屏开关
     * 供开机引导和设置使用
     */
    public static final String KEY_PROJECTION_STATUS = "projection_status";  //无线投屏开关

    /**
     * 设置触摸框是否可以触摸
     *
     * @param isTouch true，可以触摸；false：不可以触摸
     */
    public static void setTouchState(Context context, boolean isTouch) {
        Intent intent = new Intent();

        intent.setAction(Constant.SET_USB_ENABLE_ACTION);
        //标记广播是系统设置页面来的
//        intent.putExtra(Constant.USB2_TOUCH_MODULE, Constant.MODULE_SETTING);
        intent.setComponent(new ComponentName("com.eeo.systemsetting",
                "com.eeo.systemsetting.broadcast.SystemSettingBroadcast"));
        if (isTouch) {
            intent.putExtra(Constant.USB2_TOUCH_KEY, Constant.TOUCH);
        } else {
            intent.putExtra(Constant.USB2_TOUCH_KEY, Constant.UN_TOUCH);

        }
        context.sendBroadcast(intent);
    }

    /**
     * 禁用外部通道触摸
     * 这里统一处理外部通道触摸
     * 避免有其它弹窗时恢复了触摸
     */
    public static boolean enableOsd(Context context, boolean enable) {
        if (!enable) {
            if (EeoApplication.isShowMainDialog ||
                    EeoApplication.isShowInstallingDialog ||
                    EeoApplication.isShowHaveUpdateDialog ||
                    EeoApplication.isShowResetOpsDialog ||
                    EeoApplication.isShowScreenDialog ||
                    EeoApplication.isTifPlayerActivityStopped ||
                    EeoApplication.isLock ||
                    isMultiScreen(context) ||
                    isDialog(context) ||
                    isAnnotation(context)) {
                //显示着android弹窗时，不恢复外部通道触摸
                return false;
            }
        }
        return EeoApplication.udi.enableOsd(enable);
    }

    /**
     * 判断是否在点击框外
     *
     * @param activity
     * @param event
     * @return
     */
    public static boolean isOutOfBounds(Activity activity, MotionEvent event, int currentPage) {
        final int x = (int) event.getX();
        final int y = (int) event.getY();

        final int slop = ViewConfiguration.get(activity).getScaledWindowTouchSlop();
        final View decorView = activity.getWindow().getDecorView();
        if (Constant.IS_USERDEBUG) {
            Log.i(TAG, "isOutOfBounds: x : " + x + " , y : " + y + " , -slop : " + (-slop) + " , decorView.width : " + (decorView.getWidth() + slop) + " , decorView.height : " + (decorView.getHeight() + slop));
        }
        return (x < -slop) || (y < -slop)
                || (x > (decorView.getWidth() + slop))
                || (y > (decorView.getHeight() + slop))
                || !((x >= dp2px(activity, Constant.SHADOW_WIDTH_IN_DP) && x <= dp2px(activity, Constant.DIALOG_WIDTH_IN_DP + Constant.SHADOW_WIDTH_IN_DP))
                && (y >= dp2px(activity, Constant.SHADOW_WIDTH_IN_DP) && y <= dp2px(activity, Constant.DIALOG_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP)));
    }

    /**
     * 防止点击太快
     *
     * @return
     */
    public static boolean isFastClick() {
        boolean flag = true;
        long currentClickTime = System.currentTimeMillis();
        if ((currentClickTime - lastClickTime) >= FAST_CLICK_TIME_MIN) {
            flag = false;
        }
        lastClickTime = currentClickTime;
        return flag;
    }

    /**
     * 无信号界面切换和启动按钮快速点击限制2s
     * 避免切换过程中多次点击造成重启
     */
    public static boolean isNoSignalFastClick() {
        boolean flag = true;
        long currentClickTime = System.currentTimeMillis();
        if ((currentClickTime - lastClickTime) >= FAST_CLICK_TIME_2_S) {
            flag = false;
        }
        lastClickTime = currentClickTime;
        return flag;
    }

    /**
     * 自定义toast
     *
     * @param context
     */
    public static void showSuccessToast(Context context) {
        Toast toast = new Toast(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View v = layoutInflater.inflate(R.layout.toast_success_bg, null);
        toast.setView(v);
        toast.setGravity(Gravity.BOTTOM | Gravity.END, 375, 717);
        toast.show();
    }

    public static void showFailToast(Context context) {
        Toast toast = new Toast(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View v = layoutInflater.inflate(R.layout.toast_fail_bg, null);
        toast.setView(v);
        toast.setGravity(Gravity.BOTTOM | Gravity.END, 375, 717);
        toast.show();
    }

    public static void showFailToast(Context context, String msg) {
        Toast toast = new Toast(context);
        LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View v = layoutInflater.inflate(R.layout.toast_fail_bg, null);
        TextView textView = v.findViewById(R.id.txt_msg);
        textView.setText(msg);
        toast.setView(v);
        toast.setGravity(Gravity.BOTTOM | Gravity.END, 375, 717);
        toast.show();
    }

    /**
     * 返回键的广播通知
     *
     * @param activity
     */
    public static void back(Activity activity) {
        Intent intent = new Intent();
        intent.setAction(Constant.ACTION_BACK);
        activity.sendBroadcast(intent);
    }

    /**
     * 发广播通知打开隐私协议页面
     */
    public static void sendPrivacyBroadcast(Context context) {
        Intent intent = new Intent();
        intent.setAction(Constant.ACTION_PRIVACY);
        context.sendBroadcast(intent);
    }

    /**
     * 触控锁移动的距离
     *
     * @param context
     * @param screenDistance
     * @param view
     * @return
     */
    public static float getMoveDistance(Context context, int screenDistance, View view) {

        WindowManager wm = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);
        int width = wm.getDefaultDisplay().getWidth();
        int distance = (screenDistance - (width / 2) + view.getWidth());
        Log.i(TAG, "getMoveDistance: " + distance);
        return distance;
    }

    /**
     * 判断是否为合法IP
     *
     * @param ipAddress
     * @return
     */
    public static boolean isBooleanIp(String ipAddress) {
        String ip = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pattern = Pattern.compile(ip);
        Matcher matcher = pattern.matcher(ipAddress);
        return matcher.matches();
    }

    /**
     * 110吋实际尺寸大，界面缩小0.9
     * 通过调整density来实现
     */
    public static void updateDensity(Context context) {
        int density = Constant.DENSITY_BS86A;
        if (Constant.IS_BS65A) {
            density = Constant.DENSITY_BS65A;
        } else if (Constant.IS_75) {
            density = Constant.DENSITY_BS75A;
        } else if (Constant.IS_110) {
            density = Constant.DENSITY_BS110A;
        }
        Resources resources = context.getResources();
        Configuration configuration = new Configuration(resources.getConfiguration());
        configuration.densityDpi = density;
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
    }

    public static int dp2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, context.getResources().getDisplayMetrics());
    }


    public static int sp2px(Context context, float spValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, spValue, context.getResources().getDisplayMetrics());
    }

    /**
     * 手机系统版本
     */
    public static String getSdkVersion() {
        return android.os.Build.VERSION.RELEASE;
    }

    /**
     * 获取android当前可用运行内存大小
     *
     * @param context
     */
    public static String getAvailMemory(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        am.getMemoryInfo(mi);
        // mi.availMem; 当前系统的可用内存
        return Formatter.formatFileSize(context, mi.availMem);// 将获取的内存大小规格化
    }

    /**
     * 当前的手机总存储内存大小
     *
     * @return xx GB
     */
    public static String getTotalInternalMemorySize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        return Formatter.formatFileSize(context, totalBlocks * blockSize);
    }


    /**
     * 当前手机可用存储内存大小
     *
     * @return xx GB
     */
    public static String getAvailableInternalMemorySize(Context context) {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long availableBlocks = stat.getAvailableBlocks();
        return Formatter.formatFileSize(context, availableBlocks * blockSize);
    }

    /**
     * 获取android总运行内存大小
     * 向上取整
     *
     * @param context
     */
    public static String getTotalMemory(Context context) {
        String str1 = "/proc/meminfo";// 系统内存信息文件
        String str2;
        String[] arrayOfString;
        int totalMemory = 0;
        try {
            FileReader localFileReader = new FileReader(str1);
            BufferedReader localBufferedReader = new BufferedReader(localFileReader, 8192);
            str2 = localBufferedReader.readLine();// 读取meminfo第一行，系统总内存大小
            arrayOfString = str2.split("\\s+");
            for (String num : arrayOfString) {
                Log.i(str2, num + "\t");
            }
            // 获得系统总内存，单位是KB
            int i = Integer.parseInt(arrayOfString[1]);
            totalMemory = (int) Math.ceil(i / 1024.0 / 1024);
            Log.d(TAG, "getTotalMemory: totalMemory=" + totalMemory + " GB");
            localBufferedReader.close();
        } catch (IOException e) {
            Log.e(TAG, "getTotalMemory: " + e);
        }
        if (totalMemory == 0) {
            return "4 GB";
        } else {
            return totalMemory + " GB";
        }
    }

    /**
     * 是否有网络
     *
     * @param context
     * @return
     */
    public static boolean isNetSystemUsable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if (networkInfo != null) {
            boolean connectedOrConnecting = networkInfo.isConnectedOrConnecting();
            return connectedOrConnecting;
        } else {
            return false;
        }
    }

    /**
     * 获取是否有网络连接
     *
     * @param context
     * @return
     */
    public static boolean isNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return mNetworkInfo.isAvailable();
            }
        }
        return false;
    }

    /**
     * 记录无信号状态
     * 供画中画判断
     */
    public static void setNoSignal(Context context, boolean noSignal) {
        Settings.Global.putInt(context.getContentResolver(), KEY_NO_SIGNAL, noSignal ? 1 : 0);
    }

    /**
     * 无信号时
     * 禁止召唤画中画
     */
    public static boolean isNoSignal(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), KEY_NO_SIGNAL, 0) == 1;
    }

    /**
     * 密码输入、windows还原、系统升级等弹窗显示时
     * 供画中画判断
     */
    public static void setIsDialog(Context context, boolean isDialog) {
        Settings.Global.putInt(context.getContentResolver(), KEY_IS_DIALOG, isDialog ? 1 : 0);
    }

    /**
     * 密码输入、windows还原、系统升级等弹窗显示时
     * 禁止召唤画中画
     */
    public static boolean isDialog(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), KEY_IS_DIALOG, 0) == 1;
    }

    /**
     * 记录触控锁状态
     * 供画中画判断
     */
    public static void setLocked(Context context, boolean isLocked) {
        EeoApplication.isLock = isLocked;
//        Settings.Global.putInt(context.getContentResolver(), KEY_IS_LOCKED, isLocked ? 1 : 0);
    }

    /**
     * 触控锁时
     * 禁止召唤画中画
     */
    public static boolean isLocked(Context context) {
        return EeoApplication.isLock;
//        return Settings.Global.getInt(context.getContentResolver(), KEY_IS_LOCKED, 0) == 1;
    }

    public static void setAnnotation(Context context, int status) {
        Settings.Global.putInt(context.getContentResolver(), KEY_IS_ANNOTATION, status);
    }

    /**
     * 批注时
     * 禁止召唤画中画
     * 批注状态：0-未处于批注 1-批注 2-熄屏写
     */
    public static boolean isAnnotation(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), KEY_IS_ANNOTATION, 0) != 0;
    }

    /**
     * 批注处于熄屏写状态
     */
    public static boolean isAnnotationWithoutScreenOn(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), KEY_IS_ANNOTATION, 0) == 2;
    }

    /**
     * 半屏模式
     * 禁止召唤画中画
     */
    public static boolean isHalfScreen(Context context) {
        return EeoApplication.isHalf || ScreenMoveView.getScreenOffset() != ScreenMoveView.SCREEN_ORIGIN;
    }

    public static void setProjection(Context context, boolean isProjection) {
        Settings.Global.putInt(context.getContentResolver(), KEY_IS_PROJECTION, isProjection ? 1 : 0);
    }

    /**
     * 是否处于画中画
     */
    public static boolean isProjection(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), KEY_IS_PROJECTION, 0) == 1;
    }


    public static void enableWirelessScreen(Context context, boolean enable) {
        Settings.System.putInt(context.getContentResolver(), KEY_PROJECTION_STATUS, enable ? 1 : 0);
    }

    /**
     * 开机引导是否开启无线投屏开关
     */
    public static boolean isWirelessScreenEnabled(Context context) {
        return Settings.System.getInt(context.getContentResolver(), KEY_PROJECTION_STATUS, 1) == 1;
    }

    /**
     * 检验和设置无线投屏相关
     * 开机时和开机引导后调用
     */
    public static void checkAndSetWirelessScreen(Context context) {
        //投屏热点相关
        if (CommonUtils.isWirelessScreenEnabled(context)) {
            ScreenUtil.enableWirelessScreen(context);
            HotspotUtil hotspotUtil = new HotspotUtil(context);
            hotspotUtil.checkAndStartHotspot();
            //关闭投屏授权开关
            enableMirrorPermission(context, false);
        } else {
            ScreenUtil.disableWirelessScreen(context);
        }
    }

    /**
     * 恢复出厂前先存飞图激活码到/data/ft_activated_data.xml
     */
    public static void saveActivatedData(Context context) {
        String activatedData = Settings.Global.getString(context.getContentResolver(), Constant.KEY_MULTI_SCREEN_ACTIVATED_DATA);
        FileUtil.saveActivatedDataToFile(activatedData);
    }

    /**
     * 恢复出厂后开机时恢复飞图投屏激活码
     * 避免无网络时丢失激活状态
     */
    public static void getAndSetActivatedData(Context context) {
        String activatedData = FileUtil.getActivatedData();
        if (activatedData != null) {
            Log.d(TAG, "getAndSetActivatedData: ");
            Settings.Global.putString(context.getContentResolver(), Constant.KEY_MULTI_SCREEN_ACTIVATED_DATA, activatedData);
            FileUtil.deleteActivatedDataFile();
            //重启飞图，才会使用global的激活码
            ScreenUtil.restartWirelessScreen(context);
        }
    }


    /**
     * 启动大屏控制界面
     */
    public static void startMainActivity(Context context) {
        //退出画中画、批注、投屏引导界面等
        exitAll(context, false);

        //飞图投屏中时，需要发生广播允许弹窗
        if (isMultiScreenInstalled(context) && isMultiScreen(context)) {
            sendMultiScreenBroadcast(context);
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        //启动大屏控制界面
        Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     * 启动/退出批注
     * x,y用于批注菜单的显示位置
     *
     * @param status 1启动批注，2关闭批注，3熄屏写
     */
    public static void startAnnotation(Context context, int status, int x, int y) {
        if (status != 2) {
            EeoApplication.lastClickTime = System.currentTimeMillis();
        } else if (!isAnnotation(context)) {
            return;
        }
        Intent intent = new Intent("com.eeo.action.Annotation");
        intent.setPackage("com.eeo.annotation");
        intent.putExtra("x", x);
        intent.putExtra("y", y);
        intent.putExtra("AnnotationStatus", status);  //1启动批注，2关闭批注。
        context.startService(intent);
    }

    /**
     * 亮屏、退出熄屏写
     */
    public static void screenOnAndExitAnnotation(Context context) {
        if (!EeoApplication.udi.isScreenOn()) {
            //熄屏时亮屏
            EeoApplication.udi.changeScreenStatus(true, false, false);
        } else if (isAnnotationWithoutScreenOn(context)) {
            //熄屏写时来投屏弹窗退出批注
            startAnnotation(context, 2, 0, 0);
        }
    }

    /**
     * 按电源键、关机/重启、信号源变化时
     * 退出画中画、批注、控制界面、投屏引导界面等
     */
    public static void exitAll(Context context) {
        exitAll(context, true);
    }

    /**
     * 按电源键时不在这控制退出大屏控制界面
     */
    public static void exitAll(Context context, boolean exitMain) {
        //退出画中画
        stopProjection(context);
        //切信号源时退出批注
        startAnnotation(context, 2, 0, 0);
        //退出大屏控制界面
        if (exitMain) {
            sendExitBroadcast(context);
        }
        //退出投屏引导界面
        ScreenManager.getInstance(context).dismissScreenDialog();
    }

    /**
     * 退出画中画
     */
    public static void stopProjection(Context context) {
        if (EeoApplication.isProjection) {
            Intent intent = new Intent("com.eeo.systemsetting.action.startService");
            intent.setPackage("com.eeo.systemsetting");
            intent.putExtra("ProjectionStatus", 2);  //1启动，2关闭。
            context.startService(intent);
        }
    }

    /**
     * 发广播让设置大屏控制界面退出
     */
    public static void sendExitBroadcast(Context context) {
        if (EeoApplication.isShowMainDialog) {
            Intent intent = new Intent(Constant.ACTION_EXIT);
            intent.setPackage("com.eeo.systemsetting");
            context.sendBroadcast(intent);
        }
    }

    /**
     * 发广播通知TifPlayerActivity显示信源分辨率弹窗
     */
    public static void sendBroadcastToShowResolution(Context context) {
        Intent intent = new Intent(TifPlayerActivity.ACTION_SHOW_RESOLUTION);
        intent.setPackage("com.eeo.systemsetting");
        context.sendBroadcast(intent);
    }

    /**
     * 发广播通知TifPlayerActivity刷新下TvView
     */
    public static void sendBroadcastToTuneTvView(Context context) {
        Intent intent = new Intent(TifPlayerActivity.ACTION_RETUNE_TVVIEW);
        intent.setPackage("com.eeo.systemsetting");
        context.sendBroadcast(intent);
    }

    /**
     * 系统是否装了某个应用
     */
    public static boolean isAppInstalled(Context context, String pkgName) {
        try {
            ApplicationInfo info = context.getPackageManager().getApplicationInfo(pkgName, PackageManager.GET_UNINSTALLED_PACKAGES);
            if (info != null) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 装了飞图投屏应用
     */
    public static boolean isMultiScreenInstalled(Context context) {
        return isAppInstalled(context, Constant.MULTI_SCREEN_PKG_NAME);
    }

    /**
     * 投屏中
     * 这里取2个task的topActivity(大屏控制界面activity可能在投屏上面)
     */
    public static boolean isMultiScreen(Context context) {
//        return Constant.MULTI_SCREEN_TOP_ACTIVITY_NAME.equals(getTopActivity(context));
        ActivityManager manager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(2);
        for (ActivityManager.RunningTaskInfo taskInfo : taskInfos) {
            String topActivityName = taskInfo.topActivity.getClassName();
            if (Constant.MULTI_SCREEN_TOP_ACTIVITY_NAME.equals(topActivityName)) {
                return true;
            }
            if (Constant.MULTI_SCREEN_TOP_ACTIVITY_NAME2.equals(topActivityName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 发广播让飞图投屏时允许弹窗
     */
    public static void sendMultiScreenBroadcast(Context context) {
        Intent intent = new Intent(Constant.ACTION_MULTI_SCREEN_OS_MARK_OPEN);
        intent.setPackage(Constant.MULTI_SCREEN_PKG_NAME);
        context.sendBroadcast(intent);
    }

    /**
     * 修改投屏码开关
     *
     * @param enable true-开/false-关
     */
    public static void enablePinCode(Context context, boolean enable) {
        int status = enable ? 1 : 0;
        Intent intent = new Intent(Constant.ACTION_MULTI_SCREEN_PINCODE_MODIFY);
        intent.setPackage(Constant.MULTI_SCREEN_PKG_NAME);
        intent.putExtra("status", status);
        context.sendBroadcast(intent);
    }

    /**
     * 外部请求pinCode
     */
    public static void requestPinCode(Context context) {
        Intent intent = new Intent(Constant.ACTION_MULTI_SCREEN_PINCODE_REQUEST);
        intent.setPackage(Constant.MULTI_SCREEN_PKG_NAME);
        context.sendBroadcast(intent);
    }

    /**
     * 修改投屏授权开关
     *
     * @param enable true-开/false-关
     */
    public static void enableMirrorPermission(Context context, boolean enable) {
        int status = enable ? 1 : 0;
        Intent intent = new Intent(Constant.ACTION_MULTI_SCREEN_MIRROR_PERMISSION_MODIFY);
        intent.setPackage(Constant.MULTI_SCREEN_PKG_NAME);
        intent.putExtra("status", status);
        context.sendBroadcast(intent);
    }

    /**
     * 请求获取投屏码
     * 结果从ACTION_MIRROR_PERMISSION_RESPONSE广播中获取
     */
    public static void requestMirrorPermission(Context context) {
        Intent intent = new Intent(Constant.ACTION_MULTI_SCREEN_MIRROR_PERMISSION_REQUEST);
        intent.setPackage(Constant.MULTI_SCREEN_PKG_NAME);
        context.sendBroadcast(intent);
    }

    /**
     * 通过弹非透明窗体来结束投屏
     * 切换到非android通道时，结束投屏
     */
    public static void finishMultiScreen(Context context) {
        if (CommonUtils.isMultiScreen(context)) {
            if (!EeoApplication.udi.getCurrentSource().equals(UdiConstant.SOURCE_ANDROID)) {
                Intent intent = new Intent(context, FinishMultiScreenActivity.class);
                context.startActivity(intent);
            }
        }
    }

    /**
     * 开机引导界面
     */
    public static boolean isSetup(Context context) {
        return getTopActivity(context).contains(Constant.SETUP_PKG_NAME);
    }

    public static String getTopActivity(Context context) {
        ActivityManager manager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(1);
        if (taskInfos != null && taskInfos.size() > 0) {
            String topActivityName = taskInfos.get(0).topActivity.getClassName();
            return topActivityName;
        }
        return "";
    }

    /**
     * 无线模块是否支持
     */
    public static boolean isWifiDeviceSupported() {
        return !Constant.WIFI_DEVICE_STATUS_UNSUPPORTED.equals(SystemProperties.get(Constant.PROP_WIFI_DEVICE_STATUS));
    }

    /**
     * 获取热点配置信息
     */
    public static WifiConfiguration getWifiApConfiguration(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiConfiguration wifiConfiguration = null;
        try {
            Method getWifiApConfiguration = wifiManager.getClass().getMethod("getWifiApConfiguration");
            wifiConfiguration = (WifiConfiguration) getWifiApConfiguration.invoke(wifiManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return wifiConfiguration;
    }

    /**
     * 获取设备名称
     */
    public static String getDeviceName() {
        return SystemProperties.get("persist.eeo.device.name", "");
    }

    public static void setLanguage(Locale locale) {
        Log.e(TAG, "setLanguage: " + locale.toString());
        try {
            IActivityManager am = ActivityManagerNative.getDefault();
            Configuration config = am.getConfiguration();

           /* SystemProperties.set("persist.sys.language", language);
            SystemProperties.set("persist.sys.country", country);
            SystemProperties.set("persist.sys.localevar", variant);*/
            //设置开机动画：0-default,1-CN,2-EN
            SystemProperties.set("persist.sys.boot_animation", String.valueOf(Constant.LANGUAGE_ZH.equals(locale.getLanguage()) ? 1 : 2));
            config.setLocale(locale);
            am.updatePersistentConfiguration(config);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * ops是否插入
     */
    public static boolean isOpsInserted() {
        //0：未插入  1：插入  -1：接口异常
        return SystemControlManager.getInstance().getOPSInsertStatus() == 1;
    }

    /**
     * 内置电脑通道是否禁用
     */
    public static boolean isOpsDisable() {
        return SystemControlManager.getInstance().getOpsHdmiDisable() == 1;
    }

    /**
     * 第一次开机
     */
    public static boolean isFirstBoot() {
        //0：未插入  1：插入  -1：接口异常
        return SystemProperties.getInt("ro.boot.firstboot", 0) == 1;
    }

    /**
     * 允许无障碍服务功能
     * TODO 后续其它应用用到无障碍功能，需要拼接包名
     */
    public static void enablePopupAlertService(Context context, boolean enable) {
        ComponentName componentName = new ComponentName(context.getPackageName(), PopupAlertService.class.getName());
        if (enable) {
            Settings.Secure.putString(context.getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                    componentName.flattenToString());
            Settings.Secure.putString(context.getContentResolver(), Settings.Secure.ACCESSIBILITY_ENABLED, "1");
        } else {
            Settings.Secure.putString(context.getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                    null);
            Settings.Secure.putString(context.getContentResolver(), Settings.Secure.ACCESSIBILITY_ENABLED, "0");
        }
    }

    public static String getBootForceSource(Context context) {
        String bootForceSource = SaveDateUtils.getBootForceSource(context);
        if (TextUtils.isEmpty(bootForceSource)) {
            bootForceSource = SystemProperties.get(Constant.PROP_FORCE_SOURCE);
            SaveDateUtils.setBootForceSource(context, bootForceSource);
        }
        return bootForceSource;
    }

    public static String getAutoSwitchChannel(Context context) {
        String autoSwitchChannel = SaveDateUtils.getAutoSwitchChannel(context);
        if (TextUtils.isEmpty(autoSwitchChannel)) {
            autoSwitchChannel = SystemProperties.get(Constant.PROP_AUTO_SWITCH_CHANNEL);
            SaveDateUtils.setAutoSwitchChannel(context, autoSwitchChannel);
        }
        return autoSwitchChannel;
    }

    /**
     * 将byte数组转化为十六进制字符串
     * 如：[-86,-69,-52,7,65,0,72,-35,-18,-1] -> "AA BB CC 07 41 00 48 DD EE FF"
     * 这里传入的是JSonArray
     */
    public static String byteArrayToHexString(JSONArray jsonArray) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                int b = jsonArray.getInt(i);
                String hexString = Integer.toHexString(b & 0xFF);
//                sb.append("0x");
                if (hexString.length() == 1) {
                    sb.append("0");
                }
                sb.append(hexString);
                if (i != jsonArray.length() - 1) {
                    sb.append(" ");
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return sb.toString().toUpperCase();
    }

    public static String byteArrayToHexString(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            try {
                String hexString = Integer.toHexString(bytes[i] & 0xFF);
//                sb.append("0x");
                if (hexString.length() == 1) {
                    sb.append("0");
                }
                sb.append(hexString);
                if (i != bytes.length - 1) {
                    sb.append(" ");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return sb.toString().toUpperCase();
    }

    public static byte[] hexStringToByteArray(String hexString) {
        String[] strings = hexString.split(" ");
        byte[] result = new byte[strings.length];
        for (int i = 0; i < strings.length; i++) {
            result[i] = (byte) Integer.parseInt(strings[i], 16);
        }
        return result;
    }

    /**
     * 发送RS232数据
     * 如发送开机(关机)指令通知扩展坞大屏已开机(关机)
     *
     * @param key 发送的key值
     */
    public static boolean sendRS232Data(String key) {
        boolean result = false;
        try {
            JSONArray jsonArray = new JSONArray(CommonUtils.hexStringToByteArray(key));
            result = EeoApplication.udi.sendRS232Data(jsonArray);
        } catch (JSONException e) {
            Log.e(TAG, "sendRS232Data: e");
            e.printStackTrace();
        }
        return result;
    }

    public static void resetHpd() {
        SystemControlManager.getInstance().resetHdmiInputSignal();
    }

    /**
     * 拉低hpd
     */
    public static void hpd0() {
        try {
            Process process = Runtime.getRuntime().exec("imbug 1379");
            DataOutputStream os = new DataOutputStream(process.getOutputStream());
            os.writeBytes("echo hpd0 > /sys/class/hdmirx/hdmirx0/debug\n");
            os.flush();
            os.close();
            process.waitFor();
        } catch (Exception e) {
            Log.e(TAG, "hpd0 exception:" + e);
            e.printStackTrace();
        }
    }

    /**
     * 拉高hpd
     */
    public static void hpd1() {
        try {
            Process process = Runtime.getRuntime().exec("imbug 1379");
            DataOutputStream os = new DataOutputStream(process.getOutputStream());
            os.writeBytes("echo hpd1 > /sys/class/hdmirx/hdmirx0/debug\n");
            os.flush();
            os.close();
            process.waitFor();
        } catch (Exception e) {
            Log.e(TAG, "hpd1 exception:" + e);
            e.printStackTrace();
        }
    }

    public static boolean isTouchOutInserted() {
        return SystemControlManager.getInstance().getTouchOutDetState() == 0;
    }
}
