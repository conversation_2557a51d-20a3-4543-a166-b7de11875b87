package com.eeo.ota.update;

import android.content.Context;
import android.os.UpdateEngine;
import android.os.UpdateEngineCallback;
import android.util.Log;

import com.eeo.ota.bean.PayloadSpec;
import com.eeo.ota.callback.InstallListener;
import com.eeo.ota.util.PayloadSpecUtil;
import com.eeo.ota.util.Util;

import java.io.File;
import java.io.IOException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023年6月25日16:36:47
 * Amlogic t982升级方法
 */
public class FirmwareUpdateT982 {
    private final static String TAG = "FirmwareUpdateT982";

    /**
     * 升级
     */
    public static void updateSystem(Context context, final String absolutePayloadPath, InstallListener installListener) {

        if (absolutePayloadPath == null || absolutePayloadPath.equals("")) {
            Log.d(TAG, "updateSystem Error!!! absolutePayloadPath is invalid.");
            if (installListener != null) {
                installListener.onInstallFail("update.zip path is invalid");
            }
            return;
        }
        File file = new File(absolutePayloadPath);
        if (!file.exists()) {
            Log.e(TAG, "updateSystem: file is not exists!");
            return;
        }
        UpdateEngine updateEngine = new UpdateEngine();
        updateEngine.bind(new UpdateEngineCallback() {
            @Override
            public void onStatusUpdate(int status, float percent) {
                switch (status) {
                    case UpdateEngine.UpdateStatusConstants.DOWNLOADING:
                        Log.d(TAG, "onStatusUpdate: downloading " + percent);
                        if (installListener != null) {
                            installListener.onInstallProgress(percent);
                        }
                        break;
                    case UpdateEngine.UpdateStatusConstants.UPDATED_NEED_REBOOT:
                        Log.d(TAG, "onStatusUpdate: UPDATED_NEED_REBOOT");
                        Util.reboot(context, true);
                    default:
                        break;
                }
            }

            @Override
            public void onPayloadApplicationComplete(int errCode) {
                if (errCode == UpdateEngine.ErrorCodeConstants.SUCCESS) {
                    Log.d(TAG, "onSuccess: ");
                    if (installListener != null) {
                        installListener.onInstallSuccess();
                    }
                } else {
                    Log.d(TAG, "onFail: " + errCode);
                    if (installListener != null) {
                        //TODO switch errCode
                        installListener.onInstallFail(String.valueOf(errCode));
                    }
                }
            }
        });

        try {
            PayloadSpec payloadSpec = PayloadSpecUtil.forNonStreaming(file);
            if (payloadSpec != null) {
                Log.d(TAG, "updateSystem: payloadSpec=" + payloadSpec.toString());
                updateEngine.applyPayload(payloadSpec.getUrl(), payloadSpec.getOffset(), payloadSpec.getSize(),
                        payloadSpec.getProperties().toArray(new String[0]));
            } else {
                if (installListener != null) {
                    installListener.onInstallFail("update.zip is wrong.");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            if (installListener != null) {
                installListener.onInstallFail(e.toString());
            }
        }
    }
}
