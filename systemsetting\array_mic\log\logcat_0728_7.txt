2025-07-28 15:41:21.562  4761-4761  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-07-28 15:41:21.566  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-07-28 15:41:21.573  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-07-28 15:41:21.573  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-07-28 15:41:21.582  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-28 15:41:21.582  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-07-28 15:41:21.582  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-07-28 15:41:31.587  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-28 15:41:31.588  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-07-28 15:41:31.589  4761-4842  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-07-28 15:41:35.860  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-07-28 15:41:35.869  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-07-28 15:41:35.869  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-07-28 15:41:35.869  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-07-28 15:41:38.903  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-07-28 15:41:38.903  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-07-28 15:41:38.905  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A041, file=QH303_V197_20250718.swu
2025-07-28 15:41:38.948  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Current version: A013, Target version: A041
2025-07-28 15:41:38.948  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? true. Is specific error version? false
2025-07-28 15:41:38.948  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-07-28 15:41:38.948  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-07-28 15:41:38.950  4761-4863  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh
2025-07-28 15:41:38.991  4761-4863  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh] finished with exit code: 0
2025-07-28 15:41:40.994  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-07-28 15:41:40.996  4761-4869  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-07-28 15:41:41.032  4761-4869  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 0
2025-07-28 15:41:43.033  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-07-28 15:41:43.035  4761-4875  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-07-28 15:41:43.074  4761-4875  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /mnt/UDISK/*] finished with exit code: 0
2025-07-28 15:41:45.077  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-07-28 15:41:45.079  4761-4881  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device push system/ota/QH303_V197_20250718.swu /mnt/UDISK/
2025-07-28 15:41:46.279  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [  0%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:46.280  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [  0%] /mnt/UDISK/QH303_V197_20250718.swu

......省略了这部分无关log


2025-07-28 15:41:58.501  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 92%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.501  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 92%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.501  4761-4881  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device push system/ota/QH303_V197_20250718.swu /mnt/UDISK/] finished with exit code: 0
2025-07-28 15:41:58.502  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: VALIDATING_FIRMWARE
2025-07-28 15:41:58.506  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 92%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.506  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 92%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.506  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 93%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.506  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 93%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.506  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 94%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.507  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 94%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.507  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 95%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 95%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 96%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 96%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 97%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 97%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 98%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 98%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.510  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 99%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.511  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 99%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.511  4761-4883  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [100%] /mnt/UDISK/QH303_V197_20250718.swu
2025-07-28 15:41:58.544  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Validating firmware size. Local: 35676160, Remote: 35676160
2025-07-28 15:41:58.544  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Firmware validation successful.
2025-07-28 15:41:58.544  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: EXECUTING_UPGRADE
2025-07-28 15:41:58.545  4761-4887  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20250718.swu -e stable,upgrade_recovery
2025-07-28 15:41:58.579  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: config new swupdate
2025-07-28 15:41:58.584  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_input: ##-i /mnt/UDISK/QH303_V197_20250718.swu -e stable,upgrade_recovery##
2025-07-28 15:41:59.266  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## set swupdate_param done ##
2025-07-28 15:41:59.377  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## Error: "swu_version" not defined
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_param: ##-i /mnt/UDISK/QH303_V197_20250718.swu##
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_software: ##stable##
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_mode: ##upgrade_recovery##
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###now do swupdate###
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###log in /mnt/UDISK/swupdate.log###
2025-07-28 15:41:59.379  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## swupdate -v  -i /mnt/UDISK/QH303_V197_20250718.swu -e stable,upgrade_recovery ##
2025-07-28 15:42:03.455  4761-4889  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_next: ##reboot##
2025-07-28 15:42:04.063  4761-4887  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20250718.swu -e stable,upgrade_recovery] finished with exit code: 0
2025-07-28 15:42:04.064  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_DISCONNECT
2025-07-28 15:42:04.090  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-28 15:42:04.090  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_CONNECT
2025-07-28 15:42:36.525  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Device reconnected after reboot.
2025-07-28 15:42:36.526  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: FINAL_VERSION_VALIDATION
2025-07-28 15:42:36.526  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to validate final version...
2025-07-28 15:42:36.579  4761-4761  ArrayMicOTA             com.eeo.systemsetting                W  Validation failed on attempt 1. Version read: A013. Retrying in 5s...
2025-07-28 15:42:41.585  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 2/3 to validate final version...
2025-07-28 15:42:41.621  4761-4761  ArrayMicOTA             com.eeo.systemsetting                W  Validation failed on attempt 2. Version read: A013. Retrying in 5s...
2025-07-28 15:42:46.627  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 3/3 to validate final version...
2025-07-28 15:42:46.667  4761-4761  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Final version validation failed after 3 attempts. Expected A041, but got A013
2025-07-28 15:42:46.667  4761-4761  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Final version validation failed after 3 attempts. Expected A041, but got A013
2025-07-28 15:42:46.667  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-28 15:42:46.667  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-28 15:42:46.668  4761-4761  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-07-28 15:42:46.670  4761-4925  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-07-28 15:42:48.968  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-28 15:42:48.969  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-07-28 15:42:48.969  4761-4761  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-28 15:42:48.973  4761-4761  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@4bdc313