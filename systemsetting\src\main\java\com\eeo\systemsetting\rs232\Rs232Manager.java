package com.eeo.systemsetting.rs232;

import android.app.Instrumentation;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.media.AudioManager;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import com.eeo.WhiteboardAccelerate.IBinderPoolInterface;
import com.eeo.WhiteboardAccelerate.IClassInToolsInterface;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.system.RS232DataListener;

import org.json.JSONArray;

public class Rs232Manager {
    private static final String TAG = "Rs232Manager";
    private Context mContext;
    private static Rs232Manager mRs232Manager = null;

    private AudioManager mAudioManager;
    private VolumeView mVolumeView;

    public static final String KEY_BACK = "AA BB CC 07 0A 00 11 DD EE FF";
    public static final String KEY_VOLUME_DOWN = "AA BB CC 07 41 00 48 DD EE FF";
    public static final String KEY_VOLUME_UP = "AA BB CC 07 03 00 0A DD EE FF";
    //（注：xx=音量大小值，如音量为30(十进制）=1E（十六进制），xx=1E，**=03+00+1E（十六进制）=21）
    public static final String KEY_VOLUME = "AA BB CC 03 00 xx ** DD EE FF";
    public static final String KEY_VOLUME_PRE = "AA BB CC 03 00";
    public static final String KEY_POWER_ON = "AA BB CC 01 00 00 01 DD EE FF";
    public static final String KEY_POWER_OFF = "AA BB CC 01 01 00 02 DD EE FF";
    public static final String KEY_PC = "AA BB CC 02 08 00 0A DD EE FF";
    public static final String KEY_HDMI1 = "AA BB CC 02 06 00 08 DD EE FF";
    public static final String KEY_HDMI2 = "AA BB CC 02 07 00 09 DD EE FF";
    public static final String KEY_TYPE_C = "AA BB CC 02 05 00 07 DD EE FF";
    public static final String KEY_MUTE = "AA BB CC 03 01 00 04 DD EE FF";
    public static final String KEY_UNMUTE = "AA BB CC 03 01 01 05 DD EE FF";
    //扩展和复制
    public static final String KEY_DOUBLE_SCREEN_MODE_EXTEND = "AA BB CC 09 01 00 0A DD EE FF";
    public static final String KEY_DOUBLE_SCREEN_MODE_DUPLICATE = "AA BB CC 09 02 00 0B DD EE FF";
    //打开/关闭控制界面
    public static final String KEY_SETTING = "AA BB CC 0A 01 00 0A DD EE FF";
    public static final String KEY_SCREEN_SHOT = "AA BB CC 0A 02 00 0C DD EE FF";

    /**
     * 控制ClassinX软件命令
     * AA BB CC 0B 01 xx ** DD EE FF
     * （注：xx=控制命令，**=0B+01+xx（十六进制）)
     * 控制命令定义如下，后续依次扩展
     * 01:切换ClassinX启动和退出
     * 02:切换ClassinX桌面模式和黑板模式
     * 03:切换win系统扩展模式和复制模式
     * 04:切换ClassinX主屏显示和副屏显示
     */
    public static final String KEY_CLASSIN_START = "AA BB CC 0B 01";

    private Rs232Manager(Context context) {
        mContext = context;
        init();
    }

    public static Rs232Manager getInstance(Context context) {
        if (mRs232Manager == null) {
            mRs232Manager = new Rs232Manager(context);
        }
        return mRs232Manager;
    }

    private void init() {
        EeoApplication.udi.registerRS232Listener(new RS232DataListener() {
            @Override
            public void onRS232Data(JSONArray mArray) {
                String result = CommonUtils.byteArrayToHexString(mArray);
                handleRS232Key(result);
            }
        });
        mVolumeView = new VolumeView(mContext);
        mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        bindAccelerateService();
    }

    /**
     * 处理对应的串口指令
     */
    private void handleRS232Key(String key) {
        Log.d(TAG, "handleRS232Key: key=" + key);
        if (TextUtils.isEmpty(key)) {
            return;
        }
        if (key.startsWith(KEY_BACK)) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    Instrumentation instrumentation = new Instrumentation();
                    instrumentation.sendKeyDownUpSync(KeyEvent.KEYCODE_BACK);
                }
            }).start();
        } else if (key.startsWith(KEY_POWER_OFF)) {
            PowerUtil.getInstance(mContext).showShutdownDialog();
        } else if (key.startsWith(KEY_PC)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
        } else if (key.startsWith(KEY_HDMI1)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
        } else if (key.startsWith(KEY_HDMI2)) {
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI2);
        } else if (key.startsWith(KEY_TYPE_C)) {
            EeoApplication.udi.changeSource(Constant.SOURCE_TYPE_C);
        } else if (key.startsWith(KEY_VOLUME_PRE)) {
            //①解析音量 ②设置音量并显示设置弹窗
            int volume = parseVolume(key);
            if (volume >= 0 && volume <= 100) {
                setSystemVolume(volume);
                mVolumeView.showVolumeView(volume);
            }
        } else if (key.startsWith(KEY_VOLUME_UP)) {
            if (mAudioManager.isStreamMute(AudioManager.STREAM_MUSIC)) {
                setMute(false);
            }
            int volume = getCurrentVolume();
            volume++;
            if (volume > 100) {
                volume = 100;
            }
            setSystemVolume(volume);
            mVolumeView.showVolumeView(volume);
        } else if (key.startsWith(KEY_VOLUME_DOWN)) {
            if (mAudioManager.isStreamMute(AudioManager.STREAM_MUSIC)) {
                setMute(false);
            }
            int volume = getCurrentVolume();
            volume--;
            if (volume < 0) {
                volume = 0;
            }
            setSystemVolume(volume);
            mVolumeView.showVolumeView(volume);
        } else if (key.startsWith(KEY_MUTE)) {
            setMute(true);
            mVolumeView.showVolumeView(-1);
        } else if (key.startsWith(KEY_UNMUTE)) {
            setMute(false);
            mVolumeView.showVolumeView(-1);
        } else if (key.startsWith(KEY_DOUBLE_SCREEN_MODE_EXTEND)) {
            setDoubleScreenMode(1);
        } else if (key.startsWith(KEY_DOUBLE_SCREEN_MODE_DUPLICATE)) {
            setDoubleScreenMode(2);
        } else if (key.startsWith(KEY_SETTING)) {
            if (!EeoApplication.udi.isScreenOn()) {
                //熄屏时亮屏
                boolean result = EeoApplication.udi.changeScreenStatus(true, false, false);
                if (!result) {
                    //刚灭屏时会亮屏失败，retry
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            Log.e(TAG, "try again to change screen on");
                            EeoApplication.udi.changeScreenStatus(true, false, false);
                        }
                    }, 2000);
                }
            }
            CommonUtils.startMainActivity(mContext);
        } else if (key.startsWith(KEY_CLASSIN_START)) {
            if (CommonUtils.isOpsInserted()) {
                int command = parseClassInCommand(key);
                if (command != -1) {
                    setControlCommand(command);
                }
            }
        } else if (key.startsWith(KEY_SCREEN_SHOT)) {
            EeoApplication.udi.screenshot(Environment.getExternalStorageDirectory().getAbsolutePath() + "/screen.png");
        }
    }

    private int getCurrentVolume() {
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        return mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
    }

    private void setSystemVolume(int volume) {
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, AudioManager.FLAG_PLAY_SOUND);
    }

    /**
     * 使用android原生接口
     * udi的mute接口会显示静音图标
     * isShowUi不生效
     */
    private void setMute(boolean isMute) {
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.adjustSuggestedStreamVolume(isMute ? AudioManager.ADJUST_MUTE : AudioManager.ADJUST_UNMUTE, AudioManager.STREAM_MUSIC, 0);
        //mute状态改变后等一会，避免获取音量状态不对
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * AA BB CC 03 00 xx ** DD EE FF
     * xx=音量大小值，如音量为30(十进制）=1E（十六进制），xx=1E，**=03+00+1E（十六进制）=21）
     * 这里只解析xx音量值，不对**做校验
     */
    private int parseVolume(String key) {
        if (key.length() < 29) {
            return -1;
        }
        String volumeStr = key.substring(15, 17);
        try {
            return Integer.parseInt(volumeStr, 16);
        } catch (NumberFormatException e) {
            Log.e(TAG, "parseVolume: exception:" + e);
        }
        return -1;
    }

    /**
     * AA BB CC 0B 01 xx ** DD EE FF
     * xx=控制命令，**=0B+01+xx（十六进制）
     */
    private int parseClassInCommand(String key) {
        if (key.length() < 29) {
            return -1;
        }
        try {
            int check1 = Integer.parseInt(key.substring(9, 11), 16);
            int check2 = Integer.parseInt(key.substring(12, 14), 16);
            int command = Integer.parseInt(key.substring(15, 17), 16);
            int checkSum = Integer.parseInt(key.substring(18, 20), 16);
            if (check1 + check2 + command == checkSum) {
                return command;
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "parseClassInCommand: exception:" + e);
        }
        return -1;
    }

    /**
     * 绑定板书加速服务相关
     * 通过板书加速服务来和classIn通讯
     */
    private IBinderPoolInterface mIBinderPoolInterface;
    private IClassInToolsInterface mIClassInToolsInterface;
    private boolean mIsServiceConnected;
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "WhiteboardAccelerate onServiceConnected: ");
            mIsServiceConnected = true;
            mIBinderPoolInterface = IBinderPoolInterface.Stub.asInterface(service);
            try {
                mIClassInToolsInterface = IClassInToolsInterface.Stub.asInterface(mIBinderPoolInterface.getBinderInterface(2));
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            try {
                service.linkToDeath(mDeathRecipient, 0);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "WhiteboardAccelerate onServiceDisconnected: ");
            mIsServiceConnected = false;
        }
    };

    /**
     * 中控控制ClassIn X双屏模式切换
     *
     * @param mode：0x01:扩展模式，0x02:复制模式
     * @return -2:当前不在ops通道或无信号 -1:ClassIn通信失败 0：设置成功
     */
    public int setDoubleScreenMode(int mode) {
        //避免非pc通道时，
        if (!UdiConstant.SOURCE_PC.equals(EeoApplication.mCurrentSource)
                || CommonUtils.isNoSignal(mContext)) {
            Log.d(TAG, "setDoubleScreenMode: no signal or not in pc source!");
            return -2;
        }
        int result = -1;
        try {
            if (mIClassInToolsInterface != null) {
                result = mIClassInToolsInterface.setDoubleScreenMode(mode);
            }
        } catch (RemoteException e) {
            Log.e(TAG, "setDoubleScreenMode: exception:" + e);
            e.printStackTrace();
        }
        Log.d(TAG, "setDoubleScreenMode: mode=" + mode + " , result=" + result);
        return result;
    }

    /**
     * 发送控制ClassinX软件命令
     *
     * @param command： 控制命令定义如下，后续依次扩展
     *                 01:切换ClassinX启动和退出
     *                 02:切换ClassinX桌面模式和黑板模式
     *                 03:切换win系统扩展模式和复制模式
     *                 04:切换ClassinX主屏显示和副屏显示
     * @return -1:ClassIn通信失败 0：设置成功
     */
    public int setControlCommand(int command) {
        int result = -1;
        try {
            if (mIClassInToolsInterface != null) {
                result = mIClassInToolsInterface.setControlCommand(command);
            }
        } catch (RemoteException e) {
            Log.e(TAG, "setControlCommand: exception:" + e);
            e.printStackTrace();
        }
        Log.d(TAG, "setControlCommand: command=" + command + " , result=" + result);
        return result;
    }

    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            if (mIClassInToolsInterface != null) {
                mIClassInToolsInterface.asBinder().unlinkToDeath(this, 0);
                mIClassInToolsInterface = null;
                bindAccelerateService();
            }
        }
    };

    private void bindAccelerateService() {
        //绑定板书加速服务
        Intent intent = new Intent("com.eeo.WhiteboardAccelerate.action.startService");
        intent.setPackage("com.eeo.WhiteboardAccelerate");
        mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
    }

    private void unbindAccelerateService() {
        if (mIsServiceConnected) {
            mContext.unbindService(mServiceConnection);
        }
    }
}
