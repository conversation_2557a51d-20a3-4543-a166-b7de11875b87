2025-08-01 15:02:25.104   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-01 15:02:25.105   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-01 15:02:25.105   885-885   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service
2025-08-01 15:02:25.106   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-01 15:02:25.228   885-885   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:283 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:148 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:119 
2025-08-01 15:02:25.236   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity
2025-08-01 15:02:25.237   885-913   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@89c7810 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-01 15:02:25.238   885-948   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@89c7810 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-01 15:02:25.549   885-885   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-01 15:02:25.552   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-01 15:02:25.594   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-01 15:02:25.594   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-01 15:02:25.595   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-08-01 15:02:25.595   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-01 15:02:25.596   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-01 15:02:34.175   885-1724  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 15:02:34.176   885-1724  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":false,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 15:02:34.203   885-1400  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 15:02:34.204   885-1400  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":false,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 15:02:35.599   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-01 15:02:35.599   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-01 15:02:35.601   885-1969  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-01 15:02:37.906   885-1354  o.systemsettin          com.eeo.systemsetting                W  Long monitor contention with owner Thread-9 (1969) at void com.droidlogic.app.SystemControlManager.systemCmd(java.lang.String)(SystemControlManager.java:1354) waiters=0 in java.util.ArrayList com.droidlogic.app.SystemControlManager.getI2cData(int, int, int, int) for 2.291s
2025-08-01 15:02:38.975   885-1400  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 15:02:38.976   885-1400  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 15:02:38.986   885-2223  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 15:02:38.989   885-2223  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 15:02:39.907   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-01 15:02:44.678   885-1400  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 15:02:44.679   885-1400  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 15:02:45.239   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-08-01 15:02:45.239   885-885   ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-08-01 15:02:45.240   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 15:02:48.293   885-885   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-01 15:02:48.293   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-01 15:02:48.296   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-01 15:02:48.343   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-08-01 15:02:48.343   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-08-01 15:02:48.343   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-08-01 15:02:48.343   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-08-01 15:02:48.344   885-2443  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh
2025-08-01 15:02:48.418   885-2445  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: qpid ==>1388
2025-08-01 15:02:48.427   885-2443  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh] finished with exit code: 0
2025-08-01 15:02:50.429   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-08-01 15:02:50.431   885-2459  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-01 15:02:50.456   885-2462  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: error: device '303_usb_device' not found
2025-08-01 15:02:50.459   885-2459  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 1
2025-08-01 15:02:50.460   885-885   ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Command failed: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-01 15:02:50.460   885-885   ArrayMicOTA             com.eeo.systemsetting                D  ArrayMicOTA: Setting SP key to not completed (failed)
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Command failed: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                E  Array mic update failed in systemsetting module: Command failed: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-08-01 15:02:50.461   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-08-01 15:02:50.463   885-2464  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-08-01 15:02:52.727   885-1354  o.systemsettin          com.eeo.systemsetting                W  Long monitor contention with owner Thread-17 (2464) at void com.droidlogic.app.SystemControlManager.systemCmd(java.lang.String)(SystemControlManager.java:1354) waiters=0 in java.util.ArrayList com.droidlogic.app.SystemControlManager.getI2cData(int, int, int, int) for 2.258s
2025-08-01 15:02:52.754   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-08-01 15:02:52.754   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-08-01 15:02:52.755   885-885   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-08-01 15:02:52.755   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Array mic update all finished in systemsetting module
2025-08-01 15:02:52.755   885-885   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-01 15:02:52.759   885-885   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@708aa89
2025-08-01 15:02:52.759   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-08-01 15:02:52.759   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-08-01 15:02:52.759   885-885   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.