package com.eeo.commonlibrary.source;

import java.util.ArrayList;

public interface SourceInterface {

    /**
     * 获取当前信号源
     * @return
     */
    int getSignalStatus();

    /**
     * 获取当前支持的信号源列表
     * @return
     */
    ArrayList<Integer> getAvailSourceList();

    /**
     * 获取当前信号源的序列号
     * @return
     */
    int getCurSourceId();

    /**
     * 切换信号源
     * @param sourceId 信号源id
     * @return
     */
    boolean changeToSource(int sourceId);

}
