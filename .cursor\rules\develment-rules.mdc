---
alwaysApply: true
---
- 每次对话仅聚焦解决一个问题，逐步推进。
- 必须严格遵循用户指令：只修复用户明确指定的编译错误，禁止修改任何无关代码。任何系统性重构、批量修复或主动扩展任务均为禁止事项。
- 禁止修改与当前任务目标无关的任何代码，哪怕出于优化、重构、消除警告等理由。
- 禁止因推测、怀疑编译问题而去修改原本不会报错的正常代码，只有用户明确指定或真实编译报错时才允许修复编译问题怀疑对象仅限于 AI 修改的代码， AI 没有修改过的原本代码不会导致编译报错。
- 所有代码修改范围必须严格按当前任务点约束，未进入下一步前绝不提前触碰后续内容。
- 所有开发动作前，必须再次自查是否越界，确保只聚焦当前任务。
- 所有代码文件在进行任何修改前，必须先完整阅读并理解其内容和上下文，绝不允许在未分析代码的情况下进行任何编辑。这是最高级别的开发纪律，必须严格遵守。
- 调用 powershell 使用多句命令时，命令间用 ; 连接，而不是用 && 连接。