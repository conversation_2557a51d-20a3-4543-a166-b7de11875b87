2025-08-04 11:20:58.453   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-04 11:20:58.453   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-04 11:20:58.453   850-850   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service and waiting for completion
2025-08-04 11:20:58.453   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-04 11:20:58.455   850-850   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:284 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:149 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:120 
2025-08-04 11:20:58.461   850-895   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@e43982d className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-04 11:20:58.462   850-895   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@e43982d startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-04 11:20:58.572   850-850   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-04 11:20:58.574   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-04 11:20:58.585   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-04 11:20:58.585   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-04 11:20:58.588   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 1/3)
2025-08-04 11:20:58.588   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-04 11:20:58.588   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-04 11:21:08.596   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-04 11:21:08.597   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-04 11:21:08.598   850-3117  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-04 11:21:12.871   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-04 11:21:12.880   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-04 11:21:12.880   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-04 11:21:12.881   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:21:24.004   850-850   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed on attempt 1/2. Retrying after delay...
2025-08-04 11:21:27.007   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:21:37.131   850-850   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed after 2 attempts. Checking USB device status...
2025-08-04 11:21:37.134   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 4
2025-08-04 11:21:37.134   850-850   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-04 11:21:37.134   850-850   ArrayMicOTA             com.eeo.systemsetting                W  USB device lost. Array mic may have been switched by other code.
2025-08-04 11:21:37.134   850-850   ArrayMicOTA             com.eeo.systemsetting                W  Overall update attempt 1 failed: USB device lost during ADB detection. Retrying...
2025-08-04 11:21:39.136   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 2/3)
2025-08-04 11:21:39.136   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Retry attempt, skipping initial delay and going directly to USB switch...
2025-08-04 11:21:39.136   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-04 11:21:39.136   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-04 11:21:39.138   850-3153  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-04 11:21:43.399   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-04 11:21:43.403   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-04 11:21:43.403   850-850   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-04 11:21:45.411   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-04 11:21:45.411   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-04 11:21:45.411   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:21:45.434   850-850   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-04 11:21:45.434   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-04 11:21:45.437   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-04 11:21:45.483   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Current version: A013, Target version: A013
2025-08-04 11:21:45.484   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? false
2025-08-04 11:21:45.484   850-850   ArrayMicOTA             com.eeo.systemsetting                I  No update required. Cleaning up...
2025-08-04 11:21:45.484   850-850   ArrayMicOTA             com.eeo.systemsetting                D  ArrayMicOTA: Setting SP key to completed (no update needed)
2025-08-04 11:21:45.484   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-08-04 11:21:45.484   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-08-04 11:21:45.485   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-08-04 11:21:45.486   850-3173  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-08-04 11:21:47.765   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-08-04 11:21:47.765   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-08-04 11:21:47.765   850-850   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-08-04 11:21:47.765   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Array mic update all finished in systemsetting module
2025-08-04 11:21:47.765   850-850   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-04 11:21:47.766   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity after array mic service completion
2025-08-04 11:21:47.862   850-850   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@36e4c55
2025-08-04 11:21:47.862   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-08-04 11:21:47.862   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-08-04 11:21:47.862   850-850   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.