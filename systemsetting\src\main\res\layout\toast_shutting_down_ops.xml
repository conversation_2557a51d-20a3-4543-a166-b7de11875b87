<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_resolution_bg"
    android:minWidth="@dimen/toast_shutting_down_ops_width"
    android:minHeight="@dimen/toast_shutting_down_ops_height">

    <ImageView
        android:id="@+id/iv_shutting_down_ops"
        android:layout_width="@dimen/iv_screen_width"
        android:layout_height="@dimen/iv_screen_height"
        android:layout_marginTop="@dimen/toast_shutting_down_ops_iv_margin_top"
        android:background="@drawable/searching_animation"
        android:importantForAccessibility="no"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_shutting_down_ops"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/toast_shutting_down_ops_tv_margin_top"
        android:gravity="center_horizontal"
        android:text="@string/toast_shutting_down_ops"
        android:textColor="@color/text_black_100"
        android:textSize="@dimen/toast_resolution_tv_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_shutting_down_ops" />

</androidx.constraintlayout.widget.ConstraintLayout>