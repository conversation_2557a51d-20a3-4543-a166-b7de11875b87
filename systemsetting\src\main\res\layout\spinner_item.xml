<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/img_arrow"
        android:layout_width="@dimen/adb_iv_reset_width"
        android:layout_height="@dimen/adb_iv_reset_height"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="@drawable/ic_arrow_right" />

    <TextView
        android:id="@+id/tv_item"
        style="@style/NetWork_TextView"
        android:layout_width="@dimen/fragment_network_tv_ip_setting_width"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@id/img_arrow"
        android:alpha="0.7"
        android:gravity="end"
        android:text="@string/simplified_chinese"
        android:textColor="@color/black_100" />

</RelativeLayout>