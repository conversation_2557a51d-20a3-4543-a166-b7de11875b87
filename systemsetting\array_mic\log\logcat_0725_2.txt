2025-07-25 14:54:54.063  6668-6668  EeoApplication==        com.eeo.systemsetting                I  EeoApplication:onCreate - Sending broadcast to internal ArrayMicUpdateCheckReceiver.
2025-07-25 14:54:54.076  6668-6689  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 113 RECEIVER: 0 / ReceiverData{intent=Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver } packageName=com.eeo.systemsetting resultCode=-1 resultData=null resultExtras=null}
2025-07-25 14:54:54.431  6668-6668  ActivityThread          com.eeo.systemsetting                V  Performing receive of Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver }: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver}, dir=/data/app/~~xuuOCZe-tG3Li0jgbNyA5Q==/com.eeo.systemsetting-Kj11qYBIwzAVW7RXR8Y7SQ==/base.apk
2025-07-25 14:54:54.431  6668-6668  ArrayMicUp...ckReceiver com.eeo.systemsetting                I  Received action to check Array Microphone update.
2025-07-25 14:54:54.432  6668-6668  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver.onReceive:20 android.app.ActivityThread.handleReceiver:4030 
2025-07-25 14:54:54.436  6668-6687  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@5817d0f className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-25 14:54:54.440  6668-6687  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@5817d0f startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-07-25 14:54:54.604  6668-6668  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-07-25 14:54:54.607  6668-6668  ArrayMicUpdateService   com.eeo.systemsetting                D  Service onCreate.
2025-07-25 14:54:54.620  6668-6668  ArrayMicUpdateService   com.eeo.systemsetting                D  Service onStartCommand.
2025-07-25 14:54:54.620  6668-6668  ArrayMicUpdate          com.eeo.systemsetting                D  Checking for array mic update...
2025-07-25 14:54:54.625  6668-6668  ArrayMicUpdate          com.eeo.systemsetting                E  Failed to parse mic_config.json
                                                                                                    java.io.FileNotFoundException: /system/etc/mic_config.json: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:492)
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:160)
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:115)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdate.parseConfig(ArrayMicUpdate.java:63)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdate.checkUpdate(ArrayMicUpdate.java:36)
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdateService.onStartCommand(ArrayMicUpdateService.java:95)
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4324)
                                                                                                    	at android.app.ActivityThread.access$1800(ActivityThread.java:239)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1953)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loop(Looper.java:223)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7680)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:952)
                                                                                                    Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.Linux.open(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                    	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                    	at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7566)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:478)
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:160) 
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:115) 
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdate.parseConfig(ArrayMicUpdate.java:63) 
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdate.checkUpdate(ArrayMicUpdate.java:36) 
                                                                                                    	at com.eeo.ota.arraymic.ArrayMicUpdateService.onStartCommand(ArrayMicUpdateService.java:95) 
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4324) 
                                                                                                    	at android.app.ActivityThread.access$1800(ActivityThread.java:239) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1953) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106) 
                                                                                                    	at android.os.Looper.loop(Looper.java:223) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7680) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:952) 
2025-07-25 14:54:54.625  6668-6668  ArrayMicUpdateService   com.eeo.systemsetting                I  No update needed for array microphone.
2025-07-25 14:54:55.178  6668-6668  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@4d02073
2025-07-25 14:54:55.178  6668-6668  ArrayMicUpdateService   com.eeo.systemsetting                D  Service onDestroy.
2025-07-25 14:54:55.178  6668-6668  ArrayMicUpdate          com.eeo.systemsetting                D  Releasing resources.