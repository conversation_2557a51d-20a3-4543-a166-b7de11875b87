package com.eeo.systemsetting.projection;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.utils.CommonUtils;

public class ProjectionUtil {
    public static final String TAG = "Projection-Util";
    public static final String ACTION_EEO_POWER_OFF = "com.eeo.action.POWER_OFF";
    public static final String ACTION_EEO_REBOOT = "com.eeo.action.REBOOT";
    private static final String ACTION_RETUNE_TVVIEW = "com.eeo.action.retune.tvview"; //重新抢占tvview，如画中画释放后
    public static final String PKG_NAME_SETTING = "com.eeo.systemsetting";

    /**
     * 811发送广播通过设置去关机
     */
    public static void shutdown(Context context) {
        Intent intent = new Intent(ACTION_EEO_POWER_OFF);
        intent.setPackage(PKG_NAME_SETTING);
        context.sendBroadcast(intent);
    }

    /**
     * 启动批注
     * x,y用于批注菜单的显示位置
     */
    public static void startAnnotation(Context context, int x, int y) {
        Intent intent = new Intent("com.eeo.action.Annotation");
        intent.setPackage("com.eeo.annotation");
        intent.putExtra("x", x);
        intent.putExtra("y", y);
        context.startService(intent);
    }

    /**
     * 启动TifPlayerActivity
     * 恢复设置全屏TvView显示
     */
    public static void startTifPlayerActivity(Context context) {
        Intent intent = new Intent("com.cvte.intent.ACTION_TIF_PLAYER_ACTIVITY");
        intent.setPackage("com.eeo.systemsetting");
        context.startActivity(intent);
    }

    /**
     * 发广播通知TifPlayerActivity恢复设置全屏TvView显示
     */
    public static void sendBroadcastToRetuneTvView(Context context) {
        Intent intent = new Intent(ACTION_RETUNE_TVVIEW);
        intent.setPackage(PKG_NAME_SETTING);
        context.sendBroadcast(intent);
    }

    /**
     * 一些场景下禁止召唤画中画
     */
    public static boolean isSmallWindowDisabled(Context context) {
        if (CommonUtils.isAnnotation(context)) {
            Log.d(TAG, "isSmallWindowDisabled: isAnnotation");
            return true;
        }
        if (CommonUtils.isLocked(context)) {
            Log.d(TAG, "isSmallWindowDisabled: isLocked");
            return true;
        }
        if (CommonUtils.isNoSignal(context)) {
            Log.d(TAG, "isSmallWindowDisabled: isNoSignal");
            return true;
        }
        if (CommonUtils.isDialog(context)) {
            Log.d(TAG, "isSmallWindowDisabled: isDialog");
            return true;
        }
        if (CommonUtils.isMultiScreen(context)) {
            Log.d(TAG, "isSmallWindowDisabled: isMultiScreen");
            return true;
        }
        if (CommonUtils.isHalfScreen(context)) {
            //处于半屏模式
            Log.d(TAG, "isSmallWindowDisabled: isScreenOffset");
            return true;
        }
        return false;
    }

    /**
     * 其它互斥的在启动中时，也不启动画中画
     */
    public static boolean isStarting() {
        if (System.currentTimeMillis() - EeoApplication.lastClickTime < 500) {
            Log.d(TAG, "isStarting");
            return true;
        }
        return false;
    }

    /**
     * 重置
     * 避免异常导致无法启动画中画
     */
    public static void resetSmallWindowDisabled(Context context) {
        CommonUtils.setNoSignal(context, false);
        CommonUtils.setLocked(context, false);
        CommonUtils.setIsDialog(context, false);
        CommonUtils.setAnnotation(context, 0);
        CommonUtils.setProjection(context, false);
    }
}
