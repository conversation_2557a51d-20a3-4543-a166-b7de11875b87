package com.eeo.ota;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;
import com.eeo.ota.callback.InstallListener;
import com.eeo.ota.server.TencentCloud;
import com.eeo.ota.update.FirmwareUpdate;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;
import com.tencent.iot.hub.device.java.core.common.Status;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class Ota {
    public static final String TAG = "Ota";
    /**
     * 下载保存路径
     */
    public static final String PATH_SDCARD = Environment.getExternalStorageDirectory().getAbsolutePath();
    /**
     * 下载到sdcard/ota/下
     * xbhOta开机时会自动删除sdcard/update.zip
     */
    public static final String PATH_DOWNLOAD_PARENT = PATH_SDCARD + "/ota";
    /**
     * update.zip拷贝到sdcard/update.zip
     * 朗国的更新只能在根目录
     */
    public static final String PATH_UPDATE_PACKAGE = PATH_SDCARD + "/update.zip";

    private static Ota mOta = null;

    private Context mContext;

    /**
     * 服务器目前使用腾讯云，
     * 后面可扩展使用EEO
     */
    private TencentCloud mTencentCloud;

    private final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    private Ota(Context context) {
        mContext = context;
        init();
    }

    public static Ota getInstance(Context context) {
        if (mOta == null) {
            mOta = new Ota(context);
        }
        return mOta;
    }

    private void init() {
        mTencentCloud = new TencentCloud(mContext);
    }

    /**
     * 检测版本
     */
    public void checkVersion(CheckVersionCallback checkVersionCallback) {
        if (!Util.isNetworkConnect(mContext)) {
            checkVersionCallback.onCheckFail(CheckVersionCallback.ERR_CODE_NO_NETWORK, mContext.getString(R.string.no_network));
        } else {
            mTencentCloud.checkVersion(checkVersionCallback);
        }
    }

    /**
     * 更新包是否已下载
     */
    public boolean isDownloaded() {
        String newVersion = SharedPreferencesUtil.getNewVersion(mContext);
        String outputFileVersion = SharedPreferencesUtil.getOutputFileVersion(mContext);
        String outputFile = SharedPreferencesUtil.getOutputFile(mContext);
        if (newVersion == null || outputFileVersion == null || outputFile == null) {
            return false;
        }
        if (!newVersion.equals(outputFileVersion)) {
            return false;
        }
        return new File(outputFile).exists();
    }

    /**
     * 下载
     * 支持断点续传
     */
    public void download(DownloadListener downloadListener) {
        if (isDownloaded()) {
            Log.e(TAG, "download: is already downloaded.");
            if (downloadListener != null) {
                downloadListener.onDownloadCompleted(SharedPreferencesUtil.getOutputFile(mContext));
            }
            return;
        }
        if (!Util.isNetworkConnect(mContext)) {
            downloadListener.onDownloadFailure(DownloadListener.ERR_CODE_NO_NETWORK);
        } else {
            mTencentCloud.download(downloadListener);
        }
    }

    /**
     * 停止下载
     * 并删除sdcard/ota/下的子文件和子文件夹
     */
    public void stopDownload() {
        mTencentCloud.stopDownload();
        //删除下载的文件：这里直接把ota/下的子文件全部删掉
        Util.deleteAllOtaFile();
    }

    /**
     * 安装更新包
     */
    public void installPackage(InstallListener installListener) {
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                if (!new File(PATH_UPDATE_PACKAGE).exists()) {
                    //文件不存在，先拷贝
                    String outputFilePath = SharedPreferencesUtil.getOutputFile(mContext);
                    if (outputFilePath != null) {
                        File downloadFile = new File(outputFilePath);
                        if (downloadFile.exists()) {
                            Util.copyFile(downloadFile, PATH_UPDATE_PACKAGE);
                        }
                    }
                }
                //再次检测文件和md5
                File file = new File(PATH_UPDATE_PACKAGE);
                if (file.exists()) {
                    //check md5
                    Log.d(TAG, "installPackage: checking md5...");
                    String md5 = Util.getFileMD5(file);
                    String remoteMd5 = SharedPreferencesUtil.getMd5(mContext);
                    Log.d(TAG, "md5=" + md5 + ",remote md5=" + remoteMd5);
                    if (md5 != null && md5.equalsIgnoreCase(remoteMd5)) {
                        //删除sdcard/ota/
                        Util.deleteOtaFile();
                        SharedPreferencesUtil.setShouldReport(mContext, true);
                        SharedPreferencesUtil.setShowUpdateDialog(mContext, true);

                        Log.d(TAG, "installPackage: check md5 successful,start to update");
                        FirmwareUpdate.updateSystem(mContext, PATH_UPDATE_PACKAGE, installListener);
                    } else {
                        Log.e(TAG, "installPackage fail : error md5!");
                        file.delete();
                        //删除sdcard/ota/
                        Util.deleteOtaFile();
                        installListener.onInstallFail(mContext.getString(R.string.error_md5));
                    }
                } else {
                    Log.e(TAG, "installPackage fail : update.zip not exist!");
                    installListener.onInstallFail(mContext.getString(R.string.file_not_exist));
                }
            }
        });
    }

    /**
     * 上报状态:升级完成
     */
    public void reportOtaSuccess() {
        mTencentCloud.reportOtaSuccess();
    }

    /**
     * 释放
     */
    public void release() {
        mTencentCloud.release();
        mOta = null;
    }
}
