package com.eeo.ota.server;

import static com.eeo.ota.Ota.PATH_DOWNLOAD_PARENT;
import static com.eeo.ota.Ota.PATH_UPDATE_PACKAGE;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.eeo.ota.R;
import com.eeo.ota.bean.Constant;
import com.eeo.ota.bean.VersionInfo;
import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;
import com.eeo.ota.util.ParseUtil;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;
import com.tencent.iot.explorer.device.java.mqtt.TXMqttRequest;
import com.tencent.iot.hub.device.java.core.common.Status;
import com.tencent.iot.hub.device.java.core.dynreg.TXMqttDynreg;
import com.tencent.iot.hub.device.java.core.dynreg.TXMqttDynregCallback;
import com.tencent.iot.hub.device.java.core.mqtt.TXMqttActionCallBack;
import com.tencent.iot.hub.device.java.core.mqtt.TXMqttConnection;
import com.tencent.iot.hub.device.java.core.mqtt.TXOTACallBack;
import com.tencent.iot.hub.device.java.core.mqtt.TXOTAConstansts;

import org.eclipse.paho.client.mqttv3.DisconnectedBufferOptions;
import org.eclipse.paho.client.mqttv3.MqttClientPersistence;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by liuxinxi on 2022-5-7 18:42:50
 * 腾讯云sdk使用封装
 */
public class TencentCloud extends Server {
    public static final String TAG = "ota-TencentCloud";
    /**
     * 产品ID、产品密钥
     */
    //110大屏
    private static final String PRODUCT_ID_CXD11 = "1M8PPGU63B";
    private static final String PRODUCT_SECRET_CXD11 = "KgwWphhECUE7if02WG0jZEEE";

    //110大屏海外东南亚版
    private static final String PRODUCT_ID_CXD11_SOUTHEAST_ASIA = "2X807EU2TH";
    private static final String PRODUCT_SECRET_CXD11_SOUTHEAST_ASIA = "cu0e9CLixVjikrQ4gV9hZsjb";

    //BS65A
    private static final String PRODUCT_ID_BS65A = "HTBO794JIV";
    private static final String PRODUCT_SECRET_BS65A = "xlUndruPZSaxxvLC6pQ0iwfL";
    //BS65A海外东南亚版
    private static final String PRODUCT_ID_BS65A_SOUTHEAST_ASIA = "CGCC6GP575";
    private static final String PRODUCT_SECRET_BS65A_SOUTHEAST_ASIA = "77hhE07OVPsHcK2yFxnHQAgC";

    //BS75A
    private static final String PRODUCT_ID_BS75A = "796OEMPFA2";
    private static final String PRODUCT_SECRET_BS75A = "EcCd0YE5KRJ4V5qaBHMYJ6b8";
    //BS75A海外东南亚版
    private static final String PRODUCT_ID_BS75A_SOUTHEAST_ASIA = "GDGMH5UKVZ";
    private static final String PRODUCT_SECRET_BS75A_SOUTHEAST_ASIA = "OC5cbTenQBgGNcE2znaTDRDc";

    //BS86A
    private static final String PRODUCT_ID_BS86A = "WCI40IPKBR";
    private static final String PRODUCT_SECRET_BS86A = "TPVSoUyXyH3RbZloNxolId7t";
    //BS86A海外东南亚版
    private static final String PRODUCT_ID_BS86A_SOUTHEAST_ASIA = "FJLPK4WWGS";
    private static final String PRODUCT_SECRET_BS86A_SOUTHEAST_ASIA = "iVOdmK8Id9g28dqvtszDRA4e";

    //BS110A
    private static final String PRODUCT_ID_BS110A = "RGU91787AP";
    private static final String PRODUCT_SECRET_BS110A = "L0Wytw1zU3BN1bTjSbzP03b2";
    //BS110A海外东南亚版
    private static final String PRODUCT_ID_BS110A_SOUTHEAST_ASIA = "MHDG9O6J6Y";
    private static final String PRODUCT_SECRET_BS110A_SOUTHEAST_ASIA = "pXDcluIu3KiwlFHktZFbalhT";

    //BSP75A
    private static final String PRODUCT_ID_BSP75A = "TKNR0JHL1H";
    private static final String PRODUCT_SECRET_BSP75A = "LZnxkzVY8xoNCgaulztpIF7V";

    //BSP86A
    private static final String PRODUCT_ID_BSP86A = "VFJ0STJ175";
    private static final String PRODUCT_SECRET_BSP86A = "Lolbo7sKG1ii9dLCqDDXfhzb";

    //BSP110A
    private static final String PRODUCT_ID_BSP110A = "M6JETTUMH1";
    private static final String PRODUCT_SECRET_BSP110A = "fgMC1r3sQowXDh9A2c386ofw";

    //音视频盒子
    private static final String PRODUCT_ID_CXB01 = "CYPG6F9IV6";
    private static final String PRODUCT_SECRET_CXB01 = "Ajkoik8EpAQXBsOGw0XkcjSW";

    private String mProductId = PRODUCT_ID_CXD11;
    private String mProductSecret = PRODUCT_SECRET_CXD11;

    /**
     * 设备名称
     * 每台机器唯一码，可选按固件版本升级
     * 或按设备名称(在腾讯云控制台导入)
     */
    protected static final String DEVICE_NAME = Util.getSerialNumber();

    /**
     * 东南亚URL
     * 腾讯云物联网通信默认地址 "${ProductId}.iotcloud.tencentdevices.com:8883"
     * 默认的动态注册 URL:https://ap-guangzhou.gateway.tencentdevices.com/device/register
     */
    private static final String URL_SERVER_OVERSEA = "tcp://" + PRODUCT_ID_CXD11_SOUTHEAST_ASIA + ".ap-bangkok.iothub.tencentdevices.com:1883";
    private static final String URL_DYNAMIC_REGISTER_OVERSEA = "https://ap-bangkok.gateway.tencentdevices.com/device/register";
    private String mServerUrl;
    private String mDynRegUrl = "https://ap-guangzhou.gateway.tencentdevices.com/device/register";

    /**
     * 设备密钥
     * 每台设备唯一
     * 通过动态注册获得
     */
    private String mDevicePsk;

    private static final AtomicInteger requestID = new AtomicInteger(0);

    private CustomerTXMqttConnection mClient;

    private Context mContext;

    private boolean mHasInit = false;
    /**
     * 重复初始化connect会导致异常
     */
    private boolean mIsIniting = false;

    private boolean mHasInitOta = false;

    private boolean mDynRecFail = false;

    private NetworkChangeReceiver mNetworkChangeReceiver;

    /**
     * 出口ip地址信息
     * 区分国内和海外
     */
    private static final String URL_CIP = "https://ipinfo.io/json";
    private boolean mHasGottenIpAddress = false;
    private String mIpCountry = null;
    private boolean mIsOversea = false;

    /**
     * 连接状态
     * 断开还未自动重连的需要等重连后再次操作
     */
    private boolean mIsConnected = false;

    private boolean mShouldRetryCheckVersion = false;
    private boolean mShouldRetryDownload = false;
    private boolean mShouldRetryReport = false;

    /**
     * 设备当前版本
     */
    private final String mCurrentVersion = Util.getCurrentVersion();

    private boolean mDownload = false;

    private List<CheckVersionCallback> mCheckVersionCallbackList = new ArrayList<>();

    private List<DownloadListener> mDownloadListenerList = new ArrayList<>();

    private final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    /**
     * 检测版本未发现新版本
     */
    private static final int MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION = 0;
    /**
     * 下载未发现新版本
     */
    private static final int MSG_DOWNLOAD_NOT_FOUND_NEW_VERSION = 1;
    /**
     * 连接断开时再20s重试
     */
    private static final int MSG_NOT_CONNECT_RETRY_CHECK_VERSION_TIMEOUT = 2;
    private static final int MSG_NOT_CONNECT_RETRY_DOWNLOAD_TIMEOUT = 3;
    private static final int MSG_REPORT_VERSION_TIMEOUT = 4;

    private static final int TIME_CHECK_VERSION_TIMEOUT = 10000;
    private static final int TIME_RETRY_CHECK_VERSION_TIMEOUT = 20000;
    private static final int TIME_REPORT_VERSION_TIMEOUT = 8000;


    /**
     * 上次下载进度
     */
    private int mLastPercent;

    /**
     * 更新说明
     */
    private String mDescription;
    private String mDescriptionEn;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION:
                case MSG_NOT_CONNECT_RETRY_CHECK_VERSION_TIMEOUT:
                    mShouldRetryCheckVersion = false;
                    if (mIsConnected) {
                        onCheckVersionFail(CheckVersionCallback.ERR_CODE_NOT_FOUND_NEW_VERSION,
                                mContext.getString(R.string.not_found_new_version));
                    } else {
                        onCheckVersionFail(CheckVersionCallback.ERR_CODE_CONNECT_FAIL,
                                mContext.getString(R.string.connect_fail));
                    }
                    break;
                case MSG_REPORT_VERSION_TIMEOUT:
                    mShouldRetryCheckVersion = false;
                    onCheckVersionFail(CheckVersionCallback.ERR_CODE_REPORT_VERSION_TIMEOUT,
                            mContext.getString(R.string.report_version_timeout));
                    removeMessages(MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION);
                    break;
                case MSG_DOWNLOAD_NOT_FOUND_NEW_VERSION:
                case MSG_NOT_CONNECT_RETRY_DOWNLOAD_TIMEOUT:
                    mShouldRetryDownload = false;
                    for (DownloadListener downloadListener : mDownloadListenerList) {
                        downloadListener.onDownloadFailure(DownloadListener.ERR_CODE_NOT_FOUND_NEW_VERSION);
                    }
                    mDownloadListenerList.clear();
                    break;
            }
        }
    };

    public TencentCloud(Context context) {
        mContext = context;
//        init();
        registerNetworkChangeReceiver();
    }

    private void init() {
        Log.d(TAG, "init: mIsIniting=" + mIsIniting);
        if (mIsIniting) {
            return;
        }
        mIsIniting = true;
        if (!Util.isNetworkConnect(mContext)) {
            mIsIniting = false;
            return;
        }
        if (!mHasGottenIpAddress) {
            getIpAddress();
            mIsIniting = false;
            return;
        }
        mDevicePsk = SharedPreferencesUtil.getDevicePsk(mContext);
        if (Constant.IS_CXD11) {
            if (Constant.IS_SOUTHEAST_ASIA) {
                mProductId = PRODUCT_ID_CXD11_SOUTHEAST_ASIA;
                mProductSecret = PRODUCT_SECRET_CXD11_SOUTHEAST_ASIA;
                mServerUrl = URL_SERVER_OVERSEA;
                mDynRegUrl = URL_DYNAMIC_REGISTER_OVERSEA;
            } else {
                mProductId = PRODUCT_ID_CXD11;
                mProductSecret = PRODUCT_SECRET_CXD11;
            }
        } else if (Constant.IS_BS65A) {
            if (mIsOversea) {
                mProductId = PRODUCT_ID_BS65A_SOUTHEAST_ASIA;
                mProductSecret = PRODUCT_SECRET_BS65A_SOUTHEAST_ASIA;
                mServerUrl = "tcp://" + mProductId + ".ap-bangkok.iothub.tencentdevices.com:1883";
                mDynRegUrl = URL_DYNAMIC_REGISTER_OVERSEA;
            } else {
                mProductId = PRODUCT_ID_BS65A;
                mProductSecret = PRODUCT_SECRET_BS65A;
            }
        } else if (Constant.IS_BS75A) {
            if (mIsOversea) {
                mProductId = PRODUCT_ID_BS75A_SOUTHEAST_ASIA;
                mProductSecret = PRODUCT_SECRET_BS75A_SOUTHEAST_ASIA;
                mServerUrl = "tcp://" + mProductId + ".ap-bangkok.iothub.tencentdevices.com:1883";
                mDynRegUrl = URL_DYNAMIC_REGISTER_OVERSEA;
            } else {
                mProductId = PRODUCT_ID_BS75A;
                mProductSecret = PRODUCT_SECRET_BS75A;
            }
        } else if (Constant.IS_BS86A) {
            if (mIsOversea) {
                mProductId = PRODUCT_ID_BS86A_SOUTHEAST_ASIA;
                mProductSecret = PRODUCT_SECRET_BS86A_SOUTHEAST_ASIA;
                mServerUrl = "tcp://" + mProductId + ".ap-bangkok.iothub.tencentdevices.com:1883";
                mDynRegUrl = URL_DYNAMIC_REGISTER_OVERSEA;
            } else {
                mProductId = PRODUCT_ID_BS86A;
                mProductSecret = PRODUCT_SECRET_BS86A;
            }
        } else if (Constant.IS_BS110A) {
            if (mIsOversea) {
                mProductId = PRODUCT_ID_BS110A_SOUTHEAST_ASIA;
                mProductSecret = PRODUCT_SECRET_BS110A_SOUTHEAST_ASIA;
                mServerUrl = "tcp://" + mProductId + ".ap-bangkok.iothub.tencentdevices.com:1883";
                mDynRegUrl = URL_DYNAMIC_REGISTER_OVERSEA;
            } else {
                mProductId = PRODUCT_ID_BS110A;
                mProductSecret = PRODUCT_SECRET_BS110A;
            }
        } else if (Constant.IS_BSP75A) {
            mProductId = PRODUCT_ID_BSP75A;
            mProductSecret = PRODUCT_SECRET_BSP75A;
        } else if (Constant.IS_BSP86A) {
            mProductId = PRODUCT_ID_BSP86A;
            mProductSecret = PRODUCT_SECRET_BSP86A;
        } else if (Constant.IS_BSP110A) {
            mProductId = PRODUCT_ID_BSP110A;
            mProductSecret = PRODUCT_SECRET_BSP110A;
        } else if (Constant.IS_CXB01) {
            mProductId = PRODUCT_ID_CXB01;
            mProductSecret = PRODUCT_SECRET_CXB01;
        } else {
            mProductId = PRODUCT_ID_CXB01;
            mProductSecret = PRODUCT_SECRET_CXB01;
        }
        //没有设备密钥的需要先动态注册、获取设备密钥
        if (TextUtils.isEmpty(mDevicePsk)) {
            dynReg();
        } else {
            connect();
        }
    }

    /**
     * 动态注册
     * 未在腾讯云控制台添加的设备需要动态注册
     * 另外获取设备密钥也通过动态注册获得
     */
    private void dynReg() {
        Log.d(TAG, "dynReg: ");
        TXMqttDynreg dynreg = new TXMqttDynreg(mDynRegUrl, mProductId, mProductSecret, DEVICE_NAME, new TXMqttDynregCallback() {
            @Override
            public void onGetDevicePSK(String devicePsk) {
                Log.d(TAG, "onGetDevicePSK:" + DEVICE_NAME + " , " + devicePsk);
                mDynRecFail = false;
                mDevicePsk = devicePsk;
                SharedPreferencesUtil.setDevicePsk(mContext, devicePsk);
                connect();
            }

            @Override
            public void onGetDeviceCert(String deivceCert, String devicePriv) {
                Log.d(TAG, "onGetDeviceCert:" + deivceCert + "  , " + devicePriv);
            }

            @Override
            public void onFailedDynreg(Throwable cause, String errMsg) {
                Log.d(TAG, "onFailedDynreg:" + cause + "  , " + errMsg);
                mDynRecFail = true;
                mIsIniting = false;
                onCheckVersionFail(CheckVersionCallback.ERR_CODE_DYNREG_FAIL,
                        mContext.getString(R.string.dynrec_fail));
            }

            @Override
            public void onFailedDynreg(Throwable cause) {
                mDynRecFail = true;
                mIsIniting = false;
                Log.d(TAG, "onFailedDynreg:" + cause);
                onCheckVersionFail(CheckVersionCallback.ERR_CODE_DYNREG_FAIL,
                        mContext.getString(R.string.dynrec_fail));
            }
        });//初始化TXMqttDynreg
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                if (dynreg.doDynamicRegister()) {//调起动态注册
                    Log.d(TAG, "Dynamic Register OK!");
                } else {
                    Log.e(TAG, "Dynamic Register failed!");
                }
            }
        });
    }

    /**
     * 连接TXMqtt
     */
    private void connect() {
        Log.d(TAG, "connect: ");
        if (mIsConnected) {
            return;
        }
        if (mClient == null) {
            mClient = new CustomerTXMqttConnection(mServerUrl, mProductId, DEVICE_NAME, mDevicePsk, null, null, new TXMqttActionCallBack() {
                @Override
                public void onConnectCompleted(Status status, boolean reconnect, Object userContext, String msg, Throwable cause) {
                    Log.d(TAG, "onConnectCompleted: status:" + status.toString() + ", msg=" + msg);
                    mIsIniting = false;
                    mHasInit = true;
                    mIsConnected = true;
                    //status:ERROR, msg=代理程序不可用 (3)：三元组信息不正确
                    //这里错误情况下统一需要再次动态注册
                    if (status == Status.ERROR) {
                        SharedPreferencesUtil.setDevicePsk(mContext, null);
                        mDevicePsk = null;
                        mHasInit = false;
                        mIsConnected = false;
                        mClient = null;
                        init();
                        return;
                    }
                    unregisterNetworkChangeReceiver();
                    if (mShouldRetryCheckVersion) {
                        mHandler.removeMessages(MSG_NOT_CONNECT_RETRY_CHECK_VERSION_TIMEOUT);
                        mShouldRetryCheckVersion = false;
                        checkVersion(null);
                    }
                    if (mShouldRetryDownload) {
                        mHandler.removeMessages(MSG_NOT_CONNECT_RETRY_DOWNLOAD_TIMEOUT);
                        mShouldRetryDownload = false;
                        download(null);
                    }
                    if (mShouldRetryReport) {
                        mShouldRetryReport = false;
                        reportOtaSuccess();
                    }
                }

                @Override
                public void onConnectionLost(Throwable cause) {
                    mIsConnected = false;
                    Log.e(TAG, "onConnectionLost: cause" + cause.toString());
                }

                @Override
                public void onDisconnectCompleted(Status status, Object userContext, String msg, Throwable cause) {
                    mIsConnected = false;
                    Log.d(TAG, "onDisconnectCompleted: status" + status.toString() + "  msg=" + msg + "   cause=" + cause.toString());
                }
            });
        }
        MqttConnectOptions options = new MqttConnectOptions();
        options.setConnectionTimeout(8);
        options.setKeepAliveInterval(240);
        options.setAutomaticReconnect(true);
        TXMqttRequest mqttRequest = new TXMqttRequest("connect", requestID.getAndIncrement());
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                mClient.connect(options, mqttRequest);
            }
        });
    }

    public void release() {
        if (mClient != null) {
            mClient.disConnect(null);
        }
        mShouldRetryCheckVersion = false;
        mShouldRetryDownload = false;
        mShouldRetryReport = false;
        mHandler.removeCallbacksAndMessages(null);
    }

    private void initOTA() {
        File file = new File(PATH_DOWNLOAD_PARENT);
        if (!file.exists()) {
            file.mkdirs();
        }
        mClient.initOTA(PATH_DOWNLOAD_PARENT, Arrays.copyOfRange(com.tencent.iot.hub.device.java.core.device.CA.cosServerCaCrtList, 0, 1), new TXOTACallBack() {
            @Override
            public void onReportFirmwareVersion(int resultCode, String version, String resultMsg) {
                Log.d(TAG, "onReportFirmwareVersion:" + resultCode + ", version:" + version + ", resultMsg:" + resultMsg);
                mHandler.removeMessages(MSG_REPORT_VERSION_TIMEOUT);
            }

            @Override
            public boolean onLastestFirmwareReady(String url, String md5, String version) {
                Log.d(TAG, "onLastestFirmwareReady: url=" + url + ", md5=" + md5 + " ,version=" + version);
                mHandler.removeMessages(MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION);
                mHandler.removeMessages(MSG_DOWNLOAD_NOT_FOUND_NEW_VERSION);
                VersionInfo versionInfo = new VersionInfo();
                versionInfo.url = url;
                versionInfo.md5 = md5;
                versionInfo.versionName = version;
                versionInfo.fileSize = Util.convertSizeToString(Util.getSizeFromUrl(url));
                versionInfo.description = mDescription;
                versionInfo.descriptionEn = mDescriptionEn;
                onCheckVersionSuccess(versionInfo);
                SharedPreferencesUtil.setNewVersion(mContext, version);
                SharedPreferencesUtil.setMD5(mContext, md5);
                // false 自动触发下载升级文件  true 需要手动触发下载升级文件
                return !mDownload;
            }

            @Override
            public void onDownloadProgress(int percent, String version) {
                Log.d(TAG, "onDownloadProgress: version=" + version + ",percent=" + percent);
                for (DownloadListener downloadListener : mDownloadListenerList) {
                    downloadListener.onDownloadProgress(percent);
                }
                if (mLastPercent == 0) {
                    //清除output_file，避免判断是否已下载出错
                    SharedPreferencesUtil.setOutputFile(mContext, null);
                    SharedPreferencesUtil.setOutputFileVersion(mContext, null);
                }
                mLastPercent = percent;
            }

            @Override
            public void onDownloadCompleted(String outputFile, String version) {
                Log.d(TAG, "onDownloadCompleted: " + outputFile + " ,version=" + version);
                mDownload = false;
                mLastPercent = 0;
                SharedPreferencesUtil.setOutputFile(mContext, outputFile);
                SharedPreferencesUtil.setOutputFileVersion(mContext, version);
                mExecutorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        //拷贝到sdcard/update.zip
                        File file = new File(outputFile);
                        Util.copyFile(file, PATH_UPDATE_PACKAGE);
                        for (DownloadListener downloadListener : mDownloadListenerList) {
                            downloadListener.onDownloadCompleted(outputFile);
                        }
                        mDownloadListenerList.clear();
                        //上报状态：正在升级
                        mClient.reportOTAState(TXOTAConstansts.ReportState.BURNING, 0, "OK", version);
                    }
                });
            }

            @Override
            public void onDownloadFailure(int errCode, String version) {
                Log.e(TAG, "onDownloadFailure: " + errCode + " ,version=" + version);
                mDownload = false;
                for (DownloadListener downloadListener : mDownloadListenerList) {
                    downloadListener.onDownloadFailure(errCode);
                }
                mDownloadListenerList.clear();
                //上报状态：升级失败
                mClient.reportOTAState(TXOTAConstansts.ReportState.FAIL, errCode, "FAIL", version);
            }
        });
        mHasInitOta = true;
    }

    public void checkVersion(CheckVersionCallback checkVersionCallback) {
        Log.d(TAG, "checkVersion: ");
        addCheckVersionCallback(checkVersionCallback);
        if (!mHasInit && !mIsIniting) {
            init();
            if (mDynRecFail) {
                onCheckVersionFail(CheckVersionCallback.ERR_CODE_DYNREG_FAIL, mContext.getString(R.string.dynrec_fail));
                return;
            } /*else {
                onCheckVersionFail(CheckVersionCallback.ERR_CODE_CONNECT_FAIL, mContext.getString(R.string.connect_fail));
            }
            return;*/
        }
        mDescription = mDescriptionEn = null;
        if (mIsConnected) {
            mHandler.removeMessages(MSG_REPORT_VERSION_TIMEOUT);
            mHandler.sendEmptyMessageDelayed(MSG_REPORT_VERSION_TIMEOUT, TIME_REPORT_VERSION_TIMEOUT);
            mHandler.removeMessages(MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION);
            mHandler.sendEmptyMessageDelayed(MSG_CHECK_VERSION_NOT_FOUND_NEW_VERSION, TIME_CHECK_VERSION_TIMEOUT);
            reportCurrentFirmwareVersion();
        } else {
            mShouldRetryCheckVersion = true;
            mHandler.removeMessages(MSG_NOT_CONNECT_RETRY_CHECK_VERSION_TIMEOUT);
            mHandler.sendEmptyMessageDelayed(MSG_NOT_CONNECT_RETRY_CHECK_VERSION_TIMEOUT, TIME_RETRY_CHECK_VERSION_TIMEOUT);
        }
    }

    /**
     * 下载
     * 支持断点续传
     */
    public void download(DownloadListener downloadListener) {
        //下载前确认sdcard/ota/是否存在
        File file = new File(PATH_DOWNLOAD_PARENT);
        if (!file.exists()) {
            file.mkdirs();
        }
        addDownloadListenerList(downloadListener);
        if (mIsConnected) {
            mDownload = true;
            //再次上报可触发下载
            reportCurrentFirmwareVersion();
            mHandler.sendEmptyMessageDelayed(MSG_DOWNLOAD_NOT_FOUND_NEW_VERSION, TIME_CHECK_VERSION_TIMEOUT);
        } else {
            mShouldRetryDownload = true;
            mHandler.removeMessages(MSG_NOT_CONNECT_RETRY_DOWNLOAD_TIMEOUT);
            mHandler.sendEmptyMessageDelayed(MSG_NOT_CONNECT_RETRY_DOWNLOAD_TIMEOUT, TIME_RETRY_CHECK_VERSION_TIMEOUT);
        }
    }

    /**
     * 停止下载
     */
    public void stopDownload() {
        mDownload = false;
        if (mClient != null) {
            mClient.stopDownloadOTATask();
        }
    }

    /**
     * 上报当前固件版本
     * 下载之类的由该方法触发
     */
    private void reportCurrentFirmwareVersion() {
        if (mClient != null) {
            if (!mHasInitOta) {
                initOTA();
            }
            Log.d(TAG, "reportCurrentFirmwareVersion: " + mCurrentVersion);
            mClient.reportCurrentFirmwareVersion(mCurrentVersion);
        }
    }

    /**
     * 上报状态:升级完成
     */
    public Status reportOtaSuccess() {
        if (!mHasInit && !mIsIniting) {
            init();
            if (mDynRecFail) {
                return Status.ERROR;
            }
        }
        if (mIsConnected) {
            if (mClient != null) {
                if (!mHasInitOta) {
                    initOTA();
                }
                Status status = mClient.reportOTAState(TXOTAConstansts.ReportState.DONE, 0, "OK", mCurrentVersion);
                Log.d(TAG, "reportOtaSuccess: " + mCurrentVersion + " , result = " + status);
                if (status == Status.OK) {
                    SharedPreferencesUtil.setShouldReport(mContext, false);
                }
                return status;
            }
        } else {
            mShouldRetryReport = true;
        }
        return Status.ERROR;
    }

    private void onCheckVersionSuccess(VersionInfo versionInfo) {
        for (CheckVersionCallback checkVersionCallback : mCheckVersionCallbackList) {
            checkVersionCallback.onCheckSuccess(versionInfo);
        }
        mCheckVersionCallbackList.clear();
    }

    private void onCheckVersionFail(int errCode, String reason) {
        for (CheckVersionCallback checkVersionCallback : mCheckVersionCallbackList) {
            checkVersionCallback.onCheckFail(errCode, reason);
        }
        mCheckVersionCallbackList.clear();
    }

    private void addCheckVersionCallback(CheckVersionCallback checkVersionCallback) {
        if (checkVersionCallback != null && !mCheckVersionCallbackList.contains(checkVersionCallback)) {
            mCheckVersionCallbackList.add(checkVersionCallback);
        }
    }

    private void removeCheckVersionCallback(CheckVersionCallback checkVersionCallback) {
        mCheckVersionCallbackList.remove(checkVersionCallback);
    }

    private void addDownloadListenerList(DownloadListener downloadListener) {
        if (downloadListener != null && !mDownloadListenerList.contains(downloadListener)) {
            mDownloadListenerList.add(downloadListener);
        }
    }

    private void removeDownloadListenerList(DownloadListener downloadListener) {
        mDownloadListenerList.remove(downloadListener);
    }

    public void getIpAddress() {
        new GetIpAddressTask().execute();
    }

    private class GetIpAddressTask extends AsyncTask<Void, Void, String> {
        @Override
        protected String doInBackground(Void... voids) {
            try {
                URL url = new URL(URL_CIP);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    return response.toString();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            mHasGottenIpAddress = true;
            init();
            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);
            Log.d(TAG, "onPostExecute: result=" + result);
            if (result != null) {
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    mIpCountry = jsonObject.optString("country");
                    mIsOversea = !result.toUpperCase().contains("CHINA") &&
                            !TextUtils.isEmpty(mIpCountry) &&
                            !mIpCountry.equalsIgnoreCase("CN") &&
                            !mIpCountry.equalsIgnoreCase("HK") &&
                            !mIpCountry.equalsIgnoreCase("MO") &&
                            !mIpCountry.equalsIgnoreCase("TW");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            mHasGottenIpAddress = true;
            init();
        }
    }

    private void registerNetworkChangeReceiver() {
        if (mNetworkChangeReceiver == null) {
            mNetworkChangeReceiver = new NetworkChangeReceiver();
        }
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        mContext.registerReceiver(mNetworkChangeReceiver, filter);
    }

    private void unregisterNetworkChangeReceiver() {
        if (mNetworkChangeReceiver != null) {
            mContext.unregisterReceiver(mNetworkChangeReceiver);
            mNetworkChangeReceiver = null;
        }
    }

    private class NetworkChangeReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onReceive: " + intent.getAction() + " ,mHasInit=" + mHasInit);
            if (!mHasInit && Util.isNetworkConnect(context)) {
                mShouldRetryCheckVersion = true;
                init();
            }
        }
    }

    /**
     * 通过重写messageArrived来接收ota升级自定义信息
     * TXOTAImpl processMessage消耗了，没有messageArrived回调
     */
    private class CustomerTXMqttConnection extends TXMqttConnection {

        public CustomerTXMqttConnection(String serverURI, String productID, String deviceName, String secretKey, DisconnectedBufferOptions bufferOpts, MqttClientPersistence clientPersistence, TXMqttActionCallBack callBack) {
            super(serverURI, productID, deviceName, secretKey, bufferOpts, clientPersistence, callBack);
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            //parse description
            String[] description = ParseUtil.parseDescription(message);
            if (description != null && description.length >= 2) {
                mDescription = description[0];
                mDescriptionEn = description[1];
            }
            super.messageArrived(topic, message);
        }
    }
}
