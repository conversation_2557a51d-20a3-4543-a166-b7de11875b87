<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/main_width"
    android:layout_height="@dimen/main_height"
    android:minHeight="@dimen/main_height">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/iv_back_width"
        android:layout_height="@dimen/iv_back_height"
        android:layout_marginStart="@dimen/iv_back_margin_start"
        android:layout_marginTop="@dimen/iv_back_margin_top"
        android:importantForAccessibility="no"
        android:src="@drawable/select_left_icon" />

    <TextView
        android:id="@+id/txt_title"
        style="@style/Title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/setting_tv_title_margin_top"
        android:text="@string/privacy_policy" />

    <View
        android:id="@+id/line1"
        style="@style/Line"
        android:layout_below="@id/iv_back"
        android:layout_marginTop="@dimen/setting_line1_margin_top" />

    <ScrollView
        android:id="@+id/sv_privacy"
        android:layout_width="match_parent"
        android:layout_height="@dimen/privacy_height"
        android:layout_below="@id/line1"
        android:layout_marginTop="@dimen/privacy_margin_top"
        android:fadeScrollbars="false"
        android:overScrollMode="never"
        android:scrollbarThumbVertical="@drawable/shape_network_scrollview"
        android:scrollbars="vertical">

        <WebView
            android:id="@+id/webView"
            android:layout_width="@dimen/privacy_width"
            android:layout_height="@dimen/privacy_height"
            android:layout_gravity="center_horizontal" />
    </ScrollView>


</RelativeLayout>