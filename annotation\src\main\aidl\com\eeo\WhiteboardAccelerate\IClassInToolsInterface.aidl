// IClassInToolsInterface.aidl
package com.eeo.WhiteboardAccelerate;

interface IClassInToolsInterface {
    /**
     * 控制ClassIn X应用
     * @param status：0x01:表示打开
     *                0x02:表示关闭
     *                0x03:表示最小化
     *                0x04:表示最小化
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setClassInApp(int status);
    /**
     * 控制ClassIn X骰子工具
     * @param status：0x01:打开，0x02:关闭，0x03:开始
     *        topX：左上角x坐标，topY:左上角y坐标
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setDiceTool(int status,int topX,int topY);
    /**
     * 控制ClassIn X定时器工具
     * @param status：0x01:打开，0x02:关闭，0x03:开始。
     *        topX：左上角x坐标，topY:左上角y坐标
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setChronometerTool(int status,int topX,int topY);
    /**
     * 控制ClassIn X计时器工具
     * @param status：0x01:打开，0x02:关闭，0x03:开始。
     *        topX：左上角x坐标，topY:左上角y坐标
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setTimerTool(int status,int topX,int topY);
    /**
     * 控制ClassIn X尺规工具
     * @param status：0x01:打开，0x02:关闭，0x03：旋转，0x04：移动
     *        type: 0x01:直尺，0x02:三角尺，0x03:等腰三角尺，0x04:量角器，0x05:圆规
     *        angle:角度，0x1000表示负0角度，0x0000表示正角度，如-100度表示0x1064
     *        topX：左上角x坐标，topY:左上角y坐标
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setRulerTool(int status,int type,int angle,int topX,int topY);

    /**
     * 中控控制ClassIn X双屏模式切换
     * @param mode：0x01:扩展模式，0x02:复制模式
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setDoubleScreenMode(int mode);
    /**
     * 转发中控控制ClassIn X命令
     * @param command参考RS232串口协议文档定义
     * @return -1:ClassIn通信失败 0：设置成功
     */
    int setControlCommand(int command);
}