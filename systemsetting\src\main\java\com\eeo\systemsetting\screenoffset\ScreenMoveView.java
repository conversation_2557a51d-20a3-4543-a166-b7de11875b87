package com.eeo.systemsetting.screenoffset;

import static com.eeo.udisdk.UdiConstant.TYPE_TOUCH_OFFSET_PC;

import android.content.Context;
import android.graphics.PixelFormat;
import android.os.SystemProperties;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.SaveDateUtils;

public class ScreenMoveView implements View.OnTouchListener {

    public static final String PROPERTY_SCREEN_YOFFSET = "vendor.screen.yoffset";
    public static final int SCREEN_YOFFSET = 800;
    public static final int SCREEN_ORIGIN = 0;
    private Context mContext;
    private WindowManager mWindowManager;
    private View mScreenView;
    private WindowManager.LayoutParams params;
    private TextView mScreenOffsetTv;

    public ScreenMoveView(Context context) {
        mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        initScreenMoveView();
    }

    private void initScreenMoveView() {
        mScreenView = LayoutInflater.from(mContext).inflate(R.layout.screen_offset_view, null);
        mScreenOffsetTv = mScreenView.findViewById(R.id.tv_screen_offset);
        params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                        | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT);
        params.gravity = Gravity.TOP;
        params.height = SCREEN_YOFFSET;
        mScreenView.setVisibility(View.GONE);
        mScreenView.setOnTouchListener(this);
        mWindowManager.addView(mScreenView, params);
    }

    /**
     * @param left 是否左滑条触发
     */
    public void show(boolean left) {
        if (CommonUtils.isLocked(mContext)) {
            Log.d("ScreenMoveView", "cannot move half screen down : is locked");
            return;
        }
        if (!SaveDateUtils.isTouchSliderEnabled(mContext)) {
            Log.d("ScreenMoveView", "cannot move half screen down : Touch slider is disabled");
            return;
        }
        if (CommonUtils.isMultiScreen(mContext)) {
            Log.d("ScreenMoveView", "cannot move half screen down : is multiScreen");
            return;
        }

        EeoApplication.isHalf = true;
        if (EeoApplication.isProjection) {
            CommonUtils.stopProjection(mContext);
        }
        setScreenOffset(SCREEN_YOFFSET);
        mScreenView.setVisibility(View.VISIBLE);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mScreenOffsetTv.getLayoutParams();
        if (left) {
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_END);
            layoutParams.addRule(RelativeLayout.ALIGN_PARENT_START);
        } else {
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_START);
            layoutParams.addRule(RelativeLayout.ALIGN_PARENT_END);
        }
        mScreenOffsetTv.setLayoutParams(layoutParams);
    }

    public void hide() {
        EeoApplication.isHalf = false;
        setScreenOffset(SCREEN_ORIGIN);
        mScreenView.setVisibility(View.GONE);
    }

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
            hide();
        }
        return false;
    }

    public static void setScreenOffset(int offset) {
        SystemProperties.set(PROPERTY_SCREEN_YOFFSET, "" + offset);
        EeoApplication.udi.setTouchOffset(TYPE_TOUCH_OFFSET_PC, offset);
    }

    public static int getScreenOffset() {
        return SystemProperties.getInt(PROPERTY_SCREEN_YOFFSET, SCREEN_ORIGIN);
    }

    public void onConfigurationChanged() {
        mScreenOffsetTv.setText(mContext.getString(R.string.eeo_screen_move_txt));
    }
}
