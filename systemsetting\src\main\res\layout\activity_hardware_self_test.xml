<?xml version="1.0" encoding="utf-8"?>
<com.zyp.cardview.YcCardView xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/Main_YcCardView"
    android:layout_width="@dimen/main_cv_width"
    android:layout_height="@dimen/main_cv_height">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/setting_tv_title_margin_top"
            android:text="@string/hardware_self_test" />

        <View
            android:id="@+id/line1"
            style="@style/Line"
            android:layout_below="@id/tv_title"
            android:layout_marginTop="@dimen/setting_line1_margin_top" />

        <TextView
            android:id="@+id/tv_cpu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/line1"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_CPU"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_cpu_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_cpu"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_memory"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_cpu"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_memory"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_memory_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_memory"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_hard_disk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_memory"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_hard_disk"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_hard_disk_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_hard_disk"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_touch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_hard_disk"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_touch"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_touch_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_touch"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_ethernet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_touch"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_ethernet"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_ethernet_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_ethernet"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_wifi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_ethernet"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_wifi"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_wifi_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_wifi"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_mic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_wifi"
            android:layout_marginStart="@dimen/screen_dialog_tv_margin_start"
            android:layout_marginTop="@dimen/screen_dialog_switch_enable_wireless_screen_margin_top"
            android:text="@string/hardware_self_test_mic"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

        <TextView
            android:id="@+id/tv_mic_test_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_mic"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/screen_dialog_tv_margin_start"
            android:text="@string/hardware_self_test_no_test"
            android:textColor="@color/black_100"
            android:textSize="@dimen/screen_dialog_title_text_size" />

    </RelativeLayout>
</com.zyp.cardview.YcCardView>