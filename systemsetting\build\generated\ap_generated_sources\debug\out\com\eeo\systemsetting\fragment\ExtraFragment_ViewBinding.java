// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.Switch;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ExtraFragment_ViewBinding implements Unbinder {
  private ExtraFragment target;

  @UiThread
  public ExtraFragment_ViewBinding(ExtraFragment target, View source) {
    this.target = target;

    target.mWriteWithoutScreenOnSwitch = Utils.findRequiredViewAsType(source, R.id.sw_write_without_screen_on, "field 'mWriteWithoutScreenOnSwitch'", Switch.class);
    target.mBreathLedOnSwitch = Utils.findRequiredViewAsType(source, R.id.sw_breath_led_on, "field 'mBreathLedOnSwitch'", Switch.class);
    target.mTouchSliderLayout = Utils.findRequiredViewAsType(source, R.id.rl_touch_slider, "field 'mTouchSliderLayout'", RelativeLayout.class);
    target.mTouchSliderSwitch = Utils.findRequiredViewAsType(source, R.id.sw_touch_slider, "field 'mTouchSliderSwitch'", Switch.class);
    target.mWindowsDisableSwitch = Utils.findRequiredViewAsType(source, R.id.sw_windows_disable, "field 'mWindowsDisableSwitch'", Switch.class);
    target.mStartChannelSpinner = Utils.findRequiredViewAsType(source, R.id.spinner_startup_channel, "field 'mStartChannelSpinner'", Spinner.class);
    target.mHardwareSelfTestRl = Utils.findRequiredViewAsType(source, R.id.rl_hardware_self_test, "field 'mHardwareSelfTestRl'", RelativeLayout.class);
    target.mWindowsTaskManagerRl = Utils.findRequiredViewAsType(source, R.id.rl_windows_task_manager, "field 'mWindowsTaskManagerRl'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    ExtraFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mWriteWithoutScreenOnSwitch = null;
    target.mBreathLedOnSwitch = null;
    target.mTouchSliderLayout = null;
    target.mTouchSliderSwitch = null;
    target.mWindowsDisableSwitch = null;
    target.mStartChannelSpinner = null;
    target.mHardwareSelfTestRl = null;
    target.mWindowsTaskManagerRl = null;
  }
}
