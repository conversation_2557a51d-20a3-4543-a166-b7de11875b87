<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scrollview"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbarSize="@dimen/fragment_about_scrollbar_size"
    android:scrollbarThumbVertical="@drawable/shape_network_scrollview">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <LinearLayout
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_network_margin_top"
            android:gravity="center_vertical">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/network" />

            <Switch
                android:id="@+id/sw_network"
                style="@style/NetWork_Switch"
                android:layout_marginStart="@dimen/fragment_network_sw_margin_start"
                android:checked="false"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_mac_address"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_item_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/mac_address" />

            <TextView
                android:id="@+id/txt_mac_address"
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/fragment_network_content_width"
                android:layout_marginStart="@dimen/fragment_network_content_margin_start"
                android:gravity="end"
                android:text="122:123:234:12"
                android:textColor="@color/black_70" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_ip_mode"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_item_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/ip_setting" />

            <TextView
                android:id="@+id/txt_ip_setting"
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/fragment_network_tv_ip_setting_width"
                android:layout_marginStart="@dimen/fragment_network_content_margin_start"
                android:gravity="end"
                android:text="@string/manual"
                android:textColor="@color/black_70" />

            <ImageView
                android:id="@+id/img_arrow"
                android:layout_width="@dimen/adb_iv_reset_width"
                android:layout_height="@dimen/adb_iv_reset_height"
                android:background="@drawable/ic_arrow_right" />

        </LinearLayout>

        <EditText
            android:id="@+id/edt_focus"
            style="@style/Network_EditText"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/network_edit_bg" />

        <LinearLayout
            android:id="@+id/ll_ip"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_ll_ip_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/ip_address" />

            <ImageView
                android:id="@+id/img_ip"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginStart="@dimen/fragment_network_iv_ip_margin_start"
                android:background="@drawable/ic_status_wrong"
                android:visibility="invisible" />

            <EditText
                android:id="@+id/edt_ip_address"
                style="@style/Network_EditText"
                android:background="@drawable/network_edit_bg" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_mask"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_ll_mask_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/subnet_mask" />

            <ImageView
                android:id="@+id/img_mask"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginStart="@dimen/fragment_network_iv_ip_margin_start"
                android:background="@drawable/ic_status_wrong"
                android:visibility="invisible" />

            <EditText
                android:id="@+id/edt_mask_address"
                style="@style/Network_EditText"
                android:background="@drawable/network_edit_bg" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_gateway"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_ll_mask_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:text="@string/gateway" />

            <ImageView
                android:id="@+id/img_gateway"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginStart="@dimen/fragment_network_iv_ip_margin_start"
                android:background="@drawable/ic_status_wrong"
                android:visibility="invisible" />

            <EditText
                android:id="@+id/edt_gateway"
                style="@style/Network_EditText"
                android:background="@drawable/network_edit_bg" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_dns1"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_ll_mask_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/fragment_network_dns_title_width"
                android:text="@string/DNS1" />

            <ImageView
                android:id="@+id/img_dns1"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginStart="@dimen/fragment_network_dns_content_margin_start"
                android:background="@drawable/ic_status_wrong"
                android:visibility="invisible" />

            <EditText
                android:id="@+id/edt_dns1"
                style="@style/Network_EditText"
                android:background="@drawable/network_edit_bg" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_dns2"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_ll_mask_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/fragment_network_dns_title_width"
                android:text="@string/DNS2" />

            <ImageView
                android:id="@+id/img_dns2"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginStart="@dimen/fragment_network_dns_content_margin_start"
                android:background="@drawable/ic_status_wrong"
                android:visibility="invisible" />

            <EditText
                android:id="@+id/edt_dns2"
                style="@style/Network_EditText"
                android:background="@drawable/network_edit_bg" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_btn"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_network_btn_confirm_margin_top"
            android:layout_marginBottom="@dimen/fragment_network_btn_confirm_margin_top">

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="@dimen/shutdown_btn_confirm_width"
                android:layout_height="@dimen/shutdown_btn_confirm_height"
                android:layout_marginStart="@dimen/fragment_network_btn_confirm_margin_start"
                android:background="@drawable/shape_shutdown_btn_white"
                android:gravity="center"
                android:stateListAnimator="@null"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black_100"
                android:textSize="@dimen/shutdown_btn_confirm_text_size"
                android:visibility="visible" />

            <Button
                android:id="@+id/btn_confirm"
                android:layout_width="@dimen/shutdown_btn_confirm_width"
                android:layout_height="@dimen/shutdown_btn_confirm_height"
                android:layout_marginStart="@dimen/fragment_network_btn_confirm_margin_start"
                android:background="@drawable/shape_shutdown_btn_green"
                android:gravity="center"
                android:stateListAnimator="@null"
                android:text="@string/confirm"
                android:textAllCaps="false"
                android:textColor="@color/text_enable_white"
                android:textSize="@dimen/shutdown_btn_confirm_text_size"
                android:visibility="visible" />
        </LinearLayout>

    </LinearLayout>

</ScrollView>