package com.eeo.ota.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 更新包信息
 */
public class VersionInfo implements Parcelable {
    /**
     * 下载地址
     */
    public String url;
    /**
     * 版本号
     */
    public String versionName;
    /**
     * 文件大小
     */
    public String fileSize;
    /**
     * 下载后的更新包本地路径
     */
    public String filePath;
    /**
     * md5
     */
    public String md5;
    /**
     * 更新说明
     */
    public String description;
    public String descriptionEn;

    public VersionInfo() {
    }


    public VersionInfo(Parcel in) {
        url = in.readString();
        versionName = in.readString();
        fileSize = in.readString();
        md5 = in.readString();
        description = in.readString();
        descriptionEn = in.readString();
    }

    public static final Creator<VersionInfo> CREATOR = new Creator<VersionInfo>() {
        @Override
        public VersionInfo createFromParcel(Parcel in) {
            return new VersionInfo(in);
        }

        @Override
        public VersionInfo[] newArray(int size) {
            return new VersionInfo[size];
        }
    };

    @Override
    public String toString() {
        return "VersionInfo{" +
                "url='" + url + '\'' +
                ", versionName='" + versionName + '\'' +
                ", fileSize='" + fileSize + '\'' +
                ", filePath='" + filePath + '\'' +
                ", md5='" + md5 + '\'' +
                ", description='" + description + '\'' +
                ", descriptionEn='" + descriptionEn + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(url);
        dest.writeString(versionName);
        dest.writeString(fileSize);
        dest.writeString(md5);
        dest.writeString(description);
        dest.writeString(descriptionEn);
    }
}
