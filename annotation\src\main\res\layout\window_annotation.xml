<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.eeo.annotation.view.DrawingView
        android:id="@+id/drawing_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/sleep_write_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <ImageView
            android:id="@+id/write_image"
            android:layout_width="@dimen/iv_write_without_screen_on_width"
            android:layout_height="@dimen/iv_write_without_screen_on_height"
            android:scaleType="centerInside"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="287dp"
            android:alpha="0.3"
            android:background="@drawable/pen_sleep_write" />

        <TextView
            android:id="@+id/write_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/write_image"
            android:layout_centerInParent="true"
            android:layout_marginStart="9dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="9dp"
            android:alpha="0.2"
            android:text="@string/sleep_write_status"
            android:textColor="#ffffffff"
            android:textSize="12sp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/write_status"
            android:layout_centerInParent="true"
            android:layout_margin="55dp">

            <ImageButton
                android:id="@+id/enter_screen"
                android:layout_width="160dp"
                android:layout_height="40dp"
                android:alpha="0.2"
                android:background="@drawable/enter_screen_bg" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:alpha="0.2"
                android:text="@string/enter_screen"
                android:textColor="#ffffffff"
                android:textSize="12sp" />
        </RelativeLayout>
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
