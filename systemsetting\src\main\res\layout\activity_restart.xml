<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/main_width"
    android:layout_height="@dimen/main_height"
    android:minWidth="@dimen/main_width"
    android:minHeight="@dimen/main_height"
    android:background="@drawable/shape_main_bg">

    <ImageView
        android:id="@+id/img_restart"
        style="@style/Shutdown_Icon"
        android:background="@drawable/ic_restart_bg" />

    <TextView
        style="@style/Shutdown_Text_Title"
        android:layout_toEndOf="@id/img_restart"
        android:text="@string/restart_title" />

    <TextView
        android:id="@+id/txt_content"
        style="@style/Shutdown_Text_Content"
        android:layout_below="@id/img_restart"
        android:text="@string/restart_content" />


    <Button
        android:id="@+id/btn_restart"
        style="@style/Shutdown_Btn_Confirm"
        android:layout_below="@id/txt_content"
        android:text="@string/restart" />

    <Button
        android:id="@+id/btn_cancel"
        style="@style/Shutdown_Btn_Cancel"
        android:layout_below="@id/txt_content"
        android:text="@string/cancel" />

</RelativeLayout>