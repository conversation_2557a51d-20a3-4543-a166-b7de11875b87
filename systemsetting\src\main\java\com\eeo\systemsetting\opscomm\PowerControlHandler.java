package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.util.Log;
import com.eeo.systemsetting.utils.PowerUtil;

/**
 * 关机控制处理器
 * 负责处理Windows发送的关机控制指令
 * 
 * 协议格式：
 * 关机命令：7F 08 99 A2 B3 C4 02 FF 01 01 CF
 * 关机响应：7F 09 99 A2 B3 C4 02 FF 01 01 XX CF (XX=01成功，XX=00失败)
 */
public class PowerControlHandler {
    private static final String TAG = "PowerControlHandler";
    
    // 关机控制相关常量
    private static final byte[] POWER_OFF_PATTERN = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x01, (byte) 0xCF
    };
    
    private static final byte[] POWER_OFF_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x01
    };
    
    // 响应状态码
    private static final byte RESPONSE_SUCCESS = (byte) 0x01;
    private static final byte RESPONSE_FAILURE = (byte) 0x00;
    private static final byte FRAME_TAIL = (byte) 0xCF;
    
    private Context mContext;
    
    public PowerControlHandler(Context context) {
        mContext = context;
        Log.d(TAG, "PowerControlHandler initialized");
    }
    
    /**
     * 检查是否为关机控制命令
     */
    public boolean isPowerOffCommand(byte[] packet) {
        if (packet == null || packet.length != POWER_OFF_PATTERN.length) {
            return false;
        }
        
        // 比较命令模式
        for (int i = 0; i < POWER_OFF_PATTERN.length; i++) {
            if (packet[i] != POWER_OFF_PATTERN[i]) {
                return false;
            }
        }
        
        Log.d(TAG, "Detected power off command");
        return true;
    }
    
    /**
     * 处理关机控制请求
     * @return 响应数据包
     */
    public byte[] handlePowerOffCommand() {
        Log.d(TAG, "Processing power off command...");
        
        try {
            // 检查关机权限
            boolean hasPermission = checkPowerOffPermission();
            if (!hasPermission) {
                Log.w(TAG, "No power off permission, returning failure response");
                return buildPowerOffResponse(RESPONSE_FAILURE);
            }
            
            // 执行关机操作
            Log.i(TAG, "Power off command received - executing shutdown...");
            performActualShutdown();
            
            return buildPowerOffResponse(RESPONSE_SUCCESS);
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while handling power off command", e);
            return buildPowerOffResponse(RESPONSE_FAILURE);
        }
    }
    
    /**
     * 检查关机权限
     */
    private boolean checkPowerOffPermission() {
        try {
            // 检查是否为系统应用
            int uid = android.os.Process.myUid();
            boolean isSystemApp = (uid == android.os.Process.SYSTEM_UID);
            
            Log.d(TAG, "Power off permission check - UID: " + uid + ", isSystemApp: " + isSystemApp);
            
            // 系统应用通常有关机权限
            return isSystemApp;
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while checking power off permission", e);
            return false;
        }
    }
    
    /**
     * 构建关机响应数据包
     * @param statusCode 状态码 (0x01=成功, 0x00=失败)
     */
    private byte[] buildPowerOffResponse(byte statusCode) {
        byte[] response = new byte[POWER_OFF_RESPONSE_PREFIX.length + 2]; // +1状态码 +1帧尾
        
        // 复制响应前缀
        System.arraycopy(POWER_OFF_RESPONSE_PREFIX, 0, response, 0, POWER_OFF_RESPONSE_PREFIX.length);
        
        // 添加状态码
        response[POWER_OFF_RESPONSE_PREFIX.length] = statusCode;
        
        // 添加帧尾
        response[response.length - 1] = FRAME_TAIL;
        
        String statusText = (statusCode == RESPONSE_SUCCESS) ? "SUCCESS" : "FAILURE";
        Log.d(TAG, "Built power off response (" + statusText + "): " + bytesToHexString(response));
        
        return response;
    }
    
    /**
     * 执行实际关机操作
     * 使用项目定制化的关机方法
     */
    private void performActualShutdown() {
        try {
            Log.i(TAG, "Executing shutdown using PowerUtil.showShutdownDialog()");
            PowerUtil.getInstance(mContext).showShutdownDialog();
        } catch (Exception e) {
            Log.e(TAG, "Exception while executing shutdown", e);
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b & 0xFF));
        }
        return sb.toString().trim();
    }
} 