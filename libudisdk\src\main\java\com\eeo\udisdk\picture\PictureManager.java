package com.eeo.udisdk.picture;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 图像相关
 */
public class PictureManager {
    private static final String TAG = "Udi-PictureManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;

    public PictureManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    /**
     * 是否护眼模式
     */
    public boolean isEyeCareEnabled() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/picture/eyecare/enable"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isEyeCareEnable:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isEyeCareEnabled body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("enable");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 设置护眼模式
     */
    public boolean enableEyeCare(boolean enable) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("enable", enable);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/picture/eyecare/enable"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "enableEyeCare:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取屏幕亮度
     * @return
     */
    public int getBrightnessValue() {
        Gson gson = new Gson();
        String body = "-1";
        int value = -1;
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/picture/backlight/current"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();

            if (response.code() == 200) {
                 body = response.body().string();
                JsonParser jp = new JsonParser();
                //将json字符串转化成json对象
                JsonObject jo = jp.parse(body).getAsJsonObject();
                value = jo.get("value").getAsInt();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 设置屏幕亮度
     * @param value
     * @return
     */
    public boolean setBrightnessValue(int value){
        boolean success = false;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("value", value);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        OkHttpClient client = UdiSdk.newOkHttpClientBuilder().build();
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/picture/backlight/current"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = client.newCall(request).execute();
            Log.i(TAG, "setBrightnessValue: response : " + response);
            success = response.code() == 200;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return success;

    }


}
