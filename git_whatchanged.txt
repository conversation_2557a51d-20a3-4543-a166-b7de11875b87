commit d5f927b88e7c6f17cb6f5f9d61f305ba2a783db5
Author: chensibo <<EMAIL>>
Date:   Mon Jul 14 11:20:38 2025 +0800

    [systemsetting]新增集控通信功能；响应mac地址、关机、信号源切换功能

:100644 100644 554d2a5 1dc6cd7 M	systemsetting/build.gradle
:000000 100644 0000000 5edf329 A	systemsetting/src/main/cpp/CMakeLists.txt
:000000 100644 0000000 18fabdf A	systemsetting/src/main/cpp/serialport.c
:000000 100644 0000000 8d1b532 A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/JniSerialPortManager.java
:000000 100644 0000000 df4ef92 A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/MacAddressHandler.java
:000000 100644 0000000 3ef8eaa A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/OpsCommManager.java
:000000 100644 0000000 4220643 A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/PowerControlHandler.java
:000000 100644 0000000 413c8a8 A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/ProtocolHandler.java
:000000 100644 0000000 1a5faa7 A	systemsetting/src/main/java/com/eeo/systemsetting/opscomm/SignalSwitchHandler.java
:100644 100644 1a12ea2 e641cb0 M	systemsetting/src/main/java/com/eeo/systemsetting/service/SystemSettingService.java
