#include <jni.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <android/log.h>

#define TAG "SerialPortJNI"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

/**
 * 打开串口设备
 */
JNIEXPORT jint JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeOpenSerialPort(JNIEnv *env, jobject thiz, jstring devicePath) {
    const char *path = (*env)->GetStringUTFChars(env, devicePath, NULL);
    if (path == NULL) {
        LOGE("Failed to get device path string");
        return -1;
    }

    LOGI("Opening serial port: %s", path);

    // 使用O_RDWR | O_NOCTTY | O_NDELAY标志打开设备
    // O_RDWR: 读写模式
    // O_NOCTTY: 不将设备设为控制终端
    // O_NDELAY: 非阻塞模式
    int fd = open(path, O_RDWR | O_NOCTTY | O_NDELAY);
    
    if (fd < 0) {
        LOGE("Failed to open serial port %s: %s", path, strerror(errno));
        (*env)->ReleaseStringUTFChars(env, devicePath, path);
        return -1;
    }

    // 设置为阻塞模式（清除O_NDELAY标志）
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        LOGE("Failed to get file flags: %s", strerror(errno));
        close(fd);
        (*env)->ReleaseStringUTFChars(env, devicePath, path);
        return -1;
    }
    
    if (fcntl(fd, F_SETFL, flags & ~O_NDELAY) == -1) {
        LOGE("Failed to set file flags: %s", strerror(errno));
        close(fd);
        (*env)->ReleaseStringUTFChars(env, devicePath, path);
        return -1;
    }

    LOGI("Serial port opened successfully, fd: %d", fd);
    (*env)->ReleaseStringUTFChars(env, devicePath, path);
    return fd;
}

/**
 * 配置串口参数
 */
JNIEXPORT jint JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeConfigureSerialPort(JNIEnv *env, jobject thiz, 
                                                                                  jint fd, jint baudRate, 
                                                                                  jint dataBits, jint stopBits, 
                                                                                  jint parity) {
    struct termios options;
    
    LOGI("Configuring serial port fd=%d, baud=%d, data=%d, stop=%d, parity=%d", 
         fd, baudRate, dataBits, stopBits, parity);

    // 获取当前配置
    if (tcgetattr(fd, &options) != 0) {
        LOGE("Failed to get terminal attributes: %s", strerror(errno));
        return -1;
    }

    // 设置为原始模式（RAW模式）
    cfmakeraw(&options);

    // 设置波特率
    speed_t speed;
    switch (baudRate) {
        case 9600:   speed = B9600;   break;
        case 19200:  speed = B19200;  break;
        case 38400:  speed = B38400;  break;
        case 57600:  speed = B57600;  break;
        case 115200: speed = B115200; break;
        default:
            LOGE("Unsupported baud rate: %d", baudRate);
            return -2;
    }
    
    if (cfsetispeed(&options, speed) != 0 || cfsetospeed(&options, speed) != 0) {
        LOGE("Failed to set baud rate: %s", strerror(errno));
        return -3;
    }

    // 控制标志 (c_cflag)
    options.c_cflag &= ~CSIZE;  // 清除数据位掩码
    
    // 设置数据位
    switch (dataBits) {
        case 5: options.c_cflag |= CS5; break;
        case 6: options.c_cflag |= CS6; break;
        case 7: options.c_cflag |= CS7; break;
        case 8: options.c_cflag |= CS8; break;
        default:
            LOGE("Unsupported data bits: %d", dataBits);
            return -4;
    }

    // 设置停止位
    if (stopBits == 1) {
        options.c_cflag &= ~CSTOPB;  // 1个停止位
    } else if (stopBits == 2) {
        options.c_cflag |= CSTOPB;   // 2个停止位
    } else {
        LOGE("Unsupported stop bits: %d", stopBits);
        return -5;
    }

    // 设置校验位
    options.c_cflag &= ~PARENB;  // 清除校验位
    options.c_cflag &= ~PARODD;  // 清除奇校验
    if (parity == 1) {
        options.c_cflag |= PARENB;   // 偶校验
    } else if (parity == 2) {
        options.c_cflag |= PARENB | PARODD;  // 奇校验
    }
    // parity == 0 时无校验（已清除）

    // 设置其他控制标志
    options.c_cflag |= CLOCAL;   // 本地连接，不使用调制解调器控制
    options.c_cflag |= CREAD;    // 使能接收器

    // 输入标志 (c_iflag) - 完全禁用所有输入处理
    options.c_iflag = 0;
    options.c_iflag &= ~(IGNBRK | BRKINT | PARMRK | ISTRIP | INLCR | IGNCR | ICRNL | IXON | IXOFF | IXANY);

    // 输出标志 (c_oflag) - 完全禁用所有输出处理
    options.c_oflag = 0;
    options.c_oflag &= ~OPOST;

    // 本地标志 (c_lflag) - 完全禁用所有本地处理
    options.c_lflag = 0;
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ECHOK | ECHONL | ISIG | IEXTEN);

    // 设置读取参数
    options.c_cc[VMIN] = 0;   // 非阻塞读取，最少0个字符
    options.c_cc[VTIME] = 1;  // 超时时间100ms（1 * 100ms）

    // 应用设置
    if (tcsetattr(fd, TCSANOW, &options) != 0) {
        LOGE("Failed to set terminal attributes: %s", strerror(errno));
        return -6;
    }

    // 验证设置是否生效
    struct termios verify_options;
    if (tcgetattr(fd, &verify_options) == 0) {
        LOGI("Serial port configuration verified:");
        LOGI("  Input flags: 0x%08x", verify_options.c_iflag);
        LOGI("  Output flags: 0x%08x", verify_options.c_oflag);
        LOGI("  Control flags: 0x%08x", verify_options.c_cflag);
        LOGI("  Local flags: 0x%08x", verify_options.c_lflag);
        LOGI("  VMIN: %d, VTIME: %d", verify_options.c_cc[VMIN], verify_options.c_cc[VTIME]);
    }

    LOGI("Serial port configured successfully");
    return 0;
}

/**
 * 清空串口缓冲区
 */
JNIEXPORT jint JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeFlushSerialBuffers(JNIEnv *env, jobject thiz, jint fd) {
    LOGI("Flushing serial port buffers for fd=%d", fd);

    // 清空输入和输出缓冲区
    if (tcflush(fd, TCIOFLUSH) != 0) {
        LOGE("Failed to flush serial buffers: %s", strerror(errno));
        return -1;
    }

    // 等待所有输出数据传输完成
    if (tcdrain(fd) != 0) {
        LOGE("Failed to drain serial port: %s", strerror(errno));
        return -2;
    }

    LOGI("Serial port buffers flushed successfully");
    return 0;
}

/**
 * 读取数据
 */
JNIEXPORT jint JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeReadData(JNIEnv *env, jobject thiz, 
                                                                       jint fd, jbyteArray buffer, 
                                                                       jint bufferSize) {
    jbyte *nativeBuffer = (*env)->GetByteArrayElements(env, buffer, NULL);
    if (nativeBuffer == NULL) {
        LOGE("Failed to get byte array elements");
        return -1;
    }

    // 使用read系统调用直接读取数据
    ssize_t bytesRead = read(fd, nativeBuffer, bufferSize);
    
    if (bytesRead < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            // 非阻塞模式下无数据可读
            (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
            return 0;
        } else {
            LOGE("Error reading from serial port: %s", strerror(errno));
            (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
            return -1;
        }
    }

    if (bytesRead > 0) {
        // LOGD("JNI read %zd bytes", bytesRead);
        
        /* === 详细RAW数据日志（已注释，调试时可启用） ===
        // 打印原始字节数据进行验证
        LOGI("JNI read %zd bytes:", bytesRead);
        char hexStr[bytesRead * 3 + 1];
        hexStr[0] = '\0';
        for (int i = 0; i < bytesRead; i++) {
            char temp[4];
            snprintf(temp, sizeof(temp), "%02X ", (unsigned char)nativeBuffer[i]);
            strcat(hexStr, temp);
        }
        LOGI("JNI raw bytes: %s", hexStr);

        // 检查关键字节是否被转换
        for (int i = 0; i < bytesRead; i++) {
            unsigned char byte = (unsigned char)nativeBuffer[i];
            if (byte == 0xC4) {
                LOGI("Found original 0xC4 at position %d", i);
            } else if (byte == 0xE4) {
                LOGW("Found converted 0xE4 at position %d (should be 0xC4)", i);
            } else if (byte == 0xCF) {
                LOGI("Found original 0xCF at position %d", i);
            } else if (byte == 0xEF) {
                LOGW("Found converted 0xEF at position %d (should be 0xCF)", i);
            }
        }
        === 详细RAW数据日志结束 === */
    }

    // 释放字节数组，JNI_COMMIT表示将修改提交到Java数组
    (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
    
    return (jint)bytesRead;
}

/**
 * 写入数据
 */
JNIEXPORT jint JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeWriteData(JNIEnv *env, jobject thiz, 
                                                                        jint fd, jbyteArray data, 
                                                                        jint length) {
    jbyte *nativeData = (*env)->GetByteArrayElements(env, data, NULL);
    if (nativeData == NULL) {
        LOGE("Failed to get byte array elements for writing");
        return -1;
    }

    // LOGD("JNI writing %d bytes", length);
    
    /* === 详细写入数据日志（已注释，调试时可启用） ===
    // 打印要发送的数据
    LOGI("JNI writing %d bytes:", length);
    char hexStr[length * 3 + 1];
    hexStr[0] = '\0';
    for (int i = 0; i < length; i++) {
        char temp[4];
        snprintf(temp, sizeof(temp), "%02X ", (unsigned char)nativeData[i]);
        strcat(hexStr, temp);
    }
    LOGI("JNI write bytes: %s", hexStr);
    === 详细写入数据日志结束 === */

    // 使用write系统调用直接写入数据
    ssize_t bytesWritten = write(fd, nativeData, length);
    
    if (bytesWritten < 0) {
        LOGE("Error writing to serial port: %s", strerror(errno));
        (*env)->ReleaseByteArrayElements(env, data, nativeData, JNI_ABORT);
        return -1;
    }

    // 确保数据立即发送
    if (fsync(fd) != 0) {
        LOGW("Warning: Failed to sync serial port: %s", strerror(errno));
    }

    LOGI("JNI successfully wrote %zd bytes", bytesWritten);
    
    (*env)->ReleaseByteArrayElements(env, data, nativeData, JNI_ABORT);
    return (jint)bytesWritten;
}

/**
 * 关闭串口设备
 */
JNIEXPORT void JNICALL
Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeCloseSerialPort(JNIEnv *env, jobject thiz, jint fd) {
    LOGI("Closing serial port fd=%d", fd);
    
    if (fd >= 0) {
        if (close(fd) != 0) {
            LOGE("Error closing serial port: %s", strerror(errno));
        } else {
            LOGI("Serial port closed successfully");
        }
    } else {
        LOGW("Invalid file descriptor for closing: %d", fd);
    }
}

/**
 * JNI库加载时的初始化函数
 */
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
    LOGI("SerialPort JNI library loaded");
    return JNI_VERSION_1_6;
}

/**
 * JNI库卸载时的清理函数
 */
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM *vm, void *reserved) {
    LOGI("SerialPort JNI library unloaded");
} 