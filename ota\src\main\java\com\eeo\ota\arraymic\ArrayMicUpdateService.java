package com.eeo.ota.arraymic;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.util.Log;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.os.Build;

import com.eeo.ota.callback.SubDeviceUpdateCallback;

/**
 * Background service to manage the lifecycle of the Array Microphone update process.
 */
public class ArrayMicUpdateService extends Service {

    private static final String TAG = "ArrayMicOTA";
    private static final String CHANNEL_ID = "eeo_array_mic_ota_channel";

    private ArrayMicUpdate mArrayMicUpdate;
    private SubDeviceUpdateCallback mExternalCallback;
    private static SubDeviceUpdateCallback sStaticExternalCallback;

    private final SubDeviceUpdateCallback mInternalCallback = new SubDeviceUpdateCallback() {
        @Override
        public void onUpdateSuccess() {
            Log.i(TAG, "Internal callback: Update success.");
            if (mExternalCallback != null) {
                mExternalCallback.onUpdateSuccess();
            }
        }

        @Override
        public void onUpdateFail(String errMsg) {
            Log.e(TAG, "Internal callback: Update fail: " + errMsg);
            if (mExternalCallback != null) {
                mExternalCallback.onUpdateFail(errMsg);
            }
        }

        @Override
        public void onAllUpdateFinish() {
            Log.i(TAG, "Internal callback: All updates finished. Stopping service.");
            if (mExternalCallback != null) {
                mExternalCallback.onAllUpdateFinish();
            }
            stopSelf();
        }

        @Override
        public void onUpdateProgressChanged(int progress) {
            if (mExternalCallback != null) {
                mExternalCallback.onUpdateProgressChanged(progress);
            }
        }
    };

    public class ArrayMicBinder extends Binder {
        public ArrayMicUpdateService getService() {
            return ArrayMicUpdateService.this;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return new ArrayMicBinder();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate.");
        startForegroundService();
        // Set static external callback
        mExternalCallback = sStaticExternalCallback;
        mArrayMicUpdate = new ArrayMicUpdate(this, mInternalCallback);
    }

    private void startForegroundService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager nm = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, "Array Mic Update", NotificationManager.IMPORTANCE_LOW);
            nm.createNotificationChannel(channel);
            Notification notification = new Notification.Builder(this, CHANNEL_ID)
                    .setContentTitle("Array Mic Update")
                    .setContentText("Array microphone update service is running.")
                    .build();
            startForeground(3, notification); // Use a unique ID
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand.");
        // The entire logic flow, including the check, is now managed by the updater.
        // The service's job is just to start the process.
        mArrayMicUpdate.update();
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service onDestroy.");
        if (mArrayMicUpdate != null) {
            mArrayMicUpdate.release();
        }
        super.onDestroy();
    }

    public static void setExternalCallback(SubDeviceUpdateCallback callback) {
        sStaticExternalCallback = callback;
    }
}
