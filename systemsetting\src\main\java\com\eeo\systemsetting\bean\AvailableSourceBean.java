package com.eeo.systemsetting.bean;

import java.util.List;

public class AvailableSourceBean {

    private List<SourcesBean> sources;

    public List<SourcesBean> getSources() {
        return sources;
    }

    public void setSources(List<SourcesBean> sources) {
        this.sources = sources;
    }

    public static class SourcesBean {
        /**
         * sourceItem : PC
         * originName : PC
         * name : PC
         * visible : true
         * hideWhenNoSignal : false
         * hasSignal : true
         */

        private String sourceItem;
        private String originName;
        private String name;
        private boolean visible;
        private boolean hideWhenNoSignal;
        private boolean hasSignal;

        public String getSourceItem() {
            return sourceItem;
        }

        public void setSourceItem(String sourceItem) {
            this.sourceItem = sourceItem;
        }

        public String getOriginName() {
            return originName;
        }

        public void setOriginName(String originName) {
            this.originName = originName;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isVisible() {
            return visible;
        }

        public void setVisible(boolean visible) {
            this.visible = visible;
        }

        public boolean isHideWhenNoSignal() {
            return hideWhenNoSignal;
        }

        public void setHideWhenNoSignal(boolean hideWhenNoSignal) {
            this.hideWhenNoSignal = hideWhenNoSignal;
        }

        public boolean isHasSignal() {
            return hasSignal;
        }

        public void setHasSignal(boolean hasSignal) {
            this.hasSignal = hasSignal;
        }
    }
}
