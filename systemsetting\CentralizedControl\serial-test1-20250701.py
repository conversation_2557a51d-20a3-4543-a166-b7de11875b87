#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows与Android串口通信测试脚本
用于验证MAC地址获取命令的数据传输
"""

import serial
import time
import sys

def main():
    print("=" * 60)
    print("Windows-Android串口通信测试")
    print("=" * 60)
    
    # 串口配置参数
    PORT = input("请输入串口号 (例如: COM1, COM3): ").strip()
    if not PORT:
        PORT = "COM1"  # 默认值
    
    BAUDRATE = 9600
    BYTESIZE = 8
    PARITY = 'N'
    STOPBITS = 1
    TIMEOUT = 2.0  # 2秒超时
    
    # MAC地址获取命令 (13字节)
    MAC_REQUEST_CMD = bytes([
        0x7F, 0x0A, 0x99, 0xA2, 0xB3, 0xC4, 0x02, 
        0xFF, 0xF1, 0x04, 0xFF, 0xFF, 0xCF
    ])
    
    print(f"串口配置:")
    print(f"  端口: {PORT}")
    print(f"  波特率: {BAUDRATE}")
    print(f"  数据位: {BYTESIZE}")
    print(f"  校验位: {PARITY}")
    print(f"  停止位: {STOPBITS}")
    print(f"  超时: {TIMEOUT}s")
    print(f"  流控制: 禁用")
    
    try:
        # 打开串口
        print(f"\n正在打开串口 {PORT}...")
        ser = serial.Serial(
            port=PORT,
            baudrate=BAUDRATE,
            bytesize=BYTESIZE,
            parity=PARITY,
            stopbits=STOPBITS,
            timeout=TIMEOUT,
            rtscts=False,      # 禁用RTS/CTS硬件流控制
            dsrdtr=False,      # 禁用DSR/DTR硬件流控制
            xonxoff=False      # 禁用软件流控制
        )
        
        print(f"串口 {PORT} 打开成功!")
        
        # 清空串口缓冲区
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        print("串口缓冲区已清空")
        
        # 等待串口稳定
        time.sleep(0.5)
        
        # 准备发送命令
        print(f"\n准备发送MAC地址获取命令:")
        cmd_hex = ' '.join([f'{b:02X}' for b in MAC_REQUEST_CMD])
        print(f"命令内容: {cmd_hex}")
        print(f"命令长度: {len(MAC_REQUEST_CMD)} 字节")
        
        input("\n按回车键发送命令...")
        
        # 记录发送时间
        send_time = time.time()
        
        # 发送命令
        bytes_sent = ser.write(MAC_REQUEST_CMD)
        ser.flush()  # 确保数据发送
        
        print(f"\n✅ 命令发送成功!")
        print(f"发送时间: {time.strftime('%H:%M:%S', time.localtime(send_time))}.{int((send_time % 1) * 1000):03d}")
        print(f"发送字节数: {bytes_sent}")
        print(f"发送数据: {cmd_hex}")
        
        # 等待响应
        print(f"\n等待Android响应 (超时 {TIMEOUT} 秒)...")
        response_data = b''
        start_time = time.time()
        
        while (time.time() - start_time) < TIMEOUT:
            if ser.in_waiting > 0:
                # 读取可用数据
                chunk = ser.read(ser.in_waiting)
                response_data += chunk
                
                # 打印接收到的数据块
                chunk_hex = ' '.join([f'{b:02X}' for b in chunk])
                recv_time = time.time()
                print(f"接收数据块 ({len(chunk)} 字节): {chunk_hex}")
                print(f"接收时间: {time.strftime('%H:%M:%S', time.localtime(recv_time))}.{int((recv_time % 1) * 1000):03d}")
                
                # 检查是否收到完整响应 (简单长度判断)
                if len(response_data) >= 17:  # MAC响应通常17字节
                    print("可能收到完整响应，继续等待0.5秒...")
                    time.sleep(0.5)
                    # 读取剩余数据
                    if ser.in_waiting > 0:
                        final_chunk = ser.read(ser.in_waiting)
                        response_data += final_chunk
                        if final_chunk:
                            final_hex = ' '.join([f'{b:02X}' for b in final_chunk])
                            print(f"最终数据块 ({len(final_chunk)} 字节): {final_hex}")
                    break
                    
            time.sleep(0.01)  # 避免CPU占用过高
        
        # 分析接收结果
        print(f"\n" + "=" * 60)
        print("接收结果分析:")
        print("=" * 60)
        
        if response_data:
            total_hex = ' '.join([f'{b:02X}' for b in response_data])
            print(f"✅ 收到响应数据!")
            print(f"响应长度: {len(response_data)} 字节")
            print(f"响应内容: {total_hex}")
            
            # 检查是否为预期的MAC响应格式
            if len(response_data) >= 17 and response_data[0] == 0x7F:
                print(f"\n🎯 响应格式分析:")
                print(f"帧头: {response_data[0]:02X} (期望: 7F)")
                if len(response_data) > 1:
                    print(f"长度: {response_data[1]:02X} (期望: 0E 或其他)")
                if len(response_data) > 6:
                    print(f"命令回显: {' '.join([f'{b:02X}' for b in response_data[2:7]])}")
                if len(response_data) >= 17:
                    print(f"帧尾: {response_data[-1]:02X} (期望: CF)")
                    mac_bytes = response_data[10:16] if len(response_data) >= 16 else b''
                    if mac_bytes:
                        mac_str = ':'.join([f'{b:02X}' for b in mac_bytes])
                        print(f"MAC地址: {mac_str}")
        else:
            print(f"❌ 未收到任何响应数据")
            print(f"可能原因:")
            print(f"  1. Android端未运行或未启动串口监听")
            print(f"  2. 串口连接问题")
            print(f"  3. 波特率不匹配")
            print(f"  4. 数据传输错误")
        
        # 验证关键字节
        print(f"\n🔍 关键字节验证:")
        print(f"发送的关键字节: C4={0xC4:02X}, CF={0xCF:02X}")
        if response_data:
            # 在响应中查找这些字节或转换后的字节
            has_c4 = 0xC4 in response_data
            has_e4 = 0xE4 in response_data
            has_cf = 0xCF in response_data
            has_ef = 0xEF in response_data
            
            print(f"响应中的字节情况:")
            print(f"  C4 存在: {has_c4}")
            print(f"  E4 存在: {has_e4} {'(转换后)' if has_e4 and not has_c4 else ''}")
            print(f"  CF 存在: {has_cf}")
            print(f"  EF 存在: {has_ef} {'(转换后)' if has_ef and not has_cf else ''}")
            
            if has_e4 and not has_c4:
                print(f"⚠️  检测到C4→E4转换")
            if has_ef and not has_cf:
                print(f"⚠️  检测到CF→EF转换")
            if (has_c4 or not has_e4) and (has_cf or not has_ef):
                print(f"✅ 未检测到字节转换问题")
        
    except serial.SerialException as e:
        print(f"❌ 串口错误: {e}")
        print(f"请检查:")
        print(f"  1. 串口号是否正确")
        print(f"  2. 串口是否被其他程序占用")
        print(f"  3. 串口硬件连接是否正常")
        
    except KeyboardInterrupt:
        print(f"\n用户中断操作")
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        
    finally:
        try:
            if 'ser' in locals() and ser.is_open:
                ser.close()
                print(f"\n串口 {PORT} 已关闭")
        except:
            pass
    
    print(f"\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()