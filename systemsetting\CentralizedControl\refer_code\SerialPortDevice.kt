package com.eeo.WhiteboardAccelerate.SerialPort

import android.util.Log
import com.eeo.WhiteboardAccelerate.SkiaUtils.native_serialport_close
import com.eeo.WhiteboardAccelerate.SkiaUtils.native_serialport_open
import java.io.*

class SerialPortDevice {
    private val mFd: FileDescriptor?
    private val mFileInputStream: FileInputStream
    private val mFileOutputStream: FileOutputStream
    private val TAG = "SerialPortDevice"

    constructor(device: File, baudRate: Int, flags: Int){
        if (!device.canRead() || !device.canWrite()) {
            Log.e(TAG, "serial port permission is not 777")
            /* Missing read/write permission, trying to chmod the file */
            val su: Process = Runtime.getRuntime().exec("/system/bin/su")
            val cmd =  "chmod 666 " + device.getAbsolutePath()+"exit\n";
            su.outputStream.write(cmd.toByteArray())
            if (su.waitFor() != 0 || !device.canRead() || !device.canWrite()) {
                throw SecurityException()
            }
        }
        mFd = native_serialport_open(device.absolutePath, baudRate, flags)
        if (mFd == null) {
            throw IOException()
        }
        mFileInputStream = FileInputStream(mFd)
        mFileOutputStream = FileOutputStream(mFd)
    }

    fun getInputStream():InputStream{
        return  mFileInputStream;
    }

    fun getOutputStream():OutputStream{
        return mFileOutputStream
    }

    fun close(){
        mFd?.let { native_serialport_close(it) };
    }

}