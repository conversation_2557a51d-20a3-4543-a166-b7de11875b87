package cn.eeo.classin.setup;

import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;

import com.elvishew.xlog.XLog;

import cn.eeo.classin.setup.databinding.FragmentFirstBinding;
import cn.eeo.classin.setup.utils.CommonUtils;

public class FirstFragment extends Fragment {

    private static final String TAG = "FirstFragment";
    private FragmentFirstBinding binding;
    private int clickCount = 0;
    private static final int MAX_CLICKS = 5;
    private static final long TIME_INTERVAL = 1000; // 1秒
    private Handler handler = new Handler();
    private Runnable resetClickCountRunnable = () -> clickCount = 0;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentFirstBinding.inflate(inflater, container, false);
        XLog.d(TAG + "onCreateView");
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.activateBt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(FirstFragment.this)
                        .navigate(R.id.action_FirstFragment_to_SecondFragment);
            }
        });
        binding.transparentButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 按钮点击时增加计数
                clickCount++;

                // 如果点击次数达到设定的次数，显示Toast并重置计数
                if (clickCount >= MAX_CLICKS) {
                    //showToast("Button clicked 5 times!");
                    startSetting();
                    CommonUtils.finishSetupWizard(getContext());
                    getActivity().finish();
                    clickCount = 0;
                }
                // 启动计时器，5秒后重置计数
                handler.removeCallbacks(resetClickCountRunnable);
                handler.postDelayed(resetClickCountRunnable, TIME_INTERVAL * MAX_CLICKS);
                XLog.d("ON CLICK ");
                Log.d(TAG, "ON CLICK ");
            }
        });

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    public void startSetting() {
        XLog.d("startSetting");
        PackageManager pm = getContext().getPackageManager();
        String packageName = "com.eeo.systemsetting";
        String ActivityName = "com.eeo.systemsetting.launcher.FallbackHomeActivity";
        //String ActivityName = "com.eeo.systemsetting.launcher.TifPlayerActivity";
        ComponentName component = new ComponentName(packageName, ActivityName);
        //int status = pm.getComponentEnabledSetting(component);
        //XLog.d( "status:" + status);
        try {
            Intent intent = new Intent(); //pm.getLaunchIntentForPackage(packageName);//
            intent.setComponent(component);
            intent.setAction("com.cvte.intent.ACTION_TIF_PLAYER_ACTIVITY");
            intent.addCategory("android.intent.category.DEFAULT");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            intent.putExtra("from", "setup");
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }

    }

}