package com.eeo.systemsetting.fragment;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Handler;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.popupwindow.AutoManualPopupWindow;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.network.EthernetConfig;

import butterknife.BindView;
import butterknife.OnClick;
import butterknife.OnFocusChange;
import butterknife.OnTextChanged;

public class NetWorkFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener, View.OnScrollChangeListener {
    public static final String TAG = "NetWorkFragment===";
    @BindView(R.id.scrollview)
    ScrollView scrollview;
    @BindView(R.id.sw_network)
    Switch swNetwork;
    @BindView(R.id.txt_mac_address)
    TextView txtMacAddress;
    @BindView(R.id.txt_ip_setting)
    TextView txtIpSetting;
    @BindView(R.id.img_arrow)
    ImageView imgArrow;
    @BindView(R.id.ll_mac_address)
    LinearLayout llMacAddress;
    @BindView(R.id.ll_ip_mode)
    LinearLayout llIpMode;
    @BindView(R.id.ll_ip)
    LinearLayout llIp;
    @BindView(R.id.img_ip)
    ImageView imgIp;
    @BindView(R.id.edt_ip_address)
    EditText edtIpAddress;
    @BindView(R.id.ll_mask)
    LinearLayout llMask;
    @BindView(R.id.img_mask)
    ImageView imgMask;
    @BindView(R.id.edt_mask_address)
    EditText edtMaskAddress;
    @BindView(R.id.ll_gateway)
    LinearLayout llGateway;
    @BindView(R.id.img_gateway)
    ImageView imgGateway;
    @BindView(R.id.edt_gateway)
    EditText edtGateway;
    @BindView(R.id.ll_dns1)
    LinearLayout llDns1;
    @BindView(R.id.img_dns1)
    ImageView imgDns1;
    @BindView(R.id.edt_dns1)
    EditText edtDns1;
    @BindView(R.id.ll_dns2)
    LinearLayout llDns2;
    @BindView(R.id.img_dns2)
    ImageView imgDns2;
    @BindView(R.id.edt_dns2)
    EditText edtDns2;
    @BindView(R.id.ll_btn)
    LinearLayout llBtn;

    @BindView(R.id.btn_cancel)
    Button btnCancel;

    @BindView(R.id.btn_confirm)
    Button btnConfirm;

    //自动还是手动标记
    private int selectIndex = -1;
    private final int AUTO = 0;
    private final int MANUAL = 1;
    private final String ETHERNET_MANUAL = "MANUAL";
    private final String ETHERNET_DHCP = "DHCP";
    //当手动时候点击确定之后，给个标记，让按钮置灰不让点击
    private boolean isSetting = true;
    private NetWorkChangeBroadcast netWorkChangeBroadcast;

    private final int IP_ADDRESS = 0;
    private final int SUBNET_MASK = 1;
    private final int GATEWAY = 2;
    private final int DNS1 = 3;
    private final int DNS2 = 4;

    private boolean isScrollDown = false;

    private boolean ipSettingClickable = true;

    /**
     * 是否读取过网络模式
     */
    private boolean mGetEthernetMode = false;

    private InputMethodManager inputMethodManager;

    private final static String IP_NULL = "0.0.0.0";

    @Override
    public int getLayout() {
        return R.layout.fragment_network;
    }

    @Override
    public void initDate() {
        scrollview.setOnScrollChangeListener(this);
        //设置监听
        swNetwork.setOnCheckedChangeListener(this);

    }

    @Override
    public void onStart() {
        super.onStart();
        //获取开关状态
        boolean ethernetEnabled = SaveDateUtils.isEthernetEnable(getContext());
        swNetwork.setChecked(ethernetEnabled);
        if (!ethernetEnabled) {
            setAllItemVisible(false);
        }
        if (netWorkChangeBroadcast == null) {
            netWorkChangeBroadcast = new NetWorkChangeBroadcast();
        }
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        getContext().registerReceiver(netWorkChangeBroadcast, filter);
    }

    @Override
    public void onStop() {
        super.onStop();
        if (netWorkChangeBroadcast != null) {
            getContext().unregisterReceiver(netWorkChangeBroadcast);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();


    }

    @OnClick({R.id.txt_ip_setting, R.id.img_arrow, R.id.btn_confirm, R.id.btn_cancel})
    public void onClick(View view) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.txt_ip_setting:
            case R.id.img_arrow:
                if (!ipSettingClickable) {
                    return;
                }
                hideInputMethod(view.getWindowToken());

                AutoManualPopupWindow autoManualPopupWindow = new AutoManualPopupWindow(getActivity(), imgArrow, selectIndex);
                autoManualPopupWindow.setAutoManualClickCallback(new AutoManualPopupWindow.OnClickCallback() {
                    @Override
                    public void onClickCallback(int index) {
                        if (selectIndex != index) {
                            selectIndex = index;
                            isSetting = true;
                            if (index == AUTO) {
                                txtIpSetting.setText(getContext().getString(R.string.auto));
                                llBtn.setVisibility(View.GONE);
                                EeoApplication.udi.setEthernetMode(UdiConstant.ETHERNET_MODE_DHCP);
                            } else {
                                txtIpSetting.setText(getContext().getString(R.string.manual));
                                llBtn.setVisibility(View.VISIBLE);
                                EeoApplication.udi.setEthernetMode(UdiConstant.ETHERNET_MODE_MANUAL);
                            }
                            updateNetworkData();
                            //避免频繁切换异常
                            ipSettingClickable = false;
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    ipSettingClickable = true;
                                }
                            }, 1000);
                        }
                    }
                });
                break;

            case R.id.btn_confirm:
                hideInputMethod(view.getWindowToken());
                EthernetConfig ethernetConfig = new EthernetConfig(edtIpAddress.getText().toString(), edtGateway.getText().toString(), edtMaskAddress.getText().toString(), edtDns1.getText().toString(), edtDns2.getText().toString());
                boolean isSuccess = EeoApplication.udi.setEthernetConfig(ethernetConfig);
                if (isSuccess) {
                    isSetting = false;
                    btnConfirm.setClickable(false);
                    btnConfirm.setEnabled(false);
                    scrollview.post(new Runnable() {
                        @Override
                        public void run() {
                            // 将ScrollView滚动到最底部
                            scrollview.fullScroll(ScrollView.FOCUS_UP);
                        }
                    });
                } else {
                    CommonUtils.showFailToast(getContext(), getContext().getString(R.string.toast_set_ip_fail));
                }
                break;

            case R.id.btn_cancel:
                hideInputMethod(view.getWindowToken());
                updateNetworkData();
                break;

            default:
                break;
        }
    }

    /**
     * 隐藏所有item
     */
    private void setAllItemVisible(boolean visible) {
        llMacAddress.setVisibility(visible ? View.VISIBLE : View.GONE);
        llBtn.setVisibility(visible ? View.VISIBLE : View.GONE);
        llIpMode.setVisibility(visible ? View.VISIBLE : View.GONE);
        llIp.setVisibility(visible ? View.VISIBLE : View.GONE);
        llGateway.setVisibility(visible ? View.VISIBLE : View.GONE);
        llMask.setVisibility(visible ? View.VISIBLE : View.GONE);
        llDns1.setVisibility(visible ? View.VISIBLE : View.GONE);
        llDns2.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    private void updateNetworkData() {
        EthernetConfig ethernetConfig = getEthernetConfig();
        edtIpAddress.setText(ethernetConfig.getIp());
        edtMaskAddress.setText(ethernetConfig.getMask());
        edtGateway.setText(ethernetConfig.getGateway());
        edtDns1.setText(ethernetConfig.getDns1());
        edtDns2.setText(ethernetConfig.getDns2());
        if (selectIndex == MANUAL) {
            edtIpAddress.setEnabled(true);
            edtMaskAddress.setEnabled(true);
            edtGateway.setEnabled(true);
            edtDns1.setEnabled(true);
            edtDns2.setEnabled(true);
        } else {
            edtIpAddress.setEnabled(false);
            edtMaskAddress.setEnabled(false);
            edtGateway.setEnabled(false);
            edtDns1.setEnabled(false);
            edtDns2.setEnabled(false);
            imgIp.setVisibility(View.INVISIBLE);
            imgMask.setVisibility(View.INVISIBLE);
            imgGateway.setVisibility(View.INVISIBLE);
            imgDns1.setVisibility(View.INVISIBLE);
            imgDns2.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 隐藏键盘
     */
    private void hideInputMethod(IBinder windowToken) {
        if (inputMethodManager == null) {
            inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        }
        inputMethodManager.hideSoftInputFromWindow(windowToken, 0);
    }


    /**
     * 判断是否滚动到底部
     *
     * @return
     */
    private boolean isScrollDown() {

        int height = scrollview.getHeight() + scrollview.getScrollY();
        int childHeight = scrollview.getChildAt(0).getHeight();
        if (height == childHeight) {
            return true;
        } else {
            return false;
        }
    }

    @OnFocusChange(value = {R.id.edt_ip_address, R.id.edt_mask_address, R.id.edt_gateway, R.id.edt_dns1, R.id.edt_dns2})
    public void expandAppBarOnFocusChangeListener(View view, boolean hasFocus) {
        switch (view.getId()) {
            case R.id.edt_ip_address:

                if (!hasFocus) {
                    if (selectIndex == MANUAL) {
                        boolean isIpAddress = checkoutEditIpByIndex(IP_ADDRESS);
                        if (isIpAddress) {
                            imgIp.setVisibility(View.INVISIBLE);
                        } else {
                            imgIp.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
                    isSetting = true;
                    if (!isScrollDown) {
                        // 使用post方法确保在视图布局完成后执行滚动操作
                        scrollview.post(new Runnable() {
                            @Override
                            public void run() {
                                // 将ScrollView滚动到最底部
                                scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                                edtIpAddress.setFocusable(true);
                                edtIpAddress.setFocusableInTouchMode(true);
                                edtIpAddress.requestFocus();
                            }
                        });

                    }


                }
                break;

            case R.id.edt_mask_address:
                if (!hasFocus) {

                    boolean isSubNetMask = checkoutEditIpByIndex(SUBNET_MASK);
                    if (isSubNetMask) {
                        imgMask.setVisibility(View.INVISIBLE);
                    } else {
                        imgMask.setVisibility(View.VISIBLE);
                    }

                } else {
                    isSetting = true;
                    if (!isScrollDown) {
                        // 使用post方法确保在视图布局完成后执行滚动操作
                        scrollview.post(new Runnable() {
                            @Override
                            public void run() {
                                // 将ScrollView滚动到最底部
                                scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                                edtMaskAddress.setFocusable(true);
                                edtMaskAddress.setFocusableInTouchMode(true);
                                edtMaskAddress.requestFocus();
                            }
                        });
                    }
                }

                break;

            case R.id.edt_gateway:
                if (!hasFocus) {
                    boolean isGateWay = checkoutEditIpByIndex(GATEWAY);
                    if (isGateWay) {
                        imgGateway.setVisibility(View.INVISIBLE);
                    } else {
                        imgGateway.setVisibility(View.VISIBLE);
                    }
                } else {
                    isSetting = true;
                    if (!isScrollDown) {
                        // 使用post方法确保在视图布局完成后执行滚动操作
                        scrollview.post(new Runnable() {
                            @Override
                            public void run() {
                                // 将ScrollView滚动到最底部
                                scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                                edtGateway.setFocusable(true);
                                edtGateway.setFocusableInTouchMode(true);
                                edtGateway.requestFocus();
                            }
                        });
                    }
                }

                break;

            case R.id.edt_dns1:
                if (!hasFocus) {

                    boolean isDns1 = checkoutEditIpByIndex(DNS1);
                    if (isDns1) {
                        imgDns1.setVisibility(View.INVISIBLE);
                    } else {
                        imgDns1.setVisibility(View.VISIBLE);
                    }
                } else {
                    isSetting = true;
                    if (!isScrollDown) {
                        // 使用post方法确保在视图布局完成后执行滚动操作
                        scrollview.post(new Runnable() {
                            @Override
                            public void run() {
                                // 将ScrollView滚动到最底部
                                scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                                edtDns1.setFocusable(true);
                                edtDns1.setFocusableInTouchMode(true);
                                edtDns1.requestFocus();
                            }
                        });
                    }
                }

                break;

            case R.id.edt_dns2:
                if (!hasFocus) {

                    boolean isDns2 = checkoutEditIpByIndex(DNS2);
                    if (isDns2) {
                        imgDns2.setVisibility(View.INVISIBLE);
                    } else {
                        imgDns2.setVisibility(View.VISIBLE);
                    }
                } else {
                    isSetting = true;
                    if (!isScrollDown) {
                        // 使用post方法确保在视图布局完成后执行滚动操作
                        scrollview.post(new Runnable() {
                            @Override
                            public void run() {
                                // 将ScrollView滚动到最底部
                                scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                                edtDns2.setFocusable(true);
                                edtDns2.setFocusableInTouchMode(true);
                                edtDns2.requestFocus();
                            }
                        });
                    }
                }

                break;


            default:
                break;
        }
    }

    @OnTextChanged(value = {R.id.edt_ip_address, R.id.edt_mask_address, R.id.edt_gateway, R.id.edt_dns1, R.id.edt_dns2})
    public void onEditTextChange() {
        if (selectIndex == MANUAL) {
            if (!TextUtils.isEmpty(edtIpAddress.getText().toString())
                    && !TextUtils.isEmpty(edtMaskAddress.getText().toString())
                    && !TextUtils.isEmpty(edtGateway.getText().toString())
                    && !TextUtils.isEmpty(edtDns1.getText().toString())
                    && !TextUtils.isEmpty(edtDns2.getText().toString())) {
                if (checkoutEditIpByIndex(IP_ADDRESS) && checkoutEditIpByIndex(SUBNET_MASK) && checkoutEditIpByIndex(GATEWAY) && checkoutEditIpByIndex(DNS1) && checkoutEditIpByIndex(DNS2) && isSetting) {
                    imgIp.setVisibility(View.INVISIBLE);
                    imgMask.setVisibility(View.INVISIBLE);
                    imgGateway.setVisibility(View.INVISIBLE);
                    imgDns1.setVisibility(View.INVISIBLE);
                    imgDns2.setVisibility(View.INVISIBLE);
                    btnConfirm.setClickable(true);
                    btnConfirm.setEnabled(true);
                } else {
                    if (!checkoutEditIpByIndex(IP_ADDRESS)) {
                        imgIp.setVisibility(View.VISIBLE);
                    }

                    if (!checkoutEditIpByIndex(SUBNET_MASK)) {
                        imgMask.setVisibility(View.VISIBLE);
                    }

                    if (!checkoutEditIpByIndex(GATEWAY)) {
                        imgGateway.setVisibility(View.VISIBLE);
                    }

                    if (!checkoutEditIpByIndex(DNS1)) {
                        imgDns1.setVisibility(View.VISIBLE);
                    }

                    if (!checkoutEditIpByIndex(DNS2)) {
                        imgDns2.setVisibility(View.VISIBLE);
                    }

                    btnConfirm.setClickable(false);
                    btnConfirm.setEnabled(false);
                }

            } else {
                btnConfirm.setClickable(false);
                btnConfirm.setEnabled(false);
            }
        }
    }

    private boolean checkoutEditIpByIndex(int index) {
        if (index == IP_ADDRESS) {
            if (TextUtils.isEmpty(edtIpAddress.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtIpAddress.getText().toString());
            }
        }

        if (index == SUBNET_MASK) {

            if (TextUtils.isEmpty(edtMaskAddress.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtMaskAddress.getText().toString());
            }

        }

        if (index == GATEWAY) {

            if (TextUtils.isEmpty(edtGateway.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtGateway.getText().toString());
            }

        }

        if (index == DNS1) {

            if (TextUtils.isEmpty(edtDns1.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtDns1.getText().toString());
            }

        }

        if (index == DNS2) {

            if (TextUtils.isEmpty(edtDns2.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtDns2.getText().toString());
            }

        }

        return true;
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean isOpen) {
        Log.d(TAG, "onCheckedChanged: " + isOpen);
        hideInputMethod(compoundButton.getWindowToken());
        EeoApplication.udi.enableEthernet(isOpen);
        SaveDateUtils.setEthernetEnable(getContext(), isOpen);
        if (isOpen) {
            setAllItemVisible(true);
            txtMacAddress.setText(EeoApplication.udi.getMacAddress());
            //获取整机有线网络连接模式
            String ethernetMode = EeoApplication.udi.getEthernetMode();
            if (ethernetMode.equals(ETHERNET_DHCP)) {
                selectIndex = AUTO;
            } else if (ethernetMode.equals(ETHERNET_MANUAL)) {
                selectIndex = MANUAL;
            }
            if (selectIndex == AUTO) {
                txtIpSetting.setText(getContext().getString(R.string.auto));
                llBtn.setVisibility(View.GONE);
            } else {
                txtIpSetting.setText(getContext().getString(R.string.manual));
                llBtn.setVisibility(View.VISIBLE);
            }
            updateNetworkData();
        } else {
            //隐藏所有
            Log.i(TAG, "onCheckedChanged: close_network");
            setAllItemVisible(false);
        }
    }

    @Override
    public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
        View contentView = scrollview.getChildAt(0);
        if (contentView != null && contentView.getMeasuredHeight() == (scrollview.getScrollY() + scrollview.getHeight())) {
            //底部
            isScrollDown = true;
        } else {
            isScrollDown = false;
        }
    }

    class NetWorkChangeBroadcast extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (swNetwork.isChecked()) {
                updateNetworkData();
            }
        }
    }

    public EthernetConfig getEthernetConfig() {
        EthernetConfig ethernetConfig = EeoApplication.udi.getEthernetConfig();
        String ip = ethernetConfig.getIp();
        if (ip != null && !ip.equals(IP_NULL)) {
            //本地存储上一次非空config，供静态ip读到为null时使用
            SaveDateUtils.setEthernetConfig(getContext(), ethernetConfig);
        } else if (selectIndex == MANUAL) {
            ethernetConfig = SaveDateUtils.getEthernetConfig(getContext());
            Log.d(TAG, "SaveDateUtils.getEthernetConfig：" + ethernetConfig);
        }
        return ethernetConfig;
    }
}
