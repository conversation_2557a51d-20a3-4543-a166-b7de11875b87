package com.eeo.ota.touch;

import static com.eeo.ota.touch.SubDeviceUpdate.PID_TOUCH_ZHONGYUAN_UPDATE_FAIL;
import static com.eeo.ota.touch.SubDeviceUpdate.VID_TOUCH_ZHONGYUAN;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.eeo.ota.R;
import com.eeo.ota.bean.Constant;
import com.eeo.ota.bean.VersionInfo;
import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.dialog.UpdateProgressDialog;
import com.eeo.ota.util.ParseUtil;
import com.example.touchsdk.TouchCommunicator;

import java.io.File;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 众远触摸框
 */
public class TouchZY extends Touch {
    private static final String TAG = "TouchZY";

    /**
     * 所有子设备有统一显示
     * 是否单独额外显示对话框
     */
    private static final boolean SHOW_DIALOG = false;

    /**
     * GetProductModel():EBG110B-D-A2-P-YIOU-UU-20P 截取EBG110B
     */
    public static final String MODEL_ZHONGYUAN_EBG = "EBG110B";
    /**
     * 2023.04.04.M811.R30G86B-C-A1-F-40P.GD405.S613.40P.V5401.0x8E6D 截取EBG110B
     */
    public static final String MODEL_ZHONGYUAN_R30_65 = "R30G65N";
    public static final String MODEL_ZHONGYUAN_R30_75 = "R30G75N";
    public static final String MODEL_ZHONGYUAN_R30_86 = "R30G86N";
    public static final String MODEL_ZHONGYUAN_R30_110 = "R30G110N";
    public static final String MODEL_ZHONGYUAN_E30_75 = "E30A75N";
    public static final String MODEL_ZHONGYUAN_E30_86 = "E30A86N";
    public static final String MODEL_ZHONGYUAN_E30_110 = "E30A110N";

    private TouchCommunicator mTouchCommunicator;
    private VersionInfo mTargetVersionInfo;
    private String mModelName;

    private Context mContext;

    private SubDeviceUpdateCallback mSubDeviceUpdateCallback;

    private Timer mTimer;

    private int mLastProgress;

    private boolean mIsUpdating;

    /**
     * 升级失败重新升级次数
     */
    private int mUpdateRetryTimes = 0;
    private static final int MAX_TIME_RETRY_UPDATE = 5;

    /**
     * 检验是否升级失败次数
     * R30有遇到升级完后要过10s左右才读到正常
     */
    private int mCheckUpdateFailedRetryTimes = 0;
    private static final int MAX_TIME_RETRY_CHECK_UPDATE_FAIL = 15;

    /**
     * progress无变化20s超时
     */
    private static final int TIME_UPDATE_PROGRESS_TIMEOUT = 20000;

    /**
     * 更新完成后触摸框需要等一会才能使用
     * 最迟等30s再提示更新完成
     */
    private static final int TIME_UPDATE_FINISH_DELAY = 30000;

    public static final int MSG_UPDATE_PROGRESS_TIMEOUT = 0x001;
    public static final int MSG_UPDATE_PROGRESS = 0x002;
    public static final int MSG_UPDATE_FINISH = 0x003;
    public static final int MSG_UPDATE_FAIL = 0x004;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_UPDATE_PROGRESS_TIMEOUT:
                    stopUpdate();
                    if (mSubDeviceUpdateCallback != null) {
                        mSubDeviceUpdateCallback.onUpdateFail("update timeout.");
                    }
                    if (SHOW_DIALOG) {
                        Toast.makeText(mContext, "update fail!", Toast.LENGTH_SHORT).show();
                    }
                    break;
                case MSG_UPDATE_PROGRESS:
                    int progress = msg.arg1;
                    if (mSubDeviceUpdateCallback != null) {
                        mSubDeviceUpdateCallback.onUpdateProgressChanged(progress);
                    }
                    if (SHOW_DIALOG && progress > 0) {
                        //progress大于0显示弹窗
                        UpdateProgressDialog.showProgressDialog(mContext,
                                mContext.getString(R.string.updating_touch), progress);
                    }
                    if (progress == 100) {
                        //最迟30s
                        removeMessages(MSG_UPDATE_FINISH);
                        sendEmptyMessageDelayed(MSG_UPDATE_FINISH, TIME_UPDATE_FINISH_DELAY);
                        startCheckUpdateSuccessTimer();
                    }
                    break;
                case MSG_UPDATE_FINISH:
                    Log.i(TAG, "upgrade finish!!!!!!!!!!!!!!!");
                    mIsUpdating = false;
                    stopTimer();
                    stopCheckUpdateSuccessTimer();
                    if (isFirmwareUpdateFailed()) {
                        if (mUpdateRetryTimes < MAX_TIME_RETRY_UPDATE) {
                            removeMessages(MSG_UPDATE_FAIL);
                            sendEmptyMessage(MSG_UPDATE_FAIL);
                            break;
                        }
                    }
                    mCheckUpdateFailedRetryTimes = 0;
                    mUpdateRetryTimes = 0;
                    if (SHOW_DIALOG) {
                        UpdateProgressDialog.dismissProgressDialog();
                        Toast.makeText(mContext, "update finish!", Toast.LENGTH_SHORT).show();
                    }
                    if (mSubDeviceUpdateCallback != null) {
                        mSubDeviceUpdateCallback.onUpdateSuccess();
                    }
                    break;
                case MSG_UPDATE_FAIL:
                    if (mCheckUpdateFailedRetryTimes < MAX_TIME_RETRY_CHECK_UPDATE_FAIL) {
                        //check again
                        mCheckUpdateFailedRetryTimes++;
                        if (isFirmwareUpdateFailed()) {
                            removeMessages(MSG_UPDATE_FAIL);
                            sendEmptyMessageDelayed(MSG_UPDATE_FAIL, 1000);
                            break;
                        }
                    } else {
                        if (mUpdateRetryTimes < MAX_TIME_RETRY_UPDATE) {
                            //update again
                            mUpdateRetryTimes++;
                            mCheckUpdateFailedRetryTimes = 0;
                            Log.e(TAG, "upgrade finish: isFirmwareUpdateFailed,mRetryTimes=" + mUpdateRetryTimes);
                            update();
                            break;
                        }
                    }
                    removeMessages(MSG_UPDATE_FINISH);
                    sendEmptyMessage(MSG_UPDATE_FINISH);
                    break;
            }
        }
    };

    public TouchZY(Context context, SubDeviceUpdateCallback callback) {
        mContext = context;
        mSubDeviceUpdateCallback = callback;
        mTouchCommunicator = new TouchCommunicator();
        mTouchCommunicator.InitSDK();
    }

    /**
     * {
     * "name":"2022.05.05.M468.EBG110B-D-A2-P-YIOU-UU.GD405.SUndefined.20P.V4900.0x7F5B.bin",
     * "version":"4900_7F5B",
     * "md5":"30454D15C80A017A4E13019145013AA4",
     * "path":"system/ota/touch_EBG110B/2022.05.05.M468.EBG110B-D-A2-P-YIOU-UU.GD405.SUndefined.20P.V4900.0x7F5B.bin"
     * }
     */
    @Override
    public void parseVersionInfo() {
        if (Constant.IS_BS65A) {
            mModelName = MODEL_ZHONGYUAN_R30_65;
        } else if (Constant.IS_BS75A) {
            mModelName = MODEL_ZHONGYUAN_R30_75;
        } else if (Constant.IS_BS86A) {
            mModelName = MODEL_ZHONGYUAN_R30_86;
        } else if (Constant.IS_BS110A) {
            mModelName = MODEL_ZHONGYUAN_R30_110;
        } else if (Constant.IS_BSP75A) {
            mModelName = MODEL_ZHONGYUAN_R30_75;
        } else if (Constant.IS_BSP86A) {
            mModelName = MODEL_ZHONGYUAN_R30_86;
        } else if (Constant.IS_BSP110A) {
            mModelName = MODEL_ZHONGYUAN_R30_110;
        }
        mTargetVersionInfo = ParseUtil.getVersionInfo(mModelName);
        Log.d(TAG, "parseVersionInfo: " + mTargetVersionInfo);
    }

    /**
     * GetFirmwareVersion():4900
     * 版本号增加或者升级失败读到error才升级
     */
    @Override
    public boolean checkVersion() {
        if (mTargetVersionInfo == null || TextUtils.isEmpty(mTargetVersionInfo.versionName)) {
            Log.e(TAG, "checkVersion: no update");
            return false;
        }
        //众远的用checkSum
        String version = null;
        String checkSum = null;
        String productModel = null;
        for (int i = 0; i < 10; i++) {
            version = mTouchCommunicator.GetFirmwareVersion();
            checkSum = mTouchCommunicator.GetFirmwareCheckSum();
            productModel = mTouchCommunicator.GetProductModel();
            Log.d(TAG, "touchCommunicator get " + i + " ,currentVersion=" + version + ",checkSum=" + checkSum + " ,productModel=" + productModel);
            if ("error".equals(version) || "error".equals(checkSum)
                    || "0000".equals(version) || "0000".equals(checkSum)
            ) {
                //对比一下前后版本的productModel，避免升级错机型
                // R30G86N-D-A1-YO-50P
                if (productModel != null && !productModel.equals("error") && !productModel.contains(mModelName)) {
                    Log.e(TAG, "checkVersion product model different: productModel=" + productModel + " , mModelName=" + mModelName);
                    return false;
                }
                //固件升级失败的需要更新
                if (isFirmwareUpdateFailed()) {
                    Log.e(TAG, "checkVersion: isFirmwareUpdateFailed");
                    return true;
                }
                //触摸框滑动过程中有时候读到error,再重新读
                mTouchCommunicator.InitSDK();
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    Log.e(TAG, "checkVersion InterruptedException " + e);
                    e.printStackTrace();
                }
            } else {
                break;
            }
        }
        //对比一下前后版本的productModel，避免升级错机型
        // R30G86N-D-A1-YO-50P
        if (productModel != null && !productModel.equals("error") && !productModel.contains(mModelName)) {
            Log.e(TAG, "checkVersion product model different: productModel=" + productModel + " , mModelName=" + mModelName);
            return false;
        }
        //固件升级失败的需要更新
        if (isFirmwareUpdateFailed()) {
            Log.e(TAG, "checkVersion: isFirmwareUpdateFailed");
            return true;
        }
        Log.d(TAG, "checkVersion: current version=" + version + ",target version=" + mTargetVersionInfo.versionName);
        if ("error".equals(version)) {
            //读到异常的不升级(手指一直按着屏幕，sdk读到一直是error)
            return false;
        }
//        return !mTargetVersionInfo.versionName.equalsIgnoreCase(version); //版本不相等就升
        return mTargetVersionInfo.versionName.compareTo(version) > 0;  //版本递增才升
    }


    @Override
    public void update() {
        if (mIsUpdating) {
            Log.e(TAG, "update: is already updating!");
            return;
        }
        Log.d(TAG, "update: " + mTargetVersionInfo.filePath);
        File file = new File(mTargetVersionInfo.filePath);
        if (file.exists()) {
//            if (mTargetVersionInfo.md5.equals(Util.getFileMD5(file))) {
            Log.d(TAG, "start to update.");
            mIsUpdating = true;
            mTouchCommunicator.UpgradeFirmware(mTargetVersionInfo.filePath);
            startTimer();
//            } else {
//                Log.e(TAG, "update: md5 unequal!");
//            }
        } else {
            if (mSubDeviceUpdateCallback != null) {
                mSubDeviceUpdateCallback.onUpdateFail("file not exists!");
            }
            Log.e(TAG, "update: file not exists!");
        }
    }

    @Override
    public boolean isUpdating() {
        return mIsUpdating;
    }

    /**
     * 众远的没有提供停止的接口
     * 可通过DeInitSDK()去控制
     */
    @Override
    public void stopUpdate() {
        Log.e(TAG, "stopUpdate.");
        mIsUpdating = false;
        mUpdateRetryTimes = 0;
        if (SHOW_DIALOG) {
            UpdateProgressDialog.dismissProgressDialog();
        }
        stopTimer();
        stopCheckUpdateSuccessTimer();
        release();
    }

    private void startTimer() {
        if (mTimer == null) {
            mTimer = new Timer();
        }
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mTouchCommunicator != null) {
                    int progress = mTouchCommunicator.GetUpgradeProcessbar();
                    if (progress != mLastProgress) {
                        mLastProgress = progress;
                        //send timeout msg
                        mHandler.removeMessages(MSG_UPDATE_PROGRESS_TIMEOUT);
                        if (progress != 100) {
                            mHandler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS_TIMEOUT, TIME_UPDATE_PROGRESS_TIMEOUT);
                        }
                        //send show dialog msg
                        mHandler.removeMessages(MSG_UPDATE_PROGRESS);
                        Message message = new Message();
                        message.what = MSG_UPDATE_PROGRESS;
                        message.arg1 = progress;
                        mHandler.sendMessage(message);
                    }
                }
            }
        }, 0, 200);
    }

    private void stopTimer() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }

    @Override
    public void release() {
        if (mTouchCommunicator != null) {
            mTouchCommunicator.DeInitSDK();
            mTouchCommunicator = null;
        }
        mHandler.removeCallbacksAndMessages(null);
    }

    /**
     * 通过usb枚举
     * 是否有连接该设备
     */
    @Override
    public boolean isPlugged() {
        UsbManager usbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        HashMap<String, UsbDevice> deviceHashMap = usbManager.getDeviceList();
        for (UsbDevice usbDevice : deviceHashMap.values()) {
            if (usbDevice.getVendorId() == VID_TOUCH_ZHONGYUAN) {
                Log.d(TAG, "isPlugged: true");
                return true;
            }
        }
        Log.d(TAG, "isPlugged: false");
        return false;
    }

    /**
     * VID_TOUCH_ZHONGYUAN = 8183;   //0x1FF7
     * PID_TOUCH_ZHONGYUAN = 3890; //0x0F32 正常
     * PID_TOUCH_ZHONGYUAN_UPDATE_FAIL = 3850; //0x0F0A 固件升级失败
     */
    @Override
    public boolean isFirmwareUpdateFailed() {
        UsbManager usbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        HashMap<String, UsbDevice> deviceHashMap = usbManager.getDeviceList();
        for (UsbDevice usbDevice : deviceHashMap.values()) {
            if (usbDevice.getVendorId() == VID_TOUCH_ZHONGYUAN) {
                Log.d(TAG, "VID_TOUCH_ZHONGYUAN,productId:" + usbDevice.getProductId());
                return usbDevice.getProductId() == PID_TOUCH_ZHONGYUAN_UPDATE_FAIL;
            }
        }
        return false;
    }

    private Timer mCheckUpdateSuccessTimer;

    private void startCheckUpdateSuccessTimer() {
        if (mCheckUpdateSuccessTimer == null) {
            mCheckUpdateSuccessTimer = new Timer();
        }
        mCheckUpdateSuccessTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (isPlugged()) {
                    //升级完成后识别到设备后再500ms后显示完成
                    mHandler.removeMessages(MSG_UPDATE_FINISH);
                    mHandler.sendEmptyMessageDelayed(MSG_UPDATE_FINISH, 500);
                }
            }
        }, 0, 1000);
    }

    private void stopCheckUpdateSuccessTimer() {
        if (mCheckUpdateSuccessTimer != null) {
            mCheckUpdateSuccessTimer.cancel();
            mCheckUpdateSuccessTimer = null;
        }
    }
}
