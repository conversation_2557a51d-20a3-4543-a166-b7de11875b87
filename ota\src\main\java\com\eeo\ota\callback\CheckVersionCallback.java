package com.eeo.ota.callback;

import com.eeo.ota.bean.VersionInfo;

public interface CheckVersionCallback {

    /**
     * 检测完成
     */
    void onCheckSuccess(VersionInfo versionInfo);

    /**
     * @param errCode 1001:没有网络
     * 1002：未发现新版本
     * 1003:连接服务器失败，请检查网络后重试
     * 1004:动态注册失败
     * 1005:上报版本超时
     */
    int ERR_CODE_NO_NETWORK = 1001;
    int ERR_CODE_NOT_FOUND_NEW_VERSION = 1002;
    int ERR_CODE_CONNECT_FAIL = 1003;
    int ERR_CODE_DYNREG_FAIL = 1004;
    int ERR_CODE_REPORT_VERSION_TIMEOUT = 1005;
    void onCheckFail(int errCode, String reason);
}
