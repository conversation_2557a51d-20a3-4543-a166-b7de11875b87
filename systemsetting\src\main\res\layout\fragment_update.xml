<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        style="@style/NetWork_Linear"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_network_network_margin_top">

        <TextView
            style="@style/NetWork_TextView"
            android:text="@string/auto_update" />

        <Switch
            android:id="@+id/sw_network"
            style="@style/NetWork_Switch"
            android:layout_marginStart="@dimen/fragment_network_sw_margin_start"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

    </LinearLayout>

    <View
        style="@style/About_Line"
        android:layout_marginTop="@dimen/fragment_update_line_margin_top" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll_not_network"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_no_network"
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_no_network_margin_top"
                android:gravity="start"
                android:text="@string/network_error" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/guide_network_error" />
        </LinearLayout>

        <TextView
            android:id="@+id/txt_version_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
            android:layout_marginTop="@dimen/fragment_update_tv_version_margin_top"
            android:textColor="@color/black_100"
            android:textSize="@dimen/fragment_update_tv_version_text_size"
            android:visibility="gone" />


        <LinearLayout
            android:id="@+id/ll_check_update"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <ProgressBar
                android:id="@+id/progressbar_check_update"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="@dimen/dialog_installing_pb_width"
                android:layout_height="@dimen/dialog_installing_pb_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/fragment_update_pb_check_update_margin_top"
                android:indeterminateTint="@color/press_color" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/fragment_update_tv_checking_margin_top"
                android:text="@string/checking_update"
                android:textColor="@color/black_check"
                android:textSize="@dimen/fragment_update_tv_version_text_size" />

        </LinearLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_right_update"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <TextView
                android:id="@+id/txt_right_update"
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_version_margin_top"
                android:text="可更新至：*********"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txt_title_update_description"
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_update_description_margin_top"
                android:alpha="0.7"
                android:text="@string/update_description"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_right_update" />

            <ScrollView
                android:id="@+id/sv_update_description"
                android:layout_width="@dimen/fragment_update_tv_update_description_width"
                android:layout_height="@dimen/fragment_update_tv_update_description_height"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_update_description_margin_top"
                android:fadeScrollbars="false"
                android:overScrollMode="never"
                android:scrollbarThumbVertical="@drawable/shape_network_scrollview"
                android:scrollbars="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_title_update_description">

                <TextView
                    android:id="@+id/txt_update_description"
                    style="@style/Update_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="3dp"
                    android:layout_marginEnd="3dp"
                    android:alpha="0.7" />

            </ScrollView>

            <TextView
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_check_network_margin_top"
                android:layout_marginEnd="@dimen/fragment_update_pb_download_margin_end"
                android:maxHeight="@dimen/fragment_update_tv_update_description_max_height"
                android:text="@string/update_msg"
                android:textColor="@color/text_black_100"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_right_update" />

            <Button
                android:id="@+id/btn_right_update"
                style="@style/Update_Button"
                android:layout_marginTop="@dimen/fragment_update_btn_right_update_margin_top"
                android:text="@string/right_update"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_title_update_description"
                app:layout_goneMarginTop="@dimen/fragment_update_btn_right_update_gone_margin_top" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_download"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_version_margin_top"
                android:text="@string/download"
                android:textColor="@color/black_100"
                android:textSize="@dimen/fragment_update_tv_version_text_size" />

            <ProgressBar
                android:id="@+id/progress_download"
                style="@style/update_progress_horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_pb_download_margin_top"
                android:layout_marginEnd="@dimen/fragment_update_pb_download_margin_end"
                android:max="100"
                android:progress="30" />

            <Button
                android:id="@+id/btn_cancel"
                style="@style/Update_Button"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/fragment_update_btn_cancel_margin_top"
                android:text="@string/cancel_download" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_install"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_version_margin_top"
                android:text="@string/install_title" />

            <TextView
                style="@style/Update_TextView"
                android:layout_marginStart="@dimen/fragment_update_tv_no_network_margin_start"
                android:layout_marginTop="@dimen/fragment_update_tv_check_network_margin_top"
                android:text="@string/install_msg"
                android:textColor="@color/text_black_100" />

            <Button
                android:id="@+id/btn_install"
                style="@style/Update_Button"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/fragment_update_btn_install_margin_top"
                android:text="@string/install_right" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>