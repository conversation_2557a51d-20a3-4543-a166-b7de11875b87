<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.eeo.systemsetting"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="com.android.providers.tv.permission.ACCESS_ALL_EPG_DATA" />
    <uses-permission android:name="com.android.providers.tv.permission.READ_EPG_DATA" />
    <uses-permission android:name="com.android.providers.tv.permission.WRITE_EPG_DATA" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.SHUTDOWN" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.SET_TIME" />
    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="com.mstar.android.permissionn.ACCESS_TV_DATA" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.HDMI_CEC" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 覆盖wifi配置 需要是 system app -->
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" />
    <uses-permission android:name="android.permission.INJECT_EVENTS"/>
    <!-- <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" /> -->
    <protected-broadcast android:name="com.eeo.systemsetting.back" />
    <protected-broadcast android:name="com.eeo.wifi.more" />
    <protected-broadcast android:name="com.eeo.wifi.key" />
    <protected-broadcast android:name="com.eeo.set.usb.enable" />
    <protected-broadcast android:name="com.eeo.systemsetting.exit" />

    <application
        android:name=".EeoApplication"
        android:allowBackup="true"
        android:defaultToDeviceProtectedStorage="false"
        android:directBootAware="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SystemSetting"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".activity.MainActivity"
            android:configChanges="keyboard|locale|layoutDirection"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/EeoDialogStyle">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <!-- <category android:name="android.intent.category.LAUNCHER"/> -->
            </intent-filter>
        </activity>
        <activity
            android:name=".launcher.FallbackHomeActivity"
            android:configChanges="keyboard|keyboardHidden|touchscreen|uiMode"
            android:exported="true"
            android:resizeableActivity="false">
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.TV_HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <uses-library
            android:name="tvapi.software.core"
            android:required="false" />

        <activity
            android:name=".launcher.TifPlayerActivity"
            android:configChanges="keyboard|keyboardHidden|touchscreen|uiMode|locale|layoutDirection"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:resizeableActivity="false">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.cvte.intent.ACTION_TIF_PLAYER_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".activity.FinishMultiScreenActivity"
            android:exported="true" />

        <service
            android:name=".projection.GestureDetectorService"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.eeo.systemsetting.action.startService" />
            </intent-filter>
        </service>
        <service
            android:name=".service.SystemSettingService"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="true"/>

        <service
            android:name=".service.PopupAlertService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <receiver
            android:name=".broadcast.SystemSettingBroadcast"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.eeo.SendHotKey" />
                <action android:name="com.eeo.set.usb.enable" />
                <action android:name="com.eeo.action.LONG_PRESS_POWER_OFF" />
                <action android:name="com.eeo.action.POWER_OFF" />
                <action android:name="com.eeo.action.REBOOT" />
                <action android:name="com.android.toofifi.start_mirror" />
                <action android:name="com.android.toofifi.stop_mirror" />

            </intent-filter>
        </receiver>

        <receiver
            android:name=".receiver.ArrayMicUpdateCheckReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
