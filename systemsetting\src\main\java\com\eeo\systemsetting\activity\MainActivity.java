package com.eeo.systemsetting.activity;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.media.AudioManager;
import android.net.wifi.WifiInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.cvte.tv.api.TvApiSDKManager;
import com.cvte.tv.api.aidl.ITvNotifyListener;
import com.eeo.systemsetting.rgb.RgbManager;
import com.eeo.systemsetting.touchlock.TouchLockManager;
import com.eeo.systemsetting.utils.PowerUtil;
import com.eeo.systemsetting.view.CustomerSeekBar;
import com.eeo.systemsetting.view.PrivacyFrameLayout;
import com.eeo.udisdk.PcKeyboardCode;
import com.eeo.udisdk.UdiConstant;
import com.zyp.cardview.YcCardView;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.base.BaseActivity;
import com.eeo.systemsetting.bean.AvailableSourceBean;
import com.eeo.systemsetting.screen.ScreenManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.systemsetting.view.SettingFrameLayout;
import com.eeo.systemsetting.view.SignalFrameLayout;
import com.eeo.systemsetting.view.ViewGroupWrapper;
import com.eeo.systemsetting.view.WiFiMoreFrameLayout;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class MainActivity extends BaseActivity implements SeekBar.OnSeekBarChangeListener {
    public static final String TAG = "MainActivity===";
    @BindView(R.id.frame)
    FrameLayout frame;
    @BindView(R.id.rlBg)
    RelativeLayout rlBg;
    @BindView(R.id.fl_shutdown)
    FrameLayout flShutdown;
    @BindView(R.id.iv_shutdown)
    ImageView ivShutdown;
    @BindView(R.id.tv_shutdown)
    TextView tvShutdown;
    @BindView(R.id.fl_restart)
    FrameLayout flRestart;
    @BindView(R.id.iv_restart)
    ImageView ivRestart;
    @BindView(R.id.tv_restart)
    TextView tvRestart;
    @BindView(R.id.fl_rest)
    FrameLayout flRest;
    @BindView(R.id.iv_rest)
    ImageView ivRest;
    @BindView(R.id.tv_rest)
    TextView tvRest;
    @BindView(R.id.fl_desktop)
    FrameLayout flDesktop;
    @BindView(R.id.iv_desktop)
    ImageView ivDesktop;
    @BindView(R.id.tv_desktop)
    TextView tvDesktop;
    @BindView(R.id.fl_touch_lock)
    FrameLayout flTouchLock;
    @BindView(R.id.iv_touch_lock)
    ImageView ivTouchLock;
    @BindView(R.id.txt_touch_lock)
    TextView txtTouchLock;
    @BindView(R.id.txt_title)
    TextView txtTitle;
    @BindView(R.id.cv_screen)
    YcCardView cvScreen;
    @BindView(R.id.iv_screen)
    ImageView ivScreen;
    @BindView(R.id.txt_screen)
    TextView txtScreen;
    @BindView(R.id.line1)
    View line1;
    @BindView(R.id.fl_signal)
    FrameLayout flSignal;
    @BindView(R.id.iv_signal)
    ImageView ivSignal;
    @BindView(R.id.txt_signal)
    TextView txtSignal;
    @BindView(R.id.fl_setting)
    FrameLayout flSetting;
    @BindView(R.id.iv_setting)
    ImageView ivSetting;
    @BindView(R.id.txt_setting)
    TextView txtSetting;
    @BindView(R.id.cv_write)
    YcCardView cvWrite;
    @BindView(R.id.iv_write)
    ImageView ivWrite;
    @BindView(R.id.txt_write)
    TextView txtWrite;
    @BindView(R.id.fl_eye)
    FrameLayout flEye;
    @BindView(R.id.iv_eye)
    ImageView ivEye;
    @BindView(R.id.txt_eye)
    TextView txtEye;
    @BindView(R.id.cv_bright)
    YcCardView cvBright;
    @BindView(R.id.cv_vg_setting)
    YcCardView cvVgSetting;
    @BindView(R.id.sb_bright)
    CustomerSeekBar sbBright;
    @BindView(R.id.img_bright)
    ImageView imgBright;
    @BindView(R.id.sb_volume)
    CustomerSeekBar sbVoice;
    @BindView(R.id.img_voice)
    ImageView imgVoice;

    private MainPowerBroadcast mainPowerBroadcast = null;
    private IntentFilter intentFilter;

    private final int BRIGHT_VALUE_0 = 0;
    private final int BRIGHT_VALUE_33 = 33;
    private final int BRIGHT_VALUE_66 = 66;
    private int mBrightLevel;
    private int mLastBrightLevel;


    private final int VOICE_VALUE_0 = 0;
    private final int VOICE_VALUE_33 = 33;
    private final int VOICE_VALUE_66 = 66;
    private int mVoiceLevel;
    private int mLastVoiceLevel = -1;

    private final int Sec_200 = 200;
    private final int Sec_2000 = 2 * 1000;

    //最大音量
    private static final float MAX_BRIGHTNESS = 255;

    private AudioManager audioManager;

    /**
     * 启动批注过程时不恢复透传
     */
    private boolean mIsStartingAnnotation;

    private List<AvailableSourceBean.SourcesBean> mAvailableSourceList = null;
    /**
     * 全局TVAPI回调监听器
     */
    private ITvNotifyListener mTvNotifyListener = null;

    /**
     * SeekBar滑动时避免耗时卡顿
     */
    private final ExecutorService mExecutorService = Executors.newSingleThreadExecutor();
    private int mVolumeProgress = -1;
    private long mVolumeLastSetTime = 0;
    private boolean mIsVolumeSeekBarTracking = false;
    private long mVolumeSeekBarTrackingStopTime = 0;

    private int mBrightnessProgress = -1;
    private long mBrightnessLastSetTime = 0;

    //由于offset，用来辅助判断是否手动滑动
    private boolean mFromUser = false;

    //滑动条滑动时在子线程修改亮度、音量，最后再delay50ms再次修改最终音量
    private static final int MSG_SET_BRIGHTNESS = 3;
    private static final int MSG_SET_VOLUME = 4;
    private static final int MSG_UPDATE_VOLUME_PROGRESS = 5;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_SET_BRIGHTNESS:
                    setScreenBrightness();
                    break;
                case MSG_SET_VOLUME:
                    setSystemVolume();
                    break;
                case MSG_UPDATE_VOLUME_PROGRESS:
                    updateVolumeProgress();
                    break;
            }
        }
    };
    private ViewGroupWrapper viewGroupWrapper;

    private SettingFrameLayout mSettingFrameLayout;
    private SignalFrameLayout mSignalFrameLayout;
    private PrivacyFrameLayout mPrivacyFrameLayout;

    private TouchLockManager mTouchLockManager;

    @Override
    public int getLayout() {
        if (CommonUtils.isOpsInserted() && UdiConstant.SOURCE_PC.equals(EeoApplication.mCurrentSource)) {
            return R.layout.activity_main_desktop;
        } else {
            return R.layout.activity_main;
        }
    }

    @SuppressLint("CheckResult")
    @Override
    public void initDate() {
        viewGroupWrapper = new ViewGroupWrapper(rlBg);
        //信号可用
        mAvailableSourceList = new ArrayList<>();
        EeoApplication.isShowMainDialog = true;
        sbBright.setOnSeekBarChangeListener(this);
        sbVoice.setOnSeekBarChangeListener(this);
        imgVoice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mFromUser = true;
                sbVoice.setProgress(Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
            }
        });
        //亮度调节
        int currentBrightness = EeoApplication.udi.getBrightnessValue();
        Log.i(TAG, "initDate: currentBrightness : " + currentBrightness);
        sbBright.setProgress(currentBrightness + Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
        updateVolumeProgress();

        //获取当前信号哪些可用
        Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
            @Override
            public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                String availableSource = EeoApplication.udi.getAvailableSource();
                if (availableSource != null) {
                    Gson gson = new Gson();
                    AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                    List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                    emitter.onNext(sources);
                }
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
            @Override
            public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                mAvailableSourceList.addAll(sourcesBeans);
            }
        });


        //获取护眼模式开关
        if (EeoApplication.isEyeCare == -1) {
            //第一次需要读取一次状态
            Observable.create(new ObservableOnSubscribe<Boolean>() {
                @Override
                public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                    boolean eyeCareEnabled;
                    if (SaveDateUtils.isNewEyeCare(MainActivity.this)) {
                        eyeCareEnabled = SaveDateUtils.isEyeCareEnable(MainActivity.this);
                    } else {
                        eyeCareEnabled = EeoApplication.udi.isEyeCareEnabled();
                    }
                    emitter.onNext(eyeCareEnabled);
                }
            }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Boolean>() {
                @Override
                public void accept(Boolean eyeCareEnabled) throws Exception {
                    Log.i(TAG, "initDate: eyeCareEnable : " + eyeCareEnabled);
                    if (eyeCareEnabled) {
                        ivEye.setSelected(true);
                        txtEye.setSelected(true);
                        EeoApplication.isEyeCare = 1;
                    } else {
                        ivEye.setSelected(false);
                        txtEye.setSelected(false);
                        EeoApplication.isEyeCare = 0;
                    }
                }
            });
        } else if (EeoApplication.isEyeCare == 1) {
            ivEye.setSelected(true);
            txtEye.setSelected(true);
        } else {
            ivEye.setSelected(false);
            txtEye.setSelected(false);
        }
        if (mTouchLockManager == null) {
            mTouchLockManager = TouchLockManager.getInstance(this);
            mTouchLockManager.setTouchLockClickListener(new TouchLockManager.OnTouchLockClickListener() {
                @Override
                public void onClick() {
                    finish();
                }
            });
        }
        if (EeoApplication.isLock) {
            updateTouchLockState(true);
            mTouchLockManager.dismissTouchLockView();
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.i(TAG, "onNewIntent: ");
        if (EeoApplication.isLock) {
            updateTouchLockState(true);
            //重复点击电源键时候判断是否dialog有显示，如果显示了并且没锁住屏幕，则让dialog隐藏
            if (!EeoApplication.isShowMainDialog) {
                mTouchLockManager.dismissTouchLockView();
            }
        } else {
            updateTouchLockState(false);
        }
        if (getWindow().getDecorView().getVisibility() == View.VISIBLE && EeoApplication.isShowMainDialog) {
            EeoApplication.isShowMainDialog = false;
            int childCount = frame.getChildCount();
            Log.i(TAG, "onNewIntent: moveTaskToBack count : " + childCount);
            finish();
        } else {
            EeoApplication.isShowMainDialog = true;
            frame.setVisibility(View.VISIBLE);
            rlBg.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.i(TAG, "onStart: ");
        if (mainPowerBroadcast == null) {
            mainPowerBroadcast = new MainPowerBroadcast();
        }
        intentFilter = new IntentFilter();
        intentFilter.addAction(Constant.ACTION_BACK);
        intentFilter.addAction(Constant.ACTION_WIFI_MORE);
        intentFilter.addAction(Constant.ACTION_PRIVACY);
        intentFilter.addAction(Constant.ACTION_EXIT);
        intentFilter.addAction(Constant.ACTION_MULTI_SCREEN_START_MIRROR);
        intentFilter.addAction(Constant.ACTION_MULTI_SCREEN_STOP_MIRROR);
        intentFilter.addAction("android.media.VOLUME_CHANGED_ACTION");
        intentFilter.addAction("android.media.STREAM_MUTE_CHANGED_ACTION");
        registerReceiver(mainPowerBroadcast, intentFilter);
    }

    @Override
    protected void onResume() {
        super.onResume();
        EeoApplication.currentPage = EeoApplication.PAGE_MAIN;
        Log.i(TAG, "onResume: ");
        registerTvNotifyListener();

        CommonUtils.enableOsd(this, true);
        if (EeoApplication.isResettingOps) {
            ivRestart.setEnabled(false);
            tvRestart.setEnabled(false);
            ivShutdown.setEnabled(false);
            tvShutdown.setEnabled(false);
        } else {
            ivRestart.setEnabled(true);
            tvRestart.setEnabled(true);
            ivShutdown.setEnabled(true);
            tvShutdown.setEnabled(true);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.i(TAG, "onPause: ");
        unregisterTvNotifyListener();
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.i(TAG, "onStop: ");
        EeoApplication.isShowMainDialog = false;
        if (isFinishing()) {
            if (mainPowerBroadcast != null) {
                unregisterReceiver(mainPowerBroadcast);
            }
        }
        //触摸恢复
        if (!EeoApplication.isLock && !mIsStartingAnnotation && EeoApplication.udi.isScreenOn()) {
            CommonUtils.enableOsd(this, false);
        }
        mIsStartingAnnotation = false;
        //触控锁时，设置弹窗消失后恢复触控锁
        if (EeoApplication.isLock) {
//            rlBg.setVisibility(View.GONE);
            mTouchLockManager.showTouchLockView(true);
        }
        PowerUtil.getInstance(MainActivity.this).dismissRestartDialog();
        PowerUtil.getInstance(MainActivity.this).dismissShutdownDialog();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "onDestroy: ");
    }

    @OnClick({R.id.fl_desktop, R.id.fl_rest, R.id.fl_restart, R.id.fl_shutdown, R.id.fl_signal, R.id.fl_setting, R.id.cv_write, R.id.fl_touch_lock, R.id.cv_screen, R.id.fl_eye})
    public void onClick(View view) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (view.getId()) {
            //投屏
            case R.id.cv_screen:
                moveTaskToBack(false);
                ScreenManager.getInstance(MainActivity.this).showScreenDialog(mAvailableSourceList);
//                finish();
                break;
            //触控锁
            case R.id.fl_touch_lock:
                Log.i(TAG, "onClick: isLock before = " + EeoApplication.isLock);
                if (EeoApplication.isLock) {
                    CommonUtils.setLocked(MainActivity.this, false);
                    updateTouchLockState(false);
                } else {
                    CommonUtils.setLocked(MainActivity.this, true);
                    updateTouchLockState(true);
                    mTouchLockManager.showTouchLockView(false);
                    finish();
                }
                break;

            //批注
            case R.id.cv_write:
                //获取坐标
                int[] location = new int[2];
                cvWrite.getLocationOnScreen(location);
                int width = (cvWrite.getWidth() / 2);
                int height = (cvWrite.getHeight() / 2);
                Log.i(TAG, "onClick: width : " + location[0] + " , height : " + location[1]);
                CommonUtils.startAnnotation(MainActivity.this, 1, location[0] + width, location[1] + height);
                mIsStartingAnnotation = true;
                if (EeoApplication.isLock) {
                    mTouchLockManager.showTouchLockView(true);
                }
                finish();
                break;
            //桌面
            case R.id.fl_desktop:
                Log.i(TAG, "onClick: desktop");
                EeoApplication.udi.sendPcKeyEvent(PcKeyboardCode.CONTROL_KEY_BOARD_WIN,
                        PcKeyboardCode.FUNCTION_KEY_BOARD_D, PcKeyboardCode.EVENT_KEY_BOARD_CLICK);
                finish();
                break;
            //息屏
            case R.id.fl_rest:
                Log.i(TAG, "onClick: rest");
                if (SaveDateUtils.isWriteWithoutScreenOnEnabled(this)) {
                    int[] location1 = new int[2];
                    flRest.getLocationOnScreen(location1);
                    CommonUtils.startAnnotation(MainActivity.this, 3, location1[0] + flRest.getWidth() / 2, location1[1] + flRest.getHeight() / 2);
                    mIsStartingAnnotation = true;
                } else {
                    EeoApplication.udi.changeScreenStatus(false, false, true);
                }
                finish();
                break;

            //重启
            case R.id.fl_restart:
                Log.i(TAG, "onClick: restart");
                PowerUtil.getInstance(MainActivity.this).showRestartDialog();
                break;

            //关机
            case R.id.fl_shutdown:
                Log.i(TAG, "onClick: shutdown");
                PowerUtil.getInstance(MainActivity.this).showShutdownDialog();
                break;
            //信号源
            case R.id.fl_signal:
                //先隐藏所有控件
                for (int i = 0; i < rlBg.getChildCount(); i++) {
                    View childAt = rlBg.getChildAt(i);
                    childAt.setVisibility(View.INVISIBLE);
                }
//                ObjectAnimator.ofInt(viewGroupWrapper, "trueHeight", CommonUtils.dp2px(this, 308)).setDuration(200).start();
                Observable.timer(210, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        int childSignalCount = frame.getChildCount();
                        Log.i(TAG, "onClick: signal :" + childSignalCount);
                        if (childSignalCount <= 1) {
                            rlBg.setVisibility(View.GONE);
                            mSignalFrameLayout = new SignalFrameLayout(MainActivity.this, MainActivity.this, mAvailableSourceList);
                            frame.addView(mSignalFrameLayout);
                            EeoApplication.currentPage = EeoApplication.PAGE_SIGNAL;

                        }
                    }
                });

                break;
            //设置
            case R.id.fl_setting:
                Log.i(TAG, "onClick: setting");
                EeoApplication.isShowMainDialog = true;
                //先隐藏所有控件
                for (int i = 0; i < rlBg.getChildCount(); i++) {
                    View childAt = rlBg.getChildAt(i);
                    childAt.setVisibility(View.INVISIBLE);
                }
//                ObjectAnimator.ofInt(viewGroupWrapper, "trueHeight", CommonUtils.dp2px(this, 384)).setDuration(200).start();

                Observable.timer(210, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        if (EeoApplication.isShowMainDialog) {
                            int childSettingCount = frame.getChildCount();
                            if (childSettingCount <= 1) {
                                rlBg.setVisibility(View.GONE);
                                mSettingFrameLayout = new SettingFrameLayout(MainActivity.this, MainActivity.this);
                                frame.addView(mSettingFrameLayout);
                                EeoApplication.currentPage = EeoApplication.PAGE_SETTING;
                            }
                        }
                    }
                });

                break;

            //护眼
            case R.id.fl_eye:
                if (EeoApplication.isEyeCare == 1) {
                    if (SaveDateUtils.isNewEyeCare(MainActivity.this)) {
                        RgbManager.getInstance(MainActivity.this).enableEyeCare(false);
                    } else {
                        //之前用的udi的护眼模式的，还是使用udi取消护眼，下一次开启护眼在使用新的接口
                        EeoApplication.udi.enableEyeCare(false);
                    }
                    EeoApplication.isEyeCare = 0;
                    ivEye.setSelected(false);
                    txtEye.setSelected(false);
                } else {
                    RgbManager.getInstance(MainActivity.this).enableEyeCare(true);
                    EeoApplication.isEyeCare = 1;
                    ivEye.setSelected(true);
                    txtEye.setSelected(true);
                }
                break;

            default:
                break;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                boolean outOfBounds = CommonUtils.isOutOfBounds(MainActivity.this, event, EeoApplication.currentPage);
                //如果点击是弹窗外就让其消失
                if (outOfBounds) {
                    if (EeoApplication.isLock) {
                        mTouchLockManager.showTouchLockView(true);
                    }
                    finish();
                }
                break;
        }

        return true;
    }

    /**
     * 根据触控锁状态更新MainDialog
     */
    private void updateTouchLockState(boolean isLock) {
        if (isLock) {
            txtTouchLock.setText(getResources().getString(R.string.unlock));
            txtTouchLock.setSelected(true);
            ivTouchLock.setSelected(true);
        } else {
            ivTouchLock.setSelected(false);
            txtTouchLock.setText(getResources().getString(R.string.lock));
            txtTouchLock.setSelected(false);
        }
    }

    /**
     * 设置亮度图标icon
     *
     * @param brightValue
     */
    private void setBrightnessIcon(int brightValue) {
        if (BRIGHT_VALUE_0 == brightValue) {
            mBrightLevel = 0;
            if (mBrightLevel != mLastBrightLevel) {
                mLastBrightLevel = mBrightLevel;
                imgBright.setBackground(getDrawable(R.drawable.ic_bright_01));
            }
            return;
        }

        if (BRIGHT_VALUE_0 < brightValue && brightValue < BRIGHT_VALUE_33) {
            mBrightLevel = 1;
            if (mBrightLevel != mLastBrightLevel) {
                mLastBrightLevel = mBrightLevel;
                imgBright.setBackground(getDrawable(R.drawable.ic_bright_01));
            }
            return;
        }

        if (BRIGHT_VALUE_33 <= brightValue && brightValue < BRIGHT_VALUE_66) {
            mBrightLevel = 2;
            if (mBrightLevel != mLastBrightLevel) {
                mLastBrightLevel = mBrightLevel;
                imgBright.setBackground(getDrawable(R.drawable.ic_bright_02));
            }
            return;
        }

        if (BRIGHT_VALUE_66 <= brightValue) {
            mBrightLevel = 3;
            if (mBrightLevel != mLastBrightLevel) {
                mLastBrightLevel = mBrightLevel;
                imgBright.setBackground(getDrawable(R.drawable.ic_bright_03));
            }
            return;
        }
    }

    /**
     * 设置音量图标icon
     *
     * @param voiceValue
     */
    private void setVoiceIcon(int voiceValue) {
        if (VOICE_VALUE_0 == voiceValue) {
            mVoiceLevel = 0;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                imgVoice.setBackground(getDrawable(R.drawable.ic_voice_00));
            }
            return;
        }

        if (VOICE_VALUE_0 < voiceValue && voiceValue < VOICE_VALUE_33) {
            mVoiceLevel = 1;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                imgVoice.setBackground(getDrawable(R.drawable.ic_voice_01));
            }
            return;
        }

        if (VOICE_VALUE_33 <= voiceValue && voiceValue < VOICE_VALUE_66) {
            mVoiceLevel = 2;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                imgVoice.setBackground(getDrawable(R.drawable.ic_voice_02));
            }
            return;
        }

        if (VOICE_VALUE_66 <= voiceValue) {
            mVoiceLevel = 3;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                imgVoice.setBackground(getDrawable(R.drawable.ic_voice_03));
            }
            return;
        }
    }

    private void setScreenBrightness() {
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - mBrightnessLastSetTime < 200) {
            mHandler.removeMessages(MSG_SET_BRIGHTNESS);
            mHandler.sendEmptyMessageDelayed(MSG_SET_BRIGHTNESS, 200);
        } else {
            mBrightnessLastSetTime = currentTimeMillis;
            mExecutorService.execute(new Runnable() {
                @Override
                public void run() {
                    saveScreenBrightness(mBrightnessProgress);
                }
            });
        }
    }

    private void saveScreenBrightness(int value) {
        boolean isSuccess = EeoApplication.udi.setBrightnessValue(value);
        Log.i(TAG, "saveScreenBrightness: value:" + value + " , isSuccess : " + isSuccess);
    }

    private void setSystemVolume() {
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - mVolumeLastSetTime < 200) {
            mHandler.removeMessages(MSG_SET_VOLUME);
            mHandler.sendEmptyMessageDelayed(MSG_SET_VOLUME, 200);
        } else {
            mVolumeLastSetTime = currentTimeMillis;
            mExecutorService.execute(new Runnable() {
                @Override
                public void run() {
                    if (audioManager == null) {
                        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
                    }
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, mVolumeProgress, AudioManager.FLAG_PLAY_SOUND);
                }
            });
        }
    }

    private void updateVolumeProgress() {
        if (mIsVolumeSeekBarTracking || System.currentTimeMillis() - mVolumeSeekBarTrackingStopTime < 500) {
            mHandler.removeMessages(MSG_UPDATE_VOLUME_PROGRESS);
            mHandler.sendEmptyMessageDelayed(MSG_UPDATE_VOLUME_PROGRESS, 500);
            return;
        }
        if (audioManager == null) {
            audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        }
        int streamVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        sbVoice.setProgress(streamVolume + Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (progress < Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET) {
            if (fromUser) {
                mFromUser = true;
            }
            seekBar.setProgress(Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
            return;
        }
        progress = progress - Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET;
        switch (seekBar.getId()) {
            case R.id.sb_bright:
                if (mBrightnessProgress != progress) {
                    mBrightnessProgress = progress;
                    setBrightnessIcon(progress);
                    setScreenBrightness();
                }
                break;

            case R.id.sb_volume:
                if (mVolumeProgress != progress) {
                    mVolumeProgress = progress;
                    setVoiceIcon(progress);
                    if (mFromUser || fromUser) {
                        setSystemVolume();
                    }
                }
                break;

            default:
                break;
        }
        mFromUser = false;
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        if (seekBar.getId() == R.id.sb_volume) {
            mIsVolumeSeekBarTracking = true;
            mVolumeSeekBarTrackingStopTime = 0;
        }
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        if (seekBar.getId() == R.id.sb_volume) {
            mIsVolumeSeekBarTracking = false;
            mVolumeSeekBarTrackingStopTime = System.currentTimeMillis();
        }
    }


    class MainPowerBroadcast extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            String action = intent.getAction();
            //监听点击返回按钮，返回上一级页面
            if (action.equals(Constant.ACTION_BACK)) {
                int childCount = frame.getChildCount();
                if (childCount <= 1) {
                    finish();
                    return;
                }
                frame.removeViewAt(childCount - 1);
                if (childCount == 2) {
                    //回到首页
                    rlBg.setVisibility(View.VISIBLE);
//                            ObjectAnimator.ofInt(viewGroupWrapper, "trueWidth", CommonUtils.dp2px(MainActivity.this, 384)).setDuration(200).start();
//                            ObjectAnimator.ofInt(viewGroupWrapper, "trueHeight", CommonUtils.dp2px(MainActivity.this, 384)).setDuration(200).start();
                    Observable.timer(210, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            for (int i = 0; i < rlBg.getChildCount(); i++) {
                                rlBg.getChildAt(i).setVisibility(View.VISIBLE);
                            }
                            rlBg.setVisibility(View.VISIBLE);
                            EeoApplication.currentPage = EeoApplication.PAGE_MAIN;
                        }
                    });
                } else {
                    //回到其它级页面
                    frame.getChildAt(childCount - 2).setVisibility(View.VISIBLE);
                    rlBg.setVisibility(View.GONE);
                }
            } else if (action.equals(Constant.ACTION_WIFI_MORE)) { //判断是否是wifi详情页广播
                int childWiFiCount = frame.getChildCount();
                if (childWiFiCount >= 2) {
                    WifiInfo wifiInfo = intent.getParcelableExtra(Constant.ACTION_WIFI_KEY);
                    FrameLayout flSystemSetting = (FrameLayout) frame.getChildAt(1);
                    flSystemSetting.setVisibility(View.GONE);
                    rlBg.setVisibility(View.GONE);
                    frame.addView(new WiFiMoreFrameLayout(MainActivity.this, wifiInfo));
                }
            } else if (action.equals(Constant.ACTION_PRIVACY)) { //判断是否是隐私协议页广播
                int childWiFiCount = frame.getChildCount();
                if (childWiFiCount >= 2) {
                    FrameLayout flSystemSetting = (FrameLayout) frame.getChildAt(1);
                    flSystemSetting.setVisibility(View.GONE);
                    rlBg.setVisibility(View.GONE);
                    if (mPrivacyFrameLayout == null) {
                        mPrivacyFrameLayout = new PrivacyFrameLayout(MainActivity.this);
                    }
                    frame.addView(mPrivacyFrameLayout);
                }
            } else if (Constant.ACTION_EXIT.equals(action)) {
                finish();
            } else if (intent.getAction().equals(Constant.ACTION_MULTI_SCREEN_START_MIRROR)) {
                //飞图投屏开始
                finish();
            } else if (intent.getAction().equals(Constant.ACTION_MULTI_SCREEN_STOP_MIRROR)) {
                //飞图投屏结束
                finish();
            } else if (intent.getAction().equals("android.media.VOLUME_CHANGED_ACTION") || intent.getAction().equals("android.media.STREAM_MUTE_CHANGED_ACTION")) {
                updateVolumeProgress();
            }
        }

    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        CommonUtils.updateDensity(this);
        super.onConfigurationChanged(newConfig);
        txtTitle.setText(getResources().getString(R.string.screen_title));
        txtScreen.setText(getResources().getString(R.string.screen_text));
        txtSetting.setText(getResources().getString(R.string.setting));
        txtSignal.setText(getResources().getString(R.string.sign));
        txtTouchLock.setText(getResources().getString(R.string.lock));
        txtEye.setText(getResources().getString(R.string.eye));
        txtWrite.setText(getResources().getString(R.string.write_text));
        tvDesktop.setText(getResources().getString(R.string.desktop));
        tvRest.setText(getResources().getString(R.string.resting));
        tvRestart.setText(getResources().getString(R.string.restart));
        tvShutdown.setText(getResources().getString(R.string.shutdown));
        if (EeoApplication.currentPage == EeoApplication.PAGE_SETTING) {
            if (frame.getChildCount() >= 2) {
                frame.removeViewAt(1);
            }
            mSettingFrameLayout = new SettingFrameLayout(MainActivity.this, MainActivity.this);
            mSettingFrameLayout.selectLocaleFragment();
            frame.addView(mSettingFrameLayout);
        }
        mPrivacyFrameLayout = null;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.e(TAG, "onCreate: ");
    }

    private void registerTvNotifyListener() {
        mTvNotifyListener = new ITvNotifyListener() {
            @Override
            public void onTvNotify(String action, Bundle bundle) {
                Log.d(TAG, "onTvNotify:" + action);
                if ("notifySystemInputSourcePlugInOutStatus".equals(action)) {
                    //有插拔状态变化
                    //获取当前信号哪些可用
                    Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
                        @Override
                        public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                            String availableSource = EeoApplication.udi.getAvailableSource();
                            if (availableSource != null) {
                                Gson gson = new Gson();
                                AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                                List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                                emitter.onNext(sources);
                            }
                        }
                    }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
                        @Override
                        public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                            mAvailableSourceList.clear();
                            mAvailableSourceList.addAll(sourcesBeans);
                            if (mSignalFrameLayout != null && EeoApplication.currentPage == EeoApplication.PAGE_SIGNAL) {
                                mSignalFrameLayout.getAvailableSource(sourcesBeans);
                            }
                        }
                    });
                }
            }
        };
        TvApiSDKManager.getInstance().addNotifyHandle(mTvNotifyListener);
    }

    private void unregisterTvNotifyListener() {
        if (mTvNotifyListener != null) {
            TvApiSDKManager.getInstance().removeNotifyHandle(mTvNotifyListener);
            mTvNotifyListener = null;
        }
    }
}