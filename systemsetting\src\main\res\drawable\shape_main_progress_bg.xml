<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--定义seekbar滑动条的底色-->
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="7dp" />
            <solid android:color="@color/white_50"/>
        </shape>
    </item>
    <!--定义seekbar滑动条进度颜色-->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners android:radius="7dp"/>
                <solid android:color="@color/white_100"/>
                <gradient android:type="linear"
                    android:useLevel="true"
                    android:startColor="@color/white_100"
                    android:endColor="@color/white_100"
                    />
            </shape>
        </clip>
    </item>
</layer-list>