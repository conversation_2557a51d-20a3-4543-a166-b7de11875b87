025-07-28 14:34:51.011  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-07-28 14:34:51.018  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-28 14:34:51.018  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-07-28 14:34:51.019  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-07-28 14:35:01.023  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-28 14:35:01.024  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-07-28 14:35:01.027  2598-2684  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-07-28 14:35:05.294  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-07-28 14:35:07.308  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-07-28 14:35:07.308  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-07-28 14:35:07.309  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-07-28 14:35:10.348  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-07-28 14:35:10.348  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-07-28 14:35:10.350  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Current version: A013, Target version: A013
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? false
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  No update required. Cleaning up...
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-28 14:35:10.395  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-07-28 14:35:10.397  2598-2714  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-07-28 14:35:12.737  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-28 14:35:12.738  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-07-28 14:35:12.738  2598-2598  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-28 14:35:12.741  2598-2598  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@1e7a2e4
2025-07-28 14:35:12.741  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-28 14:35:12.741  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-28 14:35:12.741  2598-2598  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.