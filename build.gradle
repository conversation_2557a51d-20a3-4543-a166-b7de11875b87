// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '7.1.2' apply false
    id 'com.android.library' version '7.1.2' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext{
    buildToolsVersion = '28.0.3'
    compileSdkVersion = 30
    appcompatVersion = '28.0.0'
    minSdkVersion = 26
    targetSdkVersion = 30
    versionCode = 1
    versionName = '1.0'

    supportV4 = '28.0.0'
}