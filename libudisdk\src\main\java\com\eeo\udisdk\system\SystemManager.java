package com.eeo.udisdk.system;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;

import com.eeo.udisdk.UdiConstant;
import com.ifpdos.udi.sdk.IEventHandler;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 系统相关
 */
public class SystemManager {
    private static final String TAG = "Udi-SystemManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;
    private IEventHandler mOpsShutdownHandler = null;
    private IEventHandler mHalfScreenChangeHandler = null;
    private IEventHandler mScreenStatusChangeHandler = null;
    private IEventHandler mRS232DataHandler = null;


    private final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    public SystemManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    public boolean reboot() {
        String json = "{}";
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/reboot"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "reboot:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean shutdown() {
        String json = "{\"value\": 1}";
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/power/off"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "shutdown:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * pc开机
     */
    public boolean startOps() {
        String json = "{}";
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/pc/start"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "startOps:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * pc关机
     */
    public boolean shutdownOps() {
        String json = "{}";
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/pc/off"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "shutdownOps:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 监听pc关机
     */
    public void registerOpsShutdownListener(OpsShutdownListener opsShutdownListener) {
        if (opsShutdownListener == null) {
            return;
        }
        if (mOpsShutdownHandler != null) {
            unregisterOpsShutdownListener();
        }
        mOpsShutdownHandler = new IEventHandler() {
            @Override
            public void onEvent(String event, String data) {
                Log.d(TAG, "onEvent: " + event + ",  data=" + data);
                opsShutdownListener.onOpsShutdown();
            }
        };
        //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
        UdiSdk.registerEvents(Arrays.asList("v1/system/pc/close"), mOpsShutdownHandler);
    }

    /**
     * 取消监听pc关机
     */
    public void unregisterOpsShutdownListener() {
        if (mOpsShutdownHandler != null) {
            //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
            UdiSdk.unregisterEvents(Arrays.asList("v1/system/pc/close"), mOpsShutdownHandler);
            mOpsShutdownHandler = null;
        }
    }

    /**
     * 重置ops
     */
    public boolean resetOps() {
        String json = "{}";
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/pc/recovery"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "resetOps:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否半屏模式
     */
    public boolean isHalfScreen() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/half/enable"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isHalfScreen:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isHalfScreen body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("enable");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 设置半屏模式
     */
    public boolean setHalfScreen(boolean enable) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("enable", enable);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/half/enable"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "enableHalf:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 发送RS232数据
     *
     * @param data 十六进制数组
     *             JSONArray data = new JSONArray();
     *             data.put(0xAA);
     *             data.put(0xBB);
     *             data.put(0xCC);
     */
    public boolean sendRS232Data(JSONArray data) {
        if (data == null) {
            Log.d(TAG, "sendRS232Data:data is null!");
            return false;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("value", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/rs232/data"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "sendRS232Data:data=" + data + "  , response = " + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 监听半屏模式变化
     */
    public void registerHalfScreenChangeListener(HalfScreenChangeListener halfScreenChangeListener) {
        if (halfScreenChangeListener == null) {
            return;
        }
        if (mHalfScreenChangeHandler != null) {
            unregisterHalfScreenChangeListener();
        }
        mHalfScreenChangeHandler = new IEventHandler() {
            @Override
            public void onEvent(String event, String data) {
                Log.d(TAG, "onEvent: " + event + ",  data=" + data);
                if (data != null) {
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        boolean enable = jsonObject.optBoolean("enable");
                        halfScreenChangeListener.onHalfScreenChange(enable);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
        //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
        UdiSdk.registerEvents(Arrays.asList("v1/system/screen/half/enable"), mHalfScreenChangeHandler);
    }

    /**
     * 取消监听半屏模式变化
     */
    public void unregisterHalfScreenChangeListener() {
        if (mHalfScreenChangeHandler != null) {
            //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
            UdiSdk.unregisterEvents(Arrays.asList("v1/system/screen/half/enable"), mHalfScreenChangeHandler);
            mHalfScreenChangeHandler = null;
        }
    }

    /**
     * 监听RS232命令
     */
    public void registerRS232Listener(RS232DataListener mRS232DataListener) {
        if (mRS232DataListener == null) {
            return;
        }
        if (mRS232DataHandler != null) {
            unregisterRS232Listener();
        }
        mRS232DataHandler = new IEventHandler() {
            @Override
            public void onEvent(String event, String data) {
                Log.d(TAG, "onEvent: " + event + ",  data=" + data);
                if (data != null) {
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        JSONArray mArray = jsonObject.optJSONArray("mBufferData");
                        mRS232DataListener.onRS232Data(mArray);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
        //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
        UdiSdk.registerEvents(Arrays.asList("v1/system/rs232/data"), mRS232DataHandler);
    }

    /**
     * 取消监听RS232命令
     */
    public void unregisterRS232Listener() {
        if (mRS232DataHandler != null) {
            //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
            UdiSdk.unregisterEvents(Arrays.asList("v1/system/rs232/data"), mRS232DataHandler);
            mRS232DataHandler = null;
        }
    }

    /**
     * 查询PC模块是否插入
     */
    public boolean isOpsConnected() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/pc/connect"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isOpsConnected:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isOpsConnected body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("value");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取ops状态
     *
     * @return
     * @see UdiConstant#OPS_STATUS_ON
     */
    public String getOpsStatus() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/pc/status"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            if (response.code() == 200) {
                String body = response.body().string();
                /*返回的例子:
                {
                    "value": "ON"
                }*/
                JSONObject jsonObject = new JSONObject(body);
                return jsonObject.optString("value");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 是否亮屏
     */
    public boolean isScreenOn() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/status"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isScreenOn:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isScreenOn body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return jsonObject.optBoolean("isOn");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 亮屏/熄屏
     */
    public boolean changeScreenStatus(boolean isOn, boolean isAllowIntercept, boolean isMute) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("isOn", isOn);
            jsonObject.put("isAllowIntercept", isAllowIntercept);
            jsonObject.put("isMute", isMute);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/status"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "changeScreenStatus:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 监听屏幕状态变化
     */
    public void registerScreenStatusChangeListener(ScreenStatusChangeListener screenStatusChangeListener) {
        if (screenStatusChangeListener == null) {
            return;
        }
        if (mScreenStatusChangeHandler != null) {
            unregisterScreenStatusChangeListener();
        }
        mScreenStatusChangeHandler = new IEventHandler() {
            @Override
            public void onEvent(String event, String data) {
                Log.d(TAG, "onEvent: " + event + ",  data=" + data);
                if (data != null) {
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        boolean isOn = jsonObject.optBoolean("isOn");
                        boolean isMute = jsonObject.optBoolean("isMute");
                        screenStatusChangeListener.onScreenStatusChange(isOn, isMute);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
        //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
        UdiSdk.registerEvents(Arrays.asList("v1/system/screen/status"), mScreenStatusChangeHandler);
    }

    /**
     * 取消监听屏幕状态变化
     */
    public void unregisterScreenStatusChangeListener() {
        if (mScreenStatusChangeHandler != null) {
            //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
            UdiSdk.unregisterEvents(Arrays.asList("v1/system/screen/status"), mScreenStatusChangeHandler);
            mScreenStatusChangeHandler = null;
        }
    }


    /**
     * 截屏
     *
     * @param path 图片保存的路径
     */
    public String screenshot(String path) {
        Log.d(TAG, "screenshot: path=" + path);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("value", path);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screenshot"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "screenshot:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "screenshot body:" + body);
                JSONObject responseObject = null;
                try {
                    responseObject = new JSONObject(body);
                    return responseObject.optString("value");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 截屏
     */
    public void screenshot(ScreenshotListener screenshotListener) {
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                String path = mContext.getCacheDir() + "/screenshot.jpg";
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
                String screenshotResultPath = screenshot(path);
                try {
                    Bitmap bitmap = BitmapFactory.decodeFile(screenshotResultPath);
                    screenshotListener.onScreenshotSuccess(bitmap);
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.d(TAG, "screenshot exception: " + e);
                    screenshotListener.onScreenshotFail(e.toString());
                }
            }
        });
    }

    /**
     * 设置截屏类型
     *
     * @param type
     * @see UdiConstant#TYPE_SHOT_VIDEO_AND_OSD
     */
    public boolean setScreenshotType(String type) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("value", type);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/shot/type"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setScreenshotType:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 录屏是否支持
     */
    public boolean isScreenRecordSupport() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/record/support"))
                .build();
        /*返回的例子:
        {
           "value": true
        }*/
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isScreenRecordSupport:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isScreenRecordSupport body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                    return jsonObject.optBoolean("value");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 录屏
     */
    public boolean screenRecord(String path, boolean isRecordAudio, boolean isRecordMicrophone,
                                int systemVolume, int microphoneVolume, int resolution) {
        Log.d(TAG, "screenRecord: path=" + path);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("path", path);
            jsonObject.put("isRecordAudio", isRecordAudio);
            jsonObject.put("isRecordMicrophone", isRecordMicrophone);
            jsonObject.put("systemVolume", systemVolume);
            jsonObject.put("microphoneVolume", microphoneVolume);
            jsonObject.put("resolution", resolution);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/record/start"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "screenRecord:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 停止录屏
     */
    public boolean stopScreenRecord() {
        String json = "{}";

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/screen/record/stop"))
                .post(RequestBody.create(json, mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "stopScreenRecord:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * ota升级
     */
    public boolean otaUpgrade(String path, OtaUpgradeListener otaUpgradeListener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("path", path);
//            jsonObject.put("copyTo", "copyPath"); //可选参数，如果设置了，升级之前会把文件拷贝到这个路径下面，默认不做拷贝
            jsonObject.put("removeFilePolicy", UdiConstant.OTA_POLICY_NEVER); //可选参数，是否在成功升级之后，删除掉文件，默认为POLICY_NEVER
            jsonObject.put("rebootOnSuccess", true); //可选参数，升级完成功后，是否重启，默认不做重启
            jsonObject.put("forceModel", true); //可选参数，强制使用这个 model 升级
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/ota/upgrade"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "otaUpdate:response=" + response);
            if (response.code() == 200) {
                otaUpgradeListener.onUpgradeSuccess();
                return true;
            } else {

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 模拟PC按键
     */
    public boolean sendPcKeyEvent(int controlKey, int functionKey, int event, int duration) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("controlKey", controlKey); //控制码(CTRL=0x01,SHIFT=0x02,ALT=0x04,WIN=0x08)
            jsonObject.put("functionKey", functionKey); //按键码
            jsonObject.put("event", event); // 事件码(REVERSED=0x0,KEY_CLICK=0x1,KEY_DOWN=0x2,KEY_UP=0x3)
            jsonObject.put("time", duration); // 按键持续时间（毫秒）
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/system/event/key"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "sendPcKeyEvent:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
