package com.eeo.annotation.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;

import androidx.annotation.NonNull;

import com.eeo.annotation.R;
import com.eeo.annotation.bean.Constant;

public class DrawingSurfaceView extends SurfaceView implements Runnable, SurfaceHolder.Callback {
    public static final String TAG = "DrawingSurfaceView";

    private Thread mThread;

    private Canvas mCanvas;
    private Canvas mBufferCanvas;
    private Bitmap mBufferBitmap;
    private Paint mPaint;
    /**
     * 屏幕尺寸
     */
    private int mScreenWidth;
    private int mScreenHeight;

    private Bitmap mBackgroundBitmap;

    private boolean mIsButtonEraser;
    private Bitmap mButtonEraserBitmap; //橡皮擦背景图
    private Paint mEraserPaint;
    private float mEraserX;
    private float mEraserY;

    /**
     * 手指是否按下
     */
    private boolean mIsDrawing;

    private boolean mIsRunning;

    private final Path mPath;

    /**
     * 用来计算贝塞尔曲线
     */
    private float mLastX;
    private float mLastY;

    public DrawingSurfaceView(Context context) {
        this(context, null);
    }

    public DrawingSurfaceView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DrawingSurfaceView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        getHolder().setFormat(PixelFormat.TRANSLUCENT);
        getHolder().addCallback(this);
        mPath = new Path();
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
        mPaint.setStyle(Paint.Style.STROKE);
        mThread = new Thread(this);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        init();
    }

    private void init() {
        mBufferBitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        mBufferCanvas = new Canvas(mBufferBitmap);
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.eraser);
        mButtonEraserBitmap = Bitmap.createScaledBitmap(bitmap, Constant.BUTTON_ERASER_SIZE, Constant.BUTTON_ERASER_SIZE, true);
        mEraserPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
        mEraserPaint.setStyle(Paint.Style.STROKE);
        mEraserPaint.setStrokeCap(Paint.Cap.ROUND);
        mEraserPaint.setColor(-1);
    }

    public void setBrush(boolean isEraser, int color, int sizeInPixel) {
        mIsButtonEraser = isEraser;
        mPaint.setColor(color);
        mPaint.setStrokeWidth(sizeInPixel);
        if (isEraser) {
            mPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        } else {
            mPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC));
        }
    }

    @Override
    public void run() {
        while (mIsRunning) {
            draw();
        }
    }

    @Override
    public void surfaceCreated(@NonNull SurfaceHolder holder) {
        Log.e(TAG, "surfaceCreated: ");
        startDraw();
    }

    @Override
    public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) {
        Log.e(TAG, "surfaceChanged: ");
    }

    @Override
    public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
        Log.e(TAG, "surfaceDestroyed: ");
        stopDraw();
    }

    public void startDraw() {
        mIsRunning = true;
        if (mThread.isInterrupted()) {
            mThread = new Thread(this);
        }
        if (mThread.getState() == Thread.State.NEW) {
            mThread.start();
        }
    }

    public void stopDraw() {
        mIsRunning = false;
        mThread.interrupt();
    }

    /**
     * 清空批注
     */
    public void clear() {
        mBufferCanvas.drawColor(0, PorterDuff.Mode.CLEAR);
    }

    private void draw() {
        try {
            mCanvas = getHolder().lockCanvas();
            if (mCanvas == null) {
                return;
            }
            //每次把画布清空
            mCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
            if (mBackgroundBitmap != null) {
                mCanvas.drawBitmap(mBackgroundBitmap, 0, 0, null);
            }
            if (mIsDrawing) {
                //绘制本次down后的
//                mBufferCanvas.drawPath(mPath, mPaint); //实际显示的
                mCanvas.drawPath(mPath, mPaint);  //速度快一点，这里重复显示，有加速效果
                if (mIsButtonEraser) {
                    //绘制橡皮擦
                    mCanvas.drawBitmap(mButtonEraserBitmap, mEraserX, mEraserY, mEraserPaint);
                }
            }
            //绘制之前的buffer bitmap
            mCanvas.drawBitmap(mBufferBitmap, 0, 0, null);
        } catch (Exception e) {
            Log.e(TAG, "draw exception:" + e);
            e.printStackTrace();
        } finally {
            // 对画布内容进行提交
            if (mCanvas != null) {
                try {
                    getHolder().unlockCanvasAndPost(mCanvas);
                    mCanvas = null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        super.onTouchEvent(event);
        if (event.getPointerCount() > 1) {
            return false;
        }
        float x = event.getX();
        float y = event.getY();
        if (mIsButtonEraser) {
            //橡皮擦不超出屏幕边缘
            mEraserX = x - Constant.BUTTON_ERASER_SIZE / 2.0f;
            mEraserY = y - Constant.BUTTON_ERASER_SIZE / 2.0f;
            if (mEraserX < 0) {
                mEraserX = 0;
                x = Constant.BUTTON_ERASER_SIZE / 2.0f;
            } else if (mEraserX + Constant.BUTTON_ERASER_SIZE > mScreenWidth) {
                //屏幕右边缘
                mEraserX = mScreenWidth - Constant.BUTTON_ERASER_SIZE;
                x = mScreenWidth - Constant.BUTTON_ERASER_SIZE / 2.0f;
            }
            if (mEraserY < 0) {
                mEraserY = 0;
                y = Constant.BUTTON_ERASER_SIZE / 2.0f;
            } else if (mEraserY + Constant.BUTTON_ERASER_SIZE > mScreenHeight) {
                //屏幕底边缘
                mEraserY = mScreenHeight - Constant.BUTTON_ERASER_SIZE;
                y = mScreenHeight - Constant.BUTTON_ERASER_SIZE / 2.0f;
            }
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mIsDrawing = true;
                mPath.reset(); //只计算本次down后的
                mPath.moveTo(x, y);
                mLastX = x;
                mLastY = y;
                if (mIsButtonEraser) {
                    Paint paint = new Paint();
                    paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
                    mBufferCanvas.drawCircle(x, y, Constant.BUTTON_ERASER_SIZE / 2.0f, paint);
                }
                break;

            case MotionEvent.ACTION_MOVE:
//                mPath.lineTo(x, y);
                //贝塞尔曲线
                mPath.quadTo(mLastX, mLastY, (x + mLastX) / 2, (y + mLastY) / 2);
                mBufferCanvas.drawPath(mPath, mPaint); //实际显示的
                mLastX = x;
                mLastY = y;
                break;

            case MotionEvent.ACTION_UP:
                mIsDrawing = false;
                break;
        }
        return true;
    }

    public void setScreenSize(int screenWidth, int screenHeight) {
        Log.d(TAG, "setScreenSize: " + screenWidth + "," + screenHeight);
        mScreenWidth = screenWidth;
        mScreenHeight = screenHeight;
    }

    public void setBackgroundBitmap(Bitmap bitmap) {
        mBackgroundBitmap = bitmap;
    }
}
