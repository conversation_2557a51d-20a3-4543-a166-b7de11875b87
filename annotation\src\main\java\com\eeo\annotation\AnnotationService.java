package com.eeo.annotation;

import static com.eeo.annotation.bean.Constant.EXIT_ANNOTATION;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.DisplayMetrics;
import android.util.Log;

import androidx.annotation.Nullable;

import com.eeo.WhiteboardAccelerate.IBinderPoolInterface;
import com.eeo.WhiteboardAccelerate.IDrawNoteInterface;
import com.eeo.annotation.bean.Constant;
import com.eeo.annotation.util.Util;
import com.eeo.annotation.view.AnnotationWindow;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.system.ScreenshotListener;

public class AnnotationService extends Service {
    private final static String TAG = "AnnotationService";

    private AnnotationWindow mAnnotationWindow;
    /**
     * 屏幕尺寸
     */
    private int mScreenWidth;
    private int mScreenHeight;
    private int mDensity;

    private MediaProjectionManager mMediaProjectionManager;
    private MediaProjection mMediaProjection;
    private VirtualDisplay mVirtualDisplay;

    private ImageReader mImageReader;

    private Udi mUdi;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "onCreate: ");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel mChannel = new NotificationChannel("channel_annotation", "annotationChannel", NotificationManager.IMPORTANCE_NONE);
            notificationManager.createNotificationChannel(mChannel);
            Notification notification = new Notification.Builder(this, "channel_annotation").build();
            startForeground(1, notification);
        }
        //屏屏幕范围参数
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        mScreenWidth = displayMetrics.widthPixels * Constant.SCALE;
        mScreenHeight = displayMetrics.heightPixels * Constant.SCALE;
        mDensity = displayMetrics.densityDpi;
        mUdi = new Udi(this, UdiConstant.TOKEN_ANNOTATION);
        if (mAnnotationWindow == null) {
            mAnnotationWindow = new AnnotationWindow(AnnotationService.this, mScreenWidth, mScreenHeight);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            int status = intent.getIntExtra("AnnotationStatus", 1);  //1启动批注，2关闭批注,3进入息屏写
            Log.d(TAG, "AnnotationStatus=" + status + ",AccelerateServiceConnected=" + mIsServiceConnected);
            if (status != EXIT_ANNOTATION) {
                mAnnotationWindow.showAnnotationWindow(intent.getIntExtra(Constant.X, Constant.DEFAULT_X),
                        intent.getIntExtra(Constant.Y, Constant.DEFAULT_Y), status);
            }
            mAnnotationWindow.setAnnotationWriteStatus(status);

            if (Constant.ACCELERATE_ENABLE && !mIsServiceConnected) {
                binderAccelerateService();
            }


            if (Constant.CAPTURE_ENABLE) {
                if (Constant.UDI_ENABLE) {
                    mUdi.setScreenshotType(UdiConstant.TYPE_SHOT_VIDEO_AND_OSD);
                    mUdi.screenshot(new ScreenshotListener() {
                        @Override
                        public void onScreenshotSuccess(Bitmap bitmap) {
                            Log.e(TAG, "onScreenshotSuccess: " + bitmap.getWidth() + "," + bitmap.getHeight());
                            mAnnotationWindow.setBackgroundBitmap(bitmap);
                        }

                        @Override
                        public void onScreenshotFail(String reason) {

                        }
                    });
                } else {
                    boolean isCapture = intent.getBooleanExtra(Constant.CAPTURE, false);
                    Log.d(TAG, "onStartCommand: isCapture=" + isCapture);
                    if (isCapture) {
                        int resultCode = intent.getIntExtra(Constant.RESULT_CODE, -1);
                        Intent data = intent.getParcelableExtra(Constant.DATA);
                        if (mMediaProjectionManager == null) {
                            mMediaProjectionManager = ((MediaProjectionManager) getSystemService(Context.MEDIA_PROJECTION_SERVICE));
                        }
                        mMediaProjection = mMediaProjectionManager.getMediaProjection(resultCode, data);
                        screenCapture();
                    } else {
                        startCaptureActivity();
                    }
                }
            }
        }
        return Service.START_NOT_STICKY;
    }

    /**
     * 纯android截屏
     * 启动截屏请求Activity来获的MediaProjection
     */
    private void startCaptureActivity() {
        Intent intent = new Intent(this, ScreenCaptureActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(Constant.X, mAnnotationWindow.getMenuX());
        intent.putExtra(Constant.Y, mAnnotationWindow.getMenuY());
        startActivity(intent);
    }

    /**
     * 纯android截屏
     * 截屏获取bitmap作为批注背景
     */
    @SuppressLint("WrongConstant")
    private void screenCapture() {
        if (mImageReader == null) {
            mImageReader = ImageReader.newInstance(mScreenWidth, mScreenHeight, PixelFormat.RGBA_8888, 1);
            mImageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {

                @Override
                public void onImageAvailable(ImageReader reader) {
                    Log.d(TAG, "onImageAvailable");
                    Bitmap bitmap = Util.image2Bitmap(reader.acquireLatestImage());
                    //释放virtualDisplay,不释放会报错
                    mVirtualDisplay.release();
                    mAnnotationWindow.setBackgroundBitmap(bitmap);
                    reader.setOnImageAvailableListener(null, null);
                }
            }, null);
        }
        mVirtualDisplay = mMediaProjection.createVirtualDisplay("capture_screen", mScreenWidth, mScreenHeight,
                mDensity, DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                mImageReader.getSurface(), null, null);
    }

    private IBinderPoolInterface mIBinderPoolInterface;
    private IDrawNoteInterface mIDrawNoteInterface;
    private boolean mIsServiceConnected;
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mIsServiceConnected = true;
            mIBinderPoolInterface = IBinderPoolInterface.Stub.asInterface(service);
            try {
                mIDrawNoteInterface = IDrawNoteInterface.Stub.asInterface(mIBinderPoolInterface.getBinderInterface(1));
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            if (mAnnotationWindow != null) {
                mAnnotationWindow.onAccelerateServiceConnected(mIDrawNoteInterface);
            }
            try {
                service.linkToDeath(mDeathRecipient, 0);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected: ");
            mIsServiceConnected = false;
            if (mAnnotationWindow != null) {
                mAnnotationWindow.onAccelerateServiceDisconnected();
            }
        }
    };

    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            if (mIDrawNoteInterface != null) {
                mIDrawNoteInterface.asBinder().unlinkToDeath(this, 0);
                mIDrawNoteInterface = null;
                binderAccelerateService();
            }
        }
    };

    private void binderAccelerateService() {
        //绑定板书加速服务
        Intent intent = new Intent("com.eeo.WhiteboardAccelerate.action.startService");
        intent.setPackage("com.eeo.WhiteboardAccelerate");
        bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "onDestroy mIsServiceConnected=" + mIsServiceConnected);
        try {
            if (mIsServiceConnected) {
                if (mIDrawNoteInterface != null) {
                    mIDrawNoteInterface.setOpenWriteAccelerate(false);
                    mIDrawNoteInterface.setClearCanvas();
                    mAnnotationWindow.onAccelerateServiceDisconnected();
                }
                unbindService(mServiceConnection);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
