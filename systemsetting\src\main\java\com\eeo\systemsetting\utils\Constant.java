package com.eeo.systemsetting.utils;

import android.os.Build;
import android.os.SystemProperties;

public class Constant {
    public static final boolean IS_USERDEBUG = "userdebug".equals(Build.TYPE);

    public static final boolean IS_BS65A = Build.MODEL.contains("BS65A");
    public static final boolean IS_75 = Build.MODEL.contains("BS75A") || Build.MODEL.contains("BSP75A");
    public static final boolean IS_86 = Build.MODEL.contains("BS86A") || Build.MODEL.contains("BSP86A");
    public static final boolean IS_110 = Build.MODEL.contains("BS110A") || Build.MODEL.contains("BSP110A");

    /**
     * 81:C.T982.81_A1
     * 61:C.T982.61_A5
     * 区分61和81
     */
    public static final boolean IS_T982_61 = SystemProperties.get("ro.cvte.boardname", "").contains("T982.61");

    public static final String SERIAL_NUMBER = SystemProperties.get("persist.sys.boardsn.value", "unknown");
    /**
     * 根据序列号前缀的产品型号判断是否滑条存在。
     * BS86A,BS86E，BS110A有滑条，其他产品类型都没滑条。
     */
    public static final boolean HAS_TOUCH_SLIDER = SERIAL_NUMBER.contains("BS86A") || SERIAL_NUMBER.contains("BS86E") || SERIAL_NUMBER.contains("BS110A");

    /**
     * 65、75、86、110物理尺寸
     * 1428.48 (H)x803.52(V)  65（0.75）
     * <p>
     * 1650.24(H)x928.26(V)  75（0.87）
     * <p>
     * 1895.04(H)×1065.96(V)  86（1）
     * <p>
     * 2436.48(H) * 1370.52(V)  110（1.28）
     * <p>
     * 不同尺寸机型的设置界面通过density来配置不同的大小
     */
    public static final int DENSITY = 480;
    public static final int DENSITY_BS65A = (int) (DENSITY / 0.86);
    public static final int DENSITY_BS75A = DENSITY;
    public static final int DENSITY_BS86A = DENSITY;
    public static final int DENSITY_BS110A = (int) (DENSITY / 1.1);

    /**
     * 画中画
     * 不同尺寸机型再另外定义不同大小
     * 如BS65A的与BS86A的实际物理尺寸一样，需要在density/0.86的基础上再*1.14
     */
    public static final float PROJECTION_SCALE_BS65A = 1.14f;
    public static final float PROJECTION_SCALE_BS75A = 1.14f;
    public static final float PROJECTION_SCALE_BS110A = 0.86f;

    //返回上级页面
    public static final String ACTION_BACK = "com.eeo.systemsetting.back";

    //wifi详情页面
    public static final String ACTION_WIFI_MORE = "com.eeo.wifi.more";
    //wifi详情页的bean
    public static final String ACTION_WIFI_KEY = "com.eeo.wifi.key";
    //退出大屏控制界面
    public static final String ACTION_EXIT = "com.eeo.systemsetting.exit";
    //隐私协议页面
    public static final String ACTION_PRIVACY = "com.eeo.systemsetting.privacy";

    //开机引导
    public static final String SETUP_PKG_NAME = "cn.eeo.classin.setup";

    /**
     * 飞图投屏
     */
    public static final String MULTI_SCREEN_PKG_NAME = "com.android.toofifi";
    public static final String MULTI_SCREEN_TOP_ACTIVITY_NAME = "com.android.toofifi.ui.activity.MultiScreenActivity";
    public static final String MULTI_SCREEN_TOP_ACTIVITY_NAME2 = "com.android.toofifi.engine.ui.CastEnginePlayActivity"; //华为鸿蒙自带投屏走这个
    public static final String MULTI_SCREEN_SERVICE_NAME = "com.android.toofifi.service.LicenseCheckService"; //可通过启动该service来唤起投屏服务
    public static final String MULTI_SCREEN_RECEIVER_NAME = "com.android.toofifi.receiver.ToofifiReceiver"; //可通过启动该service来唤起投屏服务
    public static final String MULTI_SCREEN_AIDL_SERVICE__NAME = "com.android.toofifi.aidlimpl.server.RemoteAidlInterfaceService"; //aidl绑定该service
    public static final String ACTION_MULTI_SCREEN_OS_MARK_OPEN = "com.toofifi.action.OS_MARK_OPEN"; //该广播允许投屏时弹窗
    public static final String ACTION_MULTI_SCREEN_START_MIRROR = "com.android.toofifi.start_mirror"; //投屏开始
    public static final String ACTION_MULTI_SCREEN_STOP_MIRROR = "com.android.toofifi.stop_mirror";  //投屏结束
    //投屏码相关
    // 修改投屏码开关  参数：pkg—包名，status-0关闭/1打开
    public static final String ACTION_MULTI_SCREEN_PINCODE_MODIFY = "com.mphotool.action.ACTION_PINCODE_MODIFY";
    public static final String ACTION_MULTI_SCREEN_PINCODE_REQUEST = "com.mphotool.action.ACTION_PINCODE_REQUEST"; //外部请求pincode
    //对外回复pincode（pincode发生改变时也会发送）：“status”: pincode是否开启：0-未开启，1-已开启 ”pin_code“: pincode
    public static final String ACTION_MULTI_SCREEN_PINCODE_RESPONE = "com.mphotool.action.ACTION_PINCODE_RESPONE";
    public static final String ACTION_MULTI_SCREEN_PINCODE_VISIBLE_CHANGE = "com.mphotool.action.ACTION_PINCODE_VISIBLE_CHANGE"; //外部调用pincode悬浮窗是否显示
    //投屏授权相关
    // 修改投屏授权开关的广播  参数：pkg—包名，status-状态0/1
    public static final String ACTION_MULTI_SCREEN_MIRROR_PERMISSION_MODIFY = "com.mphotool.action.ACTION_MIRROR_PERMISSION_MODIFY";
    // 请求获取投屏授权开关   参数：pkg—包名
    public static final String ACTION_MULTI_SCREEN_MIRROR_PERMISSION_REQUEST = "com.mphotool.action.ACTION_MIRROR_PERMISSION_REQUEST";
    // 回复投屏授权，有变化也能收到(目前是每次request才能收到）  参数：pkg—包名（可能为空） ，status-状态0/1
    public static final String ACTION_MULTI_SCREEN_MIRROR_PERMISSION_RESPONSE = "com.mphotool.action.ACTION_MIRROR_PERMISSION_RESPONSE";
    //飞图投屏激活码：Settings.Global
    public static final String KEY_MULTI_SCREEN_ACTIVATED_DATA = "ft_activated_data";

    //触摸广播
    public static final String SET_USB_ENABLE_ACTION = "com.eeo.set.usb.enable";
    public static final String USB2_TOUCH_KEY = "usbTouchKey";
    public static final int TOUCH = 0;
    public static final int UN_TOUCH = 1;

    //广播来源
    public static final String USB2_TOUCH_MODULE = "usbTouchModuleKey";
    //设置页面
    public static final int MODULE_SETTING = 0;
    //其他页面
    public static final int MODULE_OTHER = 1;

    public static final String KEYCODE_POWER_ACTION = "com.android.eeo.SendHotKey";
    public static final String HOT_KEY = "hotkey";
    public static final String KEY_EVENT = "event";
    public static final int KEYCODE_SEEWO_POWER = 5000;  // KeyEvent.KEYCODE_SEEWO_POWER = 5000;

    /**
     * 长按电源键广播，采用强关机
     */
    public static final String ACTION_EEO_LONG_PRESS_POWER_OFF = "com.eeo.action.LONG_PRESS_POWER_OFF";
    /**
     * 其它应用通过广播来统一让设置控制关机、重启
     */
    public static final String ACTION_EEO_POWER_OFF = "com.eeo.action.POWER_OFF";
    public static final String ACTION_EEO_REBOOT = "com.eeo.action.REBOOT";
    public static final String FORCE_REBOOT = "force";

    public static final String CONFIGURED_NETWORKS_CHANGE = "android.net.wifi.CONFIGURED_NETWORKS_CHANGE";
    public static final String LINK_CONFIGURATION_CHANGED = "android.net.wifi.LINK_CONFIGURATION_CHANGED";

    public static final float DIALOG_WIDTH_IN_DP = 384;
    public static final float DIALOG_HEIGHT_IN_DP = 384;

    public static final float DIALOG_WIRELESS_SCREEN_UNFOLD_HEIGHT_IN_DP = 308;
    public static final float DIALOG_WIRELESS_SCREEN_FOLD_HEIGHT_IN_DP = 65;

    public static final float DIALOG_RGB_WIDTH_IN_DP = 264;
    public static final float DIALOG_RGB_HEIGHT_IN_DP = 384;

    public static final float DIALOG_MARIN_END_IN_DP = 8;
    public static final float DIALOG_MARIN_BOTTOM_IN_DP = 68;

    public static final float SHADOW_WIDTH_IN_DP = 5;

    public static final int WINDOW_HOST_WIDTH = 240;
    public static final int WINDOW_HOST_HEIGHT = 148;

    public static final float WINDOW_HOST_MARGIN_END = 72;
    public static final float WINDOW_HOST_MARGIN_BOTTOM = 191;

    /**
     * seekBar小进度时动画效果不好
     * 这里将进度改为offset~100+offset,如13~113
     */
    public static final int SETTING_SEEK_BAR_PROGRESS_OFFSET = 13;
    public static final int PROJECTION_SEEK_BAR_PROGRESS_OFFSET = 7;

    public static final boolean CVT_EN_TV_SOURCE = SystemProperties.getBoolean("ro.en.tvsource", false);

    public final static String LANGUAGE_ZH = "zh";
    public final static String LANGUAGE_ENGLISH = "en";

    /**
     * 开机通道
     */
    public static final String PROP_LAST_SOURCE = "persist.ctm.tv_source_id";
    public static final String PROP_FORCE_SOURCE = "persist.ctm.force.source";
    public static final String FORCE_SOURCE_NONE = "E_INPUT_SOURCE_NONE";  //none的时候是上次关机通道
    public static final String FORCE_SOURCE_ANDROID = "E_INPUT_SOURCE_ANDROID";
    public static final String FORCE_SOURCE_PC = "E_INPUT_SOURCE_PC";
    public static final String FORCE_SOURCE_HDMI1 = "E_INPUT_SOURCE_HDMI1";
    public static final String FORCE_SOURCE_HDMI2 = "E_INPUT_SOURCE_HDMI2";
    public static final String FORCE_SOURCE_TYPEC = IS_T982_61 ? "E_INPUT_SOURCE_TYPEC1" : "E_INPUT_SOURCE_TYPEC2";
    public static final String SOURCE_ID_PC = "8";
    public static final String SOURCE_ID_HDMI1 = "5";
    public static final String SOURCE_ID_HDMI2 = "6";
    public static final String SOURCE_ID_TYPEC = IS_T982_61 ? "14" : "15";

    /**
     * 通道
     */
    public static final String SOURCE_TYPE_C1 = "TYPE_C1";
    public static final String SOURCE_TYPE_C2 = "TYPE_C2";
    public static final String SOURCE_TYPE_C = IS_T982_61 ? SOURCE_TYPE_C1 : SOURCE_TYPE_C2;

    /**
     * 通道插入自动跳转
     */
    public static final String PROP_AUTO_SWITCH_CHANNEL = "persist.sys.auto.switch.channel";
    public static final String PROP_AUTO_SWITCH_CHANNEL_DISABLE = "0";
    public static final String PROP_AUTO_SWITCH_CHANNEL_ENABLE = "1";
    public static final String PROP_AUTO_SWITCH_CHANNEL_ASK = "2"; //询问


    /**
     * 无线模块状态
     */
    public static final String PROP_WIFI_DEVICE_STATUS = "wifi.device.status";
    public static final String WIFI_DEVICE_STATUS_SUPPORTED = "supported";
    public static final String WIFI_DEVICE_STATUS_UNSUPPORTED = "unsupported";
}
