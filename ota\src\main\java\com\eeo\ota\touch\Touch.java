package com.eeo.ota.touch;

public abstract class Touch {

    /**
     * 解析config.json
     * 获取对应versionInfo
     */
    public abstract void parseVersionInfo();

    /**
     * 比较当前版本和ota整包配置信息中对应的版本
     */
    public abstract boolean checkVersion();

    public abstract void update();

    public abstract boolean isUpdating();

    public abstract void stopUpdate();

    public abstract void release();

    /**
     * 触摸框是否插入
     */
    public abstract boolean isPlugged();

    /**
     * 触摸框是否升级失败
     * 升级失败的需要重新升级才能正常使用
     */
    public abstract boolean isFirmwareUpdateFailed();


}
