<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_have_update_width"
    android:layout_height="@dimen/dialog_have_update_height"
    android:background="@drawable/shape_windows_host_bg">

    <ImageView
        android:id="@+id/img_wifi"
        android:layout_width="@dimen/dialog_factory_reset_iv_warn_width"
        android:layout_height="@dimen/dialog_factory_reset_iv_warn_width"
        android:layout_marginStart="@dimen/wifi_dialog_input_password_iv_wifi_margin_start"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_iv_wifi_margin_top"
        android:background="@drawable/set_ic_wifi_n" />

    <TextView
        android:id="@+id/txt_wifi_front_str"
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/img_wifi"
        android:layout_marginStart="@dimen/wifi_dialog_input_password_title_margin_start"
        android:text="@string/wifi_front_str"/>

    <TextView
        android:id="@+id/txt_wifi_name"
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/txt_wifi_front_str"
        android:ellipsize="end"
        android:maxWidth="@dimen/wifi_dialog_input_password_title_wifi_name_max_width"
        android:maxHeight="@dimen/wifi_dialog_input_password_title_wifi_name_max_height"
        android:singleLine="true"
        android:text="HDEW506"
        android:textColor="@color/black_100"
        android:textSize="@dimen/wifi_dialog_input_password_title_text_size" />

    <TextView
        android:id="@+id/txt_wifi_behind_str"
        style="@style/Wifi_Text_NAME"
        android:layout_toEndOf="@id/txt_wifi_name"
        android:text="@string/wifi_behind_str" />

    <EditText
        android:id="@+id/edt_password"
        android:layout_width="@dimen/wifi_dialog_input_password_et_password_width"
        android:layout_height="@dimen/wifi_dialog_input_password_et_password_height"
        android:layout_below="@id/txt_wifi_name"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_et_password_margin_top"
        android:background="@drawable/wifi_edt_password"
        android:hint="@string/password"
        android:inputType="textPassword"
        android:paddingStart="@dimen/wifi_dialog_input_password_et_password_padding_start"
        android:paddingEnd="@dimen/wifi_dialog_input_password_et_password_padding_end"
        android:textColor="@color/black_100"
        android:textColorHint="@color/line1"
        android:textCursorDrawable="@drawable/shape_network_edt_cursor"
        android:textSize="@dimen/wifi_dialog_input_password_et_password_text_size" />

    <ImageView
        android:id="@+id/img_eye"
        android:layout_width="@dimen/wifi_dialog_input_password_iv_eye_width"
        android:layout_height="@dimen/wifi_dialog_input_password_iv_eye_height"
        android:background="@drawable/ic_eye_off"
        android:layout_below="@id/txt_wifi_name"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_iv_eye_margin_top"
        android:layout_marginStart="@dimen/wifi_dialog_input_password_iv_eye_margin_start"/>

    <TextView
        android:id="@+id/txt_password_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/img_eye"
        android:textSize="@dimen/wifi_dialog_input_password_tv_password_error_text_size"
        android:textColor="@color/error_red"
        android:text="@string/password_error"
        android:layout_marginStart="@dimen/wifi_dialog_input_password_tv_password_error_margin_start"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_tv_password_error_margin_top"
        android:visibility="gone"/>

    <Button
        android:id="@+id/btn_cancel"
        style="@style/SystemSetting_PopWindow_Host_Btn"
        android:layout_below="@id/edt_password"
        android:layout_marginStart="@dimen/dialog_have_update_btn_cancel_margin_start"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_btn_margin_top"
        android:background="@drawable/shape_shutdown_btn_white"
        android:text="@string/cancel" />

    <Button
        android:id="@+id/btn_confirm"
        style="@style/SystemSetting_PopWindow_Host_Btn"
        android:layout_below="@id/edt_password"
        android:layout_marginStart="@dimen/dialog_have_update_btn_cancel_margin_start"
        android:layout_marginTop="@dimen/wifi_dialog_input_password_btn_margin_top"
        android:layout_toEndOf="@id/btn_cancel"
        android:background="@drawable/btn_un_click_green"
        android:text="@string/join"
        android:textColor="@color/white_100"
        android:enabled="false"/>

</RelativeLayout>