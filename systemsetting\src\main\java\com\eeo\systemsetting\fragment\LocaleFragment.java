package com.eeo.systemsetting.fragment;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Spinner;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.adapter.CustomSpinnerAdapter;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;

public class LocaleFragment extends BaseFragment {
    public static final String TAG = "LocaleFragment==";
    @BindView(R.id.spinner_language)
    Spinner mLanguageSpinner;
    String mLanguage;

    private boolean mSpinnerItemClickAble = true;
    private int mPosition;

    private static final int MSG_ENABLE_ITEM_CLICK = 0x001;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_ENABLE_ITEM_CLICK:
                    mSpinnerItemClickAble = true;
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public int getLayout() {
        return R.layout.fragment_locale;
    }

    @Override
    public void initDate() {
        mLanguage = Locale.getDefault().getLanguage();
        String[] languageArray = getResources().getStringArray(R.array.languages);
        List<String> languageList = new ArrayList<>(Arrays.asList(languageArray));
        CustomSpinnerAdapter adapter = new CustomSpinnerAdapter(getContext(), languageList);
        mLanguageSpinner.setAdapter(adapter);
        if (Constant.LANGUAGE_ZH.equals(mLanguage)) {
            mLanguageSpinner.setSelection(0);
        } else if (Constant.LANGUAGE_ENGLISH.equals(mLanguage)) {
            mLanguageSpinner.setSelection(1);
        }
        mLanguageSpinner.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    mSpinnerItemClickAble = false;
                    mHandler.removeMessages(MSG_ENABLE_ITEM_CLICK);
                    mHandler.sendEmptyMessageDelayed(MSG_ENABLE_ITEM_CLICK, 500);
                } else if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (!mSpinnerItemClickAble) {
                        return true;
                    }
                }
                return false;
            }
        });
        mLanguageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (!mSpinnerItemClickAble) {
                    mLanguageSpinner.setSelection(mPosition);
                    return;
                }
                adapter.setSelectedPosition(position);
                mPosition = position;
                if (position == 0 && !Constant.LANGUAGE_ZH.equals(mLanguage)) {
                    CommonUtils.setLanguage(Locale.CHINA);
                    mLanguage = Constant.LANGUAGE_ZH;
                } else if (position == 1 && !Constant.LANGUAGE_ENGLISH.equals(mLanguage)) {
                    CommonUtils.setLanguage(Locale.US);
                    mLanguage = Constant.LANGUAGE_ENGLISH;
                }
                //销毁实例，避免重启、关机弹窗语言未更新
                PowerUtil.destroy();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }


    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onStart: ");
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.i(TAG, "onStop: ");
    }
}
