2025-07-25 15:45:33.683  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-07-25 15:45:33.686  7781-7888  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-07-25 15:45:33.899  7781-7888  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 0
2025-07-25 15:45:35.902  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: EXECUTING_UPGRADE
2025-07-25 15:45:35.903  7781-7893  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery
2025-07-25 15:45:35.930  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: config new swupdate
2025-07-25 15:45:35.933  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_input: ##-i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery##
2025-07-25 15:45:36.459  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## set swupdate_param done ##
2025-07-25 15:45:36.564  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## Error: "swu_version" not defined
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_param: ##-i /mnt/UDISK/QH303_V197_20240712.swu##
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_software: ##stable##
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_mode: ##upgrade_recovery##
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###now do swupdate###
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###log in /mnt/UDISK/swupdate.log###
2025-07-25 15:45:36.567  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## swupdate -v  -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery ##
2025-07-25 15:45:40.667  7781-7895  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_next: ##reboot##
2025-07-25 15:45:41.272  7781-7893  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery] finished with exit code: 0
2025-07-25 15:45:41.275  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_DISCONNECT
2025-07-25 15:45:41.292  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-25 15:45:41.293  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_CONNECT
2025-07-25 15:46:11.481  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Device reconnected after reboot.
2025-07-25 15:46:16.485  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: FINAL_VERSION_VALIDATION
2025-07-25 15:46:16.518  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update successful! New version: A013
2025-07-25 15:46:16.518  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: Update success.
2025-07-25 15:46:16.519  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:46:16.519  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:46:16.520  7781-7924  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 128174120,0,0.
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:46:17.546  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:46:17.547  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:46:17.548  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:46:17.548  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:46:17.548  7781-7926  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:46:17.548  7781-7924  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:46:27.608  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:46:27.608  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:46:27.608  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:46:27.609  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:46:27.610  7781-7934  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 70203432,0,0.
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:46:28.637  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:46:28.638  7781-7936  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:46:28.639  7781-7934  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:46:38.700  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:46:38.700  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:46:38.701  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:46:38.701  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:46:38.702  7781-7944  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:46:39.726  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 259311656,0,0.
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:46:39.727  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:46:39.729  7781-7944  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:46:39.731  7781-7946  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:46:49.786  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:46:49.787  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:46:49.787  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:46:49.787  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:46:49.788  7781-7954  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 271439912,0,0.
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:46:50.819  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:46:50.820  7781-7956  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:46:50.822  7781-7954  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:47:00.878  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:47:00.878  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:47:00.878  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:47:00.878  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:47:00.880  7781-7966  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:47:01.907  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:47:01.907  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:47:01.908  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 66283560,0,0.
2025-07-25 15:47:01.908  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:47:01.908  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:47:01.908  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:47:01.909  7781-7966  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:47:01.909  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:47:01.909  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:47:01.910  7781-7968  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:47:11.971  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:47:11.971  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:47:11.971  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:47:11.971  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:47:11.976  7781-7976  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 116668456,0,0.
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:47:13.001  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:47:13.002  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:47:13.002  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:47:13.002  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:47:13.002  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:47:13.003  7781-7978  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:47:13.004  7781-7976  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:47:23.070  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:47:23.070  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:47:23.071  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:47:23.071  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:47:23.072  7781-7988  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 103344168,0,0.
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:47:24.097  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:47:24.099  7781-7988  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:47:24.099  7781-7990  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:47:34.164  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:47:34.164  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:47:34.165  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:47:34.165  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:47:34.166  7781-8000  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:47:35.196  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:47:35.196  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:47:35.196  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 98961448,0,0.
2025-07-25 15:47:35.198  7781-8000  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:47:35.199  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:47:35.200  7781-8002  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:47:45.266  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Operation timed out in state: CLEANUP
2025-07-25 15:47:45.267  7781-7781  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Operation timed out in state: CLEANUP
2025-07-25 15:47:45.267  7781-7781  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:47:45.267  7781-7781  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:47:45.268  7781-8014  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 80623656,0,0.
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 15:47:46.296  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 15:47:46.297  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 15:47:46.297  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 15:47:46.297  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 15:47:46.297  7781-8016  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 15:47:46.300  7781-8014  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0