[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: armeabi-v7a", "file_": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -630855243}, {"level_": 0, "message_": "JSON 'D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx\\Debug\\v25325a2\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1739799027}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 295751103}]