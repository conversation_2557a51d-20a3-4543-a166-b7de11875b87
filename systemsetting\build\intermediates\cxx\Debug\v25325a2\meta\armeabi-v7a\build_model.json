{"abi": "ARMEABI_V7A", "info": {"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx\\Debug\\v25325a2\\armeabi-v7a", "soFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx\\Debug\\v25325a2\\obj\\armeabi-v7a", "soRepublishFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cmake\\debug\\obj\\armeabi-v7a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared"], "cFlagsList": [], "cppFlagsList": ["-std=c++14"], "variantName": "debug", "soFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx\\Debug\\v25325a2\\obj", "soRepublishFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cmake\\debug\\obj", "cxxBuildFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx\\Debug\\v25325a2", "intermediatesFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx\\Debug\\v25325a2", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx", "intermediatesBaseFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates", "intermediatesFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx", "gradleModulePathName": ":systemsetting", "moduleRootFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting", "moduleBuildFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build.gradle", "makeFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529", "ndkVersion": "21.4.7075529", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 30, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "D:\\software\\Android\\Android_SDK\\ndk\\cmake\\3.10.2.4988404\\bin\\cmake.exe", "minimumCmakeVersion": "3.10.2", "ninjaExe": "D:\\software\\Android\\Android_SDK\\ndk\\cmake\\3.10.2.4988404\\bin\\ninja.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0", "sdkFolder": "D:\\software\\Android\\Android_SDK\\ndk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": []}, "prefabClassPathFileCollection": [], "prefabPackageDirectoryListFileCollection": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx\\Debug\\v25325a2\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "v25325a2p3l1l1b03o4m2h2w726v4p106m55e6a71q4m44q3l2g01l6t29", "configurationArguments": ["-HD:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529", "-DCMAKE_ANDROID_NDK=D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529", "-DCMAKE_TOOLCHAIN_FILE=D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\software\\Android\\Android_SDK\\ndk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-std=c++14", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx\\Debug\\v25325a2\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\cxx\\Debug\\v25325a2\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\.cxx\\Debug\\v25325a2\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared"], "stlLibraryFile": "D:\\software\\Android\\Android_SDK\\ndk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so"}