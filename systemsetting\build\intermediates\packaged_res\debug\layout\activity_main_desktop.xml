<?xml version="1.0" encoding="utf-8"?>
<com.zyp.cardview.YcCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/frame"
    style="@style/Main_YcCardView"
    android:layout_width="@dimen/main_cv_width"
    android:layout_height="@dimen/main_cv_height"
    tools:context=".activity.MainActivity">

    <RelativeLayout
        android:id="@+id/rlBg"
        android:layout_width="@dimen/main_width"
        android:layout_height="@dimen/main_height"
        android:layout_alignParentBottom="true">

        <TextView
            android:id="@+id/txt_title"
            style="@style/Title"
            android:layout_marginTop="@dimen/main_tv_title_margin_top"
            android:text="@string/screen_title" />

        <com.zyp.cardview.YcCardView
            android:id="@+id/cv_screen"
            style="@style/Main_YcCardView"
            android:layout_width="@dimen/sl_screen_width"
            android:layout_height="@dimen/sl_screen_height"
            android:layout_alignParentStart="true"
            android:layout_marginStart="@dimen/sl_screen_margin_start"
            android:layout_marginTop="@dimen/sl_screen_margin_top"
            app:ycCardBackgroundColor="@color/white_100">

            <ImageView
                android:id="@+id/iv_screen"
                style="@style/Main_ImageView"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/iv_screen_margin_top"
                android:src="@drawable/select_main_screen_icon" />

            <TextView
                android:id="@+id/txt_screen"
                style="@style/Main_ShadowLayout_Circle_Text"
                android:layout_marginTop="@dimen/tv_screen_margin_top"
                android:gravity="center_horizontal"
                android:text="@string/screen_text" />

        </com.zyp.cardview.YcCardView>

        <com.zyp.cardview.YcCardView
            android:id="@+id/cv_write"
            style="@style/Main_YcCardView"
            android:layout_width="@dimen/sl_screen_width"
            android:layout_height="@dimen/sl_screen_height"
            android:layout_below="@id/cv_screen"
            android:layout_alignParentStart="true"
            android:layout_marginStart="@dimen/sl_screen_margin_start"
            android:layout_marginTop="@dimen/sl_write_margin_top"
            app:ycCardBackgroundColor="@color/white_100">

            <ImageView
                android:id="@+id/iv_write"
                style="@style/Main_ImageView"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/iv_screen_margin_top"
                android:src="@drawable/select_main_write_icon" />

            <TextView
                android:id="@+id/txt_write"
                style="@style/Main_ShadowLayout_Circle_Text"
                android:layout_marginTop="@dimen/tv_screen_margin_top"
                android:gravity="center_horizontal"
                android:text="@string/write_text" />
        </com.zyp.cardview.YcCardView>

        <com.zyp.cardview.YcCardView
            android:id="@+id/cv_vg_setting"
            style="@style/Main_YcCardView"
            android:layout_width="@dimen/sl_setting_width"
            android:layout_height="@dimen/sl_screen_height"
            android:layout_alignTop="@id/cv_screen"
            android:layout_marginStart="@dimen/sl_setting_margin_start"
            android:layout_toEndOf="@id/cv_screen"
            app:ycCardBackgroundColor="@color/white_100">

            <FrameLayout
                android:id="@+id/fl_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/tv_setting_margin_start"
                android:layout_marginTop="@dimen/iv_screen_margin_top">

                <ImageView
                    android:id="@+id/iv_setting"
                    style="@style/Main_ImageView"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/select_main_setting_icon" />

                <TextView
                    android:id="@+id/txt_setting"
                    style="@style/Main_ShadowLayout_Circle_Text"
                    android:layout_marginTop="@dimen/tv_setting_margin_top"
                    android:gravity="center_horizontal"
                    android:text="@string/setting" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_signal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/tv_signal_margin_start"
                android:layout_marginTop="@dimen/iv_screen_margin_top">

                <ImageView
                    android:id="@+id/iv_signal"
                    style="@style/Main_ImageView"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/select_main_sign_icon" />

                <TextView
                    android:id="@+id/txt_signal"
                    style="@style/Main_ShadowLayout_Circle_Text"
                    android:layout_marginTop="@dimen/tv_setting_margin_top"
                    android:gravity="center_horizontal"
                    android:text="@string/sign" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_touch_lock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/tv_touch_lock_margin_start"
                android:layout_marginTop="@dimen/iv_screen_margin_top">

                <ImageView
                    android:id="@+id/iv_touch_lock"
                    style="@style/Main_ImageView"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/select_main_lock_icon" />

                <TextView
                    android:id="@+id/txt_touch_lock"
                    style="@style/Main_ShadowLayout_Circle_Text"
                    android:layout_marginTop="@dimen/tv_setting_margin_top"
                    android:gravity="center_horizontal"
                    android:text="@string/lock" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_eye"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/tv_eye_margin_start"
                android:layout_marginTop="@dimen/iv_screen_margin_top">

                <ImageView
                    android:id="@+id/iv_eye"
                    style="@style/Main_ImageView"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/select_main_eye_icon" />

                <TextView
                    android:id="@+id/txt_eye"
                    style="@style/Main_ShadowLayout_Circle_Text"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/tv_setting_margin_top"
                    android:gravity="center_horizontal"
                    android:text="@string/eye" />
            </FrameLayout>
        </com.zyp.cardview.YcCardView>

        <com.zyp.cardview.YcCardView
            android:id="@+id/cv_bright"
            style="@style/Main_YcCardView"
            android:layout_width="@dimen/sl_setting_width"
            android:layout_height="@dimen/sl_screen_height"
            android:layout_alignStart="@id/cv_vg_setting"
            android:layout_alignTop="@id/cv_write"
            app:ycCardBackgroundColor="@color/white_100">

            <ImageView
                android:id="@+id/img_voice"
                android:layout_width="@dimen/iv_bright_width"
                android:layout_height="@dimen/iv_bright_height"
                android:layout_marginStart="@dimen/iv_bright_margin_start"
                android:layout_marginTop="@dimen/iv_voice_margin_top"
                android:background="@drawable/ic_voice_03" />

            <com.eeo.systemsetting.view.CustomerSeekBar
                android:id="@+id/sb_volume"
                style="@style/SeekBar_Light"
                android:layout_marginStart="@dimen/sb_bright_margin_start"
                android:layout_marginTop="@dimen/sb_voice_margin_top"
                android:progress="60" />

            <ImageView
                android:id="@+id/img_bright"
                android:layout_width="@dimen/iv_bright_width"
                android:layout_height="@dimen/iv_bright_height"
                android:layout_marginStart="@dimen/iv_bright_margin_start"
                android:layout_marginTop="@dimen/iv_bright_margin_top"
                android:background="@drawable/ic_bright_01" />

            <com.eeo.systemsetting.view.CustomerSeekBar
                android:id="@+id/sb_bright"
                style="@style/SeekBar_Light"
                android:layout_height="@dimen/sb_bright_height"
                android:layout_marginStart="@dimen/sb_bright_margin_start"
                android:layout_marginTop="@dimen/sb_bright_margin_top"
                android:progress="40" />
        </com.zyp.cardview.YcCardView>

        <FrameLayout
            android:id="@+id/fl_desktop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/tv_desktop_margin_start"
            android:layout_marginBottom="@dimen/tv_desktop_margin_bottom"
            android:paddingVertical="@dimen/fl_desktop_padding_vertical">

            <ImageView
                android:id="@+id/iv_desktop"
                style="@style/Main_ImageView"
                android:src="@drawable/select_main_desktop_icon" />

            <TextView
                android:id="@+id/tv_desktop"
                style="@style/Main_Shutdown_TextView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/tv_setting_margin_top"
                android:text="@string/desktop" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_rest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/tv_rest_margin_desktop"
            android:layout_marginBottom="@dimen/tv_desktop_margin_bottom"
            android:layout_toEndOf="@id/fl_desktop"
            android:paddingVertical="@dimen/fl_desktop_padding_vertical">

            <ImageView
                android:id="@+id/iv_rest"
                style="@style/Main_ImageView"
                android:src="@drawable/select_main_reset_icon" />

            <TextView
                android:id="@+id/tv_rest"
                style="@style/Main_Shutdown_TextView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/tv_setting_margin_top"
                android:text="@string/resting" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_restart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/tv_rest_margin_desktop"
            android:layout_marginBottom="@dimen/tv_desktop_margin_bottom"
            android:layout_toEndOf="@id/fl_rest"
            android:paddingVertical="@dimen/fl_desktop_padding_vertical">

            <ImageView
                android:id="@+id/iv_restart"
                style="@style/Main_ImageView"
                android:src="@drawable/select_main_restart_icon" />

            <TextView
                android:id="@+id/tv_restart"
                style="@style/Main_Shutdown_TextView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/tv_setting_margin_top"
                android:text="@string/restart" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_shutdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/tv_rest_margin_desktop"
            android:layout_marginBottom="@dimen/tv_desktop_margin_bottom"
            android:layout_toEndOf="@id/fl_restart"
            android:paddingVertical="@dimen/fl_desktop_padding_vertical">

            <ImageView
                android:id="@+id/iv_shutdown"
                style="@style/Main_ImageView"
                android:src="@drawable/select_main_shutdown_icon" />

            <TextView
                android:id="@+id/tv_shutdown"
                style="@style/Main_Shutdown_TextView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/tv_setting_margin_top"
                android:text="@string/shutdown" />
        </FrameLayout>

        <View
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/main_line1_height"
            android:layout_below="@id/cv_write"
            android:layout_marginTop="@dimen/main_line1_margin_top"
            android:background="@color/line1" />
    </RelativeLayout>
</com.zyp.cardview.YcCardView>
