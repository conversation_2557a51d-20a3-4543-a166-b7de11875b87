package com.eeo.systemsetting.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.Switch;

import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.adapter.CustomSpinnerAdapter;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.udisdk.PcKeyboardCode;
import com.eeo.udisdk.UdiConstant;
import com.eeo.systemsetting.view.HardwareSelfTestFrameLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;

public class ExtraFragment extends BaseFragment {
    public static final String TAG = "ExtraFragment==";
    @BindView(R.id.sw_write_without_screen_on)
    Switch mWriteWithoutScreenOnSwitch;
    @BindView(R.id.sw_breath_led_on)
    Switch mBreathLedOnSwitch;
    @BindView(R.id.rl_touch_slider)
    RelativeLayout mTouchSliderLayout;
    @BindView(R.id.sw_touch_slider)
    Switch mTouchSliderSwitch;

    @BindView(R.id.sw_windows_disable)
    Switch mWindowsDisableSwitch;
    @BindView(R.id.spinner_startup_channel)
    Spinner mStartChannelSpinner;

    @BindView(R.id.rl_hardware_self_test)
    RelativeLayout mHardwareSelfTestRl;
    @BindView(R.id.rl_windows_task_manager)
    RelativeLayout mWindowsTaskManagerRl;
    private boolean mIsWindowsDisable;
    String mStartChannel;

    private CustomSpinnerAdapter mAdapter;

    private boolean mSpinnerItemClickAble = true;
    private int mPosition;
    /**
     * 开机通道列表各通道list对应的位置
     * 禁用掉内置电脑通道后，对应位置回变化
     */
    private int mLastSourcePosition = 0;
    private int mWindowsPosition = 1;
    private int mTypeCPosition = 2;
    private int mHdmi1Postion = 3;
    private int mHdmi2Position = 4;

    private WindowManager mWindowManager;
    private View mHardwareSelfTestView;

    private static final int MSG_ENABLE_ITEM_CLICK = 0x001;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_ENABLE_ITEM_CLICK:
                    mSpinnerItemClickAble = true;
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public int getLayout() {
        return R.layout.fragment_extra;
    }

    @Override
    public void initDate() {
        mWriteWithoutScreenOnSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SaveDateUtils.enableWriteWithoutScreenOn(getContext(), isChecked);
            }
        });
        mWriteWithoutScreenOnSwitch.setChecked(SaveDateUtils.isWriteWithoutScreenOnEnabled(getContext()));

        mBreathLedOnSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SaveDateUtils.setBreathLedOn(getContext(), isChecked);
                SystemControlManager.getInstance().setBreathLedStatus(isChecked ? 4 : 5); //1：唤醒  2：休眠 3：关机或重启 4:开启常亮 5：关闭常亮
            }
        });
        mBreathLedOnSwitch.setChecked(SaveDateUtils.getBreathLedOn(getContext()));

        if (Constant.HAS_TOUCH_SLIDER) {
            mTouchSliderLayout.setVisibility(View.VISIBLE);
            mTouchSliderSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    SaveDateUtils.enableTouchSlider(getContext(), isChecked);
                }
            });
            mTouchSliderSwitch.setChecked(SaveDateUtils.isTouchSliderEnabled(getContext()));
        } else {
            mTouchSliderLayout.setVisibility(View.GONE);
        }

        mWindowsDisableSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (mIsWindowsDisable != isChecked) {
                    mIsWindowsDisable = isChecked;
                    SystemControlManager.getInstance().disableOpsHdmi(isChecked ? 1 : 0);
                    if (UdiConstant.SOURCE_PC.equals(EeoApplication.mCurrentSource)) {
                        if (isChecked) {
                            CommonUtils.sendBroadcastToTuneTvView(getContext());
                        } else {
                            //切一下其它信号源再切回来，才能快速显示ops画面
                            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
                            buttonView.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);
                                }
                            }, 500);
                        }
                    }
                    //刷新开机通道
                    initStartChannel();
                }
            }
        });
        mIsWindowsDisable = CommonUtils.isOpsDisable();
        mWindowsDisableSwitch.setChecked(mIsWindowsDisable);

        //开机通道
        initStartChannel();

        mStartChannelSpinner.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    mSpinnerItemClickAble = false;
                    mHandler.removeMessages(MSG_ENABLE_ITEM_CLICK);
                    mHandler.sendEmptyMessageDelayed(MSG_ENABLE_ITEM_CLICK, 500);
                } else if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (!mSpinnerItemClickAble) {
                        return true;
                    }
                }
                return false;
            }
        });

        mStartChannelSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (!mSpinnerItemClickAble) {
                    mStartChannelSpinner.setSelection(mPosition);
                    return;
                }
                mAdapter.setSelectedPosition(position, true);
                mPosition = position;
                if (position == mLastSourcePosition && !Constant.FORCE_SOURCE_NONE.equals(mStartChannel)) {
                    SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_NONE);
                    mStartChannel = Constant.FORCE_SOURCE_NONE;
                } else if (position == mWindowsPosition && !Constant.FORCE_SOURCE_PC.equals(mStartChannel)) {
                    SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_PC);
                    mStartChannel = Constant.FORCE_SOURCE_PC;
                } else if (position == mTypeCPosition && !Constant.FORCE_SOURCE_TYPEC.equals(mStartChannel)) {
                    SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_TYPEC);
                    mStartChannel = Constant.FORCE_SOURCE_TYPEC;
                } else if (position == mHdmi1Postion && !Constant.FORCE_SOURCE_HDMI1.equals(mStartChannel)) {
                    SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_HDMI1);
                    mStartChannel = Constant.FORCE_SOURCE_HDMI1;
                } else if (position == mHdmi2Position && !Constant.FORCE_SOURCE_HDMI2.equals(mStartChannel)) {
                    SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_HDMI2);
                    mStartChannel = Constant.FORCE_SOURCE_HDMI2;
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        mHardwareSelfTestRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "mHardwareSelfTestRl onClick: ");
                CommonUtils.sendExitBroadcast(getContext());
                showSelfTestDialog();
            }
        });

        mWindowsTaskManagerRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //Ctrl+Shift+Esc快捷键启动任务管理器
                EeoApplication.udi.sendPcKeyEvent(PcKeyboardCode.CONTROL_KEY_BOARD_CTRL | PcKeyboardCode.CONTROL_KEY_BOARD_SHIFT,
                        PcKeyboardCode.FUNCTION_KEY_BOARD_ESC, PcKeyboardCode.EVENT_KEY_BOARD_CLICK);
                if (getActivity() != null) {
                    getActivity().finish();
                }
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onStart: ");
    }

    @Override
    public void onResume() {
        super.onResume();
        if (CommonUtils.isOpsInserted() && UdiConstant.SOURCE_PC.equals(EeoApplication.mCurrentSource)) {
            mWindowsTaskManagerRl.setVisibility(View.VISIBLE);
        } else {
            mWindowsTaskManagerRl.setVisibility(View.GONE);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.i(TAG, "onStop: ");
    }

    private void showSelfTestDialog() {
        if (mHardwareSelfTestView == null) {
            mHardwareSelfTestView = new HardwareSelfTestFrameLayout(getContext());
            mHardwareSelfTestView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {

                    if (motionEvent.getAction() == MotionEvent.ACTION_OUTSIDE) {
                        Log.i(TAG, "outside");
                        dismissSelfTestDialog();
                    }
                    return false;
                }
            });
        }
        if (!mHardwareSelfTestView.isAttachedToWindow()) {
            if (mWindowManager == null) {
                mWindowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
            }
            WindowManager.LayoutParams viewParam = new WindowManager.LayoutParams();
            viewParam.width = CommonUtils.dp2px(getContext(), Constant.DIALOG_WIDTH_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.height = CommonUtils.dp2px(getContext(), Constant.DIALOG_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.gravity = Gravity.END | Gravity.BOTTOM;
            viewParam.x = CommonUtils.dp2px(getContext(), Constant.DIALOG_MARIN_END_IN_DP);
            viewParam.y = CommonUtils.dp2px(getContext(), Constant.DIALOG_MARIN_BOTTOM_IN_DP);
            viewParam.format = PixelFormat.RGBA_8888;
            viewParam.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            viewParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
            mWindowManager.addView(mHardwareSelfTestView, viewParam);
        }
    }

    private void dismissSelfTestDialog() {
        if (mWindowManager != null && mHardwareSelfTestView != null && mHardwareSelfTestView.isAttachedToWindow()) {
            mWindowManager.removeView(mHardwareSelfTestView);
            mHardwareSelfTestView = null;
        }
    }

    private void initStartChannel() {
        mStartChannel = CommonUtils.getBootForceSource(getContext());
        if (mIsWindowsDisable && Constant.FORCE_SOURCE_PC.equals(mStartChannel)) {
            //内置电脑通道禁用后切换为HDMI1
            SaveDateUtils.setBootForceSource(getContext(), Constant.FORCE_SOURCE_HDMI1);
            mStartChannel = Constant.FORCE_SOURCE_HDMI1;
        }
        String[] startChannelArray = getResources().getStringArray(mIsWindowsDisable ? R.array.startup_channels_windows_disabled : R.array.startup_channels);
        List<String> startChannelList = new ArrayList<>(Arrays.asList(startChannelArray));
        mAdapter = new CustomSpinnerAdapter(getContext(), startChannelList);
        mStartChannelSpinner.setAdapter(mAdapter);
        if (mIsWindowsDisable) {
            mLastSourcePosition = 0;
            mWindowsPosition = -1;
            mTypeCPosition = 1;
            mHdmi1Postion = 2;
            mHdmi2Position = 3;
        } else {
            mLastSourcePosition = 0;
            mWindowsPosition = 1;
            mTypeCPosition = 2;
            mHdmi1Postion = 3;
            mHdmi2Position = 4;
        }
        if (Constant.FORCE_SOURCE_NONE.equals(mStartChannel)) {
            mStartChannelSpinner.setSelection(mLastSourcePosition);
        } else if (Constant.FORCE_SOURCE_PC.equals(mStartChannel)) {
            mStartChannelSpinner.setSelection(mWindowsPosition);
        } else if (Constant.FORCE_SOURCE_TYPEC.equals(mStartChannel)) {
            mStartChannelSpinner.setSelection(mTypeCPosition);
        } else if (Constant.FORCE_SOURCE_HDMI1.equals(mStartChannel)) {
            mStartChannelSpinner.setSelection(mHdmi1Postion);
        } else if (Constant.FORCE_SOURCE_HDMI2.equals(mStartChannel)) {
            mStartChannelSpinner.setSelection(mHdmi2Position);
        }
    }

    @Override
    public Context getContext() {
        Context context = super.getContext();
        return context == null ? EeoApplication.getApplication() : context;
    }
}
