package com.eeo.WhiteboardAccelerate;

import android.os.Parcel;
import android.os.Parcelable;

public class DrawAction implements Parcelable {
    public int type;
    public float x;
    public float y;
    public float controlPointX;
    public float controlPointY;
    public float width;
    public float height;
    public float startAngle;
    public float arcLength;

    public DrawAction() {}

    public DrawAction(int type,float x, float y) {
        this.type = type;
        this.x = x;
        this.y = y;
    }
    public DrawAction(int type,float x, float y,float controlPointX,float controlPointY) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.controlPointX = controlPointX;
        this.controlPointY = controlPointY;
    }
    public DrawAction(float width, float height) {
        this.width = width;
        this.height = height;
    }

    public DrawAction(int type,float x, float y,float controlPointX,float controlPointY,float width,
                      float height,float startAngle, float arcLength) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.controlPointX = controlPointX;
        this.controlPointY = controlPointY;
        this.width = width;
        this.height = height;
        this.startAngle = startAngle;
        this.arcLength = arcLength;
    }
    /**
     * Set the point's x and y coordinates
     */
    public final void set(int type,float x, float y) {
        this.type = type;
        this.x = x;
        this.y = y;
    }
    public final void set(int type,float x, float y,float controlPointX,float controlPointY) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.controlPointX = controlPointX;
        this.controlPointY = controlPointY;
    }
    public final void set(float width, float height) {
        this.width = width;
        this.height = height;
    }

    public final void set(int type,float x, float y,float controlPointX,float controlPointY,float width,
                          float height,float startAngle, float arcLength) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.controlPointX = controlPointX;
        this.controlPointY = controlPointY;
        this.width = width;
        this.height = height;
        this.startAngle = startAngle;
        this.arcLength = arcLength;
    }

    /**
     * Set the point's x and y coordinates to the coordinates of p
     */
    public final void set(DrawAction p) {
        this.type = p.type;
        this.x = p.x;
        this.y = p.y;
        this.controlPointX = p.controlPointX;
        this.controlPointY = p.controlPointY;
        this.width = p.width;
        this.height = p.height;
        this.startAngle = p.startAngle;
        this.arcLength = p.arcLength;
    }

    /**
     * Returns true if the point's coordinates equal (x,y)
     */
    public final boolean equals(float x, float y) {
        return this.x == x && this.y == y;
    }

    @Override
    public String toString() {
        return "PointF(" + x + ", " + y + ")";
    }


    /**
     * Parcelable interface methods
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write this point to the specified parcel. To restore a point from
     * a parcel, use readFromParcel()
     * @param out The parcel to write the point's coordinates into
     */
    @Override
    public void writeToParcel(Parcel out, int flags) {
        out.writeInt(type);
        out.writeFloat(x);
        out.writeFloat(y);
        out.writeFloat(controlPointX);
        out.writeFloat(controlPointY);
        out.writeFloat(width);
        out.writeFloat(height);
        out.writeFloat(startAngle);
        out.writeFloat(arcLength);
    }

    public static final Creator<DrawAction> CREATOR = new Creator<DrawAction>() {
        /**
         * Return a new point from the data in the specified parcel.
         */
        @Override
        public DrawAction createFromParcel(Parcel in) {
            DrawAction r = new DrawAction();
            r.readFromParcel(in);
            return r;
        }

        /**
         * Return an array of rectangles of the specified size.
         */
        @Override
        public DrawAction[] newArray(int size) {
            return new DrawAction[size];
        }
    };

    /**
     * Set the point's coordinates from the data stored in the specified
     * parcel. To write a point to a parcel, call writeToParcel().
     *
     * @param in The parcel to read the point's coordinates from
     */
    public void readFromParcel(Parcel in) {
        type = in.readInt();
        x = in.readFloat();
        y = in.readFloat();
        controlPointX = in.readFloat();
        controlPointY = in.readFloat();
        width = in.readFloat();
        height = in.readFloat();
        startAngle = in.readFloat();
        arcLength = in.readFloat();
    }
}
