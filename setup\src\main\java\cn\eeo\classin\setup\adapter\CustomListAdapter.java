package cn.eeo.classin.setup.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import java.util.List;

import cn.eeo.classin.setup.R;

public class CustomListAdapter extends BaseAdapter {
    private Context context;
    private List<String> dataList;

    public CustomListAdapter(Context context, List<String> dataList) {
        this.context = context;
        this.dataList = dataList;
    }

    @Override
    public int getCount() {
        return dataList.size();
    }

    @Override
    public Object getItem(int position) {
        return dataList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.custom_spinner_item, parent, false);
        }
        TextView textView = convertView.findViewById(R.id.list_item_text);
        textView.setText(dataList.get(position));
        textView.setTextSize(8);
        textView.setTextColor(Color.WHITE);
        textView.setAlpha(0.7F);

        return convertView;
    }
    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            LayoutInflater inflater = LayoutInflater.from(context);
            convertView = inflater.inflate(R.layout.custom_spinner_item, parent, false);
        }

        TextView textView =convertView.findViewById(R.id.list_item_text);
        // 填充下拉列表视图中的数据，可以与 getView 中的数据不同
        if (position ==0){
            textView.setText(dataList.get(position));
            textView.setTextSize(8);
            textView.setTextColor(Color.WHITE);
            textView.setAlpha(0.7F);
        }else {
            textView.setText(dataList.get(position));
            textView.setTextSize(9);
            textView.setTextColor(Color.WHITE);
            textView.setAlpha(1.0F);
        }
        return convertView;
    }
}
