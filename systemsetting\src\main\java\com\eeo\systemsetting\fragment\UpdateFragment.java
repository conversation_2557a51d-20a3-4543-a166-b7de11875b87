package com.eeo.systemsetting.fragment;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.eeo.ota.bean.VersionInfo;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.callback.ServiceCallback;
import com.eeo.systemsetting.service.SystemSettingService;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;


import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;

public class UpdateFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener, ServiceCallback {
    public static final String TAG = "UpdateFragment====";
    @BindView(R.id.sw_network)
    Switch switchAutoUpdate;
    @BindView(R.id.txt_version_msg)
    TextView txtVersionMsg;
    @BindView(R.id.ll_not_network)
    LinearLayout llNotNetwork;
    @BindView(R.id.ll_check_update)
    LinearLayout llCheckUpdate;
    @BindView(R.id.cl_right_update)
    ConstraintLayout clRightUpdate;
    @BindView(R.id.txt_right_update)
    TextView txtRightUpdate;
    @BindView(R.id.txt_title_update_description)
    TextView txtUpdateDescriptionTitle;
    @BindView(R.id.txt_update_description)
    TextView txtUpdateDescription;
    @BindView(R.id.progressbar_check_update)
    ProgressBar progressbarCheckUpdate;
    @BindView(R.id.ll_download)
    LinearLayout llDownload;
    @BindView(R.id.progress_download)
    ProgressBar progressDownload;
    @BindView(R.id.btn_cancel)
    Button btnCancel;
    @BindView(R.id.ll_install)
    LinearLayout llInstall;
    @BindView(R.id.btn_install)
    Button btnInstall;
    @BindView(R.id.txt_no_network)
    TextView txtNoNetwork;

    private final static int TIME_3000 = 3000;
    private static final int CHECK_UPDATE = 1;

    //1001:没有网络
    public static final int ErrorCode_1001 = 1001;
    //1002：未发现新版本
    public static final int ErrorCode_1002 = 1002;
    //1003:连接服务器失败，请检查网络后重试
    public static final int ErrorCode_1003 = 1003;
    //1004:动态注册失败
    public static final int ErrorCode_1004 = 1004;
    //上报版本超时
    public static final int ErrorCode_1005 = 1005;

    //未发现最新版本
    public static final int NOT_NEW_VERSION = -6;
    //没有网络
    public static final int NOT_NETWORK = -7;

    private SystemSettingService mSystemSettingService;
    private NetworkChangedReceiver mNetworkChangedReceiver;
    private boolean mIsFirstNetworkChanged = true;
    private Handler mHandler = new Handler();

    @Override
    public int getLayout() {
        return R.layout.fragment_update;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mSystemSettingService == null) {
            mSystemSettingService = EeoApplication.getApplication().getSystemSettingService();
        }
        mSystemSettingService.addServiceCallBack(this);
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void initDate() {
        String language = Locale.getDefault().getLanguage();
        if (language.equals(Constant.LANGUAGE_ZH)) {
            txtNoNetwork.setGravity(Gravity.CENTER);
        }
        //是否自动更新
        Boolean isAutoUpdate = SharedPreferencesUtil.getIsAutoUpdate(getContext());
        switchAutoUpdate.setChecked(isAutoUpdate);

        switchAutoUpdate.setOnCheckedChangeListener(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.i(TAG, "onResume: ");
        initReceiver();
        //判断是否有网络
        if (!CommonUtils.isNetSystemUsable(getContext())) {
            Log.i(TAG, "onResume: not network");
            //没有网络情况下，隐藏所有
            setAllViewToInView();
            llNotNetwork.setVisibility(View.VISIBLE);
            return;
        }
        Log.i(TAG, "onResume: errorCode " + mSystemSettingService.getErrorCode());
        if (mSystemSettingService.getErrorCode() == ErrorCode_1001
                || mSystemSettingService.getErrorCode() == ErrorCode_1002
                || mSystemSettingService.getErrorCode() == ErrorCode_1003
                || mSystemSettingService.getErrorCode() == ErrorCode_1004
        ) {
            if (clRightUpdate.getVisibility() == View.VISIBLE) {
                clRightUpdate.setVisibility(View.GONE);
            }
            setAllViewToInView();
            llCheckUpdate.setVisibility(View.VISIBLE);
            if (switchAutoUpdate.isChecked()) {
                mSystemSettingService.checkUpdate(true);
            } else {
                mSystemSettingService.checkUpdate(false);
            }
            return;
        }

        //如果下载更新包已经下载完成，则直接显示安装按钮
        if (mSystemSettingService.isDownloaded()) {
            Log.i(TAG, "onResume: isDownloaded");
            if (clRightUpdate.getVisibility() == View.VISIBLE) {
                clRightUpdate.setVisibility(View.GONE);
            }
            setAllViewToInView();
            llInstall.setVisibility(View.VISIBLE);
            return;
        }

        //判断是否正在下载，如果正在下载则显示下载进度条
        if (mSystemSettingService.isDownloading()) {
            if (clRightUpdate.getVisibility() == View.VISIBLE) {
                clRightUpdate.setVisibility(View.GONE);
            }
            setAllViewToInView();
            llDownload.setVisibility(View.VISIBLE);
            return;

        }

        //自己再检测一遍更新
        if (mSystemSettingService.getErrorCode() == 0) {
            if (clRightUpdate.getVisibility() == View.VISIBLE) {
                clRightUpdate.setVisibility(View.GONE);
            }
            setAllViewToInView();
            llCheckUpdate.setVisibility(View.VISIBLE);
            if (switchAutoUpdate.isChecked()) {
                //自动更新打开，则检测完直接更新
                mSystemSettingService.checkUpdate(true);
            } else {
                mSystemSettingService.checkUpdate(false);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mNetworkChangedReceiver != null) {
            getContext().unregisterReceiver(mNetworkChangedReceiver);
            mNetworkChangedReceiver = null;
        }
    }

    /**
     * 注册网络监听的广播
     */
    private void initReceiver() {
        mIsFirstNetworkChanged = true;
        mNetworkChangedReceiver = new NetworkChangedReceiver();
        IntentFilter timeFilter = new IntentFilter();
        timeFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        getContext().registerReceiver(mNetworkChangedReceiver, timeFilter);
    }

    private void showUpdateVersionInfo(VersionInfo versionInfo) {
        setAllViewToInView();
        clRightUpdate.setVisibility(View.VISIBLE);
        //显示版本号
        String language = Locale.getDefault().getLanguage();
        if (language.equals(Constant.LANGUAGE_ZH)) {
            txtRightUpdate.setText(getString(R.string.updatable) + versionInfo.versionName);
            //更新说明
            if (TextUtils.isEmpty(versionInfo.description)) {
                txtUpdateDescriptionTitle.setVisibility(View.GONE);
                txtUpdateDescription.setVisibility(View.GONE);
            } else {
                txtUpdateDescriptionTitle.setVisibility(View.VISIBLE);
                txtUpdateDescription.setVisibility(View.VISIBLE);
                txtUpdateDescription.setText(versionInfo.description);
            }
        } else {
            txtRightUpdate.setText("There is a new version: " + versionInfo.versionName);
            //更新说明
            if (TextUtils.isEmpty(versionInfo.descriptionEn)) {
                txtUpdateDescriptionTitle.setVisibility(View.GONE);
                txtUpdateDescription.setVisibility(View.GONE);
            } else {
                txtUpdateDescriptionTitle.setVisibility(View.VISIBLE);
                txtUpdateDescription.setVisibility(View.VISIBLE);
                txtUpdateDescription.setText(versionInfo.descriptionEn);
            }
        }
    }

    /**
     * 隐藏所有view
     */
    private void setAllViewToInView() {
        llNotNetwork.setVisibility(View.GONE);
        txtVersionMsg.setVisibility(View.GONE);
        llCheckUpdate.setVisibility(View.GONE);
        llDownload.setVisibility(View.GONE);
        llInstall.setVisibility(View.GONE);
        clRightUpdate.setVisibility(View.GONE);
    }

    @OnClick(value = {R.id.btn_cancel, R.id.btn_install, R.id.btn_right_update})
    public void onClick(View view) {
        switch (view.getId()) {
            //取消
            case R.id.btn_cancel:
                if (mSystemSettingService != null) {
                    Log.i(TAG, "onClick: btn_cancel");
                    mSystemSettingService.stopDownload();
                    showUpdateVersionInfo(mSystemSettingService.getVersionInfo());
                }

                break;

            //安装
            case R.id.btn_install:
                if (mSystemSettingService != null) {
                    Log.i(TAG, "onClick: btn_install");
                    CommonUtils.sendExitBroadcast(getContext());
                    mSystemSettingService.startInstallPackage(getContext());
                }

                break;

            //立即更新
            case R.id.btn_right_update:
                Log.i(TAG, "onClick: btn_right_update");
                clRightUpdate.setVisibility(View.GONE);
                llDownload.setVisibility(View.VISIBLE);
                progressDownload.setProgress(0);

                if (mSystemSettingService != null) {
                    mSystemSettingService.startDownApk();
                }

                break;
            default:
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mHandler.removeCallbacksAndMessages(null);
        mSystemSettingService.removeServiceCallBack(this);
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean state) {
        Log.i(TAG, "onCheckedChanged: ");
        SharedPreferencesUtil.saveIsAutoUpdate(getContext(), state);
        if (state) {
            //启动ota自动升级检测服务
            Util.startAutoUpdateService(getContext());
        }
    }

    @Override
    public void onCheckSuccess(VersionInfo versionInfo) {
        Log.i(TAG, "onCheckSuccess: " + versionInfo.toString());
        //如果下载更新包已经下载完成，则直接显示安装按钮
        if (mSystemSettingService.isDownloaded()) {
            if (clRightUpdate.getVisibility() == View.VISIBLE) {
                clRightUpdate.setVisibility(View.GONE);
            }
            setAllViewToInView();
            llInstall.setVisibility(View.VISIBLE);
        } else if (!mSystemSettingService.isDownloading()) {
            //检测成功，查看版本号
            showUpdateVersionInfo(versionInfo);
        }
    }

    @Override
    public void onCheckFail(int errCode, String reason) {
        Log.i(TAG, "onCheckFail: errCode:" + errCode);
        if (llNotNetwork.getVisibility() == View.VISIBLE) {
            return;
        }
        llNotNetwork.post(new Runnable() {
            @Override
            public void run() {
                setAllViewToInView();
            }
        });
        //检测失败，查看原因
        switch (errCode) {
            //1001:没有网络
            case ErrorCode_1001:
                //1004:动态注册失败
            case ErrorCode_1004:
                //上报版本超时
            case ErrorCode_1005:
                //1003:连接服务器失败，请检查网络后重试
            case ErrorCode_1003:
                llNotNetwork.post(new Runnable() {
                    @Override
                    public void run() {
                        llNotNetwork.setVisibility(View.VISIBLE);
                    }
                });
                break;

            //1002：未发现新版本
            case ErrorCode_1002:
                Log.i(TAG, "onCheckFail: 1002");
                txtVersionMsg.post(new Runnable() {
                    @Override
                    public void run() {
                        txtVersionMsg.setVisibility(View.VISIBLE);
                        //显示当前版本号
                        String language = Locale.getDefault().getLanguage();
                        if (language.equals(Constant.LANGUAGE_ZH)) {
                            txtVersionMsg.setText(getString(R.string.system_version) + Build.ID + "," + getString(R.string.lastest_version));
                        } else {
                            txtVersionMsg.setText("Current version: " + Build.ID + getString(R.string.lastest_version));
                        }
                    }
                });
                break;
            default:
                break;
        }
    }

    @Override
    public void onDownloadProgress(int progress) {
        Log.i(TAG, "onDownloadProgress: " + progress);

        //主线程中更新UI
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (!CommonUtils.isNetworkConnected(getContext())) {
                    setAllViewToInView();
                    llNotNetwork.setVisibility(View.VISIBLE);
                    return;
                }

                if (clRightUpdate.getVisibility() == View.VISIBLE) {
                    clRightUpdate.setVisibility(View.GONE);
                    llDownload.setVisibility(View.VISIBLE);
                }

                if (llCheckUpdate.getVisibility() == View.VISIBLE) {
                    llCheckUpdate.setVisibility(View.GONE);
                    llDownload.setVisibility(View.VISIBLE);
                }

                //进度下载
                progressDownload.setProgress(progress);
            }
        });


    }

    @Override
    public void onDownloadCompleted(String outputFile) {
        Log.i(TAG, "onDownloadCompleted: " + (Looper.getMainLooper() == Looper.myLooper()));
        llDownload.setVisibility(View.GONE);
        setAllViewToInView();
        llInstall.setVisibility(View.VISIBLE);
    }

    @Override
    public void onDownloadFailure(int errCode) {
        Log.i(TAG, "onDownloadFailure: errCode : " + errCode);
        setAllViewToInView();
        if (errCode == NOT_NEW_VERSION) {
            txtVersionMsg.setVisibility(View.VISIBLE);
            String language = Locale.getDefault().getLanguage();
            if (language.equals(Constant.LANGUAGE_ZH)) {
                txtVersionMsg.setText(getString(R.string.system_lastest_version));
            } else {
                txtVersionMsg.setText("Your version is up to date");
            }
            return;
        }

        if (errCode == NOT_NETWORK) {
            llNotNetwork.setVisibility(View.VISIBLE);
            return;
        }
    }

    @Override
    public void onInstallSuccess() {
        Log.i(TAG, "onInstallSuccess: ");
    }

    @Override
    public void onInstallFail(String errMsg) {
        Log.i(TAG, "onInstallFail: " + errMsg);
    }

    @Override
    public void onInstallProgress(float progress) {
        Log.i(TAG, "onInstallProgress: " + progress);
    }

    class NetworkChangedReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (mIsFirstNetworkChanged) {
                //注册网络变化广播监听时，会立即发送一次，这次不处理
                mIsFirstNetworkChanged = false;
                return;
            }
            if (CommonUtils.isNetSystemUsable(getContext())) {
                //有网络
                Log.i(TAG, "onReceive: has network");
                setAllViewToInView();
                llCheckUpdate.setVisibility(View.VISIBLE);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mSystemSettingService.checkUpdate(false);
                    }
                }, TIME_3000);
            } else {
                // 无网络
                Log.i(TAG, "onReceive: not network");
                setAllViewToInView();
                llNotNetwork.setVisibility(View.VISIBLE);
            }
        }
    }
}
