<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/menu_bg">

    <ImageView
        android:id="@+id/pen_red"
        android:layout_width="@dimen/pen_bg_size"
        android:layout_height="@dimen/pen_bg_size"
        android:layout_marginStart="@dimen/pen_margin_parent_start"
        android:background="@drawable/pen_red_bg"
        android:scaleType="centerInside"
        android:src="@drawable/pen_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pen_yellow"
        android:layout_width="@dimen/pen_bg_size"
        android:layout_height="@dimen/pen_bg_size"
        android:layout_marginStart="@dimen/pen_margin_start"
        android:layout_toEndOf="@id/pen_red"
        android:background="@drawable/pen_yellow_bg"
        android:scaleType="centerInside"
        android:src="@drawable/pen_yellow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pen_red"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pen_blue"
        android:layout_width="@dimen/pen_bg_size"
        android:layout_height="@dimen/pen_bg_size"
        android:layout_marginStart="@dimen/pen_margin_start"
        android:background="@drawable/pen_blue_bg"
        android:scaleType="centerInside"
        android:src="@drawable/pen_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pen_yellow"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line1"
        android:layout_width="@dimen/border_line_width"
        android:layout_height="@dimen/border_line_height"
        android:layout_marginStart="@dimen/border_line_margin_start"
        android:background="@color/border_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pen_blue"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pen_eraser"
        android:layout_width="@dimen/pen_eraser_size"
        android:layout_height="@dimen/pen_eraser_size"
        android:layout_marginStart="@dimen/border_line_margin_start"
        android:scaleType="centerInside"
        android:src="@drawable/pen_eraser"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/line1"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pen_clear_all"
        android:layout_width="@dimen/pen_eraser_size"
        android:layout_height="@dimen/pen_eraser_size"
        android:layout_marginStart="@dimen/border_line_margin_start"
        android:scaleType="centerInside"
        android:src="@drawable/clear_all"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pen_eraser"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line2"
        android:layout_width="@dimen/border_line_width"
        android:layout_height="@dimen/border_line_height"
        android:layout_marginStart="@dimen/border_line_margin_start"
        android:background="@color/border_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pen_clear_all"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/exit"
        android:layout_width="@dimen/pen_eraser_size"
        android:layout_height="@dimen/pen_eraser_size"
        android:layout_marginStart="@dimen/border_line_margin_start"
        android:scaleType="centerInside"
        android:src="@drawable/exit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/line2"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
