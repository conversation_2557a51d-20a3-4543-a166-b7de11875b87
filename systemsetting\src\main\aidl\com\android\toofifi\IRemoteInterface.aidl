// IRemoteInterface.aidl
package com.android.toofifi;

// Declare any non-default types here with import statements
import com.mphotool.api.bean.PapaDevice;
import com.android.toofifi.IRemoteListener;

interface IRemoteInterface
{
    /**
    * 获取激活状态
    * @return  -1:未知, 0:未激活, 1:已试用激活, 2:已商用激活
    */
    int getActivationStatus();

    /**
    * 获取投屏状态
    * @param ip: 设备IP
    * @return 0: 未投屏  1：投屏中
    */
    int getMirrorStatus(String ip);

    /**
    * 调用发送端投屏/退屏
    * ps:playStop为0时,会根据当前投屏状态智能判断,如果当前正在投屏,则认为是callstop操作,反之认为用户是callplay操作
    *
    * @param ip
    * @param playStop 0-play/stop,1-play,2-stop
    */
    void callPlayStopCmd(String ip, int playStop);

    /**
    * 获取设备列表
    * @return List<PapaDevice>
    */
    List<PapaDevice> getDeviceInfoList();

    /**
    * 设置回调接口
    * @param listener 非空:注册; 空:注销
    */
    void setRemoteListener(IRemoteListener listener);

}
