plugins {
    id 'com.android.application'
}

android {
    compileSdk 31

    defaultConfig {
        applicationId "cn.eeo.classin.setup"
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        versionCode 3
        versionName "3.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding = true
    }
    //-------------添加android签名打包 begin -------------
    signingConfigs {
        platform {
            storeFile file("../app_signature/eeo_t982.jks")
            storePassword "android"
            keyAlias "android"
            keyPassword "android"
        }
    }
    buildTypes.each { bt ->
        bt.signingConfig = signingConfigs.platform
    }

    applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def fileName = "eeo_Setup.apk"
                    outputFileName = fileName
            }
    }
}

dependencies {

    //implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.android.support:appcompat-v7:' + rootProject.ext.supportV4
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation("androidx.navigation:navigation-fragment:2.5.3")
    implementation("androidx.navigation:navigation-ui:2.5.3")
    implementation("androidx.preference:preference:1.2.0")
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    //implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'com.android.support:recyclerview-v7:' + rootProject.ext.supportV4

    compileOnly files('..\\commonlib\\framework.jar')

    implementation files('../commonlib/binderhttpd-0.0.17.aar')
    implementation files('../commonlib/client-sdk-1.0.25.aar')
    implementation files('../commonlib/com.cvte.tvapi-lib.jar')

    implementation("com.jakewharton:butterknife:10.0.0")
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.0.0'
    implementation ("io.reactivex.rxjava2:rxandroid:2.1.0")
    implementation ("io.reactivex.rxjava2:rxjava:2.2.1")
    //uid的依赖库
    //implementation project(path: ':libudisdk')
    implementation project(':libudisdk')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation 'com.elvishew:xlog:1.10.0'
    implementation project(':ota')
}