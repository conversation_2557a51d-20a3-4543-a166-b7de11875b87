<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/menu_bg">

    <TextView
        android:id="@+id/tv_clear_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@drawable/clear_all"
        android:gravity="center"
        android:paddingHorizontal="@dimen/tv_clear_all_padding_horizontal"
        android:text="@string/clear_all"
        android:textColor="@drawable/clear_all_text_color"
        android:textSize="@dimen/tv_clear_all_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
