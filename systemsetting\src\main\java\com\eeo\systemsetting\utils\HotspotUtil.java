package com.eeo.systemsetting.utils;

import android.content.Context;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.Log;

import java.lang.reflect.Method;
import java.util.Random;

/**
 * 投屏热点相关
 */
public class HotspotUtil {
    private static final String TAG = "HotspotUtil";
    private Context mContext;
    private WifiManager mWifiManager;
    private WifiConfiguration mWifiConfiguration;
    private final String mHotspotName;
    private final String mHotspotPwd = "88887777";

    public HotspotUtil(Context context) {
        mContext = context;
        mWifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
        mHotspotName = getDeviceName();
    }

    /**
     * 检查热点有没有按eeo规则命名和打开
     * 1.检查热点是否为Classin-macAddress后四位
     * 2.是否打开热点
     */
    public void checkAndStartHotspot() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                boolean shouldUpdateWifiConfiguration = shouldUpdateWifiApConfiguration();
                if (shouldUpdateWifiConfiguration) {
                    //修改了热点信息，无论是否打开，都需要重新设置一下
                    setWifiApConfiguration();
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (isWifiApEnabled()) {
                        stopSoftAp();
                        try {
                            Thread.sleep(2000); //等关闭完成后再开启，不然会开启失败
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    startSoftAp();
                } else {
                    if (!isWifiApEnabled()) {
                        startSoftAp();
                    }
                }
            }
        }).start();
    }

    private boolean shouldUpdateWifiApConfiguration() {
        mWifiConfiguration = getWifiApConfiguration();
        if (mWifiConfiguration != null && mWifiConfiguration.SSID != null && mWifiConfiguration.SSID.equals(mHotspotName)
                && mWifiConfiguration.preSharedKey != null && mWifiConfiguration.preSharedKey.equals(mHotspotPwd)) {
            Log.d(TAG, "shouldUpdateWifiApConfiguration: false");
            return false;
        }
        Log.d(TAG, "shouldUpdateWifiApConfiguration: true");
        return true;
    }

    private WifiConfiguration getWifiApConfiguration() {
        WifiConfiguration wifiConfiguration = null;
        try {
            Method getWifiApConfiguration = mWifiManager.getClass().getMethod("getWifiApConfiguration");
            wifiConfiguration = (WifiConfiguration) getWifiApConfiguration.invoke(mWifiManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return wifiConfiguration;
    }

    public static WifiConfiguration getWifiApConfiguration(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiConfiguration wifiConfiguration = null;
        try {
            Method getWifiApConfiguration = wifiManager.getClass().getMethod("getWifiApConfiguration");
            wifiConfiguration = (WifiConfiguration) getWifiApConfiguration.invoke(wifiManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return wifiConfiguration;
    }

    /**
     * 设置热点参数
     * 这里只修改名称和密码
     * 由eeo规则生成
     */
    private void setWifiApConfiguration() {
        mWifiConfiguration = new WifiConfiguration();
        mWifiConfiguration.SSID = mHotspotName;
        mWifiConfiguration.preSharedKey = mHotspotPwd;
        mWifiConfiguration.allowedKeyManagement.set(4); //WPA2_PSK 不指定安全类型，设置密码无效为null
        try {
            Method method2 = mWifiManager.getClass().getMethod("setWifiApConfiguration", WifiConfiguration.class);
            method2.invoke(mWifiManager, mWifiConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "setWifiApConfiguration: exception=" + e);
        }
    }

    /**
     * 热点是否打开
     */
    private boolean isWifiApEnabled() {
        try {
            Method isWifiApEnabledMethod = mWifiManager.getClass().getMethod("isWifiApEnabled");
            Boolean isWifiApEnabled = (Boolean) isWifiApEnabledMethod.invoke(mWifiManager);
            if (isWifiApEnabled != null) {
                return isWifiApEnabled;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "isWifiApEnabled: exception=" + e);
        }
        return false;
    }

    /**
     * 打开热点
     */
    private void startSoftAp() {
        Log.d(TAG, "startSoftAp: ");
        try {
            Method startSoftAp = mWifiManager.getClass().getMethod("startSoftAp", WifiConfiguration.class);
            startSoftAp.invoke(mWifiManager, mWifiConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "startSoftAp: exception=" + e);
        }
    }

    public void stopSoftAp() {
        Log.d(TAG, "stopSoftAp: ");
        try {
            Method method4 = mWifiManager.getClass().getMethod("stopSoftAp");
            method4.invoke(mWifiManager);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "stopSoftAp: exception=" + e);
        }
    }

    /**
     * 投屏所用到的设备名称
     * 热点名称也用这一个
     */
    public String getDeviceName() {
        String deviceName = SystemProperties.get("persist.eeo.device.name", "");
        if (TextUtils.isEmpty(deviceName)) {
            deviceName = generateDeviceName();
            SystemProperties.set("persist.eeo.device.name", deviceName);
        }
        Log.d(TAG, "getDeviceName: " + deviceName);
        return deviceName;
    }

    /**
     * ClassIn-Mac地址后四位
     */
    private String generateDeviceName() {
        StringBuilder stringBuilder = new StringBuilder("ClassIn-");
        String macAddress = SystemProperties.get("ro.boot.mac", "");
        if (TextUtils.isEmpty(macAddress)) {
            //空的话随机生成4位
            stringBuilder.append(getRandomHexNumber());
        } else {
            String[] macAddressArray = macAddress.split(":");
            if (macAddressArray.length >= 2) {
                stringBuilder.append(macAddressArray[macAddressArray.length - 2])
                        .append(macAddressArray[macAddressArray.length - 1]);
            } else {
                //随机生成4位
                stringBuilder.append(getRandomHexNumber());
            }
        }
        Log.d(TAG, "generateDeviceName: " + stringBuilder);
        return stringBuilder.toString();
    }

    private String getRandomHexNumber() {
        Random random = new Random();
        int randomInt = random.nextInt(0xffff + 1); //生成0到ffff的数字
        String hexString = Integer.toHexString(randomInt);
        while (hexString.length() < 4) {
            //不足4位前面补0
            hexString = "0" + hexString;
        }
        Log.d(TAG, "getRandomHexNumber: " + hexString);
        return hexString;
    }
}
