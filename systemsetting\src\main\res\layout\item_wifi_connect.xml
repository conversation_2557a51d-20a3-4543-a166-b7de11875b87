<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/item_wifi_connect_height"
    android:layout_marginTop="@dimen/item_wifi_connect_margin_top"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/img_check"
        android:layout_width="@dimen/item_wifi_connect_iv_check_width"
        android:layout_height="@dimen/item_wifi_connect_iv_check_height"
        android:layout_gravity="center_vertical"
        android:background="@drawable/set_ic_checkbox"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/txt_wifi_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="HDEW506"
        android:textColor="@color/black_100"
        android:textSize="@dimen/item_wifi_tv_state_text_size" />

    <ImageView
        android:id="@+id/img_password"
        android:layout_width="@dimen/item_wifi_connect_iv_check_width"
        android:layout_height="@dimen/item_wifi_connect_iv_check_height"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/item_wifi_connect_iv_password_margin_start"
        android:background="@drawable/ic_password" />

    <ImageView
        android:id="@+id/img_rank"
        android:layout_width="@dimen/item_wifi_connect_iv_check_width"
        android:layout_height="@dimen/item_wifi_connect_iv_check_height"
        android:layout_gravity="center_vertical"
        android:background="@drawable/ic_wifi_1" />

    <ImageView
        android:id="@+id/img_more"
        android:layout_width="@dimen/item_wifi_connect_iv_check_width"
        android:layout_height="@dimen/item_wifi_connect_iv_check_height"
        android:layout_gravity="center_vertical"
        android:background="@drawable/select_wifi_img_press_icon" />

</LinearLayout>