package com.eeo.WhiteboardAccelerate.SerialPort

import android.util.Log
import com.eeo.WhiteboardAccelerate.Service.AccelerateService
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.util.concurrent.Executors

class SerialPortController {
    private val mThreadPoolExecutor = Executors.newCachedThreadPool()
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    private var serialPort: SerialPortDevice? =null
    /**
     * 返回串口是否开启
     * @return 是否开启
     */
    private var isOpened = false

    private var mOnSerialListener: OnSerialListener? = null

    /**
     * 打开串口
     * @param serialPath 串口地址
     * @param baudRate   波特率
     * @param flags      标志位
     */
    fun openSerialPort(serialPath: String?, baudRate: Int, flags: Int) {
        serialPort = SerialPortDevice(File(serialPath), baudRate, flags)
        inputStream = serialPort!!.getInputStream()
        outputStream = serialPort!!.getOutputStream()
        isOpened = true
        mOnSerialListener!!.onSerialOpenSuccess()
        mThreadPoolExecutor.execute(ReceiveDataThread())
    }

    /**
     * 关闭串口
     */
    fun closeSerialPort() {
        inputStream!!.close()
        outputStream!!.close()
        serialPort!!.close()
        isOpened = false
    }

    /**
     * 发送串口数据
     * @param bytes 发送数据
     */
    fun sendSerialPort(bytes: ByteArray?) {
        if (!isOpened) {
            return
        }
        //Log.e(AccelerateService.TAG,"sendSerialPort size:"+ bytes!!.size);
        outputStream!!.write(bytes)
        outputStream!!.flush()
    }

    /**
     * 串口返回数据内容读取
     */
    private inner class ReceiveDataThread : Thread() {
        override fun run() {
            super.run()
            while (isOpened) {
                //sleep(3)
                val readData = ByteArray(1024)
                val size = inputStream!!.read(readData)
                if (size > 0) {
                    mOnSerialListener!!.onReceivedData(readData, size)
                }
            }
        }
    }

    /**
     * 设置串口监听
     * @param onSerialListener 串口监听
     */
    fun setSerialPortListener(onSerialListener: OnSerialListener?) {
        mOnSerialListener = onSerialListener
    }

    /**
     * 串口监听
     */
    interface OnSerialListener {
        /**
         * 串口数据返回
         */
        fun onReceivedData(data: ByteArray?, size: Int)

        /**
         * 串口打开成功
         */
        fun onSerialOpenSuccess()

        /**
         * 串口打开异常
         */
        fun onSerialOpenException(e: Exception?)
    }
}