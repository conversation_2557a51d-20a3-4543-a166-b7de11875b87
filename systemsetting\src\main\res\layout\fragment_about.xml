<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fadeScrollbars="false"
    android:overScrollMode="never"
    android:paddingBottom="@dimen/fragment_about_padding_bottom"
    android:scrollbarSize="@dimen/fragment_about_scrollbar_size"
    android:scrollbarThumbVertical="@drawable/shape_network_scrollview">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/txt_name_title"
            style="@style/About_Text_Title"
            android:layout_marginTop="@dimen/fragment_about_device_name_margin_top"
            android:text="@string/device_name" />

        <TextView
            android:id="@+id/txt_name"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_name_title"
            android:text="C86A" />

        <TextView
            android:id="@+id/txt_serial_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/txt_name_title"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/serial_number" />

        <TextView
            android:id="@+id/txt_serial"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_serial_title"
            android:text="C86A-213HE0780=000431" />

        <TextView
            android:id="@+id/txt_resolution_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/txt_serial_title"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/resolution" />

        <TextView
            android:id="@+id/txt_resolution"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_resolution_title"
            android:text="3840*2160" />

        <View
            android:id="@+id/line1"
            style="@style/About_Line"
            android:layout_below="@id/txt_resolution_title"
            android:layout_marginStart="@dimen/fragment_about_line_margin_start"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:layout_marginEnd="@dimen/fragment_about_line_margin_start" />

        <TextView
            android:id="@+id/txt_android_version_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/line1"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:text="@string/android_version" />

        <TextView
            android:id="@+id/txt_android_version"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_android_version_title"
            android:text="8.0.0" />

        <LinearLayout
            android:id="@+id/ll_system_version"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_android_version_title"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top">

            <TextView
                style="@style/About_Text_Title"
                android:layout_width="@dimen/fragment_about_title_width"
                android:text="@string/system_version" />

            <TextView
                android:id="@+id/txt_system_version"
                style="@style/About_Text_Content"
                android:layout_marginStart="@dimen/fragment_about_tv_system_version_margin_start"
                android:text="8.0.0" />
        </LinearLayout>

        <TextView
            android:id="@+id/txt_touch_version_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/ll_system_version"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/touch_version" />

        <TextView
            android:id="@+id/txt_touch_version"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_touch_version_title"
            android:text="3920" />

        <View
            android:id="@+id/line2"
            style="@style/About_Line"
            android:layout_below="@id/txt_touch_version_title"
            android:layout_marginStart="@dimen/fragment_about_line_margin_start"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:layout_marginEnd="@dimen/fragment_about_line_margin_start" />

        <TextView
            android:id="@+id/txt_total_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/line2"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:text="@string/running_memory" />

        <TextView
            android:id="@+id/txt_total"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_total_title"
            android:text="总共: 3GB&#160;&#160;&#160;&#160;剩余: 0.99GB" />

        <TextView
            android:id="@+id/txt_store_total_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/txt_total_title"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/store_memory" />

        <TextView
            android:id="@+id/txt_store_total"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_store_total_title"
            android:text="总共: 3GB&#160;&#160;&#160;&#160;剩余: 0.99GB" />

        <View
            android:id="@+id/line3"
            style="@style/About_Line"
            android:layout_below="@id/txt_store_total_title"
            android:layout_marginStart="@dimen/fragment_about_line_margin_start"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:layout_marginEnd="@dimen/fragment_about_line_margin_start" />

        <TextView
            android:id="@+id/txt_company_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/line3"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:text="@string/company" />

        <TextView
            android:id="@+id/txt_company"
            style="@style/About_Text_Content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/txt_company_title"
            android:text="@string/company_name"
            android:maxLines="2"/>

        <TextView
            android:id="@+id/txt_email_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/txt_company_title"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/service_email" />

        <TextView
            android:id="@+id/txt_email"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_email_title"
            android:text="@string/email" />

        <TextView
            android:id="@+id/txt_hotline_title"
            style="@style/About_Text_Title"
            android:layout_below="@id/txt_email_title"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top"
            android:text="@string/service_hotline" />

        <TextView
            android:id="@+id/txt_hotline"
            style="@style/About_Text_Content"
            android:layout_alignTop="@id/txt_hotline_title"
            android:text="@string/hotline" />

        <View
            android:id="@+id/line4"
            style="@style/About_Line"
            android:layout_below="@id/txt_hotline_title"
            android:layout_marginStart="@dimen/fragment_about_line_margin_start"
            android:layout_marginTop="@dimen/fragment_about_line_margin_top"
            android:layout_marginEnd="@dimen/fragment_about_line_margin_start" />

        <RelativeLayout
            android:id="@+id/rl_privacy_policy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/line4"
            android:layout_marginTop="@dimen/fragment_about_device_name_margin_top">

            <TextView
                style="@style/About_Text_Title"
                android:layout_centerVertical="true"
                android:text="@string/privacy_policy" />

            <ImageView
                android:layout_width="@dimen/adb_iv_reset_width"
                android:layout_height="@dimen/adb_iv_reset_height"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/fragment_about_line_margin_start"
                android:background="@drawable/window_arror"
                android:clickable="false" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_windows_host"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_below="@id/rl_privacy_policy"
            android:layout_marginTop="@dimen/fragment_about_title_margin_top">

            <TextView
                style="@style/About_Text_Title"
                android:layout_width="@dimen/fragment_about_windows_host_width"
                android:text="@string/window_host" />

            <ImageView
                android:id="@+id/img_window"
                android:layout_width="@dimen/adb_iv_reset_width"
                android:layout_height="@dimen/adb_iv_reset_height"
                android:layout_marginStart="@dimen/fragment_about_iv_windows_host_margin_start"
                android:background="@drawable/window_arror"
                android:clickable="false" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/fragment_about_padding_view_height"
            android:layout_below="@id/ll_windows_host" />

    </RelativeLayout>

</ScrollView>