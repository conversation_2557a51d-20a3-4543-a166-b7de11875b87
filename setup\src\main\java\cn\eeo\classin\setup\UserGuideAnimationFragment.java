package cn.eeo.classin.setup;

import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.textclassifier.Log;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.elvishew.xlog.XLog;

import cn.eeo.classin.setup.base.BaseFragment;
import cn.eeo.classin.setup.utils.CommonUtils;

public class UserGuideAnimationFragment extends BaseFragment {
    private static final String TAG = "UserGuideAnimationFragment";
    private ImageView imageView;
    private LinearLayout ll_check_update;
    /*public static AnimationDrawable master_control_animationDrawable1;
    public static AnimationDrawable animationDrawable2;
    public static AnimationDrawable animationDrawable3;*/
    private int master_control_animationDrawable_flag = 0;
    private int animationDrawable2_flag = 0;
    private int animationDrawable3_flag = 0;
    private Drawable.Callback callback;


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        XLog.d("onViewCreated");
        // 创建 FrameLayout
        FrameLayout frameLayout = new FrameLayout(requireContext());
        // 设置 FrameLayout 的布局参数为全屏
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        );
        frameLayout.setLayoutParams(layoutParams);

        // 获取 LayoutInflater
        LayoutInflater inflater = LayoutInflater.from(requireContext());

        // 获取 AnimationLayout
        RelativeLayout animationLayout = (RelativeLayout) inflater.inflate(R.layout.animationlayout, (ViewGroup) view, false);
        imageView = animationLayout.findViewById(R.id.imageView);

        ll_check_update = animationLayout.findViewById(R.id.ll_system_init);
        // 将 LinearLayout 添加到 FrameLayout
        frameLayout.addView(animationLayout);

        // 将 FrameLayout 设置为当前 Fragment 的内容视图
        ((ViewGroup) view).addView(frameLayout);

        ll_check_update.setVisibility(View.VISIBLE);

        startAnimation(MainActivity.master_control_animationDrawable1);

    }

    @Override
    public int getLayout() {
        XLog.d("getLayout");
        return R.layout.fragment_animation;
    }

    @Override
    public void initDate() {

    }

    private void startAnimation(final AnimationDrawable animationDrawable) {
        if (imageView == null) {
            XLog.d("imageView is  null");
            return;
        }
        imageView.setImageDrawable(animationDrawable);
        // 设置动画监听器
        Drawable mLastFrame = animationDrawable.getFrame(animationDrawable.getNumberOfFrames() - 1);
        callback = new Drawable.Callback() {
            @Override
            public void invalidateDrawable(Drawable who) {
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        imageView.invalidateDrawable(who);
                    }
                });
                XLog.d("invalidateDrawable");
                if (mLastFrame.equals(who.getCurrent())) {
                    XLog.d("最后一帧");
                    Log.d(TAG, "最后一帧");
                    if (who == MainActivity.master_control_animationDrawable1) {
                        XLog.d("结束第一个动画");
                        Log.d(TAG, "结束第一个动画");
                        if (master_control_animationDrawable_flag < 2) {
                            master_control_animationDrawable_flag++;
                            XLog.d("startAnimation master_control_animationDrawable1");
                            Log.d(TAG, "startAnimation master_control_animationDrawable1");
                            MainActivity.master_control_animationDrawable1.stop();
                            startAnimation(MainActivity.master_control_animationDrawable1);
                        } else {
                            XLog.d("startAnimation animationDrawable2");
                            Log.d(TAG, "startAnimation animationDrawable2");
                            master_control_animationDrawable_flag = 0;
                            startAnimation(MainActivity.animationDrawable2);
                        }

                    } else if (who == MainActivity.animationDrawable2) {
                        XLog.d("结束第二个动画！！！！！！！！！");
                        Log.d(TAG, "结束第二个动画！！！！！！！！！");
                        if (animationDrawable2_flag < 2) {
                            animationDrawable2_flag++;
                            Log.d(TAG, "再次第二个动画！！！！！！！！！");
                            MainActivity.animationDrawable2.stop();
                            //MainActivity.animationDrawable2.run();
                            startAnimation(MainActivity.animationDrawable2);
                        } else {
                            XLog.d("启动第三个动画");
                            Log.d(TAG, "启动第三个动画");
                            animationDrawable2_flag = 0;
                            if (MainActivity.animationDrawable3 != null) {
                                startAnimation(MainActivity.animationDrawable3);

                            } else {
                                XLog.d("没有第三个动画");
                                Log.d(TAG, "没有第三个动画");
                                //将开机引导禁用
                                MainActivity.master_control_animationDrawable1 = null;
                                MainActivity.animationDrawable2 = null;
                                MainActivity.animationDrawable3 = null;
                                startSetting();
                                CommonUtils.finishSetupWizard(getActivity());
                            }
                        }
                    } else if (who == MainActivity.animationDrawable3) {
                        XLog.d("开始第三个动画");
                        Log.d(TAG, "开始第三个动画");
                        if (animationDrawable3_flag < 2) {
                            animationDrawable3_flag++;
                            MainActivity.animationDrawable3.stop();
                            startAnimation(MainActivity.animationDrawable3);
                        } else {
                            XLog.d("第三个动画结束");
                            Log.d(TAG, "第三个动画结束");
                            animationDrawable3_flag = 0;
                            //startAnimation(master_control_animationDrawable1);
                            //将开机引导禁用
                            MainActivity.master_control_animationDrawable1 = null;
                            MainActivity.animationDrawable2 = null;
                            MainActivity.animationDrawable3 = null;
                            startSetting();
                            CommonUtils.finishSetupWizard(getActivity());

                        }
                    } else {
                        XLog.d("不是正常动画");
                        Log.d(TAG, "不是正常动画");
                    }
                } else {
                    XLog.d("不是最后一帧");
                }

            }

            @Override
            public void scheduleDrawable(Drawable who, Runnable what, long when) {
                imageView.scheduleDrawable(who, what, when);
                XLog.d("scheduleDrawable");

            }

            @Override
            public void unscheduleDrawable(android.graphics.drawable.Drawable who, Runnable what) {
                imageView.unscheduleDrawable(who, what);
                XLog.d("unscheduleDrawable");
            }
        };

        animationDrawable.setCallback(callback);

        animationDrawable.setOneShot(true);
        animationDrawable.start();

    }

    private void startSetting() {
        XLog.d("startSetting");
        PackageManager pm = getActivity().getPackageManager();
        String packageName = "com.eeo.systemsetting";
        String ActivityName = "com.eeo.systemsetting.launcher.FallbackHomeActivity";
        //String ActivityName = "com.eeo.systemsetting.launcher.TifPlayerActivity";
        ComponentName component = new ComponentName(packageName, ActivityName);
        //int status = pm.getComponentEnabledSetting(component);
        //XLog.d( "status:" + status);
        try {
            Intent intent = new Intent(); //pm.getLaunchIntentForPackage(packageName);//
            intent.setComponent(component);
            intent.setAction("com.cvte.intent.ACTION_TIF_PLAYER_ACTIVITY");
            intent.addCategory("android.intent.category.DEFAULT");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            intent.putExtra("from", "setup"); //通过改标志通知设置，需要执行一些操作，如执行无线投屏相关操作
            startActivity(intent);
        } catch (Exception e) {
            android.util.Log.e(TAG, e.toString());
        }

    }
}
