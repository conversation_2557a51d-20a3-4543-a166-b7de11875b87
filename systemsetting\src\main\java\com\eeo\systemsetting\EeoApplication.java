package com.eeo.systemsetting;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.Log;

import androidx.multidex.MultiDex;

import com.cvte.tv.api.TvApiSDKManager;
import com.droidlogic.app.SystemControlManager;
import com.droidlogic.app.tv.TvControlManager;
import com.eeo.systemsetting.projection.ProjectionUtil;
import com.eeo.systemsetting.service.SystemSettingService;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.source.SourceChangeListener;
import com.eeo.udisdk.system.ScreenStatusChangeListener;
import com.example.touchsdk.TouchCommunicator;

import java.util.ArrayList;
import java.util.List;

public class EeoApplication extends Application {
    public static final String TAG = "EeoApplication==";
    public static TouchCommunicator touchCommunicator;
    public static Udi udi;
    private static EeoApplication eeoApplication = null;
    //判断activity弹窗是否显示
    public static boolean isShowMainDialog = false;
    /**
     * 大屏控制菜单页面层级
     */
    public static final int PAGE_MAIN = 0;
    public static final int PAGE_SIGNAL = 1;
    public static final int PAGE_SETTING = 2;
    public static int currentPage = PAGE_MAIN;
    //是否在安装ota更新
    public static boolean isShowInstallingDialog = false;
    //有新版可以更新弹窗
    public static boolean isShowHaveUpdateDialog = false;
    //重置ops弹窗
    public static boolean isShowResetOpsDialog = false;
    //投屏弹窗
    public static boolean isShowScreenDialog = false;
    //TifPlayerActivity
    public static boolean isTifPlayerActivityStopped = false;

    //判断画中画小窗口是否显示
    public static boolean isProjection = false;
    //判断画中画小窗口正在召唤中
    public static boolean isProjectionEntering = false;

    //ops还原中
    public static boolean isResettingOps = false;

    //半屏
    public static boolean isHalf = false;

    //触控锁
    public static boolean isLock = false;
    public static boolean isLockBeforeMirror = false;

    //是否处于护眼模式
    public static int isEyeCare = -1;  //-1:未读取过状态  0：关闭  1：护眼模式

    /**
     * 启动批注的时间
     * 启动不久内禁止五指启动画中画
     */
    public static long lastClickTime = -1;

    /**
     * 滑条滑动结束时，若有手指处于按下状态，待抬起后再恢复windows触摸
     */
    public static boolean mIsFingerDown = false;
    public static boolean mShouldSetTouchEnable = false;

    /**
     * udi信号源变化监听，一个应用只能一处生效
     * 统一在这里监听处理
     */
    private List<SourceChangeListener> mSourceChangeListenerList = new ArrayList<>();
    public static String mCurrentSource = null;
    private String mCurrentSourceId;

    public static boolean mShouldHandleBootSource = false;
    public static String mBootSource = Constant.FORCE_SOURCE_PC;

    public static EeoApplication getApplication() {
        return eeoApplication;
    }

    @SuppressLint("CheckResult")
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "=== EeoApplication onCreate ====");
        eeoApplication = this;
        CommonUtils.updateDensity(this);
        touchCommunicator = new TouchCommunicator();
        touchCommunicator.InitSDK();
        TvApiSDKManager.init(this);
        if (udi == null) {
            udi = new Udi(this, UdiConstant.TOKEN_SETTING);
        }

        //无线投屏相关处理
        CommonUtils.checkAndSetWirelessScreen(this);

        ProjectionUtil.resetSmallWindowDisabled(this);
        bindSystemSettingService();

        //开机后需要同步重新设置呼吸灯状态
        SystemControlManager.getInstance().setBreathLedStatus(SaveDateUtils.getBreathLedOn(this) ? 4 : 5); //1：唤醒  2：休眠 3：关机或重启 4:开启常亮 5：关闭常亮
        udi.registerScreenStatusChangeListener(new ScreenStatusChangeListener() {
            @Override
            public void onScreenStatusChange(boolean isOn, boolean isMute) {
                Log.d(TAG, "onScreenStatusChange: isOn=" + isOn + ", isMute=" + isMute);
                if (isOn) {
                    //亮屏恢复外部通道触摸
                    CommonUtils.enableOsd(EeoApplication.this, false);
                    SystemControlManager.getInstance().setBreathLedStatus(1); //1：唤醒  2：休眠 3：关机或重启
                } else {
                    //灭屏禁止外部通道触摸
                    CommonUtils.enableOsd(EeoApplication.this, true);
                    SystemControlManager.getInstance().setBreathLedStatus(2); //1：唤醒  2：休眠 3：关机或重启
                }
            }
        });
        registerSourceChangeListener(new SourceChangeListener() {
            @Override
            public void onSourceChange(String previousSource, String newSource, boolean isFinished) {
            }
        });
        TvControlManager mTvControlManager = TvControlManager.getInstance();
        mTvControlManager.setTvserverDeathListener(new TvControlManager.TvserverDeathListener() {
            @Override
            public void serviceDied(long l) {
                Log.d(TAG, "TvControlManager tvserver serviceDied");
                PowerUtil.getInstance(EeoApplication.this).shutdownOrReboot(true, "", PowerUtil.FLAG_SHUTDOWN_HARD); //硬重启
            }
        });
        //拉起应用
        //startProvisionApp();
        //允许无障碍服务功能-来监听其它应用的弹窗
        CommonUtils.enablePopupAlertService(EeoApplication.this, true);

    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this); // 初始化 MultiDex
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        touchCommunicator.DeInitSDK();
        TvApiSDKManager.destroy();
        unbindSystemSettingService();
        Log.i(TAG, "onTerminate: ");
    }

    private SystemSettingService.MyBinder mBinder;
    private SystemSettingService mSystemSettingService;
    private ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mBinder = (SystemSettingService.MyBinder) service;
            if (mBinder != null) {
                mSystemSettingService = mBinder.getService();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected: ");
            mBinder = null;
            mSystemSettingService = null;
        }
    };

    private void bindSystemSettingService() {
        Log.d(TAG, "bindGestureService: ");
        Intent intent = new Intent(this, SystemSettingService.class);
        bindService(intent, mServiceConnection, BIND_AUTO_CREATE);
    }

    private void unbindSystemSettingService() {
        unbindService(mServiceConnection);
    }

    public SystemSettingService getSystemSettingService() {
        return mSystemSettingService;
    }

    private void startProvisionApp() {
        PackageManager pm = getPackageManager();
        String packageName = "cn.eeo.classin.setup";
        String ActivityName = "cn.eeo.classin.setup.MainActivity";
        ComponentName component = new ComponentName(packageName, ActivityName);
        int status = pm.getComponentEnabledSetting(component);
        Log.d(TAG, "status:" + status);

        if (PackageManager.COMPONENT_ENABLED_STATE_DISABLED != status) {
            try {
                Intent intent = new Intent(); //pm.getLaunchIntentForPackage(packageName);//
                intent.setComponent(component);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                startActivity(intent);
            } catch (Exception e) {
                Log.e(TAG, e.toString());
            }

        }

    }

    public void registerSourceChangeListener(SourceChangeListener sourceChangeListener) {
        if (mSourceChangeListenerList.size() == 0) {
            udi.registerSourceChangeListener(new SourceChangeListener() {
                @Override
                public void onSourceChange(String previousSource, String newSource, boolean isFinished) {
                    Log.i(TAG, "onSourceChange:" + previousSource + "->" + newSource + " ,isFinished : " + isFinished);
                    if (isFinished) {
                        mCurrentSource = newSource;
                        String sourceId = SystemProperties.get(Constant.PROP_LAST_SOURCE, "0");
                        if (!sourceId.equals(mCurrentSourceId)) {
                            if (PowerUtil.getInstance(EeoApplication.this).isShuttingDown()) {
                                //关机过程中改变的信号源不做记录，这里恢复关机操作前的信号源
                                if (!TextUtils.isEmpty(mCurrentSourceId)) {
                                    Log.d(TAG, "onSourceChange: isShuttingDown reset last source id " + sourceId + " -> " + mCurrentSourceId);
                                    SystemProperties.set(Constant.PROP_LAST_SOURCE, mCurrentSourceId);
                                }
                            } else {
                                mCurrentSourceId = sourceId;
                            }
                        }
                    }
                    for (SourceChangeListener listener : mSourceChangeListenerList) {
                        listener.onSourceChange(previousSource, newSource, isFinished);
                    }
                }
            });
        }
        if (sourceChangeListener != null && !mSourceChangeListenerList.contains(sourceChangeListener)) {
            mSourceChangeListenerList.add(sourceChangeListener);
        }
    }

    public void unregisterSourceChangeListener(SourceChangeListener sourceChangeListener) {
        if (sourceChangeListener != null) {
            mSourceChangeListenerList.remove(sourceChangeListener);
        }
        if (mSourceChangeListenerList.size() == 0) {
            udi.unregisterSourceChangeListener();
        }
    }
}
