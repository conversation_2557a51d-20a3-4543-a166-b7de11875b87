package com.eeo.systemsetting.utils;

import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;

public class FileUtil {
    private static final String TAG = "FileUtil";
    public static final String PATH_ACTIVATED_DATA = "/data/ft_activated_data.xml";

    /**
     * 将飞图激活码写入data/ft_activated_data.xml
     * 供恢复出厂后使用
     */
    public static void saveActivatedDataToFile(String value) {
        if (TextUtils.isEmpty(value)) {
            return;
        }
        try {
            File file = new File(PATH_ACTIVATED_DATA);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(fileOutputStream));
            writer.write(value);
            writer.close();
            fileOutputStream.close();
        } catch (IOException e) {
            Log.e(TAG, "saveActivatedDataToFile exception: " + e);
            e.printStackTrace();
        }
    }

    /**
     * 从/data/ft_activated_data.xml读取激活码
     */
    public static String getActivatedData() {
        File file = new File(PATH_ACTIVATED_DATA);
        if (!file.exists()) {
            Log.e(TAG, "getActivatedData: file is not exist! ");
            return null;
        }
        FileInputStream fileInputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        String line;
        StringBuilder stringBuilder = null;
        try {
            fileInputStream = new FileInputStream(file);
            inputStreamReader = new InputStreamReader(fileInputStream);
            bufferedReader = new BufferedReader(inputStreamReader);
            stringBuilder = new StringBuilder();
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            Log.e(TAG, "getActivatedData exception: " + e);
            e.printStackTrace();
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (inputStreamReader != null) {
                    inputStreamReader.close();
                }
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return stringBuilder == null ? null : stringBuilder.toString();
    }

    public static void deleteActivatedDataFile() {
        File file = new File(PATH_ACTIVATED_DATA);
        if (file.exists()) {
            file.delete();
        }
    }
}
