package com.eeo.commonlibrary.eeointerface;

import com.eeo.commonlibrary.eyeconfort.EyeConfortInterface;
import com.eeo.commonlibrary.hdmi.HdmiInterface;
import com.eeo.commonlibrary.ops.OpsInterface;
import com.eeo.commonlibrary.screen.ScreenInterface;
import com.eeo.commonlibrary.sound.SoundInterface;
import com.eeo.commonlibrary.source.SourceInterface;

public interface PlatformBeanInterface {

    SourceInterface getSourceInterfaceImpl();

    SoundInterface getSoundInterfaceImpl();

    HdmiInterface getHdmiInterfaceImpl();

    EyeConfortInterface getEyeConfortInterfaceImpl();

    OpsInterface getOpsInterfaceImpl();

    ScreenInterface getScreenImpl();
}
