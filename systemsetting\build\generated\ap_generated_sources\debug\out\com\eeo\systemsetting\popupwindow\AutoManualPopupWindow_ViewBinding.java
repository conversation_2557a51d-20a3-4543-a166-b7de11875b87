// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.popupwindow;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class AutoManualPopupWindow_ViewBinding implements Unbinder {
  private AutoManualPopupWindow target;

  private View view7f0800e2;

  private View view7f0800ed;

  @UiThread
  public AutoManualPopupWindow_ViewBinding(final AutoManualPopupWindow target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.ll_auto, "field 'llAuto' and method 'onClick'");
    target.llAuto = Utils.castView(view, R.id.ll_auto, "field 'llAuto'", LinearLayout.class);
    view7f0800e2 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.imgAuto = Utils.findRequiredViewAsType(source, R.id.img_auto, "field 'imgAuto'", ImageView.class);
    target.txtAuto = Utils.findRequiredViewAsType(source, R.id.txt_auto, "field 'txtAuto'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_manual, "field 'llManual' and method 'onClick'");
    target.llManual = Utils.castView(view, R.id.ll_manual, "field 'llManual'", LinearLayout.class);
    view7f0800ed = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.imgManual = Utils.findRequiredViewAsType(source, R.id.img_manual, "field 'imgManual'", ImageView.class);
    target.txtManual = Utils.findRequiredViewAsType(source, R.id.txt_manual, "field 'txtManual'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    AutoManualPopupWindow target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.llAuto = null;
    target.imgAuto = null;
    target.txtAuto = null;
    target.llManual = null;
    target.imgManual = null;
    target.txtManual = null;

    view7f0800e2.setOnClickListener(null);
    view7f0800e2 = null;
    view7f0800ed.setOnClickListener(null);
    view7f0800ed = null;
  }
}
