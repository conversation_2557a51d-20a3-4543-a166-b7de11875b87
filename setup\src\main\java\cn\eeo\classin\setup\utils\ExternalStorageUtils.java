package cn.eeo.classin.setup.utils;

import android.content.Context;
import android.os.Build;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;

import androidx.annotation.RequiresApi;

import com.elvishew.xlog.XLog;

import java.io.File;

public class ExternalStorageUtils {

    private static final String TAG = "ExternalStorageUtils";

    @RequiresApi(api = Build.VERSION_CODES.R)
    public static boolean isUsbMounted(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                context.checkSelfPermission(android.Manifest.permission.READ_EXTERNAL_STORAGE)
                        != android.content.pm.PackageManager.PERMISSION_GRANTED) {
            // 请求权限
            // TODO: 在这里请求读取外部存储的权限
            XLog.d("权限校验失败");
            return false;
        }

        StorageManager storageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);

        // 获取所有存储卷
        StorageVolume[] storageVolumes;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            storageVolumes = storageManager.getStorageVolumes().toArray(new StorageVolume[0]);
        } else {
            // 在较低的 API 级别上处理
            XLog.d("较低版本的api");
            return false;
        }

        for (StorageVolume volume : storageVolumes) {
            String path = getUsbPath(context, volume);
            if (path!=null){
                XLog.d("volume:"+volume.getDirectory().toString());
            }else{
                XLog.d("volume is null");
            }
            XLog.d( "U 盘:"+ isUsbRootDirectoryValid(path)+":"+ isClassInFileExists(path));
            if (isUsbRootDirectoryValid(path) && isClassInFileExists(path)) {
                // U 盘根目录有效且包含 ClassIn.bin 文件
                return true;
            }
        }

        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.R)
    private static String getUsbPath(Context context, StorageVolume storageVolume) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            File directory = storageVolume.getDirectory();
            if (directory != null) {
                XLog.d("getusbpath:"+directory.getAbsolutePath().toString());
                return directory.getAbsolutePath();
            }
        } else {
            return getUsbPathBeforeAndroid11(context, storageVolume.getUuid());
        }
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.R)
    private static String getUsbPathBeforeAndroid11(Context context, String uuid) {
        StorageManager storageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);

        try {
            StorageVolume storageVolume = storageManager.getStorageVolume(new File("/storage/" + uuid));
            if (storageVolume != null) {
                File directory = storageVolume.getDirectory();
                return directory.getAbsolutePath();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private static boolean isUsbRootDirectoryValid(String usbPath) {
        if (usbPath == null) {
            XLog.d("usbPath is null");
            return false;
        }

        File usbRoot = new File(usbPath);
        return usbRoot.exists() && usbRoot.isDirectory() && usbRoot.canRead();
    }

    private static boolean isClassInFileExists(String usbPath) {
        if (usbPath == null) {
            return false;
        }
        XLog.d(usbPath.toString());
        File classInFile = new File(usbPath, "ClassIn.bin");
        XLog.d(classInFile.getAbsolutePath()+" 是否存在:"+classInFile.exists());
        return classInFile.exists();
    }
}
