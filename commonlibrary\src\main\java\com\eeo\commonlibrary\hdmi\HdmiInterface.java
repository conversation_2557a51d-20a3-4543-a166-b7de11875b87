package com.eeo.commonlibrary.hdmi;

public interface HdmiInterface {

    /**
     * 获取输入信号EDID分辨率
     *
     * @return
     */
    int getHdmirxEdidType();

    /**
     * 切换当前输入EDID分辨率
     *
     * @param hdmirxEdidType
     */
    void setHdmirxEdidType(int hdmirxEdidType);


    /**
     * 获取当前输出EDID分辨率
     *
     * @return
     */
    int getHdmiOutTimmingFormat();


    /**
     * 切换当前输出EDID分辨率
     *
     * @param type
     */
    void setHdmiOutTimmingFormat(int type);

}
