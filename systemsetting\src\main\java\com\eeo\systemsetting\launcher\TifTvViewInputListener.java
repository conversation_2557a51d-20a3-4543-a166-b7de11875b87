package com.eeo.systemsetting.launcher;

import android.media.tv.TvContentRating;
import android.media.tv.TvTrackInfo;
import android.media.tv.TvView;
import android.net.Uri;

import com.eeo.systemsetting.utils.CLog;

import java.util.List;

public class TifTvViewInputListener extends TvView.TvInputCallback {
    @Override
    public void onConnectionFailed(String inputId) {
        super.onConnectionFailed(inputId);
        CLog.tif("onConnectionFailed:inputId = " + inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onDisconnected(String inputId) {
        super.onDisconnected(inputId);
        CLog.tif("onDisconnected:inputId = " + inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onChannelRetuned(String inputId, Uri channelUri) {
        super.onChannelRetuned(inputId, channelUri);
        CLog.tif("onChannelRetuned:inputId = " + inputId + " channelUri = " + channelUri);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onTracksChanged(String inputId, List<TvTrackInfo> tracks) {
        super.onTracksChanged(inputId, tracks);
        CLog.tif("onTracksChanged:inputId = " + inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onTrackSelected(String inputId, int type, String trackId) {
        super.onTrackSelected(inputId, type, trackId);
        CLog.tif("onTrackSelected:inputId = " + inputId + " type = " + type + " trackId = " + trackId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onVideoSizeChanged(String inputId, int width, int height) {
        super.onVideoSizeChanged(inputId, width, height);
        CLog.tif("onVideoSizeChanged:inputId = " + inputId + " width = " + width + " height = " + height);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onVideoAvailable(String inputId) {
        super.onVideoAvailable(inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onVideoUnavailable(String inputId, int reason) {
        super.onVideoUnavailable(inputId, reason);
        CLog.tif("onVideoUnavailable:inputId = " + inputId + " reason = " + reason);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onContentAllowed(String inputId) {
        super.onContentAllowed(inputId);
        CLog.tif("onContentAllowed:inputId = " + inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onContentBlocked(String inputId, TvContentRating rating) {
        super.onContentBlocked(inputId, rating);
        CLog.tif("onContentBlocked:inputId = " + inputId);
    }

    @Override // android.media.tv.TvView.TvInputCallback
    public void onTimeShiftStatusChanged(String inputId, int status) {
        super.onTimeShiftStatusChanged(inputId, status);
        CLog.tif("onTimeShiftStatusChanged:inputId = " + inputId);
    }
}