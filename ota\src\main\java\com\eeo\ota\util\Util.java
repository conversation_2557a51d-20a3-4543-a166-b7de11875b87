package com.eeo.ota.util;

import static com.eeo.ota.Ota.PATH_DOWNLOAD_PARENT;
import static com.eeo.ota.Ota.PATH_UPDATE_PACKAGE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.SystemProperties;
import android.provider.Settings;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLConnection;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.Locale;

import com.eeo.ota.bean.Constant;
import com.eeo.ota.service.AutoUpdateService;
import com.hisilicon.android.tvapi.impl.CusExImpl;

public class Util {
    public static final String TAG = "ota-Util";
    public static final String PKG_NAME_SETTING = "com.eeo.systemsetting";
    public static final String FORCE_REBOOT = "force";
    public static final String KEY_IS_DIALOG = "is_dialog"; //密码输入、windows还原、系统升级等提示框显示时

    /**
     * 判断设备是否联网
     *
     * @param context 上下文
     * @return 有网络返回true，无网络返回false
     */
    public static boolean isNetworkConnect(Context context) {
        if (context == null) {
            return false;
        }
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                Log.d(TAG, "isNetworkConnect: true");
                return true;
            }
        }
        Log.e(TAG, "isNetworkConnect: false");
        return false;
    }

    /**
     * 根据url获取文件的大小
     */
    public static long getSizeFromUrl(String url) {
        long size = 0;
        try {
            URL url1 = new URL(url);
            URLConnection connection = url1.openConnection();
            size = connection.getContentLength();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return size;
    }

    /**
     * 文件大小转换为GB、MB、KB、B
     */
    public static String convertSizeToString(long size) {
        long kb = 1024;
        long mb = kb * 1024;
        long gb = mb * 1024;

        if (size >= gb) {
            float f = (float) size / gb;
            return String.format(size % gb == 0 ? "%.0f GB" : "%.2f GB", f);
        } else if (size >= mb) {
            float f = (float) size / mb;
            return String.format(size % mb == 0 ? "%.0f MB" : "%.2f MB", f);
        } else if (size >= kb) {
            float f = (float) size / kb;
            return String.format(size % kb == 0 ? "%.0f KB" : "%.2f KB", f);
        } else {
            return String.format(Locale.getDefault(), "%d B", size);
        }
    }

    public static String getCurrentVersion() {
        return Build.ID;
    }

    /**
     * Ota更新成功后要上报结果
     */
    public static boolean shouldReportOtaSuccess(Context context) {
        if (SharedPreferencesUtil.getShouldReport(context)) {
            String newVersion = SharedPreferencesUtil.getNewVersion(context);
            String currentVersion = getCurrentVersion();
            Log.d(TAG, "shouldReportOtaSuccess: newVersion=" + newVersion
                    + ",currentVersion=" + currentVersion);
            if (currentVersion.equals(newVersion)) {
                Log.d(TAG, "shouldReportOtaSuccess: true");
                return true;
            }
        }
        Log.d(TAG, "shouldReportOtaSuccess: false");
        return false;
    }

    /**
     * Ota更新成功后才显示弹窗
     */
    public static boolean shouldShowUpdateDialog(Context context) {
        if (SharedPreferencesUtil.getShowUpdateDialog(context)) {
            String newVersion = SharedPreferencesUtil.getNewVersion(context);
            String currentVersion = getCurrentVersion();
            if (currentVersion.equals(newVersion)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 删除ota相关文件
     */
    public static void deleteAllOtaFile() {
        Log.d(TAG, "deleteAllOtaFile");
        //删除sdcard/ota/下的子文件、子文件夹
        deleteOtaFile();
        //删除sdcard/update.zip
        deleteUpdateFile();
    }

    /**
     * 删除sdcard/update.zip
     */
    public static void deleteUpdateFile() {
        Log.d(TAG, "deleteUpdateFile");
        File updateFile = new File(PATH_UPDATE_PACKAGE);
        if (updateFile.exists()) {
            updateFile.delete();
        }
    }

    /**
     * 删除sdcard/ota/下的子文件、子文件夹
     */
    public static void deleteOtaFile() {
        //删除sdcard/ota/下的子文件、子文件夹
        Log.d(TAG, "deleteOtaFile");
        File file = new File(PATH_DOWNLOAD_PARENT);
        if (file.exists() && file.isDirectory()) {
            String[] subFilePaths = file.list();
            for (String subFilePath : subFilePaths) {
                deleteTarget(PATH_DOWNLOAD_PARENT + "/" + subFilePath);
            }
        }
    }

    /**
     * The full path name of the file to delete.
     * 删除指定路径的文件或文件夹
     *
     * @param path name
     * @return
     */
    public static void deleteTarget(String path) {
        File target = new File(path);
        if (!target.exists()) {
            return;
        }
        //文件存在并且是文件，则直接删除
        if (target.isFile()) {
            target.delete();
        } else if (target.isDirectory()) {
            //文件存在且是文件夹
            String[] file_list = target.list();
            //文件夹存在并且是空的文件夹则直接删除
            if (file_list != null && file_list.length == 0) {
                target.delete();
            } else if (file_list != null && file_list.length > 0) {
                //文件夹存在并且文件夹不为空，则递归将文件夹内的文件一个个删除，最后再将空的文件夹删除
                for (int i = 0; i < file_list.length; i++) {
                    String filePath = target.getAbsolutePath() + "/" + file_list[i];
                    File temp_f = new File(filePath);
                    if (temp_f.isDirectory()) {
                        deleteTarget(temp_f.getAbsolutePath());
                    } else if (temp_f.isFile()) {
                        temp_f.delete();
                    }
                }
                target.delete();
            }
        }
    }

    /**
     * 根据文件路径拷贝文件
     *
     * @param src      源文件
     * @param destPath 目标文件路径
     * @return boolean 成功true、失败false
     */
    public static boolean copyFile(File src, String destPath) {
        if (src == null || !src.exists() || destPath == null) {
            return false;
        }
        File dest = new File(destPath);
        if (dest.exists()) {
            dest.delete(); // delete file
        }
        try {
            dest.createNewFile();
        } catch (IOException e) {
            e.printStackTrace();
        }

        FileChannel srcChannel = null;
        FileChannel dstChannel = null;

        try {
            srcChannel = new FileInputStream(src).getChannel();
            dstChannel = new FileOutputStream(dest).getChannel();
            srcChannel.transferTo(0, srcChannel.size(), dstChannel);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        try {
            srcChannel.close();
            dstChannel.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 获取单个文件的MD5值！
     */
    public static String getFileMD5(File file) {
        if (!file.isFile()) {
            return null;
        }
        MessageDigest digest = null;
        FileInputStream in = null;
        byte[] buffer = new byte[1024];
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
            in = new FileInputStream(file);
            while ((len = in.read(buffer, 0, 1024)) != -1) {
                digest.update(buffer, 0, len);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        /*BigInteger bigInt = new BigInteger(1, digest.digest());
        return bigInt.toString(16);*/
        //修复开头的0丢失问题
        byte[] md5Bytes = digest.digest();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < md5Bytes.length; i++) {
            int value = ((int) md5Bytes[i]) & 0xff;
            if (value < 16) {
                stringBuilder.append("0");
            }
            stringBuilder.append(Integer.toHexString(value));
        }
        return stringBuilder.toString();
    }

    /**
     * 获取序列号
     */
    @SuppressLint("MissingPermission")
    public static String getSerialNumber() {
        String serial = "unknown";
        if (Constant.IS_CXD11) {
            //CXD11使用朗国定义的整机序列号
            serial = CusExImpl.getInstance().cus_get_serialnumber();
        } else if (Constant.IS_982) {
            //982整机序列号
            serial = SystemProperties.get("persist.sys.boardsn.value", "unknown");
        } else {
            if (Build.VERSION.SDK_INT >= 26) {
                serial = Build.getSerial();
            } else {
                serial = SystemProperties.get("ro.serialno", "unknown");
            }
        }
        Log.d(TAG, "serial : " + serial);
        return serial;
    }

    public static final String ACTION_EEO_POWER_OFF = "com.eeo.action.POWER_OFF";
    public static final String ACTION_EEO_REBOOT = "com.eeo.action.REBOOT";

    /**
     * 发送广播通过设置去重启
     *
     * @param forceReboot 超时后是否强制关ops
     */
    public static void reboot(Context context, boolean forceReboot) {
        Intent intent = new Intent(ACTION_EEO_REBOOT);
        intent.setPackage(PKG_NAME_SETTING);
        if (forceReboot) {
            intent.putExtra(FORCE_REBOOT, 1);
        }
        context.sendBroadcast(intent);
    }

    /**
     * 发送广播通过设置去关机
     */
    public static void shutdown(Context context) {
        Intent intent = new Intent(ACTION_EEO_POWER_OFF);
        intent.setPackage(PKG_NAME_SETTING);
        context.sendBroadcast(intent);
    }

    /**
     * 发送广播通过设置去开关OPS触摸
     * 因为可能有多个应用设置osd，只有全部应用都取消，才能真正取消
     * 统一由设置去控制外部通道触摸
     *
     * @param enable true:恢复外部通道触摸 false:禁用外部通道触摸
     */
    public static void sendOpsTouchEnableBroadcast(Context context, boolean enable) {
        Log.d(TAG, "sendOpsTouchEnableBroadcast : enable=" + enable);
        Intent intent = new Intent("com.eeo.set.usb.enable");
        intent.setPackage(PKG_NAME_SETTING);
        intent.putExtra("usbTouchKey", enable ? 0 : 1);  //0可触摸
        context.sendBroadcast(intent);
    }

    /**
     * 启动ota自动检测更新service
     */
    public static void startAutoUpdateService(Context context) {
        Log.d(TAG, "startAutoUpdateService");
        Intent intent = new Intent(context, AutoUpdateService.class);
        try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                context.startService(intent);
            } else {
                context.startForegroundService(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "startAutoUpdateService error:" + e);
        }
    }

    public static void setIsDialog(Context context, boolean isDialog) {
        Settings.Global.putInt(context.getContentResolver(), KEY_IS_DIALOG, isDialog ? 1 : 0);
    }
}
