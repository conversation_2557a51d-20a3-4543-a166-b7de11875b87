<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="About_Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.3dp</item>
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:background">@color/line1</item>
        <item name="android:alpha">0.5</item>
    </style>
    <!--<style name="wireless_connected_bg">
        <item android:width="331" android:height="40">
            <shape android:shape="rectangle">
                <solid android:color="#4dffffff" />
                <corners android:radius="8dp" />
            </shape>
        </item>
    </style>-->

    <style name="Wifi_Text_NAME">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">25dp</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">9sp</item>
    </style>

    <style name="SystemSetting_PopWindow_Host_Btn">
        <item name="android:layout_width">88dp</item>
        <item name="android:layout_height">28dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="textAllCaps">false</item>
        <item name="android:outlineProvider">none</item>
    </style>

    <!--Dialog将白色背景变透明，但是背景也变暗-->
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent
        </item><!-- 设置dialog背景为透明背景 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item><!-- 设置dialog背景变暗 -->
    </style>
    //
    <style name="MyButtonStyle">
        <item name="android:background">@drawable/custom_button_background</item>
        <item name="android:textColor">#1C1F25</item>
        <item name="android:textSize">11sp</item>
        <item name="android:stateListAnimator">@null</item>
        <!-- 更多样式属性 -->
    </style>

        <!-- 自定义 Spinner! 样式 -->
    <style name="CustomSpinnerStyle" parent="Widget.AppCompat.Spinner">
        <!-- 设置下拉框的背景颜色 -->
        <item name="android:background">@drawable/custom_spinner_background</item>
        <item name="android:layout_marginRight">10dp</item>
        <!-- 设置下拉框的文本样式 -->
        <item name="android:textSize">9sp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <!-- 设置下拉箭头的样式 -->
        <item name="android:popupBackground">@drawable/custom_spinner_background</item>
    </style>


    <style name="spinner_style" parent="Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/spinner_raow_selector</item>
        <item name="android:popupBackground">@drawable/custom_spinner_background</item>
        <item name="android:gravity">end</item>
    </style>

    <style name="MyCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:drawablePadding">1dp</item>
        <item name="android:button">@drawable/radio_btn_selctor</item>

    </style>

    <!-- 网络更多 -->
    <style name="IP_Dialog_Select_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">29dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="IP_Dialog_Select_Image">
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_marginLeft">29dp</item>
    </style>

    <style name="IP_Dialog_Select_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">3dp</item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:textSize">9sp</item>
    </style>
    <style name="Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/main_line</item>
        <item name="android:layout_marginTop">13dp</item>

    </style>

    <style name="WiFi_Text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">29dp</item>
        <item name="android:paddingLeft">21dp</item>
        <item name="android:textSize">9sp</item>
        <item name="android:textColor">@color/text_press_green_black</item>
        <item name="android:background">@drawable/select_wifi_item_press_color</item>
        <item name="android:layout_marginTop">21dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="NetWork_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:textSize">9sp</item>
    </style>

    <style name="Network_EditText">
        <item name="android:layout_width">140dp</item>
        <item name="android:layout_height">29dp</item>
        <item name="android:textCursorDrawable">@drawable/shape_network_edt_cursor</item>
        <item name="android:textSize">9sp</item>
        <item name="android:textColor">@color/color_network_edit</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:paddingEnd">7dp</item>
        <item name="android:gravity">center_vertical|end</item>
    </style>
    <style name="NetWork_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">29dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>

    </style>
    <style name="NetWork_Switch">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>
</resources>

