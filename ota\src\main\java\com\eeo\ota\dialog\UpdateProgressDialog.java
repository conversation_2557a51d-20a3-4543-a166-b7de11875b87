package com.eeo.ota.dialog;

import android.app.ProgressDialog;
import android.content.Context;
import android.view.WindowManager;

public class UpdateProgressDialog {
    private static final String TAG = "UpdateProgressDialog";

    private static ProgressDialog mProgressDialog;

    /**
     * 创建mProgressDialog
     */
    public static void showProgressDialog(Context context, String title, int progress) {
        if (mProgressDialog == null) {
            mProgressDialog = new ProgressDialog(context);
            mProgressDialog.setIndeterminate(false);
            mProgressDialog.setCancelable(false);
            mProgressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            //Service不能直接弹AlertDialog，需要设置使用系统的Dialog
            if (mProgressDialog.getWindow() != null) {
                mProgressDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
        }
        mProgressDialog.setTitle(title);
        mProgressDialog.setProgress(progress);
        if (!mProgressDialog.isShowing()) {
            mProgressDialog.show();
        }
        /*if (mProgressDialog.getWindow() != null) {
            //ProgressDialog去掉灰色背景，避免出现闪屏问题
            WindowManager.LayoutParams layoutParams = mProgressDialog.getWindow().getAttributes();
            layoutParams.dimAmount = 0;
            mProgressDialog.getWindow().setAttributes(layoutParams);
        }*/
    }

    /**
     * 隐藏mProgressDialog
     */
    public static void dismissProgressDialog() {
        if (mProgressDialog != null && mProgressDialog.isShowing()) {
            mProgressDialog.dismiss();
        }
    }

}
