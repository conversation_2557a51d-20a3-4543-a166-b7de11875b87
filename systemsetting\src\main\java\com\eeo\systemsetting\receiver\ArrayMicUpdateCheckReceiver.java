package com.eeo.systemsetting.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.eeo.ota.arraymic.ArrayMicUpdateService;

public class ArrayMicUpdateCheckReceiver extends BroadcastReceiver {

    private static final String TAG = "ArrayMicUpdateCheckReceiver";
    public static final String ACTION_CHECK_ARRAY_MIC_UPDATE = "com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null && ACTION_CHECK_ARRAY_MIC_UPDATE.equals(intent.getAction())) {
            Log.i(TAG, "Received action to check Array Microphone update.");
            Intent serviceIntent = new Intent(context, ArrayMicUpdateService.class);
            context.startService(serviceIntent);
        }
    }
}
