package com.eeo.systemsetting.rgb;

import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.cvte.tv.api.TvApiSDKManager;
import com.cvte.tv.api.aidl.EnumColorTempItem;
import com.cvte.tv.api.aidl.ITVApiPictureColorTempAidl;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.SaveDateUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 色温（白平衡）调节相关
 */
public class RgbManager implements View.OnClickListener, View.OnFocusChangeListener, SeekBar.OnSeekBarChangeListener {
    private static final String TAG = "RgbManager";
    private static RgbManager mRgbManager = null;
    private Context mContext;
    private WindowManager mWindowManager;
    private InputMethodManager mInputMethodManager;
    private Toast mToast;
    private View mToastView;
    private TextView mToastTv;

    private OnClickListener mClickListener;

    private ITVApiPictureColorTempAidl mColorTempAidl;
    private View mView;
    private SeekBar mColorTemperatureSb;
    private SeekBar mRedGainSb;
    private SeekBar mGreenGainSb;
    private SeekBar mBlueGainSb;
    private EditText mRedGainEt;
    private EditText mGreenGainEt;
    private EditText mBlueGainEt;
    private EditText mColorTemperatureEt;

    private boolean mIsColorTemperatureEtHasFocus = false;
    private boolean mIsRedGainEtHasFocus = false;
    private boolean mIsGreenGainEtHasFocus = false;
    private boolean mIsBlueGainEtHasFocus = false;
    private boolean mShouldHandleFocusLost = true; //点击其它事件使焦点丢失时，不修改该focus lost时的rgb值
    private boolean mStartTrackingTouch = false;
    private boolean mIsEyeCare = false;
    private int mRedGainValue;
    private int mGreenGainValue;
    private int mBlueGainValue;
    private int mColorTemperatureValue;
    private int mStandardValueIndex;
    private final static int COLOR_TEMPERATURE_VALUE_OFFSET = 10; //色温等级显示为：-10 ~ 10
    private static final int TOTAL_COLORS = COLOR_TEMPERATURE_VALUE_OFFSET * 2 + 1; // Total number of colors
    private int[][] mColorTemperatureArray = new int[TOTAL_COLORS][3]; // Array to store colors

    /**
     * BS65A色温白平衡RGB增益
     * 标准、冷色、暖色模式对应值
     * 冷色色温12000K左右
     * 暖色色温8000K左右
     * 根据这三种模式，通过插值法获取21组值供色温进度条调节
     */
    private final static int[] COLOR_TEMPERATURE_STANDARD_BS65A = {123, 124, 125};
    private final static int[] COLOR_TEMPERATURE_COLD_BS65A = {113, 128, 128};
    private final static int[] COLOR_TEMPERATURE_WARM_BS65A = {128, 128, 118};
    /**
     * BS75A
     */
    private final static int[] COLOR_TEMPERATURE_STANDARD_BS75A = {123, 124, 120};
    private final static int[] COLOR_TEMPERATURE_COLD_BS75A = {116, 128, 128};
    private final static int[] COLOR_TEMPERATURE_WARM_BS75A = {128, 128, 116};
    /**
     * BS86A
     */
    private final static int[] COLOR_TEMPERATURE_STANDARD_BS86A = {123, 124, 122};
    private final static int[] COLOR_TEMPERATURE_COLD_BS86A = {106, 128, 128};
    private final static int[] COLOR_TEMPERATURE_WARM_BS86A = {128, 128, 120};
    /**
     * BS110A
     */
    private final static int[] COLOR_TEMPERATURE_STANDARD_BS110A = {117, 121, 126};
    private final static int[] COLOR_TEMPERATURE_COLD_BS110A = {106, 116, 130};
    private final static int[] COLOR_TEMPERATURE_WARM_BS110A = {128, 116, 122};

    /**
     * 护眼模式对应值
     */
    private final static int[] COLOR_TEMPERATURE_EYE_CARE = {128, 128, 78};

    /**
     * 基于标准、冷色、暖色模式对应值
     * 通过插值法生成
     */
    private static final int[][] COLORS_BS65A = {
            {113, 128, 128}, // 0 Cold
            {114, 128, 128}, // 1
            {115, 127, 127}, // 2
            {116, 127, 127}, // 3
            {117, 126, 127}, // 4
            {118, 126, 127}, // 5
            {119, 126, 126}, // 6
            {120, 125, 126}, // 7
            {121, 125, 126}, // 8
            {122, 124, 125}, // 9
            {123, 124, 125}, // 10 Standard
            {124, 124, 124}, // 11
            {124, 125, 124}, // 12
            {125, 125, 123}, // 13
            {125, 126, 122}, // 14
            {126, 126, 122}, // 15
            {126, 126, 121}, // 16
            {127, 127, 120}, // 17
            {127, 127, 119}, // 18
            {128, 128, 119}, // 19
            {128, 128, 118}, // 20 Warm
    };
    private static final int[][] COLORS_BS75A = {
            {116, 128, 128}, // 0 Cold
            {117, 128, 127}, // 1
            {117, 127, 127}, // 2
            {118, 127, 126}, // 3
            {118, 127, 125}, // 4
            {119, 126, 125}, // 5
            {120, 126, 124}, // 6
            {120, 126, 123}, // 7
            {121, 125, 123}, // 8
            {121, 125, 122}, // 9
            {122, 125, 121}, // 10
            {122, 124, 121}, // 11
            {123, 124, 120}, // 12 Standard
            {124, 125, 120}, // 13
            {124, 125, 119}, // 14
            {125, 126, 119}, // 15
            {126, 126, 118}, // 16
            {126, 127, 118}, // 17
            {127, 127, 117}, // 18
            {127, 128, 117}, // 19
            {128, 128, 116}, // 20 Warm
    };
    private static final int[][] COLORS_BS86A = {
            {106, 128, 128}, // 0 Cold
            {107, 128, 128}, // 1
            {108, 127, 127}, // 2
            {109, 127, 127}, // 3
            {111, 127, 126}, // 4
            {112, 127, 126}, // 5
            {113, 126, 126}, // 6
            {114, 126, 125}, // 7
            {115, 126, 125}, // 8
            {116, 126, 124}, // 9
            {117, 125, 124}, // 10
            {118, 125, 124}, // 11
            {120, 125, 123}, // 12
            {121, 125, 123}, // 13
            {122, 124, 122}, // 14
            {123, 124, 122}, // 15 Standard
            {124, 125, 122}, // 16
            {125, 126, 121}, // 17
            {126, 126, 121}, // 18
            {127, 127, 120}, // 19
            {128, 128, 120}, // 20 Warm
    };
    private static final int[][] COLORS_BS110A = {
            {106, 116, 130}, // 0 Cold
            {107, 116, 130}, // 1
            {108, 117, 130}, // 2
            {109, 117, 129}, // 3
            {110, 118, 129}, // 4
            {111, 118, 129}, // 5
            {112, 119, 129}, // 6
            {113, 119, 128}, // 7
            {114, 120, 128}, // 8
            {115, 120, 128}, // 9
            {116, 121, 128}, // 10
            {117, 121, 127}, // 11
            {118, 122, 127}, // 12
            {119, 122, 127}, // 13
            {120, 123, 127}, // 14
            {121, 123, 126}, // 15
            {122, 124, 126}, // 16
            {123, 124, 126}, // 17 Standard
            {125, 121, 125}, // 18
            {126, 119, 123}, // 19
            {128, 116, 122}, // 20 Warm
    };

    private ExecutorService mExecutorService = Executors.newCachedThreadPool();

    private RgbManager(Context context) {
        mContext = context;
    }

    public static RgbManager getInstance(Context context) {
        if (mRgbManager == null) {
            mRgbManager = new RgbManager(context);
            CommonUtils.updateDensity(context);
        }
        return mRgbManager;
    }

    private void initRgbValue() {
        if (SaveDateUtils.isNewEyeCare(mContext)) {
            mIsEyeCare = SaveDateUtils.isEyeCareEnable(mContext);
        } else {
            mIsEyeCare = EeoApplication.udi.isEyeCareEnabled();
        }
        mRedGainValue = getRedGainValue();
        mGreenGainValue = getGreenGainValue();
        mBlueGainValue = getBlueGainValue();
        mColorTemperatureValue = 0;
        for (int i = 0; i < mColorTemperatureArray.length; i++) {
            if (mColorTemperatureArray[i][0] == mRedGainValue &&
                    mColorTemperatureArray[i][1] == mGreenGainValue &&
                    mColorTemperatureArray[i][2] == mBlueGainValue) {
                mColorTemperatureValue = i - COLOR_TEMPERATURE_VALUE_OFFSET;
                break;
            }
        }
    }

    /**
     * 投屏提示dialog
     */
    public void showRgbDialog() {
        if (mView == null) {
            mView = LayoutInflater.from(mContext).inflate(R.layout.dialog_rgb, null);
            mView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (event.getAction() == MotionEvent.ACTION_DOWN) {
                        cancelFocus(true);
                    }
                    return false;
                }
            });
            mColorTemperatureSb = (SeekBar) mView.findViewById(R.id.sb_color_temperature);
            mRedGainSb = (SeekBar) mView.findViewById(R.id.sb_red_gain);
            mGreenGainSb = (SeekBar) mView.findViewById(R.id.sb_green_gain);
            mBlueGainSb = (SeekBar) mView.findViewById(R.id.sb_blue_gain);
            mRedGainEt = (EditText) mView.findViewById(R.id.et_red_gain);
            mGreenGainEt = (EditText) mView.findViewById(R.id.et_green_gain);
            mBlueGainEt = (EditText) mView.findViewById(R.id.et_blue_gain);
            mColorTemperatureEt = (EditText) mView.findViewById(R.id.et_color_temperature);
            mColorTemperatureSb.setOnSeekBarChangeListener(this);
            mRedGainSb.setOnSeekBarChangeListener(this);
            mGreenGainSb.setOnSeekBarChangeListener(this);
            mBlueGainSb.setOnSeekBarChangeListener(this);
            mColorTemperatureEt.setOnFocusChangeListener(this);
            mRedGainEt.setOnFocusChangeListener(this);
            mGreenGainEt.setOnFocusChangeListener(this);
            mBlueGainEt.setOnFocusChangeListener(this);
            mView.findViewById(R.id.iv_back).setOnClickListener(this);
            mView.findViewById(R.id.iv_close).setOnClickListener(this);
            mView.findViewById(R.id.btn_reset).setOnClickListener(this);
            initColorTemperature();
            initRgbValue();
        }
        mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
        mColorTemperatureSb.setProgress(mColorTemperatureValue + COLOR_TEMPERATURE_VALUE_OFFSET);
        mRedGainEt.setText(String.valueOf(mRedGainValue));
        mRedGainSb.setProgress(mRedGainValue);
        mGreenGainEt.setText(String.valueOf(mGreenGainValue));
        mGreenGainSb.setProgress(mGreenGainValue);
        mBlueGainEt.setText(String.valueOf(mBlueGainValue));
        mBlueGainSb.setProgress(mBlueGainValue);
        if (!mView.isAttachedToWindow()) {
            if (mWindowManager == null) {
                mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
            }
            WindowManager.LayoutParams viewParam = new WindowManager.LayoutParams();
            viewParam.width = CommonUtils.dp2px(mContext, Constant.DIALOG_RGB_WIDTH_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.height = CommonUtils.dp2px(mContext, Constant.DIALOG_RGB_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.gravity = Gravity.END | Gravity.BOTTOM;
            viewParam.x = CommonUtils.dp2px(mContext, Constant.DIALOG_MARIN_END_IN_DP);
            viewParam.y = CommonUtils.dp2px(mContext, Constant.DIALOG_MARIN_BOTTOM_IN_DP);
            viewParam.dimAmount = 0;

            viewParam.format = PixelFormat.RGBA_8888;
            viewParam.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
//            viewParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            mWindowManager.addView(mView, viewParam);
            //避免初始化时自动获取焦点
            mColorTemperatureEt.setFocusable(false);
            mRedGainEt.setFocusable(false);
            mGreenGainEt.setFocusable(false);
            mBlueGainEt.setFocusable(false);
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    resetFocusable();
                }
            }, 100);
        }
    }

    @Override
    public void onClick(View v) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.iv_back:
                dismissRgbDialog();
                break;
            case R.id.iv_close:
                dismissRgbDialog();
                if (mClickListener != null) {
                    mClickListener.onClickDismiss();
                }
                break;
            case R.id.btn_reset:
                cancelFocus(false);
                if (mIsEyeCare) {
                    showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                } else {
                    setColorTemperatureValue(mStandardValueIndex);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        switch (v.getId()) {
            case R.id.et_color_temperature:
                if (hasFocus) {
                    mIsColorTemperatureEtHasFocus = true;
                } else {
                    mIsColorTemperatureEtHasFocus = false;
                    if (!mShouldHandleFocusLost) {
                        mShouldHandleFocusLost = true;
                        mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
                        return;
                    }
                    String string = mColorTemperatureEt.getText().toString();
                    if (TextUtils.isEmpty(string)) {
                        mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
                        showRgbValueWrongToast(mContext, String.format(mContext.getString(R.string.color_temperature_value_wrong),
                                -COLOR_TEMPERATURE_VALUE_OFFSET, COLOR_TEMPERATURE_VALUE_OFFSET));
                    } else if (mIsEyeCare) {
                        mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                    } else {
                        int value = Integer.parseInt(string);
                        if (value >= -COLOR_TEMPERATURE_VALUE_OFFSET && value <= COLOR_TEMPERATURE_VALUE_OFFSET) {
                            if (mColorTemperatureValue != value) {
                                mColorTemperatureValue = value;
                                setColorTemperatureValue(value + COLOR_TEMPERATURE_VALUE_OFFSET);
                            }
                        } else {
                            mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
                            showRgbValueWrongToast(mContext, String.format(mContext.getString(R.string.color_temperature_value_wrong),
                                    -COLOR_TEMPERATURE_VALUE_OFFSET, COLOR_TEMPERATURE_VALUE_OFFSET));
                        }
                    }
                }
                break;
            case R.id.et_red_gain:
                if (hasFocus) {
                    mIsRedGainEtHasFocus = true;
                } else {
                    mIsRedGainEtHasFocus = false;
                    if (!mShouldHandleFocusLost) {
                        mShouldHandleFocusLost = true;
                        mRedGainEt.setText(String.valueOf(mRedGainValue));
                        return;
                    }
                    String string = mRedGainEt.getText().toString();
                    if (TextUtils.isEmpty(string)) {
                        mRedGainEt.setText(String.valueOf(mRedGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_red_gain_value_wrong));
                    } else if (mIsEyeCare) {
                        mRedGainEt.setText(String.valueOf(mRedGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                    } else {
                        int value = Integer.parseInt(string);
                        if (value >= 0 && value <= 255) {
                            if (mRedGainValue != value) {
                                mRedGainValue = value;
                                mRedGainSb.setProgress(value);
                                setRedGainValue(value);
                            }
                        } else {
                            mRedGainEt.setText(String.valueOf(mRedGainValue));
                            showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_red_gain_value_wrong));
                        }
                    }
                }
                break;
            case R.id.et_green_gain:
                if (hasFocus) {
                    mIsGreenGainEtHasFocus = true;
                } else {
                    mIsGreenGainEtHasFocus = false;
                    if (!mShouldHandleFocusLost) {
                        mShouldHandleFocusLost = true;
                        mGreenGainEt.setText(String.valueOf(mGreenGainValue));
                        return;
                    }
                    String string = mGreenGainEt.getText().toString();
                    if (TextUtils.isEmpty(string)) {
                        mGreenGainEt.setText(String.valueOf(mGreenGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_green_gain_value_wrong));
                    } else if (mIsEyeCare) {
                        mGreenGainEt.setText(String.valueOf(mGreenGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                    } else {
                        int value = Integer.parseInt(string);
                        if (value >= 0 && value <= 255) {
                            if (mGreenGainValue != value) {
                                mGreenGainValue = value;
                                mGreenGainSb.setProgress(value);
                                setGreenGainValue(value);
                            }
                        } else {
                            mGreenGainEt.setText(String.valueOf(mGreenGainValue));
                            showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_green_gain_value_wrong));
                        }
                    }

                }
                break;
            case R.id.et_blue_gain:
                if (hasFocus) {
                    mIsBlueGainEtHasFocus = true;
                } else {
                    mIsBlueGainEtHasFocus = false;
                    if (!mShouldHandleFocusLost) {
                        mShouldHandleFocusLost = true;
                        mBlueGainEt.setText(String.valueOf(mBlueGainValue));
                        return;
                    }
                    String string = mBlueGainEt.getText().toString();
                    if (TextUtils.isEmpty(string)) {
                        mBlueGainEt.setText(String.valueOf(mBlueGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_blue_gain_value_wrong));
                    } else if (mIsEyeCare) {
                        mBlueGainEt.setText(String.valueOf(mBlueGainValue));
                        showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                    } else {
                        int value = Integer.parseInt(string);
                        if (value >= 0 && value <= 255) {
                            if (mBlueGainValue != value) {
                                mBlueGainValue = value;
                                mBlueGainSb.setProgress(value);
                                setBlueGainValue(value);
                            }
                        } else {
                            mBlueGainEt.setText(String.valueOf(mBlueGainValue));
                            showRgbValueWrongToast(mContext, mContext.getString(R.string.rgb_blue_gain_value_wrong));
                        }
                    }
                }
                break;
        }
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {

    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        mStartTrackingTouch = true;
    }

    /**
     * 松开时才响应调节
     */
    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        mStartTrackingTouch = false;
        cancelFocus(false);
        int progress = seekBar.getProgress();
        switch (seekBar.getId()) {
            case R.id.sb_color_temperature:
                if (mIsEyeCare) {
                    mColorTemperatureSb.setProgress(mColorTemperatureValue);
                    showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                } else {
                    setColorTemperatureValue(progress);
                }
                break;
            case R.id.sb_red_gain:
                if (mIsEyeCare) {
                    mRedGainSb.setProgress(mRedGainValue);
                    showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                } else {
                    setRedGainValue(progress);
                    mRedGainValue = progress;
                    mRedGainEt.setText(String.valueOf(progress));
                }
                break;
            case R.id.sb_green_gain:
                if (mIsEyeCare) {
                    mGreenGainSb.setProgress(mGreenGainValue);
                    showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                } else {
                    setGreenGainValue(progress);
                    mGreenGainValue = progress;
                    mGreenGainEt.setText(String.valueOf(progress));
                }
                break;
            case R.id.sb_blue_gain:
                if (mIsEyeCare) {
                    mBlueGainSb.setProgress(mBlueGainValue);
                    showRgbValueWrongToast(mContext, mContext.getString(R.string.color_temperature_eye_care));
                } else {
                    setBlueGainValue(progress);
                    mBlueGainValue = progress;
                    mBlueGainEt.setText(String.valueOf(progress));
                }
                break;
        }
    }

    /**
     * @param value 传入的值为0~255
     */
    private void setRedGainValue(int value) {
        if (mColorTempAidl == null) {
            try {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    mColorTempAidl.eventPictureColorTempSetValue(EnumColorTempItem.COLORTEMP_RED_GAIN, value * 8);
                    Log.e(TAG, "setRedGainValue:" + value + " , result = " + getRedGainValue());
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, "setRedGainValue exception:" + e.toString());
                }
            }
        });
    }

    private void setGreenGainValue(int value) {
        if (mColorTempAidl == null) {
            try {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    mColorTempAidl.eventPictureColorTempSetValue(EnumColorTempItem.COLORTEMP_GREEN_GAIN, value * 8);
                    Log.e(TAG, "setGreenGainValue:" + value + " , result = " + getGreenGainValue());
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, "setGreenGainValue exception:" + e.toString());
                }
            }
        });
    }

    private void setBlueGainValue(int value) {
        if (mColorTempAidl == null) {
            try {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    mColorTempAidl.eventPictureColorTempSetValue(EnumColorTempItem.COLORTEMP_BLUE_GAIN, value * 8);
                    Log.e(TAG, "setBlueGainValue:" + value + " , result = " + getBlueGainValue());
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, "setBlueGainValue exception:" + e.toString());
                }
            }
        });
    }

    private void setColorTemperatureValue(int value) {
        mColorTemperatureValue = value - COLOR_TEMPERATURE_VALUE_OFFSET;
        mRedGainValue = mColorTemperatureArray[value][0];
        mGreenGainValue = mColorTemperatureArray[value][1];
        mBlueGainValue = mColorTemperatureArray[value][2];
        Log.d(TAG, "setColorTemperatureValue " + mColorTemperatureValue + "  , red=" + mRedGainValue
                + " , green=" + mGreenGainValue + " , blue=" + mBlueGainValue);
        setRedGainValue(mRedGainValue);
        setGreenGainValue(mGreenGainValue);
        setBlueGainValue(mBlueGainValue);
        mColorTemperatureEt.setText(String.valueOf(mColorTemperatureValue));
        mColorTemperatureSb.setProgress(mColorTemperatureValue + COLOR_TEMPERATURE_VALUE_OFFSET);
        mRedGainEt.setText(String.valueOf(mRedGainValue));
        mRedGainSb.setProgress(mRedGainValue);
        mGreenGainEt.setText(String.valueOf(mGreenGainValue));
        mGreenGainSb.setProgress(mGreenGainValue);
        mBlueGainEt.setText(String.valueOf(mBlueGainValue));
        mBlueGainSb.setProgress(mBlueGainValue);
    }

    private int getRedGainValue() {
        try {
            if (mColorTempAidl == null) {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            }
            return mColorTempAidl.eventPictureColorTempGetValue(EnumColorTempItem.COLORTEMP_RED_GAIN) / 8;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    private int getGreenGainValue() {
        try {
            if (mColorTempAidl == null) {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            }
            return mColorTempAidl.eventPictureColorTempGetValue(EnumColorTempItem.COLORTEMP_GREEN_GAIN) / 8;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    private int getBlueGainValue() {
        try {
            if (mColorTempAidl == null) {
                mColorTempAidl = TvApiSDKManager.getInstance().getTvApi().getTVApiPictureColorTemp();
            }
            return mColorTempAidl.eventPictureColorTempGetValue(EnumColorTempItem.COLORTEMP_BLUE_GAIN) / 8;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    public void dismissRgbDialog() {
        if (mWindowManager != null && mView != null && mView.isAttachedToWindow()) {
            mWindowManager.removeView(mView);
            mView = null;
        }
    }

    private void cancelFocus(boolean shouldHandleFocusLost) {
        mShouldHandleFocusLost = shouldHandleFocusLost;
        //占了焦点的EditText最后set，避免其它EditText抢占一下焦点
        if (mIsColorTemperatureEtHasFocus) {
            mRedGainEt.setFocusable(false);
            mGreenGainEt.setFocusable(false);
            mBlueGainEt.setFocusable(false);
            mColorTemperatureEt.setFocusable(false);
        } else if (mIsRedGainEtHasFocus) {
            mColorTemperatureEt.setFocusable(false);
            mGreenGainEt.setFocusable(false);
            mBlueGainEt.setFocusable(false);
            mRedGainEt.setFocusable(false);
        } else if (mIsGreenGainEtHasFocus) {
            mColorTemperatureEt.setFocusable(false);
            mRedGainEt.setFocusable(false);
            mBlueGainEt.setFocusable(false);
            mGreenGainEt.setFocusable(false);
        } else if (mIsBlueGainEtHasFocus) {
            mColorTemperatureEt.setFocusable(false);
            mRedGainEt.setFocusable(false);
            mGreenGainEt.setFocusable(false);
            mBlueGainEt.setFocusable(false);
        } else {
            mShouldHandleFocusLost = true;
            return;
        }
        hideInputMethod();
        resetFocusable();
    }

    private void resetFocusable() {
        mColorTemperatureEt.setFocusable(true);
        mColorTemperatureEt.setFocusableInTouchMode(true);
        mRedGainEt.setFocusable(true);
        mRedGainEt.setFocusableInTouchMode(true);
        mGreenGainEt.setFocusable(true);
        mGreenGainEt.setFocusableInTouchMode(true);
        mBlueGainEt.setFocusable(true);
        mBlueGainEt.setFocusableInTouchMode(true);
    }

    private void hideInputMethod() {
        if (mInputMethodManager == null) {
            mInputMethodManager = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
        }
        mInputMethodManager.hideSoftInputFromWindow(mView.getWindowToken(), 0);
    }

    public void setTouchLockClickListener(OnClickListener listener) {
        mClickListener = listener;
    }

    public interface OnClickListener {
        void onClickDismiss();
    }


    /**
     * 根据配置的标准、冷色、暖色三种模式的值
     * 通过插值法生成
     */
    private void initColorTemperature() {
        if (Constant.IS_BS65A) {
//            mColorTemperatureArray = COLORS_BS65A;
            mStandardValueIndex = 10;
        } else if (Constant.IS_75) {
//            mColorTemperatureArray = COLORS_BS75A;
            mStandardValueIndex = 12;
        } else if (Constant.IS_86) {
//            mColorTemperatureArray = COLORS_BS86A;
            mStandardValueIndex = 15;
        } else if (Constant.IS_110) {
//            mColorTemperatureArray = COLORS_BS110A;
            mStandardValueIndex = 17;
        }
        int[] startColor = {112, 116, 128}; // Starting color for the first transition
        int[] middleColor = {123, 124, 125}; // Middle color
        int[] endColor = {128, 128, 120}; // Ending color for the second transition
        if (Constant.IS_BS65A) {
            startColor = COLOR_TEMPERATURE_COLD_BS65A;
            middleColor = COLOR_TEMPERATURE_STANDARD_BS65A;
            endColor = COLOR_TEMPERATURE_WARM_BS65A;
        } else if (Constant.IS_75) {
            startColor = COLOR_TEMPERATURE_COLD_BS75A;
            middleColor = COLOR_TEMPERATURE_STANDARD_BS75A;
            endColor = COLOR_TEMPERATURE_WARM_BS75A;
        } else if (Constant.IS_86) {
            startColor = COLOR_TEMPERATURE_COLD_BS86A;
            middleColor = COLOR_TEMPERATURE_STANDARD_BS86A;
            endColor = COLOR_TEMPERATURE_WARM_BS86A;
        } else if (Constant.IS_110) {
            startColor = COLOR_TEMPERATURE_COLD_BS110A;
            middleColor = COLOR_TEMPERATURE_STANDARD_BS110A;
            endColor = COLOR_TEMPERATURE_WARM_BS110A;
        }
        // Calculate the first transition
        for (int i = 0; i < mStandardValueIndex; i++) {
            mColorTemperatureArray[i][0] = interpolate(startColor[0], middleColor[0], i, mStandardValueIndex);
            mColorTemperatureArray[i][1] = interpolate(startColor[1], middleColor[1], i, mStandardValueIndex);
            mColorTemperatureArray[i][2] = interpolate(startColor[2], middleColor[2], i, mStandardValueIndex);
        }
        // Set the middle value
        mColorTemperatureArray[mStandardValueIndex] = new int[]{middleColor[0], middleColor[1], middleColor[2]};
        // Calculate the second transition
        for (int i = mStandardValueIndex + 1; i < mColorTemperatureArray.length; i++) {
            mColorTemperatureArray[i][0] = interpolate(middleColor[0], endColor[0], i - mStandardValueIndex, TOTAL_COLORS - mStandardValueIndex - 1);
            mColorTemperatureArray[i][1] = interpolate(middleColor[1], endColor[1], i - mStandardValueIndex, TOTAL_COLORS - mStandardValueIndex - 1);
            mColorTemperatureArray[i][2] = interpolate(middleColor[2], endColor[2], i - mStandardValueIndex, TOTAL_COLORS - mStandardValueIndex - 1);
        }
        /*for (int i = 0; i < mColorTemperatureArray.length; i++) {
            Log.d(TAG, "i=" + i + " : " + mColorTemperatureArray[i][0] + "," + mColorTemperatureArray[i][1] + "," + mColorTemperatureArray[i][2] + "\n");
        }*/
    }

    /**
     * 插值法
     */
    private int interpolate(int start, int end, int step, int totalSteps) {
        return Math.round(start + 1.0f * (end - start) * step / totalSteps);
    }

    /**
     * 设置护眼模式
     */
    public void enableEyeCare(boolean enable) {
        mIsEyeCare = enable;
        if (enable) {
            //①先保存之前的值
            mRedGainValue = getRedGainValue();
            mGreenGainValue = getGreenGainValue();
            mBlueGainValue = getBlueGainValue();
            SaveDateUtils.saveColorTemperatureRgbGain(mContext, mRedGainValue, mGreenGainValue, mBlueGainValue);
            //②设置护眼模式对应对应值
            setRedGainValue(COLOR_TEMPERATURE_EYE_CARE[0]);
            setGreenGainValue(COLOR_TEMPERATURE_EYE_CARE[1]);
            setBlueGainValue(COLOR_TEMPERATURE_EYE_CARE[2]);
        } else {
            int[] colorTemperature = SaveDateUtils.getColorTemperatureRgbGain(mContext);
            mRedGainValue = colorTemperature[0];
            mGreenGainValue = colorTemperature[1];
            mBlueGainValue = colorTemperature[2];
            setRedGainValue(mRedGainValue);
            setGreenGainValue(mGreenGainValue);
            setBlueGainValue(mBlueGainValue);
        }
        SaveDateUtils.setEyeCareEnable(mContext, mIsEyeCare);
    }

    public void showRgbValueWrongToast(Context context, String msg) {
        if (mToast == null) {
            mToast = new Toast(context);
            LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            mToastView = layoutInflater.inflate(R.layout.toast_fail_bg, null);
            mToastTv = mToastView.findViewById(R.id.txt_msg);
        }
        mToastTv.setText(msg);
        mToast.setView(mToastView);
        mToast.setGravity(Gravity.BOTTOM | Gravity.END, CommonUtils.dp2px(context, 125), CommonUtils.dp2px(context, 239));
        mToast.setDuration(Toast.LENGTH_SHORT);
        mToast.show();
    }
}
