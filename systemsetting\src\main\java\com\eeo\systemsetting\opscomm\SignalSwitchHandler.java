package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.util.Log;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.udisdk.UdiConstant;

/**
 * 信号源切换处理器
 * 负责处理Windows发送的信号源切换指令
 * 
 * 协议格式：
 * HDMI1切换：7F 08 99 A2 B3 C4 02 FF 01 0A CF (响应：7F 09 99 A2 B3 C4 02 FF 01 0A 01 CF)
 * HDMI2切换：7F 08 99 A2 B3 C4 02 FF 01 0B CF (响应：7F 09 99 A2 B3 C4 02 FF 01 0B 01 CF)
 * OPS内置电脑：7F 08 99 A2 B3 C4 02 FF 01 38 CF (响应：7F 09 99 A2 B3 C4 02 FF 01 38 01 CF)
 * Type-C切换：7F 08 99 A2 B3 C4 02 FF 01 61 CF (响应：7F 08 99 A2 B3 C4 02 FF 01 61 01 CF)
 */
public class SignalSwitchHandler {
    private static final String TAG = "SignalSwitchHandler";
    
    // 信号源切换相关常量
    private static final byte[] HDMI1_SWITCH_PATTERN = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0A, (byte) 0xCF
    };
    
    private static final byte[] HDMI2_SWITCH_PATTERN = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0B, (byte) 0xCF
    };
    
    private static final byte[] OPS_SWITCH_PATTERN = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x38, (byte) 0xCF
    };
    
    private static final byte[] TYPE_C_SWITCH_PATTERN = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x61, (byte) 0xCF
    };
    
    // 响应前缀
    private static final byte[] HDMI1_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0A
    };
    
    private static final byte[] HDMI2_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0B
    };
    
    private static final byte[] OPS_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x38
    };
    
    private static final byte[] TYPE_C_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x61
    };
    
    // 响应状态码
    private static final byte RESPONSE_SUCCESS = (byte) 0x01;
    private static final byte RESPONSE_FAILURE = (byte) 0x00;
    private static final byte FRAME_TAIL = (byte) 0xCF;
    
    // 信号源类型
    public static final int SIGNAL_TYPE_HDMI1 = 1;
    public static final int SIGNAL_TYPE_HDMI2 = 2;
    public static final int SIGNAL_TYPE_OPS = 3;
    public static final int SIGNAL_TYPE_TYPE_C = 4;
    
    private Context mContext;
    
    public SignalSwitchHandler(Context context) {
        mContext = context;
        Log.d(TAG, "SignalSwitchHandler initialized");
    }
    
    /**
     * 检查是否为信号源切换命令
     */
    public int getSignalSwitchType(byte[] packet) {
        if (packet == null) {
            return -1;
        }
        
        // 检查HDMI1切换命令
        if (packet.length == HDMI1_SWITCH_PATTERN.length && comparePackets(packet, HDMI1_SWITCH_PATTERN)) {
            Log.d(TAG, "Detected HDMI1 switch command");
            return SIGNAL_TYPE_HDMI1;
        }
        
        // 检查HDMI2切换命令
        if (packet.length == HDMI2_SWITCH_PATTERN.length && comparePackets(packet, HDMI2_SWITCH_PATTERN)) {
            Log.d(TAG, "Detected HDMI2 switch command");
            return SIGNAL_TYPE_HDMI2;
        }
        
        // 检查OPS内置电脑切换命令
        if (packet.length == OPS_SWITCH_PATTERN.length && comparePackets(packet, OPS_SWITCH_PATTERN)) {
            Log.d(TAG, "Detected OPS switch command");
            return SIGNAL_TYPE_OPS;
        }
        
        // 检查Type-C切换命令
        if (packet.length == TYPE_C_SWITCH_PATTERN.length && comparePackets(packet, TYPE_C_SWITCH_PATTERN)) {
            Log.d(TAG, "Detected Type-C switch command");
            return SIGNAL_TYPE_TYPE_C;
        }
        
        return -1;
    }
    
    /**
     * 处理信号源切换请求
     */
    public byte[] handleSignalSwitchCommand(int signalType) {
        Log.d(TAG, "Processing signal switch command, type: " + signalType);
        
        try {
            boolean success = false;
            
            switch (signalType) {
                case SIGNAL_TYPE_HDMI1:
                    success = switchToHDMI1();
                    return buildSignalSwitchResponse(HDMI1_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
                    
                case SIGNAL_TYPE_HDMI2:
                    success = switchToHDMI2();
                    return buildSignalSwitchResponse(HDMI2_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
                    
                case SIGNAL_TYPE_OPS:
                    success = switchToOPS();
                    return buildSignalSwitchResponse(OPS_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
                    
                case SIGNAL_TYPE_TYPE_C:
                    success = switchToTypeC();
                    return buildSignalSwitchResponse(TYPE_C_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
                    
                default:
                    Log.w(TAG, "Unknown signal type: " + signalType);
                    return null;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while handling signal switch command", e);
            // 返回失败响应
            switch (signalType) {
                case SIGNAL_TYPE_HDMI1:
                    return buildSignalSwitchResponse(HDMI1_RESPONSE_PREFIX, RESPONSE_FAILURE);
                case SIGNAL_TYPE_HDMI2:
                    return buildSignalSwitchResponse(HDMI2_RESPONSE_PREFIX, RESPONSE_FAILURE);
                case SIGNAL_TYPE_OPS:
                    return buildSignalSwitchResponse(OPS_RESPONSE_PREFIX, RESPONSE_FAILURE);
                case SIGNAL_TYPE_TYPE_C:
                    return buildSignalSwitchResponse(TYPE_C_RESPONSE_PREFIX, RESPONSE_FAILURE);
                default:
                    return null;
            }
        }
    }
    
    /**
     * 切换到HDMI1信号源
     */
    private boolean switchToHDMI1() {
        try {
            Log.i(TAG, "Switching to HDMI1 signal source...");
            
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
            Log.i(TAG, "HDMI1 signal switch completed successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to HDMI1", e);
            return false;
        }
    }
    
    /**
     * 切换到HDMI2信号源
     */
    private boolean switchToHDMI2() {
        try {
            Log.i(TAG, "Switching to HDMI2 signal source...");
            
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI2);
            Log.i(TAG, "HDMI2 signal switch completed successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to HDMI2", e);
            return false;
        }
    }
    
    /**
     * 切换到OPS内置电脑信号源
     */
    private boolean switchToOPS() {
        try {
            Log.i(TAG, "Switching to OPS signal source...");
            
            EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);      
            Log.i(TAG, "OPS signal switch completed successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to OPS", e);
            return false;
        }
    }

    /**
     * 切换到Type-C信号源
     */
    private boolean switchToTypeC() {
        try {
            Log.i(TAG, "Switching to Type-C signal source...");

            EeoApplication.udi.changeSource(UdiConstant.SOURCE_TYPE_C2);
            Log.i(TAG, "Type-C signal switch completed successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Failed to switch to Type-C", e);
            return false;
        }
    }
    
    /**
     * 比较两个数据包是否相同
     */
    private boolean comparePackets(byte[] packet1, byte[] packet2) {
        if (packet1.length != packet2.length) {
            return false;
        }
        
        for (int i = 0; i < packet1.length; i++) {
            if (packet1[i] != packet2[i]) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 构建信号源切换响应数据包
     */
    private byte[] buildSignalSwitchResponse(byte[] responsePrefix, byte statusCode) {
        byte[] response = new byte[responsePrefix.length + 2]; // +1状态码 +1帧尾
        
        // 复制响应前缀
        System.arraycopy(responsePrefix, 0, response, 0, responsePrefix.length);
        
        // 添加状态码
        response[responsePrefix.length] = statusCode;
        
        // 添加帧尾
        response[response.length - 1] = FRAME_TAIL;
        
        String statusText = (statusCode == RESPONSE_SUCCESS) ? "SUCCESS" : "FAILURE";
        Log.d(TAG, "Built signal switch response (" + statusText + "): " + bytesToHexString(response));
        
        return response;
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b & 0xFF));
        }
        return sb.toString().trim();
    }
} 