package com.eeo.ota.bean;

import android.os.Build;

import com.eeo.ota.util.Util;

public class Constant {
    /**
     * 机型
     * CXD11\BM110A : 110大屏
     * CXB01 : 视频盒子
     * BS86A : t982大屏
     * BS110A : t982大屏
     */
    public static final String MODEL = Build.MODEL;

    public static final boolean IS_CXD11 = MODEL.contains("CXD11") || MODEL.contains("BM110A");

    public static final boolean IS_BS110A = MODEL.contains("BS110A");
    public static final boolean IS_BS86A = MODEL.contains("BS86A");
    public static final boolean IS_BS75A = MODEL.contains("BS75A");
    public static final boolean IS_BS65A = MODEL.contains("BS65A");
    public static final boolean IS_BSP110A = MODEL.contains("BSP110A");
    public static final boolean IS_BSP86A = MODEL.contains("BSP86A");
    public static final boolean IS_BSP75A = MODEL.contains("BSP75A");

    //982方案的有BS86A、BS110A
    public static final boolean IS_982 = IS_BS110A || IS_BS86A || IS_BS75A || IS_BS65A
            || IS_BSP110A || IS_BSP86A || IS_BSP75A;

    /**
     * 海外版的版本号有EN
     */
    public static final boolean IS_SOUTHEAST_ASIA = Util.getCurrentVersion().contains("en") || Util.getCurrentVersion().contains("EN");

    public static final boolean IS_CXB01 = MODEL.contains("CXB01");

    public static final String PAYLOAD_BINARY_FILE_NAME = "payload.bin";
    public static final String PAYLOAD_METADATA_FILE_NAME = "payload_metadata.bin";
    public static final String PAYLOAD_PROPERTIES_FILE_NAME = "payload_properties.txt";

}
