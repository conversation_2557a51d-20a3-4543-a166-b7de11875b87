<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">SystemSetting</string>
    <string name="screen_title">Control Panel</string>
    <string name="screen_text">Mirroring</string>
    <string name="write_text">Note</string>
    <string name="setting">Settings</string>
    <string name="sign">Source</string>
    <string name="lock">Touch Lock</string>
    <string name="unlock">Unlock</string>
    <string name="click_lock">Locked</string>
    <string name="eye">Eye Care</string>
    <string name="shutdown">Shut Down</string>
    <string name="restart">Restart</string>
    <string name="resting">Sleep</string>
    <string name="help">Guide</string>
    <string name="desktop">Desktop</string>
    <string name="shutdown_title">Confirm to shut down?</string>
    <string name="restart_title">Confirm to restart?</string>
    <string name="shutdown_content">Once shut down, all running applications will end</string>
    <string name="restart_content">Once restarted, all running applications will end</string>
    <string name="restart_countdown_confirm">Restart(%ds)</string>
    <string name="cancel">Cancel</string>
    <string name="shutdown_countdown_title">Prompt</string>
    <string name="shutdown_countdown_content">Your Windows has not been shut down properly. \nDo you want to shut it down mandatorily?</string>
    <string name="shutdown_countdown_confirm">Shut down(%ds)</string>
    <string name="shutdown_countdown_cancel">Cancel</string>
    <string name="windows">Windows</string>
    <string name="windows_disabled">Windows (Disabled)</string>
    <string name="front_typec">USB C</string>
    <string name="behind_hdmi1">HDMI 1</string>
    <string name="behind_hdmi2">HDMI 2</string>
    <string name="network">Ethernet</string>
    <string name="wifi">Wi—Fi</string>
    <string name="switch_wifi">Wi-Fi</string>
    <string name="wifi_more">Wi-Fi Details</string>
    <string name="about">About</string>
    <string name="check_update">Update</string>
    <string name="mac_address">MAC Address</string>
    <string name="ip_setting">IP Setting</string>
    <string name="ip_address">IP</string>
    <string name="manual">Manual</string>
    <string name="subnet_mask">Subnet Mask</string>
    <string name="gateway">Gateway</string>
    <string name="DNS">DNS Server</string>
    <string name="DNS1">DNS 1</string>
    <string name="DNS2">DNS 2</string>
    <string name="confirm">OK</string>
    <string name="auto">Auto</string>
    <string name="toast_success">Network connected</string>
    <string name="toast_fail">Network connection failed</string>
    <string name="toast_set_ip_fail">Failed to set</string>
    <string name="device_name">Device Name</string>
    <string name="serial_number">Serial Number</string>
    <string name="resolution">Screen Resolution</string>
    <string name="running_memory">Running Memory</string>
    <string name="store_memory">Storage Memory</string>
    <string name="total">in total:&#160;</string>
    <string name="remaining">&#160;&#160;&#160;&#160;left:&#160;</string>
    <string name="android_version">Android Version</string>
    <string name="system_version">System Version</string>
    <string name="touch_version">Touch Version</string>
    <string name="touch_unplugged">Touch not recognized</string>
    <string name="company">Manufacturer</string>
    <string name="company_name">Beijing EEO Education Technology \nCo., Ltd.</string>
    <string name="service_email">Email</string>
    <string name="email"><EMAIL></string>
    <string name="service_hotline">Phone</string>
    <string name="hotline">************</string>
    <string name="window_host">System Restore</string>
    <string name="window_host_title">Confirm to restore the Windows system?</string>
    <string name="window_host_content">Once restored, all data in C Drive will be deleted.</string>
    <string name="privacy_policy">Data Protection and Privacy Policy</string>
    <string name="factory_reset">Reset</string>
    <string name="window_factory_reset_title">Confirm to reset the system?</string>
    <string name="window_factory_reset_content">Once reset, all data in Android will be deleted.</string>
    <string name="auto_update">Auto update</string>
    <string name="network_error">Network error, \nplease check the cable network or Wi-Fi connection. </string>
    <string name="check_network">Please check the cable network or Wi-Fi connection of the large screen</string>
    <string name="retry">Retry</string>
    <string name="checking_update">Checking for updates</string>
    <string name="update_description">Update Description:</string>
    <string name="update_msg">System update will take a few minutes. \nIt will be temporarily unavailable during the update process.</string>
    <string name="right_update">Update</string>
    <string name="download">Downloading, please wait...</string>
    <string name="cancel_download">Cancel</string>
    <string name="install_title">The latest version is downloaded. Click "Install" to continue the installation.</string>
    <string name="install_msg">System installation will take a few minutes. \nIt will be temporarily unavailable during the installation.</string>
    <string name="install_right">Install</string>
    <string name="wifi_network">Network</string>
    <string name="finish_lock">Unlocked</string>

    <string name="network_wifi_status_connected_no_internet">Connected but unable to access the internet</string>
    <string name="network_wifi_status_saved">Saved</string>
    <string name="network_wifi_status_idle" />
    <string name="network_wifi_status_disabled">Disactivated</string>
    <string name="network_wifi_status_network_failure">IP confuguration failed</string>
    <string name="network_wifi_status_wifi_failure">Wi-Fi connection failed</string>
    <string name="network_wifi_status_password_failure">Authentication failed</string>
    <string name="network_wifi_status_scanning">Scanning…</string>
    <string name="network_wifi_status_connecting">Connecting…</string>
    <string name="network_wifi_status_authenticating">Authentication in progress…</string>
    <string name="network_wifi_status_obtaining_ip_address">Accessing IP address…</string>
    <string name="network_wifi_status_connected">Connected</string>
    <string name="network_wifi_status_suspended">Paused</string>
    <string name="network_wifi_status_disconnecting">Disconnecting...</string>
    <string name="network_wifi_status_disconnected">Disconnected</string>
    <string name="network_wifi_status_failed">Failed</string>
    <string name="network_wifi_status_blocked">Blocked</string>
    <string name="network_wifi_status_verifying_poor_link">Temporarily closed (poor network)</string>
    <string name="password">Password</string>
    <string name="join">Join</string>
    <string name="wifi_front_str">Please enter the password of \"</string>
    <string name="wifi_behind_str">\"</string>
    <string name="password_error">* Wrong password</string>
    <string name="saved_network">Network saved</string>
    <string name="select_network">Select Wi-Fi</string>
    <string name="wifi_device_error">The Wi-Fi device is abnormal, please contact customer service for assistance. </string>
    <string name="save">Save</string>
    <string name="disconnect_network">Disconnect this network</string>
    <string name="forget_network">Remove this network</string>
    <string name="screen_name">Phone/computer mirroring</string>
    <string name="wireless_screen_name">Wireless mirroring</string>
    <string name="wireless_screen_disabled">Wireless mirroring (Disactivated)</string>
    <string name="wireless_screen_disabled_toast">Wireless mirroring is disactivated. \nPlease activate it in Settings if you need.</string>
    <string name="wireless_screen_enable">Enable wireless mirroring</string>
    <string name="wireless_screen_enable_pin_code">Enable mirroring code</string>
    <string name="wired_screen_name">Wired mirroring</string>
    <string name="screen_wifi_connect">Connect the below Wi-Fi:</string>
    <string name="screen_wifi_name">Name:&#160;</string>
    <string name="screen_wifi_eeo_guest">&#160;EEO-Guest</string>
    <string name="screen_wifi_password">Password:&#160;</string>
    <string name="screen_wifi_eeo_password">&#160;eeoguest123</string>
    <string name="screen_step_1">Download the Transcreen APP</string>
    <string name="screen_step_2">Connect hotspot\nName：%s\nPassword：%s</string>
    <string name="screen_step_3">Mirror to the board\nOpen Transcreen\nSelect【%s】to mirror</string>
    <string name="screen_msg1">Scan the QR code or\nenter transcreen.app\nto download the app</string>
    <string name="screen_pin_code">PIN Code：%s</string>
    <string name="screen_pin_code2">PIN Code</string>
    <string name="screen_permission_refuse">REFUSE</string>
    <string name="screen_air_play">AirPlay</string>
    <string name="not_network">The network is not connected</string>
    <string name="disconnect">Not connected</string>
    <string name="no_sign">No signal</string>
    <string name="current_sign">Current Channel [Windows]</string>
    <string name="current_sign_windows_disabled">Current Channel [Windows](Disabled)</string>
    <string name="current_sign_hdmi">Current Channel [HDMI]</string>
    <string name="current_sign_hdmi_1">Current Channel [HDMI 1]</string>
    <string name="current_sign_hdmi_2">Current Channel [HDMI 2]</string>
    <string name="current_sign_typec">Current Channel [USB C]</string>
    <string name="change_to_pc">Switch to Windows</string>
    <string name="start_computer">Start Windows</string>
    <string name="no_sign_msg">No signal is recognized, please try to restart</string>
    <string name="no_sign_windows_disabled">Windows is disabled, you can enable it in "Settings-More"</string>
    <string name="no_sign_hdmi">No signal is recognized, please check \nwhether the signal cable is connected properly</string>
    <string name="no_pc_1">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:01)</string>
    <string name="no_pc_2">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:02)</string>

    <string name="home">Home</string>
    <string name="annotate">Note</string>
    <string name="projection">Mirroring</string>
    <string name="hint_no_control">You cannot controll under the small screen for the current signal source.</string>
    <string name="privacy">Privacy is under protection</string>
    <string name="maximize">Maximize</string>

    <string name="ready_install">Preparing for installation, please wait...</string>
    <string name="install_ing">Installing(%d%%), please wait...</string>
    <string name="install_fail">Installation failed</string>
    <string name="install_fail_reinstall">Installation failed, please try again</string>
    <string name="reinstall">Reinstall</string>
    <string name="have_update">New version found</string>
    <string name="have_update_hint">-System update requires a restart\n-This update fixes some major issues. \nPlease update for your normal use.</string>
    <string name="lastest_version">. It is the latest version</string>
    <string name="system_lastest_version">Your version is the latest version</string>
    <string name="updatable">Available version:</string>

    <string name="reset_ops">This restoration will take about 5 minutes. Please do not cut off the power during the restoration.</string>
    <string name="eeo_screen_move_txt">Click above or swipe up to restore full screen</string>

    <string name="extra">More</string>
    <string name="wireless_screen">Wireless mirroring</string>
    <string name="write_without_screen_on">Write without screen on</string>
    <string name="breath_led_on">Logo LED always on</string>
    <string name="touch_slider">Half-Screen Slider</string>
    <string name="windows_disable">Windows Disable</string>
    <string name="windows_task_manager">Windows Task Manager</string>

    <string name="factory_menu">Factory Menu</string>
    <string name="debug_menu">Debug Menu</string>
    <string name="touch_calibration">Touch Calibration</string>

    <string name="login_title">Login is required</string>
    <string name="login">Log in</string>
    <string name="login_cancel">Cancel</string>

    <string name="adb_title">Hidden Secrets</string>
    <string name="adb">Adb Switch</string>
    <string name="stroke_algorithm">Stroke Algorithm</string>
    <string name="write_acceleration">Write Acceleration</string>
    <string name="r30_write_speed">R30 Write Speed</string>
    <string name="screen_activation_status_getting">getting...</string>
    <string name="screen_activation_status_activated">activated</string>
    <string name="screen_activation_status_nonactivated">nonactivated</string>

    <string name="language_and_locale">Language</string>
    <string name="language_change">Languages</string>
    <string name="simplified_chinese">简体中文</string>
    <string name="english">English</string>

    <string-array name="languages">
        <item>简体中文</item>
        <item>English</item>
    </string-array>

    <string name="startup_channel">Startup Source</string>

    <string-array name="startup_channels">
        <item>Last Source</item>
        <item>Windows</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>

    <string-array name="startup_channels_windows_disabled">
        <item>Last Source</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>

    <string name="toast_resolution">%s&#160;&#160;&#160;%dX%d @ %dHz</string>
    <string name="uhd_hdmi">Please use HDMI 2.0 or higher cables \nthat meet the "UHD" signal specifications.</string>
    <string name="uhd_type_c">Please use fully-equipped Type-C cables \nthat meet the "UHD" signal specifications.</string>
    <string name="toast_shutting_down_ops">Windows is shutting down.</string>

    <string name="color_temperature_adjust">Color Temperature Adjusting</string>
    <string name="color_temperature">Color Temperature</string>
    <string name="color_temperature_cold">Cold</string>
    <string name="color_temperature_warm">Warm</string>
    <string name="rgb_red_gain">Red Gain</string>
    <string name="rgb_green_gain">Green Gain</string>
    <string name="rgb_blue_gain">Blue Gain</string>
    <string name="rgb_reset">Reset</string>
    <string name="color_temperature_value_wrong">The value should be %d to %d.</string>
    <string name="rgb_red_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="rgb_green_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="rgb_blue_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="color_temperature_eye_care">In Eye Care mode</string>

    <string name="hardware_self_test">Hardware self test</string>
    <string name="hardware_self_test_no_test">No Test</string>
    <string name="hardware_self_test_testing">Testing...</string>
    <string name="hardware_self_test_success">Success</string>
    <string name="hardware_self_test_fail">Fail</string>
    <string name="hardware_self_test_CPU">CPU</string>
    <string name="hardware_self_test_memory">Memory</string>
    <string name="hardware_self_test_hard_disk">Hard Disk</string>
    <string name="hardware_self_test_touch">Touch</string>
    <string name="hardware_self_test_ethernet">Ethernet</string>
    <string name="hardware_self_test_wifi">Wifi</string>
    <string name="hardware_self_test_mic">Mic</string>
</resources>