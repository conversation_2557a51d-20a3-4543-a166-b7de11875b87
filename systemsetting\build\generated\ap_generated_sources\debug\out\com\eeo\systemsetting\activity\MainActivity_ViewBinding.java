// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.activity;

import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.view.CustomerSeekBar;
import com.zyp.cardview.YcCardView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MainActivity_ViewBinding implements Unbinder {
  private MainActivity target;

  private View view7f08008f;

  private View view7f08008c;

  private View view7f08008b;

  private View view7f0801f8;

  private View view7f080091;

  private View view7f080062;

  private View view7f080090;

  private View view7f08008e;

  private View view7f080066;

  private View view7f080086;

  @UiThread
  public MainActivity_ViewBinding(MainActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MainActivity_ViewBinding(final MainActivity target, View source) {
    this.target = target;

    View view;
    target.frame = Utils.findRequiredViewAsType(source, R.id.frame, "field 'frame'", FrameLayout.class);
    target.rlBg = Utils.findRequiredViewAsType(source, R.id.rlBg, "field 'rlBg'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.fl_shutdown, "field 'flShutdown' and method 'onClick'");
    target.flShutdown = Utils.castView(view, R.id.fl_shutdown, "field 'flShutdown'", FrameLayout.class);
    view7f08008f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivShutdown = Utils.findRequiredViewAsType(source, R.id.iv_shutdown, "field 'ivShutdown'", ImageView.class);
    target.tvShutdown = Utils.findRequiredViewAsType(source, R.id.tv_shutdown, "field 'tvShutdown'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_restart, "field 'flRestart' and method 'onClick'");
    target.flRestart = Utils.castView(view, R.id.fl_restart, "field 'flRestart'", FrameLayout.class);
    view7f08008c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivRestart = Utils.findRequiredViewAsType(source, R.id.iv_restart, "field 'ivRestart'", ImageView.class);
    target.tvRestart = Utils.findRequiredViewAsType(source, R.id.tv_restart, "field 'tvRestart'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_rest, "field 'flRest' and method 'onClick'");
    target.flRest = Utils.castView(view, R.id.fl_rest, "field 'flRest'", FrameLayout.class);
    view7f08008b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivRest = Utils.findRequiredViewAsType(source, R.id.iv_rest, "field 'ivRest'", ImageView.class);
    target.tvRest = Utils.findRequiredViewAsType(source, R.id.tv_rest, "field 'tvRest'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_desktop, "field 'flDesktop' and method 'onClick'");
    target.flDesktop = Utils.castView(view, R.id.fl_desktop, "field 'flDesktop'", FrameLayout.class);
    view7f0801f8 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivDesktop = Utils.findRequiredViewAsType(source, R.id.iv_desktop, "field 'ivDesktop'", ImageView.class);
    target.tvDesktop = Utils.findRequiredViewAsType(source, R.id.tv_desktop, "field 'tvDesktop'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_touch_lock, "field 'flTouchLock' and method 'onClick'");
    target.flTouchLock = Utils.castView(view, R.id.fl_touch_lock, "field 'flTouchLock'", FrameLayout.class);
    view7f080091 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivTouchLock = Utils.findRequiredViewAsType(source, R.id.iv_touch_lock, "field 'ivTouchLock'", ImageView.class);
    target.txtTouchLock = Utils.findRequiredViewAsType(source, R.id.txt_touch_lock, "field 'txtTouchLock'", TextView.class);
    target.txtTitle = Utils.findRequiredViewAsType(source, R.id.txt_title, "field 'txtTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.cv_screen, "field 'cvScreen' and method 'onClick'");
    target.cvScreen = Utils.castView(view, R.id.cv_screen, "field 'cvScreen'", YcCardView.class);
    view7f080062 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivScreen = Utils.findRequiredViewAsType(source, R.id.iv_screen, "field 'ivScreen'", ImageView.class);
    target.txtScreen = Utils.findRequiredViewAsType(source, R.id.txt_screen, "field 'txtScreen'", TextView.class);
    target.line1 = Utils.findRequiredView(source, R.id.line1, "field 'line1'");
    view = Utils.findRequiredView(source, R.id.fl_signal, "field 'flSignal' and method 'onClick'");
    target.flSignal = Utils.castView(view, R.id.fl_signal, "field 'flSignal'", FrameLayout.class);
    view7f080090 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivSignal = Utils.findRequiredViewAsType(source, R.id.iv_signal, "field 'ivSignal'", ImageView.class);
    target.txtSignal = Utils.findRequiredViewAsType(source, R.id.txt_signal, "field 'txtSignal'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_setting, "field 'flSetting' and method 'onClick'");
    target.flSetting = Utils.castView(view, R.id.fl_setting, "field 'flSetting'", FrameLayout.class);
    view7f08008e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivSetting = Utils.findRequiredViewAsType(source, R.id.iv_setting, "field 'ivSetting'", ImageView.class);
    target.txtSetting = Utils.findRequiredViewAsType(source, R.id.txt_setting, "field 'txtSetting'", TextView.class);
    view = Utils.findRequiredView(source, R.id.cv_write, "field 'cvWrite' and method 'onClick'");
    target.cvWrite = Utils.castView(view, R.id.cv_write, "field 'cvWrite'", YcCardView.class);
    view7f080066 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivWrite = Utils.findRequiredViewAsType(source, R.id.iv_write, "field 'ivWrite'", ImageView.class);
    target.txtWrite = Utils.findRequiredViewAsType(source, R.id.txt_write, "field 'txtWrite'", TextView.class);
    view = Utils.findRequiredView(source, R.id.fl_eye, "field 'flEye' and method 'onClick'");
    target.flEye = Utils.castView(view, R.id.fl_eye, "field 'flEye'", FrameLayout.class);
    view7f080086 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.ivEye = Utils.findRequiredViewAsType(source, R.id.iv_eye, "field 'ivEye'", ImageView.class);
    target.txtEye = Utils.findRequiredViewAsType(source, R.id.txt_eye, "field 'txtEye'", TextView.class);
    target.cvBright = Utils.findRequiredViewAsType(source, R.id.cv_bright, "field 'cvBright'", YcCardView.class);
    target.cvVgSetting = Utils.findRequiredViewAsType(source, R.id.cv_vg_setting, "field 'cvVgSetting'", YcCardView.class);
    target.sbBright = Utils.findRequiredViewAsType(source, R.id.sb_bright, "field 'sbBright'", CustomerSeekBar.class);
    target.imgBright = Utils.findRequiredViewAsType(source, R.id.img_bright, "field 'imgBright'", ImageView.class);
    target.sbVoice = Utils.findRequiredViewAsType(source, R.id.sb_volume, "field 'sbVoice'", CustomerSeekBar.class);
    target.imgVoice = Utils.findRequiredViewAsType(source, R.id.img_voice, "field 'imgVoice'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    MainActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.frame = null;
    target.rlBg = null;
    target.flShutdown = null;
    target.ivShutdown = null;
    target.tvShutdown = null;
    target.flRestart = null;
    target.ivRestart = null;
    target.tvRestart = null;
    target.flRest = null;
    target.ivRest = null;
    target.tvRest = null;
    target.flDesktop = null;
    target.ivDesktop = null;
    target.tvDesktop = null;
    target.flTouchLock = null;
    target.ivTouchLock = null;
    target.txtTouchLock = null;
    target.txtTitle = null;
    target.cvScreen = null;
    target.ivScreen = null;
    target.txtScreen = null;
    target.line1 = null;
    target.flSignal = null;
    target.ivSignal = null;
    target.txtSignal = null;
    target.flSetting = null;
    target.ivSetting = null;
    target.txtSetting = null;
    target.cvWrite = null;
    target.ivWrite = null;
    target.txtWrite = null;
    target.flEye = null;
    target.ivEye = null;
    target.txtEye = null;
    target.cvBright = null;
    target.cvVgSetting = null;
    target.sbBright = null;
    target.imgBright = null;
    target.sbVoice = null;
    target.imgVoice = null;

    view7f08008f.setOnClickListener(null);
    view7f08008f = null;
    view7f08008c.setOnClickListener(null);
    view7f08008c = null;
    view7f08008b.setOnClickListener(null);
    view7f08008b = null;
    view7f0801f8.setOnClickListener(null);
    view7f0801f8 = null;
    view7f080091.setOnClickListener(null);
    view7f080091 = null;
    view7f080062.setOnClickListener(null);
    view7f080062 = null;
    view7f080090.setOnClickListener(null);
    view7f080090 = null;
    view7f08008e.setOnClickListener(null);
    view7f08008e = null;
    view7f080066.setOnClickListener(null);
    view7f080066 = null;
    view7f080086.setOnClickListener(null);
    view7f080086 = null;
  }
}
