package com.eeo.ota.callback;

public interface DownloadListener {
    void onDownloadProgress(int progress);

    void onDownloadCompleted(String outputFile);

    /**
     * @param errCode 腾讯云： -1:下载超时； -2:文件不存在； -3:签名过期； -4:校验错误； -5:更新固件失败
     *                翼鸥自定义：-6未发现新版本 -7没有网络
     */
    int ERR_CODE_NOT_FOUND_NEW_VERSION = -6;
    int ERR_CODE_NO_NETWORK = -7;

    void onDownloadFailure(int errCode);
}
