package com.eeo.ota.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import com.eeo.ota.Ota;
import com.eeo.ota.service.SubDeviceUpdateService;
import com.eeo.ota.util.Util;

public class BootReceiver extends BroadcastReceiver {
    public static final String TAG = "ota-BootReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "onReceive action:" + action);
        if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
            //子设备更新不在开机广播后处理，太慢了
//            startSubDeviceUpdateService(context);

            if (Util.shouldReportOtaSuccess(context)) {
                //更新完成删除update.zip
                Util.deleteUpdateFile();
                Ota ota = Ota.getInstance(context);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        ota.reportOtaSuccess();
                    }
                }, 3000);

            }
        }
    }

    private void startSubDeviceUpdateService(Context context) {
        Log.d(TAG, "startSubDeviceUpdateService");
        Intent intent = new Intent(context, SubDeviceUpdateService.class);
        try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                context.startService(intent);
            } else {
                context.startForegroundService(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "startSubDeviceUpdateService error:" + e);
        }
    }
}