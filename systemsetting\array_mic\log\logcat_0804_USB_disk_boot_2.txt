2025-08-04 11:38:38.487   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-04 11:38:38.487   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-04 11:38:38.487   856-856   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service and waiting for completion
2025-08-04 11:38:38.488   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-04 11:38:38.491   856-856   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:284 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:149 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:120 
2025-08-04 11:38:38.500   856-931   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@d606516 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-04 11:38:38.502   856-931   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@d606516 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-04 11:38:38.588   856-856   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-04 11:38:38.592   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-04 11:38:38.601   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-04 11:38:38.601   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-04 11:38:38.602   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 1/3)
2025-08-04 11:38:38.602   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-04 11:38:38.602   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-04 11:38:48.609   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-04 11:38:48.609   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-04 11:38:48.610   856-3127  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-04 11:38:52.879   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-04 11:38:52.886   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-04 11:38:52.886   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-04 11:38:52.886   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:39:03.997   856-856   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed on attempt 1/2. Retrying after delay...
2025-08-04 11:39:06.997   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:39:17.114   856-856   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed after 2 attempts. Checking USB device status...
2025-08-04 11:39:17.119   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 4
2025-08-04 11:39:17.119   856-856   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-04 11:39:17.119   856-856   ArrayMicOTA             com.eeo.systemsetting                W  USB device lost. Array mic may have been switched by other code.
2025-08-04 11:39:17.120   856-856   ArrayMicOTA             com.eeo.systemsetting                W  Overall update attempt 1 failed: USB device lost during ADB detection. Retrying...
2025-08-04 11:39:19.123   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 2/3)
2025-08-04 11:39:19.123   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Retry attempt, skipping initial delay and going directly to USB switch...
2025-08-04 11:39:19.123   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-04 11:39:19.123   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-04 11:39:19.125   856-3166  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-04 11:39:23.385   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-04 11:39:23.389   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-04 11:39:23.389   856-856   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-04 11:39:25.396   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-04 11:39:25.396   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-04 11:39:25.397   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-04 11:39:25.422   856-856   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-04 11:39:25.423   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-04 11:39:25.426   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-04 11:39:25.472   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-08-04 11:39:25.472   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-08-04 11:39:25.472   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-08-04 11:39:25.473   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-08-04 11:39:25.474   856-3187  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh
2025-08-04 11:39:25.531   856-3189  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: qpid ==>1388
2025-08-04 11:39:25.537   856-3187  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh] finished with exit code: 0
2025-08-04 11:39:27.539   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-08-04 11:39:27.541   856-3192  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-04 11:39:27.731   856-3192  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 0
2025-08-04 11:39:29.734   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-08-04 11:39:29.735   856-3198  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-08-04 11:39:29.774   856-3198  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /mnt/UDISK/*] finished with exit code: 0
2025-08-04 11:39:31.777   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-08-04 11:39:31.779   856-3204  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device push system/ota/mic_qdreamer/QH303_V197_20240712.swu /mnt/UDISK/
2025-08-04 11:39:32.941   856-3206  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [  0%] /mnt/UDISK/QH303_V197_20240712.swu

已手动删除这部分无关log

2025-08-04 11:39:32.948   856-3206  ArrayMicOTA             com.eeo
2025-08-04 11:39:44.555   856-3206  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [ 99%] /mnt/UDISK/QH303_V197_20240712.swu
2025-08-04 11:39:44.555   856-3206  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [100%] /mnt/UDISK/QH303_V197_20240712.swu
2025-08-04 11:39:44.594   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Validating firmware size. Local: 35676160, Remote: 35676160
2025-08-04 11:39:44.594   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Firmware validation successful.
2025-08-04 11:39:44.594   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: EXECUTING_UPGRADE
2025-08-04 11:39:44.595   856-3210  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery
2025-08-04 11:39:44.646   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: config new swupdate
2025-08-04 11:39:44.646   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_input: ##-i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery##
2025-08-04 11:39:47.545   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## set swupdate_param done ##
2025-08-04 11:39:47.651   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## Error: "swu_version" not defined
2025-08-04 11:39:47.652   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_param: ##-i /mnt/UDISK/QH303_V197_20240712.swu##
2025-08-04 11:39:47.653   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_software: ##stable##
2025-08-04 11:39:47.653   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_mode: ##upgrade_recovery##
2025-08-04 11:39:47.653   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###now do swupdate###
2025-08-04 11:39:47.653   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###log in /mnt/UDISK/swupdate.log###
2025-08-04 11:39:47.653   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## swupdate -v  -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery ##
2025-08-04 11:39:51.710   856-3212  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_next: ##reboot##
2025-08-04 11:39:52.317   856-3210  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery] finished with exit code: 0
2025-08-04 11:39:52.318   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_DISCONNECT
2025-08-04 11:39:52.342   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-08-04 11:39:52.342   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_CONNECT
2025-08-04 11:40:22.745   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Device reconnected after reboot.
2025-08-04 11:40:22.745   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: FINAL_VERSION_VALIDATION
2025-08-04 11:40:22.745   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Validating final version...
2025-08-04 11:40:22.791   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Update successful! New version: A013
2025-08-04 11:40:22.791   856-856   ArrayMicOTA             com.eeo.systemsetting                D  ArrayMicOTA: Setting SP key to completed (success)
2025-08-04 11:40:22.791   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: Update success.
2025-08-04 11:40:22.792   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Array mic update success in systemsetting module
2025-08-04 11:40:22.792   856-856   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-04 11:40:22.792   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity after array mic service completion
2025-08-04 11:40:22.843   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-08-04 11:40:22.843   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-08-04 11:40:22.843   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-08-04 11:40:22.853   856-3239  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-08-04 11:40:25.514   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-08-04 11:40:25.514   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-08-04 11:40:25.514   856-856   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-08-04 11:40:25.515   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Array mic update all finished in systemsetting module
2025-08-04 11:40:25.515   856-856   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-04 11:40:25.515   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity after array mic service completion
2025-08-04 11:40:25.629   856-856   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@92e4bae
2025-08-04 11:40:25.629   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-08-04 11:40:25.629   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-08-04 11:40:25.629   856-856   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.