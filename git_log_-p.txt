commit d5f927b88e7c6f17cb6f5f9d61f305ba2a783db5
Author: chensibo <<EMAIL>>
Date:   Mon Jul 14 11:20:38 2025 +0800

    [systemsetting]新增集控通信功能；响应mac地址、关机、信号源切换功能

diff --git a/systemsetting/build.gradle b/systemsetting/build.gradle
index 554d2a5..1dc6cd7 100644
--- a/systemsetting/build.gradle
+++ b/systemsetting/build.gradle
@@ -14,6 +14,17 @@ android {
 
         testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
         multiDexEnabled true // 启用 MultiDex
+		
+		// 添加JNI支持
+        externalNativeBuild {
+            cmake {
+                cppFlags "-std=c++14"
+                arguments "-DANDROID_STL=c++_shared"
+            }
+        }
+        ndk {
+            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
+        }
     }
 
     buildTypes {
@@ -26,6 +37,14 @@ android {
         sourceCompatibility JavaVersion.VERSION_1_8
         targetCompatibility JavaVersion.VERSION_1_8
     }
+	
+	// 配置外部原生构建
+    externalNativeBuild {
+        cmake {
+            path file('src/main/cpp/CMakeLists.txt')
+            version '3.10.2'
+        }
+    }
 
     //-------------添加android签名打包 begin -------------
     signingConfigs {
diff --git a/systemsetting/src/main/cpp/CMakeLists.txt b/systemsetting/src/main/cpp/CMakeLists.txt
new file mode 100644
index 0000000..5edf329
--- /dev/null
+++ b/systemsetting/src/main/cpp/CMakeLists.txt
@@ -0,0 +1,23 @@
+cmake_minimum_required(VERSION 3.4.1)
+
+# 设置项目名称
+project(serialport)
+
+# 添加源文件
+add_library(
+    serialport
+    SHARED
+    serialport.c
+)
+
+# 查找并链接日志库
+find_library(
+    log-lib
+    log
+)
+
+# 链接库
+target_link_libraries(
+    serialport
+    ${log-lib}
+) 
\ No newline at end of file
diff --git a/systemsetting/src/main/cpp/serialport.c b/systemsetting/src/main/cpp/serialport.c
new file mode 100644
index 0000000..18fabdf
--- /dev/null
+++ b/systemsetting/src/main/cpp/serialport.c
@@ -0,0 +1,346 @@
+#include <jni.h>
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <unistd.h>
+#include <fcntl.h>
+#include <termios.h>
+#include <errno.h>
+#include <sys/ioctl.h>
+#include <android/log.h>
+
+#define TAG "SerialPortJNI"
+#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)
+#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
+#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
+#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)
+
+/**
+ * 打开串口设备
+ */
+JNIEXPORT jint JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeOpenSerialPort(JNIEnv *env, jobject thiz, jstring devicePath) {
+    const char *path = (*env)->GetStringUTFChars(env, devicePath, NULL);
+    if (path == NULL) {
+        LOGE("Failed to get device path string");
+        return -1;
+    }
+
+    LOGI("Opening serial port: %s", path);
+
+    // 使用O_RDWR | O_NOCTTY | O_NDELAY标志打开设备
+    // O_RDWR: 读写模式
+    // O_NOCTTY: 不将设备设为控制终端
+    // O_NDELAY: 非阻塞模式
+    int fd = open(path, O_RDWR | O_NOCTTY | O_NDELAY);
+    
+    if (fd < 0) {
+        LOGE("Failed to open serial port %s: %s", path, strerror(errno));
+        (*env)->ReleaseStringUTFChars(env, devicePath, path);
+        return -1;
+    }
+
+    // 设置为阻塞模式（清除O_NDELAY标志）
+    int flags = fcntl(fd, F_GETFL, 0);
+    if (flags == -1) {
+        LOGE("Failed to get file flags: %s", strerror(errno));
+        close(fd);
+        (*env)->ReleaseStringUTFChars(env, devicePath, path);
+        return -1;
+    }
+    
+    if (fcntl(fd, F_SETFL, flags & ~O_NDELAY) == -1) {
+        LOGE("Failed to set file flags: %s", strerror(errno));
+        close(fd);
+        (*env)->ReleaseStringUTFChars(env, devicePath, path);
+        return -1;
+    }
+
+    LOGI("Serial port opened successfully, fd: %d", fd);
+    (*env)->ReleaseStringUTFChars(env, devicePath, path);
+    return fd;
+}
+
+/**
+ * 配置串口参数
+ */
+JNIEXPORT jint JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeConfigureSerialPort(JNIEnv *env, jobject thiz, 
+                                                                                  jint fd, jint baudRate, 
+                                                                                  jint dataBits, jint stopBits, 
+                                                                                  jint parity) {
+    struct termios options;
+    
+    LOGI("Configuring serial port fd=%d, baud=%d, data=%d, stop=%d, parity=%d", 
+         fd, baudRate, dataBits, stopBits, parity);
+
+    // 获取当前配置
+    if (tcgetattr(fd, &options) != 0) {
+        LOGE("Failed to get terminal attributes: %s", strerror(errno));
+        return -1;
+    }
+
+    // 设置为原始模式（RAW模式）
+    cfmakeraw(&options);
+
+    // 设置波特率
+    speed_t speed;
+    switch (baudRate) {
+        case 9600:   speed = B9600;   break;
+        case 19200:  speed = B19200;  break;
+        case 38400:  speed = B38400;  break;
+        case 57600:  speed = B57600;  break;
+        case 115200: speed = B115200; break;
+        default:
+            LOGE("Unsupported baud rate: %d", baudRate);
+            return -2;
+    }
+    
+    if (cfsetispeed(&options, speed) != 0 || cfsetospeed(&options, speed) != 0) {
+        LOGE("Failed to set baud rate: %s", strerror(errno));
+        return -3;
+    }
+
+    // 控制标志 (c_cflag)
+    options.c_cflag &= ~CSIZE;  // 清除数据位掩码
+    
+    // 设置数据位
+    switch (dataBits) {
+        case 5: options.c_cflag |= CS5; break;
+        case 6: options.c_cflag |= CS6; break;
+        case 7: options.c_cflag |= CS7; break;
+        case 8: options.c_cflag |= CS8; break;
+        default:
+            LOGE("Unsupported data bits: %d", dataBits);
+            return -4;
+    }
+
+    // 设置停止位
+    if (stopBits == 1) {
+        options.c_cflag &= ~CSTOPB;  // 1个停止位
+    } else if (stopBits == 2) {
+        options.c_cflag |= CSTOPB;   // 2个停止位
+    } else {
+        LOGE("Unsupported stop bits: %d", stopBits);
+        return -5;
+    }
+
+    // 设置校验位
+    options.c_cflag &= ~PARENB;  // 清除校验位
+    options.c_cflag &= ~PARODD;  // 清除奇校验
+    if (parity == 1) {
+        options.c_cflag |= PARENB;   // 偶校验
+    } else if (parity == 2) {
+        options.c_cflag |= PARENB | PARODD;  // 奇校验
+    }
+    // parity == 0 时无校验（已清除）
+
+    // 设置其他控制标志
+    options.c_cflag |= CLOCAL;   // 本地连接，不使用调制解调器控制
+    options.c_cflag |= CREAD;    // 使能接收器
+
+    // 输入标志 (c_iflag) - 完全禁用所有输入处理
+    options.c_iflag = 0;
+    options.c_iflag &= ~(IGNBRK | BRKINT | PARMRK | ISTRIP | INLCR | IGNCR | ICRNL | IXON | IXOFF | IXANY);
+
+    // 输出标志 (c_oflag) - 完全禁用所有输出处理
+    options.c_oflag = 0;
+    options.c_oflag &= ~OPOST;
+
+    // 本地标志 (c_lflag) - 完全禁用所有本地处理
+    options.c_lflag = 0;
+    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ECHOK | ECHONL | ISIG | IEXTEN);
+
+    // 设置读取参数
+    options.c_cc[VMIN] = 0;   // 非阻塞读取，最少0个字符
+    options.c_cc[VTIME] = 1;  // 超时时间100ms（1 * 100ms）
+
+    // 应用设置
+    if (tcsetattr(fd, TCSANOW, &options) != 0) {
+        LOGE("Failed to set terminal attributes: %s", strerror(errno));
+        return -6;
+    }
+
+    // 验证设置是否生效
+    struct termios verify_options;
+    if (tcgetattr(fd, &verify_options) == 0) {
+        LOGI("Serial port configuration verified:");
+        LOGI("  Input flags: 0x%08x", verify_options.c_iflag);
+        LOGI("  Output flags: 0x%08x", verify_options.c_oflag);
+        LOGI("  Control flags: 0x%08x", verify_options.c_cflag);
+        LOGI("  Local flags: 0x%08x", verify_options.c_lflag);
+        LOGI("  VMIN: %d, VTIME: %d", verify_options.c_cc[VMIN], verify_options.c_cc[VTIME]);
+    }
+
+    LOGI("Serial port configured successfully");
+    return 0;
+}
+
+/**
+ * 清空串口缓冲区
+ */
+JNIEXPORT jint JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeFlushSerialBuffers(JNIEnv *env, jobject thiz, jint fd) {
+    LOGI("Flushing serial port buffers for fd=%d", fd);
+
+    // 清空输入和输出缓冲区
+    if (tcflush(fd, TCIOFLUSH) != 0) {
+        LOGE("Failed to flush serial buffers: %s", strerror(errno));
+        return -1;
+    }
+
+    // 等待所有输出数据传输完成
+    if (tcdrain(fd) != 0) {
+        LOGE("Failed to drain serial port: %s", strerror(errno));
+        return -2;
+    }
+
+    LOGI("Serial port buffers flushed successfully");
+    return 0;
+}
+
+/**
+ * 读取数据
+ */
+JNIEXPORT jint JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeReadData(JNIEnv *env, jobject thiz, 
+                                                                       jint fd, jbyteArray buffer, 
+                                                                       jint bufferSize) {
+    jbyte *nativeBuffer = (*env)->GetByteArrayElements(env, buffer, NULL);
+    if (nativeBuffer == NULL) {
+        LOGE("Failed to get byte array elements");
+        return -1;
+    }
+
+    // 使用read系统调用直接读取数据
+    ssize_t bytesRead = read(fd, nativeBuffer, bufferSize);
+    
+    if (bytesRead < 0) {
+        if (errno == EAGAIN || errno == EWOULDBLOCK) {
+            // 非阻塞模式下无数据可读
+            (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
+            return 0;
+        } else {
+            LOGE("Error reading from serial port: %s", strerror(errno));
+            (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
+            return -1;
+        }
+    }
+
+    if (bytesRead > 0) {
+        // LOGD("JNI read %zd bytes", bytesRead);
+        
+        /* === 详细RAW数据日志（已注释，调试时可启用） ===
+        // 打印原始字节数据进行验证
+        LOGI("JNI read %zd bytes:", bytesRead);
+        char hexStr[bytesRead * 3 + 1];
+        hexStr[0] = '\0';
+        for (int i = 0; i < bytesRead; i++) {
+            char temp[4];
+            snprintf(temp, sizeof(temp), "%02X ", (unsigned char)nativeBuffer[i]);
+            strcat(hexStr, temp);
+        }
+        LOGI("JNI raw bytes: %s", hexStr);
+
+        // 检查关键字节是否被转换
+        for (int i = 0; i < bytesRead; i++) {
+            unsigned char byte = (unsigned char)nativeBuffer[i];
+            if (byte == 0xC4) {
+                LOGI("Found original 0xC4 at position %d", i);
+            } else if (byte == 0xE4) {
+                LOGW("Found converted 0xE4 at position %d (should be 0xC4)", i);
+            } else if (byte == 0xCF) {
+                LOGI("Found original 0xCF at position %d", i);
+            } else if (byte == 0xEF) {
+                LOGW("Found converted 0xEF at position %d (should be 0xCF)", i);
+            }
+        }
+        === 详细RAW数据日志结束 === */
+    }
+
+    // 释放字节数组，JNI_COMMIT表示将修改提交到Java数组
+    (*env)->ReleaseByteArrayElements(env, buffer, nativeBuffer, 0);
+    
+    return (jint)bytesRead;
+}
+
+/**
+ * 写入数据
+ */
+JNIEXPORT jint JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeWriteData(JNIEnv *env, jobject thiz, 
+                                                                        jint fd, jbyteArray data, 
+                                                                        jint length) {
+    jbyte *nativeData = (*env)->GetByteArrayElements(env, data, NULL);
+    if (nativeData == NULL) {
+        LOGE("Failed to get byte array elements for writing");
+        return -1;
+    }
+
+    // LOGD("JNI writing %d bytes", length);
+    
+    /* === 详细写入数据日志（已注释，调试时可启用） ===
+    // 打印要发送的数据
+    LOGI("JNI writing %d bytes:", length);
+    char hexStr[length * 3 + 1];
+    hexStr[0] = '\0';
+    for (int i = 0; i < length; i++) {
+        char temp[4];
+        snprintf(temp, sizeof(temp), "%02X ", (unsigned char)nativeData[i]);
+        strcat(hexStr, temp);
+    }
+    LOGI("JNI write bytes: %s", hexStr);
+    === 详细写入数据日志结束 === */
+
+    // 使用write系统调用直接写入数据
+    ssize_t bytesWritten = write(fd, nativeData, length);
+    
+    if (bytesWritten < 0) {
+        LOGE("Error writing to serial port: %s", strerror(errno));
+        (*env)->ReleaseByteArrayElements(env, data, nativeData, JNI_ABORT);
+        return -1;
+    }
+
+    // 确保数据立即发送
+    if (fsync(fd) != 0) {
+        LOGW("Warning: Failed to sync serial port: %s", strerror(errno));
+    }
+
+    LOGI("JNI successfully wrote %zd bytes", bytesWritten);
+    
+    (*env)->ReleaseByteArrayElements(env, data, nativeData, JNI_ABORT);
+    return (jint)bytesWritten;
+}
+
+/**
+ * 关闭串口设备
+ */
+JNIEXPORT void JNICALL
+Java_com_eeo_systemsetting_opscomm_JniSerialPortManager_nativeCloseSerialPort(JNIEnv *env, jobject thiz, jint fd) {
+    LOGI("Closing serial port fd=%d", fd);
+    
+    if (fd >= 0) {
+        if (close(fd) != 0) {
+            LOGE("Error closing serial port: %s", strerror(errno));
+        } else {
+            LOGI("Serial port closed successfully");
+        }
+    } else {
+        LOGW("Invalid file descriptor for closing: %d", fd);
+    }
+}
+
+/**
+ * JNI库加载时的初始化函数
+ */
+JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
+    LOGI("SerialPort JNI library loaded");
+    return JNI_VERSION_1_6;
+}
+
+/**
+ * JNI库卸载时的清理函数
+ */
+JNIEXPORT void JNICALL JNI_OnUnload(JavaVM *vm, void *reserved) {
+    LOGI("SerialPort JNI library unloaded");
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/JniSerialPortManager.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/JniSerialPortManager.java
new file mode 100644
index 0000000..8d1b532
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/JniSerialPortManager.java
@@ -0,0 +1,497 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.util.Log;
+
+import java.util.concurrent.atomic.AtomicBoolean;
+
+/**
+ * JNI版本串口设备管理器
+ * 使用JNI直接操作/dev/ttyS2物理串口设备，避免Java层字符编码转换
+ * 
+ * 主要优势：
+ * 1. 绕过Java层的编码转换，确保字节级数据完整性
+ * 2. 使用Linux原生termios精确配置串口参数
+ * 3. 直接内存操作，避免多层缓冲导致的数据问题
+ * 4. 更高的实时性和更低的延迟
+ */
+public class JniSerialPortManager {
+    private static final String TAG = "JniSerialPortManager";
+    
+    // 加载本地库
+    static {
+        try {
+            System.loadLibrary("serialport");
+            Log.d(TAG, "Native library loaded successfully");
+        } catch (UnsatisfiedLinkError e) {
+            Log.e(TAG, "Failed to load native library", e);
+        }
+    }
+    
+    // 串口设备路径
+    private static final String SERIAL_DEVICE = "/dev/ttyS2";
+    
+    // 串口参数
+    private static final int BAUD_RATE = 9600;
+    private static final int DATA_BITS = 8;
+    private static final int STOP_BITS = 1;
+    private static final int PARITY = 0; // 无校验
+    
+    // stty命令用于确保串口初始状态正确（作为JNI配置的补充）
+    private static final String STTY_RAW_CMD = "stty -F /dev/ttyS2 raw " +
+            "-echo -echoe -echok -echonl -echoctl -echoprt -echoke " +
+            "-icrnl -inlcr -igncr -ixon -ixoff -ixany " +
+            "-ocrnl -onlcr -opost -ofill -olcuc " +
+            "-isig -icanon -iexten -brkint -inpck -istrip " +
+            "-parenb -parodd cs8 -hupcl -cstopb clocal cread " +
+            "min 1 time 0";
+    
+    private Context mContext;
+    private ProtocolHandler mProtocolHandler;
+    
+    // 本地串口文件描述符
+    private int mSerialFd = -1;
+    
+    // 线程控制
+    private Thread mReadThread;
+    private final AtomicBoolean mIsRunning = new AtomicBoolean(false);
+    private final AtomicBoolean mShouldStop = new AtomicBoolean(false);
+    
+    // 读取缓冲区大小
+    private static final int BUFFER_SIZE = 1024;
+    
+    // 统计信息
+    private long mTotalBytesReceived = 0;
+    private long mTotalBytesSent = 0;
+    private int mDataPacketCount = 0;
+
+    public JniSerialPortManager(Context context, ProtocolHandler protocolHandler) {
+        mContext = context;
+        mProtocolHandler = protocolHandler;
+    }
+
+    /**
+     * 启动JNI串口管理器
+     */
+    public synchronized boolean start() {
+        if (mIsRunning.get()) {
+            Log.w(TAG, "JniSerialPortManager is already running");
+            return true;
+        }
+
+        Log.d(TAG, "Starting JniSerialPortManager...");
+
+        try {
+            // 1. TODO: 暂时注释掉，视情况使用stty命令
+            // if (!executeSttyCommand()) {
+            //     Log.w(TAG, "Warning: stty command failed, continuing with JNI configuration");
+            // }
+
+            // 2. 打开串口设备
+            if (!openSerialPort()) {
+                Log.e(TAG, "Failed to open serial port");
+                return false;
+            }
+
+            // 3. 配置串口参数（JNI原生配置）
+            if (!configureSerialPort()) {
+                Log.e(TAG, "Failed to configure serial port");
+                closeSerialPort();
+                return false;
+            }
+
+            // 4. 清空缓冲区
+            if (!flushSerialBuffers()) {
+                Log.w(TAG, "Warning: Failed to flush serial buffers");
+            }
+
+            // 5. 启动读取线程
+            if (!startReadThread()) {
+                Log.e(TAG, "Failed to start read thread");
+                closeSerialPort();
+                return false;
+            }
+
+            mIsRunning.set(true);
+            Log.d(TAG, "JniSerialPortManager started successfully");
+            return true;
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while starting JniSerialPortManager", e);
+            closeSerialPort();
+            return false;
+        }
+    }
+
+    /**
+     * 停止JNI串口管理器
+     */
+    public synchronized void stop() {
+        if (!mIsRunning.get()) {
+            Log.w(TAG, "JniSerialPortManager is not running");
+            return;
+        }
+
+        Log.d(TAG, "Stopping JniSerialPortManager...");
+
+        try {
+            // 1. 设置停止标志
+            mShouldStop.set(true);
+
+            // 2. 等待读取线程结束
+            if (mReadThread != null && mReadThread.isAlive()) {
+                try {
+                    mReadThread.interrupt();
+                    mReadThread.join(3000); // 等待3秒
+                } catch (InterruptedException e) {
+                    Log.w(TAG, "Interrupted while waiting for read thread to stop");
+                    Thread.currentThread().interrupt();
+                }
+            }
+
+            // 3. 关闭串口设备
+            closeSerialPort();
+
+            // 4. 重置状态
+            mIsRunning.set(false);
+            mShouldStop.set(false);
+            mTotalBytesReceived = 0;
+            mTotalBytesSent = 0;
+            mDataPacketCount = 0;
+            
+            Log.d(TAG, "JniSerialPortManager stopped successfully");
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while stopping JniSerialPortManager", e);
+        }
+    }
+
+    /**
+     * 执行stty命令确保串口初始状态正确
+     */
+    private boolean executeSttyCommand() {
+        try {
+            Log.d(TAG, "Executing stty command to ensure serial port initial state");
+            Process process = Runtime.getRuntime().exec(STTY_RAW_CMD);
+            int exitCode = process.waitFor();
+            
+            if (exitCode == 0) {
+                Log.d(TAG, "stty command executed successfully");
+                return true;
+            } else {
+                Log.w(TAG, "stty command failed with exit code: " + exitCode);
+                return false;
+            }
+        } catch (Exception e) {
+            Log.w(TAG, "Exception executing stty command", e);
+            return false;
+        }
+    }
+
+    /**
+     * 打开串口设备
+     */
+    private boolean openSerialPort() {
+        try {
+            mSerialFd = nativeOpenSerialPort(SERIAL_DEVICE);
+            if (mSerialFd < 0) {
+                Log.e(TAG, "Failed to open serial port: " + SERIAL_DEVICE);
+                return false;
+            }
+            
+            Log.d(TAG, "Serial port opened successfully, fd: " + mSerialFd);
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while opening serial port", e);
+            return false;
+        }
+    }
+
+    /**
+     * 配置串口参数
+     */
+    private boolean configureSerialPort() {
+        try {
+            int result = nativeConfigureSerialPort(mSerialFd, BAUD_RATE, DATA_BITS, STOP_BITS, PARITY);
+            if (result != 0) {
+                Log.e(TAG, "Failed to configure serial port, error code: " + result);
+                return false;
+            }
+            
+            Log.d(TAG, "Serial port configured successfully: " + BAUD_RATE + " 8N1");
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while configuring serial port", e);
+            return false;
+        }
+    }
+
+    /**
+     * 清空串口缓冲区
+     */
+    private boolean flushSerialBuffers() {
+        try {
+            int result = nativeFlushSerialBuffers(mSerialFd);
+            if (result != 0) {
+                Log.w(TAG, "Failed to flush serial buffers, error code: " + result);
+                return false;
+            }
+            
+            Log.d(TAG, "Serial buffers flushed successfully");
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while flushing serial buffers", e);
+            return false;
+        }
+    }
+
+    /**
+     * 关闭串口设备
+     */
+    private void closeSerialPort() {
+        if (mSerialFd >= 0) {
+            try {
+                nativeCloseSerialPort(mSerialFd);
+                Log.d(TAG, "Serial port closed, fd: " + mSerialFd);
+            } catch (Exception e) {
+                Log.e(TAG, "Exception while closing serial port", e);
+            } finally {
+                mSerialFd = -1;
+            }
+        }
+    }
+
+    /**
+     * 启动读取线程
+     */
+    private boolean startReadThread() {
+        try {
+            mReadThread = new Thread(new Runnable() {
+                @Override
+                public void run() {
+                    readDataLoop();
+                }
+            }, "JniSerialPortReader");
+
+            mReadThread.start();
+            Log.d(TAG, "JNI read thread started successfully");
+            return true;
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while starting read thread", e);
+            return false;
+        }
+    }
+
+    /**
+     * JNI数据读取循环
+     */
+    private void readDataLoop() {
+        Log.d(TAG, "JNI read thread started");
+        
+        byte[] buffer = new byte[BUFFER_SIZE];
+
+        while (!mShouldStop.get() && !Thread.currentThread().isInterrupted()) {
+            try {
+                if (mSerialFd >= 0) {
+                    // 使用JNI方法读取数据，返回实际读取的字节数
+                    int bytesRead = nativeReadData(mSerialFd, buffer, BUFFER_SIZE);
+                    
+                    if (bytesRead > 0) {
+                        // 创建实际长度的数据副本
+                        byte[] receivedData = new byte[bytesRead];
+                        System.arraycopy(buffer, 0, receivedData, 0, bytesRead);
+                        
+                        // 更新统计信息
+                        mTotalBytesReceived += bytesRead;
+                        mDataPacketCount++;
+                        
+                        // 处理接收到的数据
+                        handleReceivedData(receivedData);
+                        
+                    } else if (bytesRead == 0) {
+                        // 没有数据，短暂休眠
+                        Thread.sleep(10);
+                    } else {
+                        // 读取错误
+                        Log.e(TAG, "Error reading data, error code: " + bytesRead);
+                        break;
+                    }
+                } else {
+                    Log.e(TAG, "Invalid serial port fd");
+                    break;
+                }
+
+            } catch (InterruptedException e) {
+                Log.d(TAG, "JNI read thread interrupted");
+                Thread.currentThread().interrupt();
+                break;
+            } catch (Exception e) {
+                Log.e(TAG, "Unexpected exception in JNI read loop", e);
+                break;
+            }
+        }
+
+        Log.d(TAG, "JNI read thread finished");
+    }
+
+    /**
+     * 处理接收到的数据
+     */
+    private void handleReceivedData(byte[] data) {
+        try {
+            // String hexString = bytesToHexString(data);
+            // Log.d(TAG, "Received data (" + data.length + " bytes): " + hexString);
+            
+            /* === 详细RAW数据日志（已注释，调试时可启用） ===
+            Log.i(TAG, "=== JNI RAW DATA PACKET #" + mDataPacketCount + " ===");
+            Log.i(TAG, "Raw bytes received (" + data.length + " bytes): " + hexString);
+            
+            // 字节完整性验证
+            StringBuilder byteAnalysis = new StringBuilder();
+            byteAnalysis.append("JNI byte analysis: ");
+            boolean hasConversion = false;
+            
+            for (int i = 0; i < data.length; i++) {
+                int byteValue = data[i] & 0xFF;
+                byteAnalysis.append(String.format("[%d]=%02X ", i, byteValue));
+                
+                // 检查是否有字节转换
+                if (byteValue == 0xE4 || byteValue == 0xEF) {
+                    hasConversion = true;
+                    byteAnalysis.append("(⚠️CONVERTED) ");
+                } else if (byteValue == 0xC4 || byteValue == 0xCF) {
+                    byteAnalysis.append("(✅ORIGINAL) ");
+                }
+            }
+            
+            Log.i(TAG, byteAnalysis.toString());
+            
+            if (hasConversion) {
+                Log.w(TAG, "*** ⚠️ BYTE CONVERSION DETECTED IN JNI DATA ***");
+            } else {
+                Log.i(TAG, "*** ✅ JNI DATA INTEGRITY CONFIRMED ***");
+            }
+            
+            Log.i(TAG, "Total received: " + mTotalBytesReceived + " bytes, " + mDataPacketCount + " packets");
+            Log.i(TAG, "=== END JNI RAW DATA PACKET ===");
+            === 详细RAW数据日志结束 === */
+
+            // 交给协议处理器处理
+            if (mProtocolHandler != null) {
+                mProtocolHandler.handleReceivedData(data);
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling received data", e);
+        }
+    }
+
+    /**
+     * 发送数据到串口
+     */
+    public boolean sendData(byte[] data) {
+        if (!mIsRunning.get() || mSerialFd < 0) {
+            Log.e(TAG, "Cannot send data: JniSerialPortManager not running or invalid fd");
+            return false;
+        }
+
+        try {
+            int bytesWritten = nativeWriteData(mSerialFd, data, data.length);
+            
+            if (bytesWritten == data.length) {
+                mTotalBytesSent += bytesWritten;
+                String hexString = bytesToHexString(data);
+                Log.d(TAG, "JNI sent data (" + data.length + " bytes): " + hexString);
+                return true;
+            } else {
+                Log.e(TAG, "Failed to send all data. Expected: " + data.length + ", Sent: " + bytesWritten);
+                return false;
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while sending data via JNI", e);
+            return false;
+        }
+    }
+
+    /**
+     * 获取统计信息
+     */
+    public String getStatistics() {
+        return String.format("JNI Serial Statistics - Received: %d bytes (%d packets), Sent: %d bytes", 
+                mTotalBytesReceived, mDataPacketCount, mTotalBytesSent);
+    }
+
+    /**
+     * 字节数组转十六进制字符串
+     */
+    private String bytesToHexString(byte[] bytes) {
+        StringBuilder sb = new StringBuilder();
+        for (byte b : bytes) {
+            sb.append(String.format("%02X ", b & 0xFF));
+        }
+        return sb.toString().trim();
+    }
+
+    /**
+     * 检查运行状态
+     */
+    public boolean isRunning() {
+        return mIsRunning.get();
+    }
+
+    // ======================== JNI 本地方法声明 ========================
+    
+    /**
+     * 打开串口设备
+     * @param devicePath 设备路径
+     * @return 文件描述符，失败返回-1
+     */
+    private native int nativeOpenSerialPort(String devicePath);
+
+    /**
+     * 配置串口参数
+     * @param fd 文件描述符
+     * @param baudRate 波特率
+     * @param dataBits 数据位
+     * @param stopBits 停止位
+     * @param parity 校验位
+     * @return 0成功，其他为错误码
+     */
+    private native int nativeConfigureSerialPort(int fd, int baudRate, int dataBits, int stopBits, int parity);
+
+    /**
+     * 清空串口缓冲区
+     * @param fd 文件描述符
+     * @return 0成功，其他为错误码
+     */
+    private native int nativeFlushSerialBuffers(int fd);
+
+    /**
+     * 读取数据
+     * @param fd 文件描述符
+     * @param buffer 接收缓冲区
+     * @param bufferSize 缓冲区大小
+     * @return 实际读取字节数，0表示无数据，负数表示错误
+     */
+    private native int nativeReadData(int fd, byte[] buffer, int bufferSize);
+
+    /**
+     * 写入数据
+     * @param fd 文件描述符
+     * @param data 要发送的数据
+     * @param length 数据长度
+     * @return 实际写入字节数，负数表示错误
+     */
+    private native int nativeWriteData(int fd, byte[] data, int length);
+
+    /**
+     * 关闭串口设备
+     * @param fd 文件描述符
+     */
+    private native void nativeCloseSerialPort(int fd);
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/MacAddressHandler.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/MacAddressHandler.java
new file mode 100644
index 0000000..df4ef92
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/MacAddressHandler.java
@@ -0,0 +1,401 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.net.wifi.WifiManager;
+import android.util.Log;
+
+import java.io.BufferedReader;
+import java.io.File;
+import java.io.FileReader;
+import java.net.NetworkInterface;
+import java.util.Collections;
+import java.util.List;
+
+/**
+ * MAC地址处理器
+ * 负责获取Android设备的WiFi和以太网MAC地址
+ * 针对Android 11的MAC地址访问限制进行优化
+ */
+public class MacAddressHandler {
+    private static final String TAG = "MacAddressHandler";
+    
+    private Context mContext;
+    
+    // 网络接口名称模式
+    private static final String[] WIFI_INTERFACE_PATTERNS = {"wlan", "wl"};
+    private static final String[] ETH_INTERFACE_PATTERNS = {"eth", "enp", "ens"};
+    
+    // 系统网络接口路径
+    private static final String NET_CLASS_PATH = "/sys/class/net/";
+    
+    // 缓存的MAC地址
+    private String mCachedWifiMac = null;
+    private String mCachedEthMac = null;
+    private long mLastCacheTime = 0;
+    private static final long CACHE_TIMEOUT = 30000; // 30秒缓存超时
+
+    public MacAddressHandler(Context context) {
+        mContext = context;
+        Log.d(TAG, "MacAddressHandler initialized");
+    }
+
+    /**
+     * 获取WiFi MAC地址
+     */
+    public String getWifiMacAddress() {
+        Log.d(TAG, "Getting WiFi MAC address...");
+        
+        try {
+            // 检查缓存
+            if (isCacheValid() && mCachedWifiMac != null) {
+                Log.d(TAG, "Returning cached WiFi MAC: " + mCachedWifiMac);
+                return mCachedWifiMac;
+            }
+            
+            String wifiMac = getWifiMacFromMultipleSources();
+            
+            if (wifiMac != null && !wifiMac.isEmpty()) {
+                mCachedWifiMac = wifiMac;
+                mLastCacheTime = System.currentTimeMillis();
+                Log.d(TAG, "WiFi MAC address obtained: " + wifiMac);
+                return wifiMac;
+            } else {
+                Log.w(TAG, "Failed to obtain WiFi MAC address");
+                return null;
+            }
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while getting WiFi MAC address", e);
+            return null;
+        }
+    }
+
+    /**
+     * 获取以太网MAC地址
+     */
+    public String getEthMacAddress() {
+        Log.d(TAG, "Getting Ethernet MAC address...");
+        
+        try {
+            // 检查缓存
+            if (isCacheValid() && mCachedEthMac != null) {
+                Log.d(TAG, "Returning cached Ethernet MAC: " + mCachedEthMac);
+                return mCachedEthMac;
+            }
+            
+            String ethMac = getEthMacFromMultipleSources();
+            
+            if (ethMac != null && !ethMac.isEmpty()) {
+                mCachedEthMac = ethMac;
+                mLastCacheTime = System.currentTimeMillis();
+                Log.d(TAG, "Ethernet MAC address obtained: " + ethMac);
+                return ethMac;
+            } else {
+                Log.w(TAG, "Failed to obtain Ethernet MAC address");
+                return null;
+            }
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while getting Ethernet MAC address", e);
+            return null;
+        }
+    }
+
+    /**
+     * 检查缓存是否有效
+     */
+    private boolean isCacheValid() {
+        return (System.currentTimeMillis() - mLastCacheTime) < CACHE_TIMEOUT;
+    }
+
+    /**
+     * 从多个来源获取WiFi MAC地址
+     */
+    private String getWifiMacFromMultipleSources() {
+        Log.d(TAG, "Trying multiple sources for WiFi MAC...");
+        
+        // 方法1：通过NetworkInterface获取
+        String mac = getMacFromNetworkInterface(WIFI_INTERFACE_PATTERNS);
+        if (mac != null) {
+            Log.d(TAG, "WiFi MAC from NetworkInterface: " + mac);
+            return mac;
+        }
+        
+        // 方法2：通过文件系统获取
+        mac = getMacFromFileSystem(WIFI_INTERFACE_PATTERNS);
+        if (mac != null) {
+            Log.d(TAG, "WiFi MAC from FileSystem: " + mac);
+            return mac;
+        }
+        
+        // 方法3：通过WifiManager获取（Android 11可能受限）
+        mac = getMacFromWifiManager();
+        if (mac != null) {
+            Log.d(TAG, "WiFi MAC from WifiManager: " + mac);
+            return mac;
+        }
+        
+        Log.w(TAG, "All WiFi MAC acquisition methods failed");
+        return null;
+    }
+
+    /**
+     * 从多个来源获取以太网MAC地址
+     */
+    private String getEthMacFromMultipleSources() {
+        Log.d(TAG, "Trying multiple sources for Ethernet MAC...");
+        
+        // 方法1：通过NetworkInterface获取
+        String mac = getMacFromNetworkInterface(ETH_INTERFACE_PATTERNS);
+        if (mac != null) {
+            Log.d(TAG, "Ethernet MAC from NetworkInterface: " + mac);
+            return mac;
+        }
+        
+        // 方法2：通过文件系统获取
+        mac = getMacFromFileSystem(ETH_INTERFACE_PATTERNS);
+        if (mac != null) {
+            Log.d(TAG, "Ethernet MAC from FileSystem: " + mac);
+            return mac;
+        }
+        
+        Log.w(TAG, "All Ethernet MAC acquisition methods failed");
+        return null;
+    }
+
+    /**
+     * 通过NetworkInterface获取MAC地址
+     */
+    private String getMacFromNetworkInterface(String[] patterns) {
+        try {
+            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
+            
+            for (NetworkInterface networkInterface : interfaces) {
+                String interfaceName = networkInterface.getName();
+                
+                // 检查接口名称是否匹配模式
+                if (matchesPatterns(interfaceName, patterns)) {
+                    byte[] macBytes = networkInterface.getHardwareAddress();
+                    
+                    if (macBytes != null && macBytes.length == 6) {
+                        String mac = formatMacAddress(macBytes);
+                        if (isValidMacAddress(mac)) {
+                            Log.d(TAG, "Found MAC for interface " + interfaceName + ": " + mac);
+                            return mac;
+                        }
+                    }
+                }
+            }
+            
+        } catch (Exception e) {
+            Log.w(TAG, "Exception in getMacFromNetworkInterface", e);
+        }
+        
+        return null;
+    }
+
+    /**
+     * 通过文件系统获取MAC地址
+     */
+    private String getMacFromFileSystem(String[] patterns) {
+        try {
+            File netDir = new File(NET_CLASS_PATH);
+            if (!netDir.exists() || !netDir.isDirectory()) {
+                Log.w(TAG, "Network class directory does not exist: " + NET_CLASS_PATH);
+                return null;
+            }
+            
+            File[] interfaces = netDir.listFiles();
+            if (interfaces == null) {
+                Log.w(TAG, "No network interfaces found in " + NET_CLASS_PATH);
+                return null;
+            }
+            
+            for (File interfaceDir : interfaces) {
+                String interfaceName = interfaceDir.getName();
+                
+                // 检查接口名称是否匹配模式
+                if (matchesPatterns(interfaceName, patterns)) {
+                    File addressFile = new File(interfaceDir, "address");
+                    
+                    if (addressFile.exists() && addressFile.canRead()) {
+                        String mac = readMacFromFile(addressFile);
+                        if (isValidMacAddress(mac)) {
+                            Log.d(TAG, "Found MAC for interface " + interfaceName + " from file: " + mac);
+                            return mac;
+                        }
+                    }
+                }
+            }
+            
+        } catch (Exception e) {
+            Log.w(TAG, "Exception in getMacFromFileSystem", e);
+        }
+        
+        return null;
+    }
+
+    /**
+     * 通过WifiManager获取MAC地址（Android 11可能受限）
+     */
+    private String getMacFromWifiManager() {
+        try {
+            WifiManager wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
+            if (wifiManager == null) {
+                Log.w(TAG, "WifiManager is null");
+                return null;
+            }
+            
+            // Android 11+可能返回固定的值"02:00:00:00:00:00"
+            android.net.wifi.WifiInfo wifiInfo = wifiManager.getConnectionInfo();
+            if (wifiInfo != null) {
+                String mac = wifiInfo.getMacAddress();
+                if (isValidMacAddress(mac) && !"02:00:00:00:00:00".equals(mac)) {
+                    return formatMacAddress(mac);
+                } else {
+                    Log.w(TAG, "WifiManager returned invalid or restricted MAC: " + mac);
+                }
+            }
+            
+        } catch (Exception e) {
+            Log.w(TAG, "Exception in getMacFromWifiManager", e);
+        }
+        
+        return null;
+    }
+
+    /**
+     * 从文件读取MAC地址
+     */
+    private String readMacFromFile(File file) {
+        BufferedReader reader = null;
+        try {
+            reader = new BufferedReader(new FileReader(file));
+            String line = reader.readLine();
+            if (line != null) {
+                return line.trim();
+            }
+        } catch (Exception e) {
+            Log.w(TAG, "Exception reading MAC from file: " + file.getPath(), e);
+        } finally {
+            if (reader != null) {
+                try {
+                    reader.close();
+                } catch (Exception e) {
+                    // 忽略关闭异常
+                }
+            }
+        }
+        return null;
+    }
+
+    /**
+     * 检查接口名称是否匹配模式
+     */
+    private boolean matchesPatterns(String interfaceName, String[] patterns) {
+        if (interfaceName == null || interfaceName.isEmpty()) {
+            return false;
+        }
+        
+        String lowerName = interfaceName.toLowerCase();
+        for (String pattern : patterns) {
+            if (lowerName.startsWith(pattern.toLowerCase())) {
+                return true;
+            }
+        }
+        
+        return false;
+    }
+
+    /**
+     * 验证MAC地址是否有效
+     */
+    private boolean isValidMacAddress(String mac) {
+        if (mac == null || mac.isEmpty()) {
+            return false;
+        }
+        
+        // 移除分隔符
+        String cleanMac = mac.replace(":", "").replace("-", "").replace(" ", "");
+        
+        // 检查长度
+        if (cleanMac.length() != 12) {
+            return false;
+        }
+        
+        // 检查是否为全零MAC
+        if ("000000000000".equals(cleanMac)) {
+            return false;
+        }
+        
+        // 检查是否为Android 11的限制MAC
+        if ("020000000000".equals(cleanMac)) {
+            return false;
+        }
+        
+        // 检查是否包含有效的十六进制字符
+        try {
+            Long.parseLong(cleanMac, 16);
+            return true;
+        } catch (NumberFormatException e) {
+            return false;
+        }
+    }
+
+    /**
+     * 格式化MAC地址
+     */
+    private String formatMacAddress(byte[] macBytes) {
+        if (macBytes == null || macBytes.length != 6) {
+            return null;
+        }
+        
+        StringBuilder sb = new StringBuilder();
+        for (int i = 0; i < macBytes.length; i++) {
+            if (i > 0) {
+                sb.append(":");
+            }
+            sb.append(String.format("%02X", macBytes[i] & 0xFF));
+        }
+        
+        return sb.toString();
+    }
+
+    /**
+     * 格式化MAC地址字符串
+     */
+    private String formatMacAddress(String mac) {
+        if (mac == null || mac.isEmpty()) {
+            return null;
+        }
+        
+        // 移除所有分隔符
+        String cleanMac = mac.replace(":", "").replace("-", "").replace(" ", "").toUpperCase();
+        
+        if (cleanMac.length() != 12) {
+            return null;
+        }
+        
+        // 添加冒号分隔符
+        StringBuilder sb = new StringBuilder();
+        for (int i = 0; i < cleanMac.length(); i += 2) {
+            if (i > 0) {
+                sb.append(":");
+            }
+            sb.append(cleanMac.substring(i, i + 2));
+        }
+        
+        return sb.toString();
+    }
+
+    /**
+     * 清除缓存（用于测试或强制刷新）
+     */
+    public void clearCache() {
+        mCachedWifiMac = null;
+        mCachedEthMac = null;
+        mLastCacheTime = 0;
+        Log.d(TAG, "MAC address cache cleared");
+    }
+}
+ 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/OpsCommManager.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/OpsCommManager.java
new file mode 100644
index 0000000..3ef8eaa
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/OpsCommManager.java
@@ -0,0 +1,248 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.util.Log;
+
+/**
+ * Windows串口通信管理器
+ * 负责处理Windows发送的MAC地址获取指令
+ * 采用与Rs232Manager一致的架构模式
+ * 
+ * 当前阶段：专注于MAC地址获取功能
+ * 后续阶段：扩展关机控制和信号源切换功能
+ * 
+ * 实现方式：使用JNI原生代码直接操作串口，确保字节级数据完整性
+ */
+public class OpsCommManager {
+    private static final String TAG = "OpsCommManager";
+    private Context mContext;
+    private static OpsCommManager mOpsCommManager = null;
+
+    // 核心组件
+    private JniSerialPortManager mSerialPortManager;  // JNI版本串口管理器
+    private ProtocolHandler mProtocolHandler;
+    private MacAddressHandler mMacAddressHandler;
+    private PowerControlHandler mPowerControlHandler;
+    private SignalSwitchHandler mSignalSwitchHandler;
+
+    // 运行状态
+    private volatile boolean mIsRunning = false;
+
+    // 统计信息
+    private long mCommandCount = 0;
+    private long mSuccessCount = 0;
+    private long mErrorCount = 0;
+
+    /**
+     * 私有构造函数，单例模式
+     */
+    private OpsCommManager(Context context) {
+        mContext = context.getApplicationContext();
+        init();
+    }
+
+    /**
+     * 获取单例实例
+     */
+    public static OpsCommManager getInstance(Context context) {
+        if (mOpsCommManager == null) {
+            synchronized (OpsCommManager.class) {
+                if (mOpsCommManager == null) {
+                    mOpsCommManager = new OpsCommManager(context);
+                }
+            }
+        }
+        return mOpsCommManager;
+    }
+
+    /**
+     * 初始化各组件
+     */
+    private void init() {
+        Log.d(TAG, "OpsCommManager initializing with JNI implementation...");
+        
+        try {
+            // 初始化MAC地址处理器
+            mMacAddressHandler = new MacAddressHandler(mContext);
+            
+            // 初始化关机控制处理器
+            mPowerControlHandler = new PowerControlHandler(mContext);
+            
+            // 初始化信号源切换处理器
+            mSignalSwitchHandler = new SignalSwitchHandler(mContext);
+            
+            // 初始化协议处理器（内部会创建PowerControlHandler，这里我们用自己的实例）
+            mProtocolHandler = new ProtocolHandler(mContext, mMacAddressHandler);
+            
+            // 初始化JNI串口管理器
+            mSerialPortManager = new JniSerialPortManager(mContext, mProtocolHandler);
+            
+            // 设置组件间的引用关系
+            mProtocolHandler.setOpsCommManager(this);
+            mProtocolHandler.setJniSerialPortManager(mSerialPortManager);
+            
+            Log.d(TAG, "OpsCommManager initialized successfully with JNI implementation, PowerControl and SignalSwitch support");
+        } catch (Exception e) {
+            Log.e(TAG, "Failed to initialize OpsCommManager", e);
+        }
+    }
+
+    /**
+     * 启动串口通信服务
+     */
+    public synchronized boolean start() {
+        if (mIsRunning) {
+            Log.w(TAG, "OpsCommManager is already running");
+            return true;
+        }
+
+        Log.d(TAG, "Starting OpsCommManager with JNI implementation...");
+        
+        try {
+            if (mSerialPortManager != null && mSerialPortManager.start()) {
+                mIsRunning = true;
+                Log.d(TAG, "OpsCommManager started successfully");
+                return true;
+            } else {
+                Log.e(TAG, "Failed to start JNI SerialPortManager");
+                return false;
+            }
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while starting OpsCommManager", e);
+            return false;
+        }
+    }
+
+    /**
+     * 停止串口通信服务
+     */
+    public synchronized void stop() {
+        if (!mIsRunning) {
+            Log.w(TAG, "OpsCommManager is not running");
+            return;
+        }
+
+        Log.d(TAG, "Stopping OpsCommManager...");
+        
+        try {
+            if (mSerialPortManager != null) {
+                mSerialPortManager.stop();
+            }
+            mIsRunning = false;
+            Log.d(TAG, "OpsCommManager stopped successfully");
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while stopping OpsCommManager", e);
+        }
+    }
+
+    /**
+     * 检查运行状态
+     */
+    public boolean isRunning() {
+        return mIsRunning;
+    }
+
+    /**
+     * 手动获取以太网MAC地址（用于测试）
+     */
+    public String getEthMacAddress() {
+        if (mMacAddressHandler != null) {
+            return mMacAddressHandler.getEthMacAddress();
+        }
+        return null;
+    }
+
+    /**
+     * 获取关机控制处理器（用于外部访问）
+     */
+    public PowerControlHandler getPowerControlHandler() {
+        return mPowerControlHandler;
+    }
+
+    /**
+     * 获取信号源切换处理器（用于外部访问）
+     */
+    public SignalSwitchHandler getSignalSwitchHandler() {
+        return mSignalSwitchHandler;
+    }
+
+    /**
+     * 获取当前使用的实现类型
+     */
+    public String getCurrentImplementation() {
+        return "JNI (Native)";
+    }
+
+    /**
+     * 获取详细统计信息
+     */
+    public String getDetailedStatistics() {
+        StringBuilder sb = new StringBuilder();
+        sb.append("=== OpsCommManager Statistics ===\n");
+        sb.append("Implementation: JNI (Native)\n");
+        sb.append("Running: ").append(isRunning()).append("\n");
+        sb.append("Commands: ").append(mCommandCount).append("\n");
+        sb.append("Success: ").append(mSuccessCount).append("\n");
+        sb.append("Errors: ").append(mErrorCount).append("\n");
+        sb.append("Success Rate: ").append(String.format("%.2f%%", 
+                mCommandCount > 0 ? (mSuccessCount * 100.0 / mCommandCount) : 0.0)).append("\n");
+        
+        // 添加JNI串口管理器统计信息
+        if (mSerialPortManager != null) {
+            sb.append("JNI Statistics: ").append(mSerialPortManager.getStatistics()).append("\n");
+        }
+        
+        return sb.toString();
+    }
+
+    /**
+     * 获取统计信息
+     */
+    public String getStatistics() {
+        return String.format("Commands: %d, Success: %d, Errors: %d, Success Rate: %.2f%%",
+                mCommandCount, mSuccessCount, mErrorCount,
+                mCommandCount > 0 ? (mSuccessCount * 100.0 / mCommandCount) : 0.0);
+    }
+
+    /**
+     * 重置统计信息
+     */
+    public void resetStatistics() {
+        mCommandCount = 0;
+        mSuccessCount = 0;
+        mErrorCount = 0;
+        Log.d(TAG, "Statistics reset");
+    }
+
+    /**
+     * 内部方法：增加指令计数
+     */
+    void incrementCommandCount() {
+        mCommandCount++;
+    }
+
+    /**
+     * 内部方法：增加成功计数
+     */
+    void incrementSuccessCount() {
+        mSuccessCount++;
+    }
+
+    /**
+     * 内部方法：增加错误计数
+     */
+    void incrementErrorCount() {
+        mErrorCount++;
+    }
+
+
+
+    /**
+     * 销毁资源（在服务销毁时调用）
+     */
+    public void destroy() {
+        Log.d(TAG, "Destroying OpsCommManager...");
+        stop();
+        mOpsCommManager = null;
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/PowerControlHandler.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/PowerControlHandler.java
new file mode 100644
index 0000000..4220643
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/PowerControlHandler.java
@@ -0,0 +1,152 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.util.Log;
+import com.eeo.systemsetting.utils.PowerUtil;
+
+/**
+ * 关机控制处理器
+ * 负责处理Windows发送的关机控制指令
+ * 
+ * 协议格式：
+ * 关机命令：7F 08 99 A2 B3 C4 02 FF 01 01 CF
+ * 关机响应：7F 09 99 A2 B3 C4 02 FF 01 01 XX CF (XX=01成功，XX=00失败)
+ */
+public class PowerControlHandler {
+    private static final String TAG = "PowerControlHandler";
+    
+    // 关机控制相关常量
+    private static final byte[] POWER_OFF_PATTERN = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x01, (byte) 0xCF
+    };
+    
+    private static final byte[] POWER_OFF_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x01
+    };
+    
+    // 响应状态码
+    private static final byte RESPONSE_SUCCESS = (byte) 0x01;
+    private static final byte RESPONSE_FAILURE = (byte) 0x00;
+    private static final byte FRAME_TAIL = (byte) 0xCF;
+    
+    private Context mContext;
+    
+    public PowerControlHandler(Context context) {
+        mContext = context;
+        Log.d(TAG, "PowerControlHandler initialized");
+    }
+    
+    /**
+     * 检查是否为关机控制命令
+     */
+    public boolean isPowerOffCommand(byte[] packet) {
+        if (packet == null || packet.length != POWER_OFF_PATTERN.length) {
+            return false;
+        }
+        
+        // 比较命令模式
+        for (int i = 0; i < POWER_OFF_PATTERN.length; i++) {
+            if (packet[i] != POWER_OFF_PATTERN[i]) {
+                return false;
+            }
+        }
+        
+        Log.d(TAG, "✅ Detected power off command");
+        return true;
+    }
+    
+    /**
+     * 处理关机控制请求
+     * @return 响应数据包
+     */
+    public byte[] handlePowerOffCommand() {
+        Log.d(TAG, "Processing power off command...");
+        
+        try {
+            // 检查关机权限
+            boolean hasPermission = checkPowerOffPermission();
+            if (!hasPermission) {
+                Log.w(TAG, "No power off permission, returning failure response");
+                return buildPowerOffResponse(RESPONSE_FAILURE);
+            }
+            
+            // 执行关机操作
+            Log.i(TAG, "Power off command received - executing shutdown...");
+            performActualShutdown();
+            
+            return buildPowerOffResponse(RESPONSE_SUCCESS);
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling power off command", e);
+            return buildPowerOffResponse(RESPONSE_FAILURE);
+        }
+    }
+    
+    /**
+     * 检查关机权限
+     */
+    private boolean checkPowerOffPermission() {
+        try {
+            // 检查是否为系统应用
+            int uid = android.os.Process.myUid();
+            boolean isSystemApp = (uid == android.os.Process.SYSTEM_UID);
+            
+            Log.d(TAG, "Power off permission check - UID: " + uid + ", isSystemApp: " + isSystemApp);
+            
+            // 系统应用通常有关机权限
+            return isSystemApp;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while checking power off permission", e);
+            return false;
+        }
+    }
+    
+    /**
+     * 构建关机响应数据包
+     * @param statusCode 状态码 (0x01=成功, 0x00=失败)
+     */
+    private byte[] buildPowerOffResponse(byte statusCode) {
+        byte[] response = new byte[POWER_OFF_RESPONSE_PREFIX.length + 2]; // +1状态码 +1帧尾
+        
+        // 复制响应前缀
+        System.arraycopy(POWER_OFF_RESPONSE_PREFIX, 0, response, 0, POWER_OFF_RESPONSE_PREFIX.length);
+        
+        // 添加状态码
+        response[POWER_OFF_RESPONSE_PREFIX.length] = statusCode;
+        
+        // 添加帧尾
+        response[response.length - 1] = FRAME_TAIL;
+        
+        String statusText = (statusCode == RESPONSE_SUCCESS) ? "SUCCESS" : "FAILURE";
+        Log.d(TAG, "Built power off response (" + statusText + "): " + bytesToHexString(response));
+        
+        return response;
+    }
+    
+    /**
+     * 执行实际关机操作
+     * 使用项目定制化的关机方法
+     */
+    private void performActualShutdown() {
+        try {
+            Log.i(TAG, "Executing shutdown using PowerUtil.showShutdownDialog()");
+            PowerUtil.getInstance(mContext).showShutdownDialog();
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while executing shutdown", e);
+        }
+    }
+    
+    /**
+     * 字节数组转十六进制字符串
+     */
+    private String bytesToHexString(byte[] bytes) {
+        StringBuilder sb = new StringBuilder();
+        for (byte b : bytes) {
+            sb.append(String.format("%02X ", b & 0xFF));
+        }
+        return sb.toString().trim();
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/ProtocolHandler.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/ProtocolHandler.java
new file mode 100644
index 0000000..413c8a8
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/ProtocolHandler.java
@@ -0,0 +1,569 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.util.Log;
+
+/**
+ * HHT协议处理器
+ * 负责解析Windows发送的HHT协议数据包
+ * 当前阶段：专注于以太网MAC地址获取指令的解析和响应
+ * 
+ * HHT协议格式：7F 长度 数据... CF
+ * MAC地址获取请求：7F 0A 99 A2 B3 C4 02 FF F1 04 FF FF CF (13字节)
+ * MAC地址响应：7F 0E 99 A2 B3 C4 02 FF F3 04 以太网MAC地址6字节 CF (17字节)
+ */
+public class ProtocolHandler {
+    private static final String TAG = "ProtocolHandler";
+    
+    // HHT协议常量
+    private static final byte FRAME_HEADER = (byte) 0x7F;
+    private static final byte FRAME_TAIL = (byte) 0xCF;
+    
+    // MAC地址获取指令特征码（原始版本）
+    private static final byte[] MAC_REQUEST_PATTERN = {
+        (byte) 0x7F, (byte) 0x0A, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0xF1, (byte) 0x04, (byte) 0xFF, (byte) 0xFF, (byte) 0xCF
+    };
+    
+    // MAC地址获取指令特征码（转换后版本，C4→E4, CF→EF）
+    private static final byte[] MAC_REQUEST_PATTERN_CONVERTED = {
+        (byte) 0x7F, (byte) 0x0A, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xE4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0xF1, (byte) 0x04, (byte) 0xFF, (byte) 0xFF, (byte) 0xEF
+    };
+    
+    // 响应帧前缀（不包含MAC地址和尾部）
+    private static final byte[] MAC_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x0E, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0xF3, (byte) 0x04
+    };
+    
+
+    
+    private Context mContext;
+    private MacAddressHandler mMacAddressHandler;
+    private PowerControlHandler mPowerControlHandler;
+    private SignalSwitchHandler mSignalSwitchHandler;
+    private OpsCommManager mOpsCommManager;
+    private JniSerialPortManager mSerialPortManager;
+    
+    // 数据缓冲区，用于处理粘包和拆包
+    private final StringBuilder mDataBuffer = new StringBuilder();
+    private static final int MAX_BUFFER_SIZE = 2048;
+
+    public ProtocolHandler(Context context, MacAddressHandler macAddressHandler) {
+        mContext = context;
+        mMacAddressHandler = macAddressHandler;
+        mPowerControlHandler = new PowerControlHandler(context);
+        mSignalSwitchHandler = new SignalSwitchHandler(context);
+        Log.d(TAG, "ProtocolHandler initialized with MAC, PowerControl and SignalSwitch handlers");
+    }
+
+    /**
+     * 设置OpsCommManager引用（用于统计）
+     */
+    public void setOpsCommManager(OpsCommManager opsCommManager) {
+        mOpsCommManager = opsCommManager;
+    }
+
+    /**
+     * 设置JniSerialPortManager引用（用于发送数据）
+     */
+    public void setJniSerialPortManager(JniSerialPortManager jniSerialPortManager) {
+        mSerialPortManager = jniSerialPortManager;
+    }
+
+    /**
+     * 处理接收到的原始数据
+     */
+    public void handleReceivedData(byte[] data) {
+        if (data == null || data.length == 0) {
+            Log.w(TAG, "Received empty data");
+            return;
+        }
+
+        try {
+            // 将字节数据转换为十六进制字符串
+            String hexString = bytesToHexString(data);
+            // Log.d(TAG, "Processing received data: " + hexString);
+
+            // 添加到缓冲区
+            synchronized (mDataBuffer) {
+                mDataBuffer.append(hexString.replace(" ", ""));
+                
+                // 防止缓冲区过大
+                if (mDataBuffer.length() > MAX_BUFFER_SIZE) {
+                    Log.w(TAG, "Buffer overflow, clearing buffer");
+                    mDataBuffer.setLength(0);
+                    return;
+                }
+                
+                // 尝试解析完整的数据包
+                parseCompletePackets();
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling received data", e);
+        }
+    }
+
+    /**
+     * 从缓冲区解析完整的数据包
+     */
+    private void parseCompletePackets() {
+        String buffer = mDataBuffer.toString();
+        
+        // 查找帧头7F的位置
+        int headerIndex = buffer.indexOf("7F");
+        
+        while (headerIndex != -1 && headerIndex + 4 < buffer.length()) {
+            try {
+                // 获取长度字节（帧头后的第一个字节）
+                String lengthHex = buffer.substring(headerIndex + 2, headerIndex + 4);
+                int packetLength = Integer.parseInt(lengthHex, 16);
+                
+                // 计算完整包的长度（包括帧头和帧尾）
+                int totalLength = (packetLength + 3) * 2; // *2因为是十六进制字符串
+                
+                // 检查缓冲区是否包含完整的包
+                if (headerIndex + totalLength <= buffer.length()) {
+                    String packetHex = buffer.substring(headerIndex, headerIndex + totalLength);
+                    
+                    // 验证帧尾
+                    if (packetHex.endsWith("CF")) {
+                        Log.d(TAG, "Found complete packet: " + packetHex);
+                        
+                        // 解析数据包
+                        parsePacket(hexStringToBytes(packetHex));
+                        
+                        // 从缓冲区移除已处理的数据
+                        mDataBuffer.delete(0, headerIndex + totalLength);
+                        buffer = mDataBuffer.toString();
+                        headerIndex = buffer.indexOf("7F");
+                    } else {
+                        Log.w(TAG, "Invalid packet: frame tail not found");
+                        headerIndex = buffer.indexOf("7F", headerIndex + 2);
+                    }
+                } else {
+                    // 数据包不完整，等待更多数据
+                    break;
+                }
+                
+            } catch (NumberFormatException e) {
+                Log.w(TAG, "Invalid length byte in packet");
+                headerIndex = buffer.indexOf("7F", headerIndex + 2);
+            } catch (Exception e) {
+                Log.e(TAG, "Exception while parsing packet", e);
+                headerIndex = buffer.indexOf("7F", headerIndex + 2);
+            }
+        }
+    }
+
+    /**
+     * 解析单个数据包
+     */
+    private void parsePacket(byte[] packet) {
+        if (packet == null || packet.length < 3) {
+            Log.w(TAG, "Invalid packet: too short");
+            return;
+        }
+
+        try {
+            // 验证帧头和帧尾
+            if (packet[0] != FRAME_HEADER || packet[packet.length - 1] != FRAME_TAIL) {
+                Log.w(TAG, "Invalid packet: invalid frame header or tail");
+                return;
+            }
+
+            // 验证长度字段
+            int declaredLength = packet[1] & 0xFF;
+            int actualLength = packet.length - 3; // 减去帧头、长度、帧尾
+            
+            if (declaredLength != actualLength) {
+                Log.w(TAG, "Invalid packet: length mismatch. Declared: " + declaredLength + ", Actual: " + actualLength);
+                return;
+            }
+
+            String packetHex = bytesToHexString(packet);
+            Log.d(TAG, "Parsing valid packet: " + packetHex);
+
+            // 检查是否为MAC地址获取请求
+            if (isMacAddressRequest(packet)) {
+                Log.d(TAG, "Detected MAC address request");
+                handleMacAddressRequest();
+            } 
+            // 检查是否为关机控制请求
+            else if (mPowerControlHandler.isPowerOffCommand(packet)) {
+                Log.d(TAG, "Detected power off command");
+                handlePowerOffRequest();
+            } 
+            // 检查是否为信号源切换请求
+            else {
+                int signalType = mSignalSwitchHandler.getSignalSwitchType(packet);
+                if (signalType != -1) {
+                    Log.d(TAG, "Detected signal switch command, type: " + signalType);
+                    handleSignalSwitchRequest(signalType);
+                } else {
+                    Log.d(TAG, "Unknown packet type, ignoring");
+                }
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while parsing packet", e);
+        }
+    }
+
+    /**
+     * 检查是否为MAC地址获取请求（支持原始和转换后的字节模式）
+     */
+    private boolean isMacAddressRequest(byte[] packet) {
+        if (packet.length != MAC_REQUEST_PATTERN.length) {
+            return false;
+        }
+
+        // 首先检查原始模式
+        boolean isOriginalPattern = true;
+        for (int i = 0; i < MAC_REQUEST_PATTERN.length; i++) {
+            if (packet[i] != MAC_REQUEST_PATTERN[i]) {
+                isOriginalPattern = false;
+                break;
+            }
+        }
+        
+        if (isOriginalPattern) {
+            Log.i(TAG, "✅ Detected ORIGINAL MAC request pattern (no byte conversion)");
+            return true;
+        }
+        
+        // 检查转换后的模式
+        boolean isConvertedPattern = true;
+        for (int i = 0; i < MAC_REQUEST_PATTERN_CONVERTED.length; i++) {
+            if (packet[i] != MAC_REQUEST_PATTERN_CONVERTED[i]) {
+                isConvertedPattern = false;
+                break;
+            }
+        }
+        
+        if (isConvertedPattern) {
+            Log.w(TAG, "⚠️ Detected CONVERTED MAC request pattern (C4→E4, CF→EF conversion detected)");
+            Log.w(TAG, "Original pattern:  " + bytesToHexString(MAC_REQUEST_PATTERN));
+            Log.w(TAG, "Converted pattern: " + bytesToHexString(MAC_REQUEST_PATTERN_CONVERTED));
+            Log.w(TAG, "Received packet:   " + bytesToHexString(packet));
+            return true;
+        }
+        
+        // 尝试修复可能的其他转换模式
+        byte[] repairedPacket = repairByteConversions(packet);
+        if (repairedPacket != null) {
+            boolean isRepairedPattern = true;
+            for (int i = 0; i < MAC_REQUEST_PATTERN.length; i++) {
+                if (repairedPacket[i] != MAC_REQUEST_PATTERN[i]) {
+                    isRepairedPattern = false;
+                    break;
+                }
+            }
+            
+            if (isRepairedPattern) {
+                Log.w(TAG, "🔧 Successfully repaired byte conversions in MAC request");
+                Log.w(TAG, "Original packet: " + bytesToHexString(packet));
+                Log.w(TAG, "Repaired packet: " + bytesToHexString(repairedPacket));
+                return true;
+            }
+        }
+        
+        Log.d(TAG, "❌ Unknown packet pattern: " + bytesToHexString(packet));
+        return false;
+    }
+    
+    /**
+     * 尝试修复可能的字节转换问题
+     */
+    private byte[] repairByteConversions(byte[] packet) {
+        if (packet == null || packet.length != MAC_REQUEST_PATTERN.length) {
+            return null;
+        }
+        
+        byte[] repaired = new byte[packet.length];
+        System.arraycopy(packet, 0, repaired, 0, packet.length);
+        
+        boolean hadConversions = false;
+        
+        // 修复已知的字节转换模式
+        for (int i = 0; i < repaired.length; i++) {
+            int originalByte = packet[i] & 0xFF;
+            
+            // 检查是否是C4→E4的转换
+            if (originalByte == 0xE4) {
+                // 检查上下文，看是否应该是C4
+                if (shouldBeC4(i, packet)) {
+                    repaired[i] = (byte) 0xC4;
+                    hadConversions = true;
+                    Log.d(TAG, "Repaired byte[" + i + "]: E4 → C4");
+                }
+            }
+            // 检查是否是CF→EF的转换
+            else if (originalByte == 0xEF) {
+                // 检查上下文，看是否应该是CF
+                if (shouldBeCF(i, packet)) {
+                    repaired[i] = (byte) 0xCF;
+                    hadConversions = true;
+                    Log.d(TAG, "Repaired byte[" + i + "]: EF → CF");
+                }
+            }
+        }
+        
+        return hadConversions ? repaired : null;
+    }
+    
+    /**
+     * 检查指定位置的字节是否应该是C4
+     */
+    private boolean shouldBeC4(int position, byte[] packet) {
+        // 在MAC请求模式中，C4出现在位置5
+        return position == 5;
+    }
+    
+    /**
+     * 检查指定位置的字节是否应该是CF
+     */
+    private boolean shouldBeCF(int position, byte[] packet) {
+        // 在MAC请求模式中，CF出现在最后一个位置
+        return position == packet.length - 1;
+    }
+
+    /**
+     * 处理关机控制请求
+     */
+    private void handlePowerOffRequest() {
+        Log.d(TAG, "Handling power off request...");
+
+        try {
+            // 增加指令计数
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementCommandCount();
+            }
+
+            // 处理关机请求
+            byte[] response = mPowerControlHandler.handlePowerOffCommand();
+            if (response != null) {
+                sendResponse(response);
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementSuccessCount();
+                }
+            } else {
+                Log.e(TAG, "Failed to build power off response");
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementErrorCount();
+                }
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling power off request", e);
+            
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementErrorCount();
+            }
+        }
+    }
+
+    /**
+     * 处理信号源切换请求
+     */
+    private void handleSignalSwitchRequest(int signalType) {
+        Log.d(TAG, "Handling signal switch request, type: " + signalType);
+
+        try {
+            // 增加指令计数
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementCommandCount();
+            }
+
+            // 处理信号源切换请求
+            byte[] response = mSignalSwitchHandler.handleSignalSwitchCommand(signalType);
+            if (response != null) {
+                sendResponse(response);
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementSuccessCount();
+                }
+            } else {
+                Log.e(TAG, "Failed to build signal switch response");
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementErrorCount();
+                }
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling signal switch request", e);
+            
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementErrorCount();
+            }
+        }
+    }
+
+    /**
+     * 处理MAC地址获取请求
+     */
+    private void handleMacAddressRequest() {
+        Log.d(TAG, "Handling MAC address request...");
+
+        try {
+            // 增加指令计数
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementCommandCount();
+            }
+
+            // 只获取以太网MAC地址（根据用户要求，不需要WiFi MAC地址）
+            String ethMac = mMacAddressHandler.getEthMacAddress();
+
+            Log.d(TAG, "Retrieved Ethernet MAC address: " + ethMac);
+
+            // 构建响应
+            byte[] response = buildMacAddressResponse(ethMac);
+            if (response != null) {
+                sendResponse(response);
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementSuccessCount();
+                }
+            } else {
+                Log.e(TAG, "Failed to build MAC address response");
+                
+                if (mOpsCommManager != null) {
+                    mOpsCommManager.incrementErrorCount();
+                }
+            }
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling MAC address request", e);
+            
+            if (mOpsCommManager != null) {
+                mOpsCommManager.incrementErrorCount();
+            }
+        }
+    }
+
+    /**
+     * 构建MAC地址响应帧（仅以太网MAC地址）
+     */
+    private byte[] buildMacAddressResponse(String ethMac) {
+        try {
+            if (ethMac == null || ethMac.isEmpty()) {
+                Log.e(TAG, "No valid Ethernet MAC address available");
+                return null;
+            }
+
+            // 转换MAC地址为字节数组
+            byte[] macBytes = macStringToBytes(ethMac);
+            if (macBytes == null || macBytes.length != 6) {
+                Log.e(TAG, "Invalid Ethernet MAC address format: " + ethMac);
+                return null;
+            }
+
+            return buildSingleMacResponse(macBytes);
+
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while building MAC response", e);
+            return null;
+        }
+    }
+
+    /**
+     * 构建以太网MAC地址响应（17字节）
+     */
+    private byte[] buildSingleMacResponse(byte[] macBytes) {
+        byte[] response = new byte[MAC_RESPONSE_PREFIX.length + macBytes.length + 1];
+        
+        // 复制前缀
+        System.arraycopy(MAC_RESPONSE_PREFIX, 0, response, 0, MAC_RESPONSE_PREFIX.length);
+        
+        // 复制MAC地址
+        System.arraycopy(macBytes, 0, response, MAC_RESPONSE_PREFIX.length, macBytes.length);
+        
+        // 添加帧尾
+        response[response.length - 1] = FRAME_TAIL;
+        
+        Log.d(TAG, "Built single MAC response: " + bytesToHexString(response));
+        return response;
+    }
+
+
+
+    /**
+     * 发送响应数据
+     */
+    private void sendResponse(byte[] response) {
+        Log.d(TAG, "Sending response: " + bytesToHexString(response));
+        
+        if (mSerialPortManager != null) {
+            boolean success = mSerialPortManager.sendData(response);
+            if (success) {
+                Log.d(TAG, "Response sent successfully via JNI implementation");
+            } else {
+                Log.e(TAG, "Failed to send response via JNI implementation");
+            }
+        } else {
+            Log.e(TAG, "JNI SerialPortManager is null, cannot send response");
+        }
+    }
+
+    /**
+     * MAC地址字符串转字节数组
+     */
+    private byte[] macStringToBytes(String macAddress) {
+        if (macAddress == null || macAddress.isEmpty()) {
+            return null;
+        }
+
+        try {
+            // 移除分隔符
+            String cleanMac = macAddress.replace(":", "").replace("-", "").replace(" ", "");
+            
+            if (cleanMac.length() != 12) {
+                Log.e(TAG, "Invalid MAC address length: " + macAddress);
+                return null;
+            }
+
+            byte[] bytes = new byte[6];
+            for (int i = 0; i < 6; i++) {
+                bytes[i] = (byte) Integer.parseInt(cleanMac.substring(i * 2, (i + 1) * 2), 16);
+            }
+
+            return bytes;
+
+        } catch (NumberFormatException e) {
+            Log.e(TAG, "Invalid MAC address format: " + macAddress, e);
+            return null;
+        }
+    }
+
+    /**
+     * 十六进制字符串转字节数组
+     */
+    private byte[] hexStringToBytes(String hexString) {
+        String cleanHex = hexString.replace(" ", "");
+        int len = cleanHex.length();
+        byte[] data = new byte[len / 2];
+        
+        for (int i = 0; i < len; i += 2) {
+            data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
+                                + Character.digit(cleanHex.charAt(i + 1), 16));
+        }
+        
+        return data;
+    }
+
+    /**
+     * 字节数组转十六进制字符串
+     */
+    private String bytesToHexString(byte[] bytes) {
+        StringBuilder sb = new StringBuilder();
+        for (byte b : bytes) {
+            sb.append(String.format("%02X ", b & 0xFF));
+        }
+        return sb.toString().trim();
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/SignalSwitchHandler.java b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/SignalSwitchHandler.java
new file mode 100644
index 0000000..1a5faa7
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/opscomm/SignalSwitchHandler.java
@@ -0,0 +1,282 @@
+package com.eeo.systemsetting.opscomm;
+
+import android.content.Context;
+import android.util.Log;
+import com.eeo.systemsetting.EeoApplication;
+import com.eeo.systemsetting.utils.CommonUtils;
+import com.eeo.systemsetting.utils.Constant;
+import com.eeo.udisdk.UdiConstant;
+
+/**
+ * 信号源切换处理器
+ * 负责处理Windows发送的信号源切换指令
+ * 
+ * 协议格式：
+ * HDMI1切换：7F 08 99 A2 B3 C4 02 FF 01 0A CF (响应：7F 09 99 A2 B3 C4 02 FF 01 0A 01 CF)
+ * HDMI2切换：7F 08 99 A2 B3 C4 02 FF 01 0B CF (响应：7F 09 99 A2 B3 C4 02 FF 01 0B 01 CF)
+ * OPS内置电脑：7F 08 99 A2 B3 C4 02 FF 01 38 CF (响应：7F 09 99 A2 B3 C4 02 FF 01 38 01 CF)
+ * Type-C切换：7F 08 99 A2 B3 C4 02 FF 01 61 CF (响应：7F 08 99 A2 B3 C4 02 FF 01 61 01 CF)
+ */
+public class SignalSwitchHandler {
+    private static final String TAG = "SignalSwitchHandler";
+    
+    // 信号源切换相关常量
+    private static final byte[] HDMI1_SWITCH_PATTERN = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0A, (byte) 0xCF
+    };
+    
+    private static final byte[] HDMI2_SWITCH_PATTERN = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0B, (byte) 0xCF
+    };
+    
+    private static final byte[] OPS_SWITCH_PATTERN = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x38, (byte) 0xCF
+    };
+    
+    private static final byte[] TYPE_C_SWITCH_PATTERN = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x61, (byte) 0xCF
+    };
+    
+    // 响应前缀
+    private static final byte[] HDMI1_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0A
+    };
+    
+    private static final byte[] HDMI2_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x0B
+    };
+    
+    private static final byte[] OPS_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x09, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x38
+    };
+    
+    private static final byte[] TYPE_C_RESPONSE_PREFIX = {
+        (byte) 0x7F, (byte) 0x08, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
+        (byte) 0x02, (byte) 0xFF, (byte) 0x01, (byte) 0x61
+    };
+    
+    // 响应状态码
+    private static final byte RESPONSE_SUCCESS = (byte) 0x01;
+    private static final byte RESPONSE_FAILURE = (byte) 0x00;
+    private static final byte FRAME_TAIL = (byte) 0xCF;
+    
+    // 信号源类型
+    public static final int SIGNAL_TYPE_HDMI1 = 1;
+    public static final int SIGNAL_TYPE_HDMI2 = 2;
+    public static final int SIGNAL_TYPE_OPS = 3;
+    public static final int SIGNAL_TYPE_TYPE_C = 4;
+    
+    private Context mContext;
+    
+    public SignalSwitchHandler(Context context) {
+        mContext = context;
+        Log.d(TAG, "SignalSwitchHandler initialized");
+    }
+    
+    /**
+     * 检查是否为信号源切换命令
+     */
+    public int getSignalSwitchType(byte[] packet) {
+        if (packet == null) {
+            return -1;
+        }
+        
+        // 检查HDMI1切换命令
+        if (packet.length == HDMI1_SWITCH_PATTERN.length && comparePackets(packet, HDMI1_SWITCH_PATTERN)) {
+            Log.d(TAG, "Detected HDMI1 switch command");
+            return SIGNAL_TYPE_HDMI1;
+        }
+        
+        // 检查HDMI2切换命令
+        if (packet.length == HDMI2_SWITCH_PATTERN.length && comparePackets(packet, HDMI2_SWITCH_PATTERN)) {
+            Log.d(TAG, "Detected HDMI2 switch command");
+            return SIGNAL_TYPE_HDMI2;
+        }
+        
+        // 检查OPS内置电脑切换命令
+        if (packet.length == OPS_SWITCH_PATTERN.length && comparePackets(packet, OPS_SWITCH_PATTERN)) {
+            Log.d(TAG, "Detected OPS switch command");
+            return SIGNAL_TYPE_OPS;
+        }
+        
+        // 检查Type-C切换命令
+        if (packet.length == TYPE_C_SWITCH_PATTERN.length && comparePackets(packet, TYPE_C_SWITCH_PATTERN)) {
+            Log.d(TAG, "Detected Type-C switch command");
+            return SIGNAL_TYPE_TYPE_C;
+        }
+        
+        return -1;
+    }
+    
+    /**
+     * 处理信号源切换请求
+     */
+    public byte[] handleSignalSwitchCommand(int signalType) {
+        Log.d(TAG, "Processing signal switch command, type: " + signalType);
+        
+        try {
+            boolean success = false;
+            
+            switch (signalType) {
+                case SIGNAL_TYPE_HDMI1:
+                    success = switchToHDMI1();
+                    return buildSignalSwitchResponse(HDMI1_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
+                    
+                case SIGNAL_TYPE_HDMI2:
+                    success = switchToHDMI2();
+                    return buildSignalSwitchResponse(HDMI2_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
+                    
+                case SIGNAL_TYPE_OPS:
+                    success = switchToOPS();
+                    return buildSignalSwitchResponse(OPS_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
+                    
+                case SIGNAL_TYPE_TYPE_C:
+                    success = switchToTypeC();
+                    return buildSignalSwitchResponse(TYPE_C_RESPONSE_PREFIX, success ? RESPONSE_SUCCESS : RESPONSE_FAILURE);
+                    
+                default:
+                    Log.w(TAG, "Unknown signal type: " + signalType);
+                    return null;
+            }
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Exception while handling signal switch command", e);
+            // 返回失败响应
+            switch (signalType) {
+                case SIGNAL_TYPE_HDMI1:
+                    return buildSignalSwitchResponse(HDMI1_RESPONSE_PREFIX, RESPONSE_FAILURE);
+                case SIGNAL_TYPE_HDMI2:
+                    return buildSignalSwitchResponse(HDMI2_RESPONSE_PREFIX, RESPONSE_FAILURE);
+                case SIGNAL_TYPE_OPS:
+                    return buildSignalSwitchResponse(OPS_RESPONSE_PREFIX, RESPONSE_FAILURE);
+                case SIGNAL_TYPE_TYPE_C:
+                    return buildSignalSwitchResponse(TYPE_C_RESPONSE_PREFIX, RESPONSE_FAILURE);
+                default:
+                    return null;
+            }
+        }
+    }
+    
+    /**
+     * 切换到HDMI1信号源
+     */
+    private boolean switchToHDMI1() {
+        try {
+            Log.i(TAG, "Switching to HDMI1 signal source...");
+            
+            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI1);
+            Log.i(TAG, "HDMI1 signal switch completed successfully");
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Failed to switch to HDMI1", e);
+            return false;
+        }
+    }
+    
+    /**
+     * 切换到HDMI2信号源
+     */
+    private boolean switchToHDMI2() {
+        try {
+            Log.i(TAG, "Switching to HDMI2 signal source...");
+            
+            EeoApplication.udi.changeSource(UdiConstant.SOURCE_HDMI2);
+            Log.i(TAG, "HDMI2 signal switch completed successfully");
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Failed to switch to HDMI2", e);
+            return false;
+        }
+    }
+    
+    /**
+     * 切换到OPS内置电脑信号源
+     */
+    private boolean switchToOPS() {
+        try {
+            Log.i(TAG, "Switching to OPS signal source...");
+            
+            EeoApplication.udi.changeSource(UdiConstant.SOURCE_PC);      
+            Log.i(TAG, "OPS signal switch completed successfully");
+            return true;
+            
+        } catch (Exception e) {
+            Log.e(TAG, "Failed to switch to OPS", e);
+            return false;
+        }
+    }
+
+    /**
+     * 切换到Type-C信号源
+     */
+    private boolean switchToTypeC() {
+        try {
+            Log.i(TAG, "Switching to Type-C signal source...");
+
+            EeoApplication.udi.changeSource(UdiConstant.SOURCE_TYPE_C2);
+            Log.i(TAG, "Type-C signal switch completed successfully");
+            return true;
+
+        } catch (Exception e) {
+            Log.e(TAG, "Failed to switch to Type-C", e);
+            return false;
+        }
+    }
+    
+    /**
+     * 比较两个数据包是否相同
+     */
+    private boolean comparePackets(byte[] packet1, byte[] packet2) {
+        if (packet1.length != packet2.length) {
+            return false;
+        }
+        
+        for (int i = 0; i < packet1.length; i++) {
+            if (packet1[i] != packet2[i]) {
+                return false;
+            }
+        }
+        return true;
+    }
+    
+    /**
+     * 构建信号源切换响应数据包
+     */
+    private byte[] buildSignalSwitchResponse(byte[] responsePrefix, byte statusCode) {
+        byte[] response = new byte[responsePrefix.length + 2]; // +1状态码 +1帧尾
+        
+        // 复制响应前缀
+        System.arraycopy(responsePrefix, 0, response, 0, responsePrefix.length);
+        
+        // 添加状态码
+        response[responsePrefix.length] = statusCode;
+        
+        // 添加帧尾
+        response[response.length - 1] = FRAME_TAIL;
+        
+        String statusText = (statusCode == RESPONSE_SUCCESS) ? "SUCCESS" : "FAILURE";
+        Log.d(TAG, "Built signal switch response (" + statusText + "): " + bytesToHexString(response));
+        
+        return response;
+    }
+    
+    /**
+     * 字节数组转十六进制字符串
+     */
+    private String bytesToHexString(byte[] bytes) {
+        StringBuilder sb = new StringBuilder();
+        for (byte b : bytes) {
+            sb.append(String.format("%02X ", b & 0xFF));
+        }
+        return sb.toString().trim();
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/service/SystemSettingService.java b/systemsetting/src/main/java/com/eeo/systemsetting/service/SystemSettingService.java
index 1a12ea2..e641cb0 100644
--- a/systemsetting/src/main/java/com/eeo/systemsetting/service/SystemSettingService.java
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/service/SystemSettingService.java
@@ -33,6 +33,7 @@ import com.eeo.systemsetting.R;
 import com.eeo.systemsetting.callback.ServiceCallback;
 import com.eeo.systemsetting.dialog.CommonDialog;
 import com.eeo.systemsetting.rs232.Rs232Manager;
+import com.eeo.systemsetting.opscomm.OpsCommManager;
 import com.eeo.systemsetting.utils.CommonUtils;
 import com.eeo.systemsetting.utils.Constant;
 import com.eeo.systemsetting.utils.PowerUtil;
@@ -53,6 +54,8 @@ public class SystemSettingService extends Service {
 
     private Rs232Manager mRs232Manager;
 
+    private OpsCommManager mOpsCommManager;
+
     private final static int TIME_3000 = 3000;
 
     //1001:没有网络
@@ -143,6 +146,9 @@ public class SystemSettingService extends Service {
         mOta = Ota.getInstance(this);
         //实例化Rs232对象，监听串口指令
         mRs232Manager = Rs232Manager.getInstance(this);
+
+        //实例化OpsComm对象，处理Windows串口通信
+        mOpsCommManager = OpsCommManager.getInstance(this);
         //是否自动更新
         bIsAutoUpdate = SharedPreferencesUtil.getIsAutoUpdate(this);
         Log.i(TAG, "onCreate: isAutoUpdate:" + bIsAutoUpdate);
@@ -177,6 +183,12 @@ public class SystemSettingService extends Service {
         SystemControlManager.getInstance().writeSysFs("/sys/bus/usb/drivers/zydeviceR3/writing_speed", "0");
         //发送RS232指令通知扩展坞已开机
         mHandler.sendEmptyMessageDelayed(MSG_SEND_RS232, TIME_3000);
+
+        //启动Windows串口通信服务
+        if (mOpsCommManager != null) {
+            boolean started = mOpsCommManager.start();
+            Log.d(TAG, "OpsCommManager start result: " + started);
+        }
     }
 
     public void addServiceCallBack(ServiceCallback serviceCallBack) {
@@ -356,6 +368,12 @@ public class SystemSettingService extends Service {
         super.onDestroy();
         Log.i(TAG, "onDestroy: ");
         mHandler.removeCallbacksAndMessages(null);
+
+        //停止Windows串口通信服务
+        if (mOpsCommManager != null) {
+            mOpsCommManager.stop();
+            Log.d(TAG, "OpsCommManager stopped");
+        }
     }
 
     /**
