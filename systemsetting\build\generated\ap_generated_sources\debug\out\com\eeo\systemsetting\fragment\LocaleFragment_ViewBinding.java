// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.view.View;
import android.widget.Spinner;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class LocaleFragment_ViewBinding implements Unbinder {
  private LocaleFragment target;

  @UiThread
  public LocaleFragment_ViewBinding(LocaleFragment target, View source) {
    this.target = target;

    target.mLanguageSpinner = Utils.findRequiredViewAsType(source, R.id.spinner_language, "field 'mLanguageSpinner'", Spinner.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    LocaleFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mLanguageSpinner = null;
  }
}
