package com.eeo.systemsetting.service;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.app.Service;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.droidlogic.app.SystemControlManager;
import com.eeo.ota.Ota;
import com.eeo.ota.bean.VersionInfo;
import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;
import com.eeo.ota.callback.InstallListener;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.callback.ServiceCallback;
import com.eeo.systemsetting.dialog.CommonDialog;
import com.eeo.systemsetting.rs232.Rs232Manager;
import com.eeo.systemsetting.opscomm.OpsCommManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;

public class SystemSettingService extends Service {
    public static final String TAG = "SystemSettingService";
    private Ota mOta;

    private Udi mUdi;

    private Rs232Manager mRs232Manager;

    private OpsCommManager mOpsCommManager;

    private final static int TIME_3000 = 3000;

    //1001:没有网络
    public static final int ErrorCode_1001 = 1001;
    //1002：未发现新版本
    public static final int ErrorCode_1002 = 1002;
    //1003:连接服务器失败，请检查网络后重试
    public static final int ErrorCode_1003 = 1003;
    //1004:动态注册失败
    public static final int ErrorCode_1004 = 1004;

    private List<ServiceCallback> mServiceCallbackList = new ArrayList<>();
    private boolean bIsAutoUpdate;

    private String mLastOpsStatus = UdiConstant.OPS_STATUS_OFF;

    private VersionInfo mVersionInfo;
    private boolean mIsDownloading;
    private int mErrorCode;

    private Dialog mInstallingDialog;
    private TextView mInstallingTv;
    private int mInstallProgress = 0;

    private CommonDialog mInstallFailDialog;
    private CommonDialog mHaveUpdateDialog;

    private boolean mHasSendRS232PowerOn = false;
    private int mSendRS232PowerOnRetryTimes = 0;

    private static final int MSG_CHECK_UPDATE = 0x001;
    public static final int MSG_SHOW_INSTALLING_DIALOG = 0x002;
    public static final int MSG_SHOW_INSTALL_FAIL_DIALOG = 0x003;
    private static final int MSG_SEND_RS232 = 0x004;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_SHOW_INSTALLING_DIALOG:
                    showInstallingDialog();
                    break;
                case MSG_SHOW_INSTALL_FAIL_DIALOG:
                    dismissInstallingDialog();
                    showInstallFailDialog();
                    break;
                case MSG_CHECK_UPDATE:
                    //检测更新
                    checkUpdate(true);
                    break;
                case MSG_SEND_RS232:
                    //发送RS232指令通知扩展坞已开机
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            Log.d(TAG, "handleMessage: MSG_SEND_RS232,mSendRS232PowerOnRetryTimes=" + mSendRS232PowerOnRetryTimes);
                            mHasSendRS232PowerOn = CommonUtils.sendRS232Data(Rs232Manager.KEY_POWER_ON);
                            mSendRS232PowerOnRetryTimes++;
                            if (!mHasSendRS232PowerOn && mSendRS232PowerOnRetryTimes < 5) {
                                sendEmptyMessageDelayed(MSG_SEND_RS232, TIME_3000);
                            }
                        }
                    }).start();
                default:
                    break;
            }
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return new SystemSettingService.MyBinder();
    }

    public class MyBinder extends Binder {
        public SystemSettingService getService() {
            return SystemSettingService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mUdi = new Udi(this, UdiConstant.TOKEN_SETTING);
        //实例化OTA对象，全局只设置一个OTA对象
        mOta = Ota.getInstance(this);
        //实例化Rs232对象，监听串口指令
        mRs232Manager = Rs232Manager.getInstance(this);

        //实例化OpsComm对象，处理Windows串口通信
        mOpsCommManager = OpsCommManager.getInstance(this);
        //是否自动更新
        bIsAutoUpdate = SharedPreferencesUtil.getIsAutoUpdate(this);
        Log.i(TAG, "onCreate: isAutoUpdate:" + bIsAutoUpdate);

        //是否已经下载完成
        boolean downloaded = isDownloaded();
        Log.i(TAG, "onCreate: downloaded :" + downloaded);

        Date date = new Date();
        long startTime = date.getTime();
        Log.i(TAG, "onCreate: startTime : " + startTime);

        Long endTime = SaveDateUtils.getTime(this);
        Log.i(TAG, "onCreate: endTime : " + endTime);

        long cha = startTime - endTime;
        double result = cha * 1.0 / (1000 * 60 * 60);
        Log.i(TAG, "onCreate: result : " + result);

        if (downloaded && result >= 24 && !CommonUtils.isFirstBoot()) {
            //有新版本可以更新弹窗
            showHaveUpdateDialog();
            return;
        }

        //开启自动更新才进行自动更新检测
        if (bIsAutoUpdate) {
            Util.startAutoUpdateService(this);
            mHandler.sendEmptyMessageDelayed(MSG_CHECK_UPDATE, TIME_3000);
        }
        //开机默认关闭R30书写预测算法
        SystemControlManager.getInstance().writeSysFs("/sys/bus/usb/drivers/zydeviceR3/writing_speed", "0");
        //发送RS232指令通知扩展坞已开机
        mHandler.sendEmptyMessageDelayed(MSG_SEND_RS232, TIME_3000);

        //启动Windows串口通信服务
        if (mOpsCommManager != null) {
            boolean started = mOpsCommManager.start();
            Log.d(TAG, "OpsCommManager start result: " + started);
        }
    }

    public void addServiceCallBack(ServiceCallback serviceCallBack) {
        if (!mServiceCallbackList.contains(serviceCallBack)) {
            mServiceCallbackList.add(serviceCallBack);
        }
    }

    public void removeServiceCallBack(ServiceCallback serviceCallback) {
        mServiceCallbackList.remove(serviceCallback);
    }

    /**
     * 检测更新
     */
    public void checkUpdate(boolean isRightUpdate) {
        mOta.checkVersion(new CheckVersionCallback() {
            @Override
            public void onCheckSuccess(VersionInfo versionInfo) {
                mErrorCode = 0;
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "onCheckSuccess: versionName:" + versionInfo.versionName);
                        //检测更新信息，版本号等
                        for (ServiceCallback serviceCallback : mServiceCallbackList) {
                            serviceCallback.onCheckSuccess(versionInfo);
                        }
                        mVersionInfo = versionInfo;

                        Log.i(TAG, "onCheckSuccess: isRightUpdate : " + isRightUpdate);
                        Log.i(TAG, "onCheckSuccess: isAutoUpdate : " + bIsAutoUpdate);
                        Log.i(TAG, "onCheckSuccess: isDownloaded : " + isDownloaded());
                        //如果是自动更新按钮打开，则自动开始下载
                        if (isRightUpdate && bIsAutoUpdate && !isDownloaded()) {
                            startDownApk();
                        }
                    }
                });
            }

            @Override
            public void onCheckFail(int errCode, String reason) {
                Log.i(TAG, "onCheckFail: errCode:" + errCode);
                for (ServiceCallback serviceCallback : mServiceCallbackList) {
                    serviceCallback.onCheckFail(errCode, reason);
                }
                mErrorCode = errCode;
            }
        });
    }

    //更新包是否已下载
    public boolean isDownloaded() {
        if (mOta == null) {
            return false;
        } else {
            return mOta.isDownloaded();
        }
    }

    /**
     * 开始下载
     */
    public void startDownApk() {
        Log.i(TAG, "startDownApk: ");
        if (mOta != null) {
            mIsDownloading = true;
            mOta.download(new DownloadListener() {
                @Override
                public void onDownloadProgress(int progress) {
                    for (ServiceCallback serviceCallback : mServiceCallbackList) {
                        serviceCallback.onDownloadProgress(progress);
                    }
                    Log.i(TAG, "onDownloadProgress: progress =" + progress);
                }

                @Override
                public void onDownloadCompleted(String outputFile) {
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            for (ServiceCallback serviceCallback : mServiceCallbackList) {
                                serviceCallback.onDownloadCompleted(outputFile);
                            }
                        }
                    });
                    mIsDownloading = false;
                }

                @Override
                public void onDownloadFailure(int errCode) {
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            for (ServiceCallback serviceCallback : mServiceCallbackList) {
                                serviceCallback.onDownloadFailure(errCode);
                            }
                        }
                    });
                    mIsDownloading = false;
                }
            });
        }
    }

    /**
     * 安装
     */
    public void startInstallPackage(Context context) {

        if (mOta != null) {
            mOta.installPackage(new InstallListener() {
                @Override
                public void onInstallSuccess() {
                    Log.d(TAG, "onInstallSuccess: ");
                    dismissInstallingDialog();
                    for (ServiceCallback serviceCallback : mServiceCallbackList) {
                        serviceCallback.onInstallSuccess();
                    }
                }

                @Override
                public void onInstallFail(String errMsg) {
                    mHandler.sendEmptyMessage(MSG_SHOW_INSTALL_FAIL_DIALOG);
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            for (ServiceCallback serviceCallback : mServiceCallbackList) {
                                serviceCallback.onInstallFail(errMsg);
                            }
                        }
                    });
                }

                @Override
                public void onInstallProgress(float progress) {
                    Log.d(TAG, "onInstallProgress: " + progress);
                    mInstallProgress = Math.round(progress * 100);
                    mHandler.sendEmptyMessage(MSG_SHOW_INSTALLING_DIALOG);
                    for (ServiceCallback serviceCallback : mServiceCallbackList) {
                        serviceCallback.onInstallProgress(progress);
                    }
                }
            });
        }

        mInstallProgress = 0;
        CommonUtils.setIsDialog(SystemSettingService.this, true);
        EeoApplication.isShowInstallingDialog = true;
        showInstallingDialog();
        CommonUtils.enableOsd(this, true);
    }

    //停止下载
    public void stopDownload() {
        if (mOta != null) {
            mOta.stopDownload();
        }
        mIsDownloading = false;
    }

    public VersionInfo getVersionInfo() {
        return mVersionInfo;
    }

    public boolean isDownloading() {
        return mIsDownloading;
    }

    public int getErrorCode() {
        return mErrorCode;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "onDestroy: ");
        mHandler.removeCallbacksAndMessages(null);

        //停止Windows串口通信服务
        if (mOpsCommManager != null) {
            mOpsCommManager.stop();
            Log.d(TAG, "OpsCommManager stopped");
        }
    }

    /**
     * @deprecated getOpsStatus状态变化慢，移到TifPlayerActivity实现
     */
    class CheckWindowCloseTask extends TimerTask {

        @Override
        public void run() {
            String opsStatus = mUdi.getOpsStatus();
            //isReboot是判断如果是通过systemsetting点击重启则不需要关机处理
            if (UdiConstant.OPS_STATUS_OFF.equals(opsStatus) && UdiConstant.OPS_STATUS_ON.equals(mLastOpsStatus)
                    && !PowerUtil.getInstance(SystemSettingService.this).isShuttingDown()
                    && !EeoApplication.isResettingOps
                    && mUdi.getCurrentSource().equals(UdiConstant.SOURCE_PC)
            ) {
                Log.e(TAG, "ops is not on,shut down. ");
                PowerUtil.getInstance(SystemSettingService.this).shutdownOrReboot(false, "",
                        PowerUtil.FLAG_SHUTDOWN_WEAK, false);
            } else {
                mLastOpsStatus = opsStatus;
            }
        }
    }

    private void showInstallingDialog() {
        if (mInstallingDialog == null) {
            mInstallingDialog = new Dialog(SystemSettingService.this, R.style.Dialog);
            mInstallingDialog.getWindow().setContentView(R.layout.dialog_installing);
            mInstallingTv = mInstallingDialog.getWindow().getDecorView().findViewById(R.id.tv_installing);
            mInstallingDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            mInstallingDialog.setCanceledOnTouchOutside(false);
            mInstallingDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                }
            });
            WindowManager.LayoutParams layoutParams = mInstallingDialog.getWindow().getAttributes();
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.CENTER;
            mInstallingDialog.getWindow().setAttributes(layoutParams);
            mInstallingDialog.getWindow().setDimAmount(0.6f);
        }
        if (mInstallProgress == 0) {
            mInstallingTv.setText(getString(R.string.ready_install));
        } else {
            mInstallingTv.setText(String.format(getString(R.string.install_ing), mInstallProgress));
        }
        mInstallingDialog.show();
    }

    private void dismissInstallingDialog() {
        if (mInstallingDialog != null && mInstallingDialog.isShowing()) {
            mInstallingDialog.dismiss();
        }
        CommonUtils.setIsDialog(SystemSettingService.this, false);
        EeoApplication.isShowInstallingDialog = false;
        CommonUtils.enableOsd(this, false);
    }

    private void showInstallFailDialog() {
        if (mInstallFailDialog == null) {
            mInstallFailDialog = new CommonDialog.Builder(SystemSettingService.this)
                    .view(R.layout.dialog_install_fail)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(false) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mInstallFailDialog.dismiss();
                        }
                    })
                    .addViewOnclick(R.id.btn_reinstall, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            startInstallPackage(SystemSettingService.this);
                            mInstallFailDialog.dismiss();
                        }
                    })
                    .build();
            mInstallFailDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT); //加了这个才能在无activity
        }
        mInstallFailDialog.show();

        Window window = mInstallFailDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(EeoApplication.getApplication(), Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(EeoApplication.getApplication(), Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.CENTER;
            window.setAttributes(layoutParams);
        }
    }

    private void showHaveUpdateDialog() {
        if (mHaveUpdateDialog == null) {
            mHaveUpdateDialog = new CommonDialog.Builder(this)
                    .view(R.layout.dialog_have_update)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(false) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            dismissHaveUpdateDialog();
                        }
                    })
                    .addViewOnclick(R.id.btn_reinstall, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            dismissHaveUpdateDialog();
                            startInstallPackage(SystemSettingService.this);
                        }
                    })
                    .build();
            mHaveUpdateDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT); //加了这个才能在无activity
        }
        mHaveUpdateDialog.show();
        Window window = mHaveUpdateDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(EeoApplication.getApplication(), Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(EeoApplication.getApplication(), Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.CENTER;
            window.setAttributes(layoutParams);
        }
        CommonUtils.setIsDialog(SystemSettingService.this, true);
        EeoApplication.isShowHaveUpdateDialog = true;
        CommonUtils.enableOsd(this, true);
    }

    private void dismissHaveUpdateDialog() {
        if (mHaveUpdateDialog != null && mHaveUpdateDialog.isShowing()) {
            mHaveUpdateDialog.dismiss();
        }
        long time = new Date().getTime();
        SaveDateUtils.saveTime(this, time);
        CommonUtils.setIsDialog(SystemSettingService.this, false);
        EeoApplication.isShowHaveUpdateDialog = false;
        CommonUtils.enableOsd(this, false);
    }

}
