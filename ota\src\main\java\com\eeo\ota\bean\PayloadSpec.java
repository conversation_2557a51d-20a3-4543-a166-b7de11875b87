package com.eeo.ota.bean;

import android.util.Log;

import java.io.Serializable;
import java.util.List;

public class PayloadSpec implements Serializable {
    public static final String TAG = "PayloadSpec";
    private long mOffset;
    private List<String> mProperties;
    private long mSize;
    private String mUrl;

    public static Builder newBuilder() {
        return new Builder();
    }

    public PayloadSpec(Builder b) {
        this.mUrl = b.mUrl;
        this.mOffset = b.mOffset;
        this.mSize = b.mSize;
        this.mProperties = b.mProperties;
    }

    public String getUrl() {
        return this.mUrl;
    }

    public long getOffset() {
        return this.mOffset;
    }

    public long getSize() {
        return this.mSize;
    }

    public List<String> getProperties() {
        return this.mProperties;
    }

    public static class Builder {
        private long mOffset;
        private List<String> mProperties;
        private long mSize;
        private String mUrl;

        public Builder url(String url) {
            this.mUrl = url;
            return this;
        }

        public Builder offset(long offset) {
            this.mOffset = offset;
            return this;
        }

        public Builder size(long size) {
            this.mSize = size;
            return this;
        }

        public Builder properties(List<String> properties) {
            this.mProperties = properties;
            return this;
        }

        public PayloadSpec build() {
            return new PayloadSpec(this);
        }
    }

    @Override
    public String toString() {
        return "PayloadSpec{" +
                "mOffset=" + mOffset +
                ", mProperties=" + mProperties +
                ", mSize=" + mSize +
                ", mUrl='" + mUrl + '\'' +
                '}';
    }
}