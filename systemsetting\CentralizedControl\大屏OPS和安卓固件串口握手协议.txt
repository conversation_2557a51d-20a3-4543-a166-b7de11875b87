大屏OPS和大屏固件串口握手协议

OPS软件启动之后设置串口名称以及波特率等参数，打开串口，并发起握手通信。
1.	ops发起握手：
间隔100毫秒先后发送以下两个串口码给大屏固件：
1.	0149串口码
0x7F,0x08,0x99,0xA2,0xB3,0xC4,0x02,0xFF,0x01,0x49,0xCF
2.	0F08串口码
0x7F,0x09,0x99,0xA2,0xB3,0xC4,0x02,0xFF,0x0F,0x08,0x02,0xCF
2.	Android处理握手：
Android需要应答来自ops的握手信号：
1、如果收到0149串口码：
应答：0x7F,0x09,0x99,0xA2,0xB3,0xC4,0x02,0xFF,0x01,0x49,0x32,0xCF；
2、如果收到0F08串口码：
应答：0x7F,0x08,0x99,0xA2,0xB3,0xC4,0x02,0xFF,0x0F,0x08,0xCF；





