<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_bg"
    tools:context=".SecondFragment">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="521dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:splitMotionEvents="false">

        <ImageView
            android:id="@+id/slogn_text"
            android:layout_width="158dp"
            android:layout_height="23dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="265dp"
            android:background="@drawable/logo_n"
            android:text="@string/slogn">

        </ImageView>

        <Spinner
            android:id="@+id/spinner_language"
            style="@style/spinner_style"
            android:layout_width="237dp"
            android:layout_height="29dp"
            android:layout_below="@+id/slogn_text"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="37dp"
            android:dropDownWidth="237dp"
            android:gravity="end"
            android:prompt="@string/select_language_prompt"
            android:textSize="9sp"
            tools:ignore="MissingConstraints" />

        <Spinner
            android:id="@+id/spinner_region"
            style="@style/spinner_style"
            android:layout_width="237dp"
            android:layout_height="29dp"
            android:layout_below="@id/spinner_language"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="11dp"
            android:dropDownWidth="237dp"
            android:gravity="end"
            android:prompt="@string/select_region_prompt"
            android:textSize="9sp"
            tools:ignore="MissingConstraints" />

        <Button
            android:id="@+id/bt_next"
            style="@style/MyButtonStyle"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="432dp"
            android:text="@string/next"

            />
    </RelativeLayout>

</androidx.core.widget.NestedScrollView>