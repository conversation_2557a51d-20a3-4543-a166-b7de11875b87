package com.eeo.systemsetting.fragment;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.text.method.TransformationMethod;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.adapter.WiFiConnectedAdapter;
import com.eeo.systemsetting.adapter.WiFiScanListAdapter;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.dialog.CommonDialog;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.wifi.AccessPoint;

import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class WiFiFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener {
    public static final String TAG = "WiFiFragment==";
    @BindView(R.id.rv_connected)
    RecyclerView rvWiFiConnected;
    @BindView(R.id.rv_wifi_list)
    RecyclerView rvWifiList;
    @BindView(R.id.sw_network)
    Switch swNetwork;
    @BindView(R.id.progressbar_check_update)
    ProgressBar progressbarCheckUpdate;
    @BindView(R.id.line1)
    View line;
    @BindView(R.id.txt_other_network)
    TextView txtOtherNetwork;
    @BindView(R.id.ll_network)
    LinearLayout llNetwork;
    CommonDialog inputPwdDialog;
    EditText edtPassword;
    TextView txtPwdError;
    private String lastPassword;
    private Network currentNetwork;
    private WifiManager wifiManager;
    private NetworkInfo lastNetworkInfo;
    private WifiInfo lastWifiInfo;
    private Disposable scanWifi = null;
    private WifiConfiguration lastWifiConfiguration;

    private List<AccessPoint> lastAccessPoints = new CopyOnWriteArrayList<>();
    private List<WifiInfo> wifiConnectedList = new CopyOnWriteArrayList<>();
    private WiFiScanListAdapter scanListAdapter;
    private WiFiConnectedAdapter connectedAdapter;
    private int lastPortalNetworkId = AccessPoint.INVALID_NETWORK_ID;

    private boolean isRegistered = false;

    private static final int MSG_CHECK_WIFI_DEVICE = 0x001;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_CHECK_WIFI_DEVICE:
                    if (!CommonUtils.isWifiDeviceSupported()) {
                        Log.e(TAG, "Wlan device is not supported!");
                        if (mHandler.hasCallbacks(mDotAnimRunnable)) {
                            mHandler.removeCallbacks(mDotAnimRunnable);
                            mDotAnimStr = "";
                        }
                        llNetwork.setVisibility(View.VISIBLE);
                        txtOtherNetwork.setText(getContext().getString(R.string.wifi_device_error));
                        unregisterReceiver();
                        return;
                    }
                    break;
                default:
                    break;
            }
        }
    };
    /**
     * 搜索wifi...动画
     */
    private String mDotAnimStr = "";
    private Runnable mDotAnimRunnable = new Runnable() {
        @Override
        public void run() {
            if (getContext() != null) {
                txtOtherNetwork.setText(getContext().getString(R.string.select_network) + " " + mDotAnimStr);
                mHandler.postDelayed(this, 300);
                mDotAnimStr = mDotAnimStr.length() == 0 ? "." : mDotAnimStr.length() == 1 ? ".." : mDotAnimStr.length() == 2 ? "..." : "";
            }
        }
    };

    @Override
    public int getLayout() {
        return R.layout.fragment_wifi;
    }

    @Override
    public void initDate() {
        wifiManager = (WifiManager) getActivity().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) {
            getActivity().finish();
            return;
        }
        initView();
        int permission = ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION);
        if (permission == PackageManager.PERMISSION_DENIED) {
            ActivityCompat.requestPermissions(getActivity(), new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, 1);
        }
        swNetwork.setOnCheckedChangeListener(this);
        //获取当前连接的网络信息
        lastNetworkInfo = getActiveNetworkInfo();
        lastWifiInfo = wifiManager.getConnectionInfo();
    }

    private void initView() {
        scanListAdapter = new WiFiScanListAdapter();
        scanListAdapter.setItemClickListener(this::showDialog);
        rvWifiList.setLayoutManager(new LinearLayoutManager(getActivity()));
        rvWifiList.setAdapter(scanListAdapter);
        connectedAdapter = new WiFiConnectedAdapter(getActivity());
        rvWiFiConnected.setLayoutManager(new LinearLayoutManager(getActivity()));
        rvWiFiConnected.setAdapter(connectedAdapter);
    }

    /**
     * 设置已经连接上的wifi显示
     */
    private void setConnectedWifi() {
        llNetwork.setVisibility(View.VISIBLE);
        if (lastWifiInfo != null) {
            Log.i(TAG, "initDate: wifiInfo : " + lastWifiInfo.getSSID());
            if (!lastWifiInfo.getSSID().equals(WifiManager.UNKNOWN_SSID)) {
                boolean contains = false;
                for (WifiInfo wifiInfo : wifiConnectedList) {
                    if (wifiInfo.getSSID().equals(lastWifiInfo.getSSID())
                            && wifiInfo.getBSSID().equals(lastWifiInfo.getBSSID())) {
                        contains = true;
                        break;
                    }
                }
                if (!contains) {
                    wifiConnectedList.add(lastWifiInfo);
                }
            }

            connectedAdapter.setConnectedWifi(wifiConnectedList, lastNetworkInfo);
            if (lastAccessPoints.size() > 0) {
                txtOtherNetwork.setText(R.string.select_network);
            } else {
                if (!mHandler.hasCallbacks(mDotAnimRunnable)) {
                    mHandler.post(mDotAnimRunnable);
                }
            }
        } else {
            txtOtherNetwork.setText(R.string.wifi_network);
            rvWiFiConnected.setVisibility(View.GONE);
            line.setVisibility(View.GONE);
        }
    }

    private void setWifiState() {
        if (wifiManager.isWifiEnabled()) {
            registerReceiver();
            swNetwork.setChecked(true);
        } else {
            swNetwork.setChecked(false);
            rvWiFiConnected.setVisibility(View.GONE);
            line.setVisibility(View.GONE);
            llNetwork.setVisibility(View.GONE);
            wifiConnectedList.clear();
            lastAccessPoints.clear();
            connectedAdapter.notifyDataSetChanged();
            scanListAdapter.notifyDataSetChanged();
            return;
        }

        setConnectedWifi();
        scanWifi();
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onStart: ");
        setWifiState();
    }

    private void scanWifi() {
        if (scanWifi == null) {
            scanWifi = Observable.interval(0, 10, TimeUnit.SECONDS)
                    .observeOn(Schedulers.io())
                    .doOnNext(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            wifiManager.startScan();
                        }
                    })
                    .subscribe();
        } else {
            wifiManager.startScan();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.i(TAG, "onStop: ");
        release();
    }

    public void release() {
        if (scanWifi != null && !scanWifi.isDisposed()) {
            scanWifi.dispose();
            scanWifi = null;
        }
        unregisterReceiver();
        mHandler.removeMessages(MSG_CHECK_WIFI_DEVICE);
        if (mHandler.hasCallbacks(mDotAnimRunnable)) {
            mHandler.removeCallbacks(mDotAnimRunnable);
            mDotAnimStr = "";
        }
    }

    private void registerReceiver() {
        if (!isRegistered) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
            filter.addAction(Constant.CONFIGURED_NETWORKS_CHANGE);
            filter.addAction(Constant.LINK_CONFIGURATION_CHANGED);
            filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
            filter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);
            filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
            getActivity().registerReceiver(broadcastReceiver, filter);
            ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null) {
                NetworkRequest.Builder request = new NetworkRequest.Builder()
                        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI);
                cm.registerNetworkCallback(request.build(), callback);
            }
            isRegistered = true;
        }
    }

    private void unregisterReceiver() {
        if (isRegistered) {
            getActivity().unregisterReceiver(broadcastReceiver);
            ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null) {
                cm.unregisterNetworkCallback(callback);
            }
            isRegistered = false;
        }
    }

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                    case WifiManager.SCAN_RESULTS_AVAILABLE_ACTION:
                    case Constant.CONFIGURED_NETWORKS_CHANGE:
                    case Constant.LINK_CONFIGURATION_CHANGED:
//                        Log.i(TAG, "onReceive: CONFIGURED_NETWORKS_CHANGE");
                        //定时扫描变化时，更新wifi列表
                        updateAccessPoints();

                        break;
                    case WifiManager.NETWORK_STATE_CHANGED_ACTION:
                        //WIFI 的连接状态发生变化时的广播
                        Log.i(TAG, "onReceive: NETWORK_STATE_CHANGED_ACTION");
                        //定时扫描变化时，更新wifi列表
                        updateAccessPoints();
                        NetworkInfo info = intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
                        updateNetworkInfo(info);
                        break;
                    case WifiManager.SUPPLICANT_STATE_CHANGED_ACTION:
                        //WIFI 密码错误
                        Log.i(TAG, "onReceive: SUPPLICANT_STATE_CHANGED_ACTION");
                        int error = intent.getIntExtra(WifiManager.EXTRA_SUPPLICANT_ERROR, -1);
                        if (error == WifiManager.ERROR_AUTHENTICATING) {
                            handlePasswordError();
                        }
                        break;
                    case WifiManager.WIFI_STATE_CHANGED_ACTION:
                        int state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0);
                        if (state == WifiManager.WIFI_STATE_ENABLED) {
                            mHandler.removeMessages(MSG_CHECK_WIFI_DEVICE);
                        }
                        break;
                }
            }
        }
    };

    //监听网络
    private ConnectivityManager.NetworkCallback callback = new ConnectivityManager.NetworkCallback() {

        @Override
        public void onAvailable(Network network) {
            super.onAvailable(network);
            //网络已链接
            setCurrentNetwork(network);
            portalCurrentWifi();
        }

        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
            if (network.equals(getCurrentNetwork())) {
                updateNetworkInfo(null);
            }
        }
    };

    public Network getCurrentNetwork() {
        return currentNetwork;
    }

    public void setCurrentNetwork(Network currentNetwork) {
        this.currentNetwork = currentNetwork;
    }


    /**
     * 扫描时更新list列表
     * 更新AccessPoints
     */
    private void updateAccessPoints() {
        if (!swNetwork.isChecked()) {
            return;
        }

        Single.create((SingleOnSubscribe<List<AccessPoint>>) emitter -> {
                    List<AccessPoint> accessPoints = new ArrayList<>();
                    //获取扫描到的wifi
                    List<ScanResult> scanResults = wifiManager.getScanResults();
                    if (lastWifiInfo != null && lastWifiInfo.getNetworkId() != AccessPoint.INVALID_NETWORK_ID) {
                        lastWifiConfiguration = getWifiConfigurationForNetworkId(lastWifiInfo.getNetworkId());
                    }
                    if (scanResults != null) {
                        //扫描wifi列表
                        for (ScanResult scanResult : scanResults) {
                            //过滤掉wifi中ssid为空，AccessPoint相同的wifi
                            if (TextUtils.isEmpty(scanResult.SSID)) {
                                continue;
                            }

                            AccessPoint accessPoint = new AccessPoint(getActivity().getApplicationContext(), scanResult);
                            //过滤掉重复wifi和已经连接上的wifi
                            if (accessPoints.contains(accessPoint) || accessPoint.getQuotedSSID().equals(lastWifiInfo.getSSID())) {
                                continue;
                            }
                            //获取已经配置保存好的wifi
                            List<WifiConfiguration> wifiConfigurations = wifiManager.getConfiguredNetworks();
                            if (wifiConfigurations != null) {
                                for (WifiConfiguration config : wifiConfigurations) {
                                    //如果accessPoint的ssid的名字与config的名字相同，则将config的wifi添加进accessPoint中
                                    if (accessPoint.getQuotedSSID().equals(config.SSID)) {
                                        accessPoint.setWifiConfiguration(config);
                                    }
                                }
                            }
                            if (lastWifiInfo != null && lastNetworkInfo != null) {
                                accessPoint.update(lastWifiConfiguration, lastWifiInfo, lastNetworkInfo);
                            }
                            //将过滤完的accessPoint添加到list中
                            accessPoints.add(accessPoint);
                        }
                    }

                    //升序排列
                    Collections.sort(accessPoints);
                    emitter.onSuccess(accessPoints);
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<AccessPoint>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(List<AccessPoint> accessPoints) {
                        if (!swNetwork.isChecked()) {
                            //check again
                            return;
                        }
                        lastAccessPoints = accessPoints;
                        //更新扫描列表
                        scanListAdapter.setAccessPoints(lastAccessPoints);
                        lastWifiInfo = wifiManager.getConnectionInfo();
                        if (lastWifiInfo.getSSID().equals("<unknown ssid>")) {
                            wifiConnectedList.clear();
                            rvWiFiConnected.setVisibility(View.GONE);
                            line.setVisibility(View.GONE);

                        } else {
                            rvWiFiConnected.setVisibility(View.VISIBLE);
                            line.setVisibility(View.VISIBLE);
                            wifiConnectedList.clear();
                            wifiConnectedList.add(lastWifiInfo);

                        }
                        connectedAdapter.setConnectedWifi(wifiConnectedList, lastNetworkInfo);
                        if (accessPoints.size() > 0) {
                            if (mHandler.hasCallbacks(mDotAnimRunnable)) {
                                mHandler.removeCallbacks(mDotAnimRunnable);
                                txtOtherNetwork.setText(getContext().getString(R.string.select_network));
                                mDotAnimStr = "";
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {

                    }
                });
    }

    public void updateNetworkInfo(NetworkInfo networkInfo) {
        Single.create((SingleOnSubscribe<List<AccessPoint>>) emitter -> {
                    if (networkInfo != null) {
                        lastNetworkInfo = networkInfo;
                    }
                    lastWifiInfo = wifiManager.getConnectionInfo();

                    if (lastWifiInfo.getNetworkId() == AccessPoint.INVALID_NETWORK_ID) {
                        // 表示没有 wifi 连接，lastPortalNetworkId 置为无效
                        lastPortalNetworkId = AccessPoint.INVALID_NETWORK_ID;
                    }
                    if (lastWifiInfo != null && lastWifiInfo.getNetworkId() != AccessPoint.INVALID_NETWORK_ID) {
                        lastWifiConfiguration = getWifiConfigurationForNetworkId(lastWifiInfo.getNetworkId());
                    }
                    boolean reorder = false;
                    for (AccessPoint accessPoint : lastAccessPoints) {
                        if (accessPoint.update(lastWifiConfiguration, lastWifiInfo, lastNetworkInfo)) {
                            reorder = true;
                        }
                    }
                    if (reorder) {
                        Collections.sort(lastAccessPoints);
                    }
                    emitter.onSuccess(lastAccessPoints);
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<AccessPoint>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(List<AccessPoint> accessPoints) {
                        // 更新列表
                        scanListAdapter.setAccessPoints(accessPoints);
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                    }
                });

    }

    public void showDialog(AccessPoint accessPoint) {

        if (accessPoint.isSaved()) {

            //如果不需要输入密码，则直接调用连接接口
            connect(accessPoint);

        } else {
            //如果并未保存并且还有密码，则弹出密码输入框输入密码
            if (!accessPoint.isSaved() && accessPoint.isSecured) {
                showInputPasswordDialog(accessPoint, false);

            } else {
                //如果不需要输入密码，则直接调用连接接口
                connect(accessPoint);
            }
        }
    }


    /**
     * 输入密码弹窗
     *
     * @param accessPoint
     */
    private void showInputPasswordDialog(AccessPoint accessPoint, Boolean isPasswordError) {
        final boolean[] isPwd = {true};
        if (inputPwdDialog != null && inputPwdDialog.isShowing()) {
            inputPwdDialog.dismiss();
            inputPwdDialog = null;
        }
        inputPwdDialog = new CommonDialog.Builder(getActivity())
                .view(R.layout.wifi_dialog_input_password)
                .cancelTouchout(false)
                .style(R.style.Dialog)
                .setTitle(R.id.txt_wifi_name, accessPoint.ssid)
                .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        dismissInputPasswordDialog();
                    }
                })
                .addTextWatchListener(R.id.edt_password, new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        txtPwdError.setVisibility(View.GONE);
                        Button btnConfirm = inputPwdDialog.getView().findViewById(R.id.btn_confirm);
                        if (charSequence.length() < 8) {
                            btnConfirm.setClickable(false);
                            btnConfirm.setEnabled(false);
                            btnConfirm.setBackgroundResource(R.drawable.btn_un_click_green);
                        } else {
                            btnConfirm.setClickable(true);
                            btnConfirm.setEnabled(true);
                            btnConfirm.setBackgroundResource(R.drawable.shape_shutdown_btn_green);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {

                    }
                })
                .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        lastPassword = edtPassword.getText().toString();
                        accessPoint.setPassword(lastPassword);
                        connect(accessPoint);
                        dismissInputPasswordDialog();
                    }
                })

                .addViewOnclick(R.id.img_eye, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (isPwd[0]) {
                            //设置明文显示
                            HideReturnsTransformationMethod method = HideReturnsTransformationMethod.getInstance();
                            edtPassword.setTransformationMethod(method);
                            edtPassword.setSelection(edtPassword.getText().length());
                            view.setBackgroundResource(R.drawable.ic_eye_on);
                            isPwd[0] = false;
                        } else {
                            //设置密文显示
                            TransformationMethod method = PasswordTransformationMethod.getInstance();
                            edtPassword.setTransformationMethod(method);
                            edtPassword.setSelection(edtPassword.getText().length());
                            view.setBackgroundResource(R.drawable.ic_eye_off);
                            isPwd[0] = true;
                        }
                    }
                })
                .build();
        inputPwdDialog.show();
        edtPassword = inputPwdDialog.findViewById(R.id.edt_password);
        txtPwdError = inputPwdDialog.findViewById(R.id.txt_password_error);
        CommonUtils.setIsDialog(getContext(), true);

        Window inputPwdWindow = inputPwdDialog.getWindow();
        if (inputPwdWindow != null) {
            inputPwdWindow.setLayout(CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(inputPwdWindow.getAttributes());
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            layoutParams.x = CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_MARGIN_END);
            layoutParams.y = CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_MARGIN_BOTTOM);
            inputPwdWindow.setAttributes(layoutParams);
        }

        if (isPasswordError) {
            edtPassword.setText(lastPassword);
            txtPwdError.setVisibility(View.VISIBLE);
        }
        edtPassword.requestFocus();
        edtPassword.setSelection(edtPassword.getText().length());
        // 显示软键盘
        inputPwdWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    }

    private void dismissInputPasswordDialog() {
        if (inputPwdDialog != null && inputPwdDialog.isShowing()) {
            inputPwdDialog.dismiss();
            inputPwdDialog = null;
        }
        CommonUtils.setIsDialog(getContext(), false);
    }

    private void connect(AccessPoint accessPoint) {
        accessPoint.generateNetworkConfig();
        int networkId = wifiManager.addNetwork(accessPoint.wifiConfiguration);
        wifiManager.enableNetwork(networkId, true);
    }

    /**
     * 断开链接
     *
     * @param netId
     * @return
     */
    public boolean disconnectWifi(int netId) {

        if (null != wifiManager) {

            boolean isDisable = wifiManager.disableNetwork(netId);

            boolean isDisconnect = wifiManager.disconnect();

            return isDisable && isDisconnect;

        }

        return false;

    }

    /**
     * 忘记密码
     *
     * @param networkId
     */
    public void forgetWifi(int networkId) {
        boolean result = wifiManager.removeNetwork(networkId);
//        boolean result = wifiManager.removeNetwork(accessPoint.wifiConfiguration.networkId);
//        Toast.makeText(this, result ? "取消保存成功" : "取消保存失败", Toast.LENGTH_LONG).show();
    }

    public void handlePasswordError() {
        if (lastWifiConfiguration != null) {
            AccessPoint accessPoint = new AccessPoint(lastWifiConfiguration);
            accessPoint.setPasswordError(true);
            showInputPasswordDialog(accessPoint, true);
            forgetWifi(lastWifiConfiguration.networkId);
            lastWifiConfiguration = null;
            lastPassword = null;
        }

    }

    public void portalCurrentWifi() {
        if (lastWifiInfo.getNetworkId() != lastPortalNetworkId) {
            lastPortalNetworkId = lastWifiInfo.getNetworkId();
            Single.create((SingleOnSubscribe<Boolean>) emitter -> {
                        Network currentNetwork = getCurrentNetwork();
                        HttpURLConnection urlConnection = null;
                        try {
                            // 使用当前的网络打开链接
                            urlConnection = (HttpURLConnection) currentNetwork.openConnection(new URL("http://connect.rom.miui.com/generate_204"));
                            urlConnection.setInstanceFollowRedirects(false);
                            urlConnection.setConnectTimeout(10000);
                            urlConnection.setReadTimeout(10000);
                            urlConnection.setUseCaches(false);
                            urlConnection.getInputStream();
                            int responseCode = urlConnection.getResponseCode();
                            if (responseCode == 200 && urlConnection.getContentLength() == 0) {
                                responseCode = 204;
                            }
                            emitter.onSuccess(responseCode != 204 && responseCode >= 200 && responseCode <= 399);
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            if (urlConnection != null) {
                                urlConnection.disconnect();
                            }
                        }
                    })
                    .retry(throwable -> throwable instanceof UnknownHostException)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new SingleObserver<Boolean>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                        }

                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            if (aBoolean) {
                                // 调用网络登录界面
                                new AlertDialog.Builder(getActivity())
                                        .setTitle(getString(R.string.login_title))
                                        .setPositiveButton(getString(R.string.login), null)
                                        .setNegativeButton(getString(R.string.login_cancel), null)
                                        .show();
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            e.printStackTrace();
                        }
                    });
        }
    }

    /**
     * 根据 NetworkId 获取 WifiConfiguration 信息
     *
     * @param networkId 需要获取 WifiConfiguration 信息的 networkId
     * @return 指定 networkId 的 WifiConfiguration 信息
     */
    private WifiConfiguration getWifiConfigurationForNetworkId(int networkId) {
        final List<WifiConfiguration> configs = wifiManager.getConfiguredNetworks();
        if (configs != null) {
            for (WifiConfiguration config : configs) {
                if (lastWifiInfo != null && networkId == config.networkId) {
                    return config;
                }
            }
        }
        return null;
    }

    /**
     * 获取当前网络信息
     */
    public NetworkInfo getActiveNetworkInfo() {
        ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            return cm.getActiveNetworkInfo();
        }
        return null;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean isChecked) {
        wifiManager.setWifiEnabled(isChecked);
        if (isChecked) {
            //检查无线模块是否正常
            mHandler.removeMessages(MSG_CHECK_WIFI_DEVICE);
            mHandler.sendEmptyMessageDelayed(MSG_CHECK_WIFI_DEVICE, 2000);
            registerReceiver();
            //获取当前连接的网络信息
            lastNetworkInfo = getActiveNetworkInfo();
            lastWifiInfo = wifiManager.getConnectionInfo();
            setConnectedWifi();
            scanWifi();
        } else {
            swNetwork.setChecked(false);
            rvWiFiConnected.setVisibility(View.GONE);
            line.setVisibility(View.GONE);
            llNetwork.setVisibility(View.GONE);
            wifiConnectedList.clear();
            connectedAdapter.setConnectedWifi(wifiConnectedList, lastNetworkInfo);
            lastAccessPoints.clear();
            scanListAdapter.setAccessPoints(lastAccessPoints);
            release();
        }
    }
}
