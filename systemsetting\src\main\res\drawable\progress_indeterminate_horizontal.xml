<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--未加载的进度区域-->
    <item android:id="@android:id/background">
        <shape>
            <!--进度条的圆角-->
            <corners android:radius="3dp" />
            <!--未加载的进度区域颜色-->
            <solid android:color="@color/white_100"/>
            <stroke android:color="#1A525D6D" android:width="0.3dp"/>
        </shape>
    </item>
    <!--缓冲的进度的颜色，一般视频播放的缓冲区域-->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape>
                <!--进度条的圆角-->
                <corners android:radius="3dp" />
                <!--缓冲的进度的颜色，一般视频播放的缓冲进度-->
                <solid android:color="@color/white_100"/>
                <stroke android:color="#1A525D6D" android:width="0.3dp"/>
            </shape>
        </clip>
    </item>
    <!--已经加载完的进度的区域-->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <!--进度条的圆角-->
                <corners android:radius="3dp" />
                <!--已经加载完的进度的颜色-->
                <gradient  android:startColor="@color/btn_default_bg_green" android:endColor="@color/btn_default_bg_green" />
            </shape>
        </clip>
    </item>

</layer-list>