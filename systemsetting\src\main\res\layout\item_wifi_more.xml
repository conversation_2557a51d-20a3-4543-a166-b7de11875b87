<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="@dimen/main_width"
        android:layout_height="@dimen/main_height">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/screen_dialog_iv_back_width"
            android:layout_height="@dimen/screen_dialog_iv_back_height"
            android:layout_marginStart="@dimen/iv_back_margin_start"
            android:layout_marginTop="@dimen/iv_back_margin_top"
            android:src="@drawable/select_left_icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/setting_tv_title_margin_top"
            android:text="@string/wifi_more"
            android:textColor="@color/black_100"
            android:textSize="@dimen/shutdown_tittle_text_size" />

        <TextView
            android:id="@+id/txt_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/item_wifi_more_tv_save_margin_top"
            android:layout_marginEnd="@dimen/item_wifi_more_tv_save_margin_end"
            android:clickable="true"
            android:enabled="true"
            android:text="@string/save"
            android:textColor="@color/text_enable_green"
            android:textSize="@dimen/item_wifi_more_tv_save_text_size" />

        <View
            android:id="@+id/line"
            style="@style/Line"
            android:layout_below="@id/txt_save"
            android:layout_marginTop="@dimen/item_wifi_more_line_margin_top" />

        <FrameLayout
            android:id="@+id/fl_disconnect"
            android:layout_width="match_parent"
            android:layout_height="@dimen/item_wifi_more_tv_disconnect_height"
            android:layout_below="@id/line"
            android:layout_marginTop="@dimen/item_wifi_more_fl_disconnect_margin_top"
            android:background="@drawable/select_wifi_item_press_color">

            <ImageView
                android:id="@+id/iv_disconnect"
                style="@style/Main_ImageView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/item_wifi_more_iv_disconnect_margin_start"
                android:src="@drawable/select_disconnect_network_icon" />

            <TextView
                android:id="@+id/txt_disconnect"
                style="@style/WiFi_Text"
                android:text="@string/disconnect_network" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_forget"
            android:layout_width="match_parent"
            android:layout_height="@dimen/item_wifi_more_tv_disconnect_height"
            android:layout_below="@id/fl_disconnect"
            android:layout_marginTop="@dimen/fragment_wifi_rv_connected_margin_top"
            android:background="@drawable/select_wifi_item_press_color">

            <ImageView
                android:id="@+id/iv_forget"
                style="@style/Main_ImageView"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/item_wifi_more_iv_disconnect_margin_start"
                android:src="@drawable/select_forget_network_icon" />

            <TextView
                android:id="@+id/txt_forget"
                style="@style/WiFi_Text"
                android:text="@string/forget_network" />
        </FrameLayout>

        <View
            android:id="@+id/line1"
            style="@style/About_Line"
            android:layout_below="@id/fl_forget"
            android:layout_marginTop="@dimen/item_wifi_more_line1_margin_top" />

        <TextView
            style="@style/NetWork_TextView"
            android:layout_alignTop="@id/txt_mac_address"
            android:text="@string/mac_address" />

        <TextView
            android:id="@+id/txt_mac_address"
            style="@style/NetWork_TextView"
            android:layout_width="wrap_content"
            android:layout_below="@id/line1"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
            android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
            android:gravity="end"
            android:text="122:123:234:12"
            android:textColor="@color/black_70" />


        <TextView
            style="@style/NetWork_TextView"
            android:layout_alignTop="@id/txt_ip_setting"
            android:text="@string/ip_setting" />

        <TextView
            android:id="@+id/txt_ip_setting"
            style="@style/NetWork_TextView"
            android:layout_width="@dimen/item_wifi_more_tv_ip_setting_width"
            android:layout_below="@id/txt_mac_address"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/item_wifi_more_item_margin_top"
            android:layout_marginEnd="@dimen/item_wifi_more_item_margin_end"
            android:gravity="end"
            android:text="@string/auto"
            android:textColor="@color/black_70" />

        <ImageView
            android:id="@+id/img_arrow"
            android:layout_width="@dimen/item_wifi_iv_rank_width"
            android:layout_height="@dimen/item_wifi_iv_rank_width"
            android:layout_below="@id/txt_mac_address"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/item_wifi_more_iv_arrow_margin_top"
            android:layout_marginEnd="@dimen/item_wifi_more_iv_manual_margin_end"
            android:background="@drawable/ic_arrow_right" />

        <TextView
            android:id="@+id/txt_ip_address_title"
            style="@style/NetWork_TextView"
            android:layout_below="@id/txt_ip_setting"
            android:layout_marginTop="@dimen/item_wifi_more_item_margin_top"
            android:text="@string/ip_address" />

        <TextView
            android:id="@+id/txt_ip_address"
            style="@style/NetWork_TextView"
            android:layout_width="@dimen/fragment_network_content_width"
            android:layout_alignTop="@id/txt_ip_address_title"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
            android:gravity="end"
            android:text="**************"
            android:textColor="@color/black_70" />

        <RelativeLayout
            android:id="@+id/rl_manual"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/txt_ip_setting"
            android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top">

            <TextView
                style="@style/NetWork_TextView"
                android:layout_marginTop="@dimen/item_wifi_more_ip_address_margin_top"
                android:text="@string/ip_address" />

            <EditText
                android:id="@+id/edt_ip_address"
                style="@style/Network_EditText"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_ip"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_marginTop="@dimen/item_wifi_more_ip_address_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_more_iv_ip_marin_end"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_below="@id/edt_ip_address"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:text="@string/subnet_mask" />

            <EditText
                android:id="@+id/edt_subnet_mask"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_ip_address"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/item_wifi_more_et_subnet_mask_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_subnet_mask"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_below="@id/edt_ip_address"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_more_iv_ip_marin_end"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:text="@string/gateway" />

            <EditText
                android:id="@+id/edt_gateway"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/item_wifi_more_et_subnet_mask_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_gateway"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_more_iv_ip_marin_end"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/item_wifi_more_tv_dns_width"
                android:layout_below="@id/edt_gateway"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:text="@string/DNS" />

            <EditText
                android:id="@+id/edt_DNS"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_gateway"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/item_wifi_more_et_subnet_mask_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_iv_rank_margin_end"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_DNS"
                android:layout_width="@dimen/fragment_network_iv_ip_width"
                android:layout_height="@dimen/fragment_network_iv_ip_height"
                android:layout_below="@id/edt_gateway"
                android:layout_marginTop="@dimen/item_wifi_more_rl_manual_margin_top"
                android:layout_marginEnd="@dimen/item_wifi_more_iv_ip_marin_end"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />
        </RelativeLayout>

    </RelativeLayout>
</FrameLayout>