<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_tvview"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <android.media.tv.TvView
            android:id="@+id/tv_player"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="@dimen/launcher_main_tv_hint_width"
            android:layout_height="@dimen/launcher_main_tv_hint_height"
            android:layout_centerInParent="true"
            android:background="@drawable/hint_no_control_bg"
            android:gravity="center"
            android:text="@string/hint_no_control"
            android:textColor="@color/black_100"
            android:textSize="@dimen/launcher_main_tv_hint_text_size"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_no_sign"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_no_signal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/img_no_sign"
            android:layout_width="@dimen/launcher_main_iv_no_signal_width"
            android:layout_height="@dimen/launcher_main_iv_no_signal_height"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_iv_no_signal_margin_top"
            android:background="@drawable/ic_no_signal" />

        <TextView
            android:id="@+id/txt_no_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_tv_no_signal_margin_top"
            android:text="@string/no_sign"
            android:textColor="@color/white"
            android:textSize="@dimen/launcher_main_tv_no_signal_text_size" />

        <TextView
            android:id="@+id/txt_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/txt_no_sign"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_tv_current_signal_margin_top"
            android:text="@string/current_sign"
            android:textColor="@color/white_60"
            android:textSize="@dimen/launcher_main_tv_current_signal_text_size" />

        <Button
            android:id="@+id/btn_start"
            android:layout_width="@dimen/launcher_main_btn_start_width"
            android:layout_height="@dimen/launcher_main_btn_start_height"
            android:layout_below="@id/txt_msg"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_btn_start_margin_top"
            android:background="@drawable/shape_no_sign_btn_white"
            android:stateListAnimator="@null"
            android:text="@string/start_computer"
            android:textAllCaps="false"
            android:textColor="@color/black_100"
            android:textSize="@dimen/launcher_main_tv_current_signal_text_size" />

        <TextView
            android:id="@+id/txt_no_pc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/txt_msg"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_btn_start_margin_top"
            android:gravity="center"
            android:text="@string/no_pc_1"
            android:textColor="@color/white_80"
            android:textSize="@dimen/launcher_main_tv_no_signal_text_size"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/txt_msg_phone"
            android:layout_width="@dimen/launcher_main_tv_hotline_width"
            android:layout_height="@dimen/launcher_main_tv_hotline_height"
            android:layout_below="@id/btn_start"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/launcher_main_tv_hotline_margin_top"
            android:gravity="center"
            android:text="@string/no_sign_msg"
            android:textColor="@color/white_40"
            android:textSize="@dimen/launcher_main_tv_hint_text_size" />


    </RelativeLayout>

    <View
        android:id="@+id/bg_projection"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0"
        android:background="@drawable/bg_projection"
        android:visibility="gone" />
</RelativeLayout>