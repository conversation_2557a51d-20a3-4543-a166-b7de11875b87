2025-08-01 17:34:04.269   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-01 17:34:04.269   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-01 17:34:04.269   920-920   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service
2025-08-01 17:34:04.269   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-01 17:34:04.271   920-920   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:283 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:149 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:120 
2025-08-01 17:34:04.275   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity
2025-08-01 17:34:04.276   920-939   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@b984362 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-01 17:34:04.278   920-1006  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@b984362 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-01 17:34:04.654   920-920   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-01 17:34:04.667   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-01 17:34:04.724   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-01 17:34:04.724   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-01 17:34:04.834   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 1/3)
2025-08-01 17:34:04.834   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-01 17:34:04.850   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-01 17:34:14.870   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-01 17:34:14.870   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-01 17:34:14.876   920-2133  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-01 17:34:19.236   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-01 17:34:19.272   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-01 17:34:19.281   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:34:19.282   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 65535, PID: 22136
2025-08-01 17:34:19.284   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3890
2025-08-01 17:34:19.286   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3879
2025-08-01 17:34:19.288   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 42652, PID: 34817
2025-08-01 17:34:19.291   920-920   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 6
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 65535, PID: 22136
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 1155, PID: 19523
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3890
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3879
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 42652, PID: 34817
2025-08-01 17:34:21.524   920-920   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 17:34:23.531   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-01 17:34:23.531   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:34:23.531   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8711, PID: 25
2025-08-01 17:34:23.531   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-01 17:34:23.531   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 17:34:44.792   920-920   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed on attempt 1/2. Retrying after delay...
2025-08-01 17:34:47.794   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 17:35:08.110   920-920   ArrayMicOTA             com.eeo.systemsetting                W  ADB detection failed after 2 attempts. Checking USB device status...
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 4
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 65535, PID: 22136
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3890
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 42652, PID: 34817
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 17:35:08.114   920-920   ArrayMicOTA             com.eeo.systemsetting                W  USB device lost. Array mic may have been switched by other code.
2025-08-01 17:35:08.115   920-920   ArrayMicOTA             com.eeo.systemsetting                W  Overall update attempt 1 failed: USB device lost during ADB detection. Retrying...
2025-08-01 17:35:10.117   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 2/3)
2025-08-01 17:35:10.117   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Retry attempt, skipping initial delay and going directly to USB switch...
2025-08-01 17:35:10.118   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-01 17:35:10.118   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-01 17:35:10.119   920-2588  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-01 17:35:14.400   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 65535, PID: 22136
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3890
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3879
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 42652, PID: 34817
2025-08-01 17:35:14.405   920-920   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 3725, PID: 30307
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 65535, PID: 22136
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3890
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8183, PID: 3879
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 1155, PID: 19523
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Found USB device - VID: 8711, PID: 25
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-01 17:35:16.411   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 17:35:16.436   920-920   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-01 17:35:16.436   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-01 17:35:16.439   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-01 17:35:16.486   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-08-01 17:35:16.486   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-08-01 17:35:16.486   920-920   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-08-01 17:35:16.486   920-920   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE