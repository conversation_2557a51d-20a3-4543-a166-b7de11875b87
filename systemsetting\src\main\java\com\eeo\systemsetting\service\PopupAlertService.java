package com.eeo.systemsetting.service;

import android.accessibilityservice.AccessibilityService;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;

/**
 * 通过无障碍服务来实现监听其它应用的弹窗
 */
public class PopupAlertService extends AccessibilityService {
    public static final String TAG = "PopupAlertService";

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
//        Log.d(TAG, "onAccessibilityEvent: event type=" + event.getEventType() + " ,package=" + event.getPackageName());
        if (event.getPackageName().equals(Constant.MULTI_SCREEN_PKG_NAME)) {
            if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                if (event.getText() != null) {
                    if (event.getText().contains(getString(R.string.screen_pin_code2)) //投屏码弹窗
                            || event.getText().contains(getString(R.string.screen_air_play)) //AirPlay验证码弹窗
                    ) {
                        //亮屏、退出熄屏写
                        CommonUtils.screenOnAndExitAnnotation(this);
                        return;
                    }
                }
                if (!CommonUtils.isMultiScreen(this)) { //没有投屏时
                    //亮屏、退出熄屏写
                    CommonUtils.screenOnAndExitAnnotation(this);
                }
            } else if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED) {
                if (event.getSource() != null) {
                    if (event.getSource().toString().contains(getString(R.string.screen_permission_refuse)) //投屏授权弹窗
                            || event.getSource().toString().contains(getString(R.string.screen_pin_code2)) //投屏码弹窗
                            || event.getSource().toString().contains(getString(R.string.screen_air_play)) //AirPlay验证码弹窗
                    ) {
                        //亮屏、退出熄屏写
                        CommonUtils.screenOnAndExitAnnotation(this);
                        return;
                    }
                }
                if (!CommonUtils.isMultiScreen(this)) { //没有投屏时
                    //亮屏、退出熄屏写
                    CommonUtils.screenOnAndExitAnnotation(this);
                }
            }
        }
    }


    @Override
    public void onInterrupt() {

    }
}
