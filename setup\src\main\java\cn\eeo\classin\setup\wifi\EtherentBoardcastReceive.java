package cn.eeo.classin.setup.wifi;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.NetworkUtils;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.RequiresApi;

import java.net.Inet4Address;
import java.util.regex.Pattern;
//import android.preference.ListPreference;

public class EtherentBoardcastReceive extends BroadcastReceiver  {
    private static final String TAG = "EtherentBoardcast";

    private  static String mEthMode = null;//"static";
    private  static String mEthIpAddress = null;//"*************";
    private  static String mEthNetmask = null;//"*************";
    private  static String mEthGateway = null;//"***********";
    private  static String mEthdns1 = null;//"***********";
    private  static String mEthdns2 = null;//"null";

    private final static String nullIpInfo = "0.0.0.0";

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void onReceive(Context context, Intent intent) {



    }

    private void getNetConfigFromIntent(Intent intent){
        Bundle bundle = intent.getExtras();
        if (bundle.getString("netMode") != null)
            this.mEthMode = bundle.getString("netMode");
        if (bundle.getString("ipaddr") != null)
            this.mEthIpAddress = bundle.getString("ipaddr");
        if (bundle.getString("netMask")!= null)
            this.mEthNetmask = bundle.getString("netMask");
        if (bundle.getString("gateway")!= null)
            this.mEthGateway = bundle.getString("gateway");
        if (bundle.getString("dns1") != null)
            this.mEthdns1 = bundle.getString("dns1");
        if (bundle.getString("dns2") != null)
            this.mEthdns2 = bundle.getString("dns2");
    }
    private Inet4Address getIPv4Address(String text) {
        try {
            return (Inet4Address) NetworkUtils.numericToInetAddress(text);
        } catch (IllegalArgumentException|ClassCastException e) {
            return null;
        }
    }

    /*
     * convert subMask string to prefix length
     */
    private int maskStr2InetMask(String maskStr) {
        StringBuffer sb ;
        String str;
        int inetmask = 0;
        int count = 0;
        /*
         * check the subMask format
         */
        Pattern pattern = Pattern.compile("(^((\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])$)|^(\\d|[1-2]\\d|3[0-2])$");
        if (pattern.matcher(maskStr).matches() == false) {
            Log.e(TAG,"subMask is error");
            return 0;
        }

        String[] ipSegment = maskStr.split("\\.");
        for(int n =0; n<ipSegment.length;n++) {
            sb = new StringBuffer(Integer.toBinaryString(Integer.parseInt(ipSegment[n])));
            str = sb.reverse().toString();
            count=0;
            for(int i=0; i<str.length();i++) {
                i=str.indexOf("1",i);
                if(i==-1)
                    break;
                count++;
            }
            inetmask+=count;
        }
        return inetmask;
    }



}


