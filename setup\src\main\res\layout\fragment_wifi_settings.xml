<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_bg"
    android:scrollbarSize="2dp"
    android:scrollbarThumbVertical="@drawable/shape_network_scrollview">

    <RelativeLayout
        android:layout_width="310dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="221dp">

        <ImageView
            android:id="@+id/back_ic"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_left_w"
            android:visibility="visible"></ImageView>

        <TextView
            android:id="@+id/txt_wireless_network"
            android:layout_width="wrap_content"
            android:layout_height="21dp"
            android:layout_centerHorizontal="true"
            android:text="@string/wifi_network"
            android:textColor="@color/white_100"
            android:textSize="16sp"
            android:visibility="visible" />

        <View
            android:id="@+id/line1"
            style="@style/About_Line"
            android:layout_below="@+id/txt_wireless_network"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="26dp"
            android:layout_marginRight="0dp"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_connected"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@id/line1"
            android:layout_gravity="center_vertical"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_wifi_list"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_below="@id/rv_connected"
            android:layout_gravity="center_vertical" />

        <Button
            android:id="@+id/confirm_button"
            style="@style/MyButtonStyle"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:layout_below="@id/rv_wifi_list"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="52dp"
            android:enabled="false"
            android:text="@string/confirm"></Button>

        <TextView
            android:id="@+id/wired_internet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/confirm_button"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="18dp"
            android:alpha="0.7"
            android:text="@string/wired_internet_setting"
            android:textColor="@color/white_100"
            android:textSize="9sp" />
    </RelativeLayout>

</androidx.core.widget.NestedScrollView>