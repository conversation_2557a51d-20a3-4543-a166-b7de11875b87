package com.eeo.commonlibrary.util;

import android.app.ActivityManager;
import android.content.Context;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.SystemProperties;
import android.util.Log;
import android.util.TypedValue;

import java.lang.reflect.Method;
import java.util.List;

public class CommonUtil {
    private static final String TAG = "CommonUtil";
    public static final String MULTI_SCREEN_TOP_ACTIVITY_NAME = "com.android.toofifi.ui.activity.MultiScreenActivity";

    public static int dp2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, context.getResources().getDisplayMetrics());
    }

    public static WifiConfiguration getWifiApConfiguration(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiConfiguration wifiConfiguration = null;
        try {
            Method getWifiApConfiguration = wifiManager.getClass().getMethod("getWifiApConfiguration");
            wifiConfiguration = (WifiConfiguration) getWifiApConfiguration.invoke(wifiManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return wifiConfiguration;
    }

    public static String getDeviceName() {
        return SystemProperties.get("persist.eeo.device.name", "");
    }

    /**
     * 投屏中
     * 这里取2个task的topActivity(大屏控制界面activity可能在投屏上面)
     */
    public static boolean isMultiScreen(Context context) {
//        return Constant.MULTI_SCREEN_TOP_ACTIVITY_NAME.equals(getTopActivity(context));
        ActivityManager manager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(2);
        for (ActivityManager.RunningTaskInfo taskInfo : taskInfos) {
            String topActivityName = taskInfo.topActivity.getClassName();
            if (MULTI_SCREEN_TOP_ACTIVITY_NAME.equals(topActivityName)) {
                return true;
            }
        }
        return false;
    }

    public static String getTopActivity(Context context) {
        ActivityManager manager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(1);
        if (taskInfos != null && taskInfos.size() > 0) {
            String topActivityName = taskInfos.get(0).topActivity.getClassName();
            return topActivityName;
        }
        return "";
    }
}
