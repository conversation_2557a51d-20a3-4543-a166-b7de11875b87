<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_language"
        style="@style/NetWork_TextView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/fragment_locale_tv_language_height"
        android:layout_marginTop="@dimen/fragment_locale_tv_language_margin_top"
        android:gravity="center_vertical"
        android:text="@string/language_change" />

    <Spinner
        android:id="@+id/spinner_language"
        android:layout_width="@dimen/fragment_locale_spinner_width"
        android:layout_height="@dimen/fragment_locale_tv_language_height"
        android:layout_alignTop="@id/tv_language"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/fragment_locale_iv_language_margin_end"
        android:background="@null"
        android:dropDownVerticalOffset="25dp"
        android:gravity="center_vertical|end"
        android:popupBackground="@drawable/shape_network_auto_manual"
        android:popupElevation="@dimen/dp_0"/>

</RelativeLayout>