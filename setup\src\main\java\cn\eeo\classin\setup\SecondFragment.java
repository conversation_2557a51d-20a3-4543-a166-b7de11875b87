package cn.eeo.classin.setup;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;

import com.elvishew.xlog.XLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import cn.eeo.classin.setup.adapter.CustomListAdapter;
import cn.eeo.classin.setup.constant.SetupKeyTag;
import cn.eeo.classin.setup.databinding.FragmentSecondBinding;
import cn.eeo.classin.setup.utils.CommonUtils;

public class SecondFragment extends Fragment {

    private static final String TAG = "SecondFragment";
    private FragmentSecondBinding binding;
    Spinner region_spinner;
    Spinner language_spinner;
    SharedPreferences preferences;
    SharedPreferences.Editor editor;
    private static boolean region_selected_flag = false;
    private static boolean language_selected_flag =false;
    private LanguageChangeReceiver languageChangeReceiver;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentSecondBinding.inflate(inflater, container, false);
        return binding.getRoot();


    }

    @Override
    public void onResume() {
        super.onResume();
        languageChangeReceiver = new LanguageChangeReceiver();
        IntentFilter intentFilter = new IntentFilter(LanguageChangeReceiver.ACTION_LANGUAGE_CHANGED);
        requireActivity().registerReceiver(languageChangeReceiver, intentFilter);
    }

    @Override
    public void onPause() {
        super.onPause();
        requireActivity().unregisterReceiver(languageChangeReceiver);
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        languageChangeReceiver =null;
    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        preferences = getContext().getSharedPreferences(SetupKeyTag.SHAERED_TAG, Context.MODE_PRIVATE);
        editor = preferences.edit();
        // 获取Spinner元素 地区
         region_spinner = view.findViewById(R.id.spinner_region);
         language_spinner = view.findViewById(R.id.spinner_language);

        // 创建一个数组适配器，用于设置下拉框的选项
        String[] regionsArray = getResources().getStringArray(R.array.regions_array);
        String[] languageArray = getResources().getStringArray(R.array.languages_array);

        List<String> regionsList = new ArrayList<>();
        // 遍历字符串数组并将每个元素添加到列表
        for (String item : regionsArray) {
            regionsList.add(item);
        }
        List<String> languagesList = new ArrayList<>();
        // 遍历字符串数组并将每个元素添加到列表
        for (String item : languageArray) {
            languagesList.add(item);
        }

        // 设置下拉框的样式
        CustomListAdapter regions_adapter = new CustomListAdapter(getContext(),regionsList);
        CustomListAdapter languages_adapter = new CustomListAdapter(getContext(),languagesList);

        // 将适配器与Spinner关联
        region_spinner.setAdapter(regions_adapter);
        language_spinner.setAdapter(languages_adapter);

        binding.btNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (region_selected_flag && language_selected_flag){
                    if (editor==null){
                        XLog.d("editor is null");
                    }else {
                        editor.apply();
                    }
                    boolean EthernetEnable = EeoApplication.udi.isEthernetEnabled();
                    //如果有线网络连接成功了，直接跳转到
                    if (EthernetEnable || isWiFiConnected(getContext())){
                        NavHostFragment.findNavController(SecondFragment.this)
                                .navigate(R.id.action_SecondFragment_to_BasicSettingFragment);
                    }else {
                        NavHostFragment.findNavController(SecondFragment.this)
                                .navigate(R.id.action_SecondFragment_to_NetworkFragment);
                    }

                }else {
                    Toast.makeText(getContext(), getString(R.string.no_select_tips), Toast.LENGTH_SHORT).show();
                }

            }
        });
        language_spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                int l_p = preferences.getInt(SetupKeyTag.SHAERED_KEY_LANGUAGE_POSITION,0);
                if (position>0){
                    language_selected_flag =true;
                    editor.putInt(SetupKeyTag.SHAERED_KEY_LANGUAGE_POSITION,position);
                    editor.commit();

                }else {
                    language_selected_flag =false;
                }

                /*
                中文：chinese
                英语：english
                * */
                Intent languageChangedIntent;
                switch (position){
                    case 1:
                        editor.putString(SetupKeyTag.SHAERED_KEY_LANGUAGE,"chinese");
                        CommonUtils.setLanguage(Locale.CHINA);

                        //requireActivity().recreate();
                        if (l_p !=position){
                            Log.d(TAG,"diff value position 111");
                            languageChangedIntent = new Intent(LanguageChangeReceiver.ACTION_LANGUAGE_CHANGED);
                            requireActivity().sendBroadcast(languageChangedIntent);
                        }
                        break;
                    case 2:
                        CommonUtils.setLanguage(Locale.US);
                        editor.putString(SetupKeyTag.SHAERED_KEY_LANGUAGE,"english");
                        //requireActivity().recreate();
                        if (l_p !=position){
                            Log.d(TAG,"diff value position 222");
                            languageChangedIntent = new Intent(LanguageChangeReceiver.ACTION_LANGUAGE_CHANGED);
                            requireActivity().sendBroadcast(languageChangedIntent);
                        }
                        break;
                }
                String selectedData = adapterView.getItemAtPosition(position).toString();
                // 这里可以对选择的数据执行你的操作
                //Toast.makeText(getContext(), "你选择了: "+position+":" + selectedData, Toast.LENGTH_SHORT).show();
                XLog.d("language_spinner onItemSelected  position:"+position +" selectedData:"+selectedData);
                Log.d(TAG,"language_spinner onItemSelected  position:"+position +" selectedData:"+selectedData);
            }
            @Override
            public void onNothingSelected(AdapterView<?> parentView) {

                // 当没有选择项时触发的操作
            }
        });

        region_spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                String selectedData = adapterView.getItemAtPosition(position).toString();
                Log.d(TAG,"region_spinner onItemSelected  position:"+position +" selectedData:"+selectedData);
                editor.putInt(SetupKeyTag.SHAERED_KEY_REGION_POSITION,position);
                editor.commit();
                if (position>0){
                    region_selected_flag =true;
                }else {
                    region_selected_flag =false;
                }
                if (editor==null){
                    XLog.d("editor is null");
                    return;
                }

                /*
                    中国：China
                    新加坡：Singapore
                    美国：USA
                    韩国：Korea
                    阿联酋：UAE
                    英国：UK
                    其他：other
                * */
                switch (position){
                    case 1:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"China");
                        break;
                    case 2:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"Singapore");
                        break;
                    case 3:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"USA");
                        break;
                    case 4:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"Korea");
                        break;
                    case 5:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"UAE");
                        break;
                    case 6:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"UK");
                        break;
                    case 7:
                        editor.putString(SetupKeyTag.SHAERED_KEY_REGION,"other");
                        break;

                }

                XLog.d("onItemSelected  position:"+position +" selectedData:"+selectedData);
            }
            @Override
            public void onNothingSelected(AdapterView<?> parentView) {
                // 当没有选择项时触发的操作
            }
        });
    }

    private void switchLanguage() {
        // 切换语言逻辑...

        // 创建一个数组适配器，用于设置下拉框的选项
        String[] regionsArray = getResources().getStringArray(R.array.regions_array);
        String[] languageArray = getResources().getStringArray(R.array.languages_array);

        List<String> regionsList = new ArrayList<>();
        // 遍历字符串数组并将每个元素添加到列表
        for (String item : regionsArray) {
            regionsList.add(item);
        }
        List<String> languagesList = new ArrayList<>();
        // 遍历字符串数组并将每个元素添加到列表
        for (String item : languageArray) {
            languagesList.add(item);
        }

        // 设置下拉框的样式
        CustomListAdapter regions_adapter = new CustomListAdapter(getContext(),regionsList);
        CustomListAdapter languages_adapter = new CustomListAdapter(getContext(),languagesList);
        int l_p = preferences.getInt(SetupKeyTag.SHAERED_KEY_LANGUAGE_POSITION,0);
        int r_p = preferences.getInt(SetupKeyTag.SHAERED_KEY_REGION_POSITION,0);
        // 将适配器与Spinner关联
        region_spinner.setAdapter(regions_adapter);
        region_spinner.setSelection(r_p);
        language_spinner.setAdapter(languages_adapter);
        language_spinner.setSelection(l_p);
        if (binding.btNext!=null){
            binding.btNext.setText(R.string.next);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    private static boolean isWiFiConnected(Context context) {
        ConnectivityManager connManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);

        return networkInfo != null && networkInfo.isConnected();
    }
    private class LanguageChangeReceiver extends BroadcastReceiver {

        public static final String ACTION_LANGUAGE_CHANGED = "action_language_changed";

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() != null && intent.getAction().equals(ACTION_LANGUAGE_CHANGED)) {
                // 处理语言变化事件
                // 在这里可以触发 Fragment 中的语言切换逻辑
                Log.d(TAG,"onReceive ACTION_LANGUAGE_CHANGED");
                switchLanguage();
            }
        }
    }

}