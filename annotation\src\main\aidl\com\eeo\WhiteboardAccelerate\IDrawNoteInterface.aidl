// IDrawNoteInterface.aidl
package com.eeo.WhiteboardAccelerate;

import com.eeo.WhiteboardAccelerate.IDrawPointCallback;

interface IDrawNoteInterface {
    /**
     * 设置开启/关闭书写加速
     * @param true：开启书写加速  false:关闭书写加速
     */
    void setOpenWriteAccelerate(boolean status);
    /**
     * 设置进入/退出批注模式
     * @param true：进入批注  false:退出批注
     */
    void setEnterNoteModel(boolean model);
    /**
     * 设置批注画笔的宽度
     * @param width：画笔的实际宽度
     */
    void setPaintWidth(int width);
    /**
     * 设置批注画笔的颜色
     * @param a：alpha,r:red,g:green,b:blue
     */
    void setPaintColor(int a,int r,int g,int b);
    /**
     * 设置清除加速线条
     * @param
     */
    void setClearCanvas();
    /**
     * 注册批注加速坐标监听回调
     * @param callback
     */
    void registerCallback(IDrawPointCallback callback);
    /**
     * 解除批注加速坐标监听回调
     * @param callback
     */
    void unregisterCallback(IDrawPointCallback callback);
}