package com.eeo.annotation.view;

import static com.eeo.annotation.bean.Constant.HANDLER_ACCELERATE_POINT;
import static com.eeo.annotation.bean.Constant.HANDLER_CANVAS_CLEAR;
import static com.eeo.annotation.bean.Constant.HANDLER_START_PAINT;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import com.eeo.WhiteboardAccelerate.DrawAction;
import com.eeo.WhiteboardAccelerate.IDrawNoteInterface;
import com.eeo.WhiteboardAccelerate.IDrawPointCallback;
import com.eeo.annotation.R;
import com.eeo.annotation.bean.Constant;

import java.util.ArrayList;
import java.util.List;

public class DrawingView extends View{
    public static final String TAG = "DrawingView";

    private Canvas mBufferCanvas;
    private Bitmap mBufferBitmap;
    private Paint mPaint;
    private Paint mEraserPaint; //橡皮擦时不使用边缘阴影或模糊

    private int mPenSize;

    /**
     * 屏幕尺寸
     */
    private int mScreenWidth;
    private int mScreenHeight;

    private Bitmap mBackgroundBitmap;
    private volatile boolean mIsOpenAccelerate =false;//是否处于书写加速状态
    private volatile boolean mIsButtonEraser;//按钮板擦
    private Bitmap mButtonEraserBitmap; //橡皮擦背景图
    private float mEraserX;
    private float mEraserY;

    /**
     * 接触面积达到阈值使用橡皮
     */
    private volatile boolean mIsAreaEraser;
    private final static int THRESHOLD_ERASER_AREA = 300;
    private Bitmap mAreaEraserBitmap;

    /**
     * 手指是否按下
     */
    private volatile boolean mIsDrawing;

    private final Path mPath;

    private float mLastX =0;
    private float mLastY =0;

    /**
     * move阈值
     * 小于时不绘制
     */
    private static final float TOUCH_TOLERANCE = 4;

    /**
     * 使用书写加速由驱动报的点
     */
    private final Path mAcceleratePath;

    private Handler handler;

    public DrawingView(Context context) {
        this(context, null);
    }

    public DrawingView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DrawingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mPath = new Path();
        mAcceleratePath = new Path();
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mEraserPaint = new Paint();
        mEraserPaint.setStyle(Paint.Style.FILL);
        mEraserPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        handler = new pointHandler(Looper.getMainLooper());
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        init();
    }

    private void init() {
        mBufferBitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        mBufferCanvas = new Canvas(mBufferBitmap);
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.eraser);
        mButtonEraserBitmap = Bitmap.createScaledBitmap(bitmap, Constant.BUTTON_ERASER_SIZE, Constant.BUTTON_ERASER_SIZE, true);
        mAreaEraserBitmap = Bitmap.createScaledBitmap(bitmap, Constant.AREA_ERASER_SIZE, Constant.AREA_ERASER_SIZE, true);
    }

    public void setBrush(boolean isEraser, int color, int sizeInPixel) {
        mIsButtonEraser = isEraser;
        exitAreaEraser();
        mPenSize = sizeInPixel;
        if (isEraser) {
            //mEraserPaint.setStrokeWidth(sizeInPixel);
        } else {
            mPaint.setColor(color);
            mPaint.setStrokeWidth(mPenSize);
        }
        if (mIDrawNoteInterface != null) {
            try {
                Log.d(TAG,"alpha="+Color.alpha(color)+",red"+ Color.red(color)+ ",green="+Color.green(color)+",blue="+Color.blue(color));
                mIDrawNoteInterface.setPaintColor(Color.alpha(color), Color.red(color),  Color.green(color), Color.blue(color));
                mIDrawNoteInterface.setPaintWidth(sizeInPixel);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 清空批注
     */
    public void clear() {
        mBufferCanvas.drawColor(0, PorterDuff.Mode.CLEAR);
        setClearAccelerateCanvas();
    }
    private void exitAreaEraser(){
        mEraserX = 0;
        mEraserY = 0;
        if(mIsAreaEraser) {
            mIsAreaEraser = false;
        }
    }
    private void enterAreaEraser(){
        mEraserX = 0;
        mEraserY = 0;
        if(!mIsAreaEraser) {
            mIsAreaEraser = true;
            setClearAccelerateCanvas();
        }
    }
    private float getEraserX(float mX,float mEraserWidth){
        float x = mX;
        mEraserX = x - mEraserWidth / 2.0f;
        if (mEraserX < 0) {
            mEraserX = 0;
            x = mEraserWidth / 2.0f;
        } else if (mEraserX + mEraserWidth > mScreenWidth) {
            //屏幕右边缘
            mEraserX = mScreenWidth - mEraserWidth;
            x = mScreenWidth - mEraserWidth / 2.0f;
        }
        return x;
    }
    private float getEraserY(float mY,float mEraserWidth){
        float y = mY;
        mEraserY = y - mEraserWidth / 2.0f;
        if (mEraserY < 0) {
            mEraserY = 0;
            y = mEraserWidth / 2.0f;
        } else if (mEraserY + mEraserWidth > mScreenHeight) {
            //屏幕底边缘
            mEraserY = mScreenHeight - mEraserWidth;
            y = mScreenHeight - mEraserWidth / 2.0f;
        }
        return y;
    }
    private void eraserCanvas(float x1,float y1,float x2,float y2,int eraserSize){
        double dist = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        int distRange = eraserSize/2;
        float moveX = x1;
        float moveY = y1;
        if (dist > distRange) {
            double u1 = (x2 - x1) / dist;
            double u2 = (y2 - y1) / dist;
            double remainingDistance = dist;
            while (remainingDistance > distRange) {
                moveX += distRange * u1;
                moveY += distRange * u2;
                remainingDistance -= distRange;
                mBufferCanvas.drawCircle(getEraserX(moveX,eraserSize), getEraserY(moveY,eraserSize), eraserSize/2, mEraserPaint);
            }
        }
        mBufferCanvas.drawCircle(getEraserX(x2,eraserSize), getEraserY(y2,eraserSize), eraserSize/2, mEraserPaint);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //每次把画布清空
        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
        if (mBackgroundBitmap != null) {
            canvas.drawBitmap(mBackgroundBitmap, 0, 0, null);
        }
        //将buffer bitmap 绘制到画布上
        canvas.drawBitmap(mBufferBitmap, 0, 0, null);
        if(mEraserX == 0 && mEraserY ==0)
            return;
        if (mIsAreaEraser) {
            //绘制橡皮擦
            canvas.drawBitmap(mAreaEraserBitmap, mEraserX, mEraserY, null);
        } else if (mIsButtonEraser) {
            //绘制橡皮擦
            canvas.drawBitmap(mButtonEraserBitmap, mEraserX, mEraserY, null);
        }
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        super.onTouchEvent(event);
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                //handler.sendEmptyMessageDelayed(HANDLER_START_PAINT,30);
                break;
            case MotionEvent.ACTION_MOVE:
                float mTouchX = event.getX();
                float mTouchY = event.getY();
                if(mLastX == 0 && mLastY == 0){
                    mLastX = mTouchX;
                    mLastY = mTouchY;
                    mPath.moveTo(mTouchX,mTouchY);
                    break;
                }
                int touchMajor = (int) (event.getTouchMajor() * event.getTouchMinor());
                if (touchMajor > THRESHOLD_ERASER_AREA && !mIsOpenAccelerate) {
                    enterAreaEraser();
                }

                if(mIsAreaEraser){
                    eraserCanvas(mLastX,mLastY,mTouchX,mTouchY,Constant.AREA_ERASER_SIZE);
                    invalidate();
                }
                else if (mIsButtonEraser) {
                    eraserCanvas(mLastX,mLastY,mTouchX,mTouchY,Constant.BUTTON_ERASER_SIZE);
                    invalidate();
                }else if (Constant.ACCELERATE_ENABLE){
                    setOpenAccelerateCanvas();
                }
                else {
                    mPath.lineTo(mTouchX,mTouchY);
                    mBufferCanvas.drawPath(mPath, mPaint);
                    invalidate();
                }
                mLastX = mTouchX;
                mLastY = mTouchY;
                break;
            case MotionEvent.ACTION_UP:
                mLastX = 0;
                mLastY = 0;
                mPath.reset();
                if(event.getPointerCount() == 1){
                    exitAreaEraser();
                }
                invalidate();
                break;
        }
        return true;
    }

    private class pointHandler extends  Handler{
        public pointHandler(Looper mainLooper) {
            super(mainLooper);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case HANDLER_CANVAS_CLEAR:
                    setClearAccelerateCanvas();
                    break;
            }
        }
    }
    public void setScreenSize(int screenWidth, int screenHeight) {
        Log.d(TAG, "setScreenSize: " + screenWidth + "," + screenHeight);
        mScreenWidth = screenWidth;
        mScreenHeight = screenHeight;
    }

    public void setBackgroundBitmap(Bitmap bitmap) {
        mBackgroundBitmap = bitmap;
        invalidate();
    }
    public void setClearAccelerateCanvas(){
        try {
            invalidate();
            if(mIDrawNoteInterface != null && Constant.ACCELERATE_ENABLE) {
                if(mIsOpenAccelerate) {
                    mIsOpenAccelerate = false;
                    mIDrawNoteInterface.setOpenWriteAccelerate(false);
                }
                mIDrawNoteInterface.setClearCanvas();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setOpenAccelerateCanvas(){
        try {
            if(mIDrawNoteInterface != null && !mIsOpenAccelerate) {
                mIsOpenAccelerate = true;
                mIDrawNoteInterface.setOpenWriteAccelerate(true);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private IDrawNoteInterface mIDrawNoteInterface;

    /**
     * 驱动通知书写加速的坐标
     * 为确保画线一直，也使用这些点画线
     */
    private IDrawPointCallback mIDrawPointCallback = new IDrawPointCallback.Stub() {
        @Override
        public void onPointChanged(List<PointF> mPointF) throws RemoteException {
            if (mPointF == null) {
                return;
            }
            handler.removeMessages(HANDLER_CANVAS_CLEAR);
            for (int i = 0; i < mPointF.size(); ++i) {
                float rx = mPointF.get(i).x;
                float ry = mPointF.get(i).y;
                if (rx == 0 && ry == 0) {
                    if(!mIsOpenAccelerate){
                        mAcceleratePath.reset();
                    }
                    handler.sendEmptyMessageDelayed(HANDLER_CANVAS_CLEAR,200);
                }
                else{
                    if(i==0){
                        mAcceleratePath.moveTo(rx,ry);
                    }
                    else{
                        mAcceleratePath.lineTo(rx,ry);
                    }
                }
            }
            if(mIsOpenAccelerate){
                if(mPaint.getStyle() != Paint.Style.STROKE)
                    mPaint.setStyle(Paint.Style.STROKE);
                mBufferCanvas.drawPath(mAcceleratePath, mPaint);
                mAcceleratePath.reset();
            }
        }

        @Override
        public void onPointDrawAction(List<DrawAction> mDrawAction) throws RemoteException {
            handler.removeMessages(HANDLER_CANVAS_CLEAR);
            for (DrawAction mPointF : mDrawAction) {
                if (mPointF.x == 0 && mPointF.y == 0) {
                    if(!mIsOpenAccelerate){
                        mAcceleratePath.reset();
                    }
                    handler.sendEmptyMessageDelayed(HANDLER_CANVAS_CLEAR,200);
                }
                else{
                    if(mPointF.type==1){
                        mAcceleratePath.moveTo(mPointF.x,mPointF.y);
                    }
                    else if(mPointF.type==2){
                        mAcceleratePath.lineTo(mPointF.x,mPointF.y);
                    }
                    else if(mPointF.type==3){
                        mAcceleratePath.quadTo(mPointF.x,mPointF.y,mPointF.controlPointX,mPointF.controlPointY);
                    }
                    else if(mPointF.type==4){

                    }
                    else if(mPointF.type==5){
                        mAcceleratePath.close();
                    }
                }
            }
            if(mIsOpenAccelerate){
                if(mPaint.getStyle() != Paint.Style.FILL)
                    mPaint.setStyle(Paint.Style.FILL);
                mBufferCanvas.drawPath(mAcceleratePath, mPaint);
                mAcceleratePath.reset();
            }
        }
    };

    public void onAccelerateServiceConnected(IDrawNoteInterface iDrawNoteInterface) {
        mIDrawNoteInterface = iDrawNoteInterface;
        if (mIDrawNoteInterface != null) {
            try {
                mIDrawNoteInterface.setEnterNoteModel(true);
                Log.d(TAG,"registerCallback mIDrawPointCallback="+mIDrawPointCallback);
                mIDrawNoteInterface.registerCallback(mIDrawPointCallback);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void onAccelerateServiceDisconnected() {
        if (mIDrawNoteInterface != null) {
            try {
                Log.d(TAG,"unregisterCallback mIDrawPointCallback="+mIDrawPointCallback);
                mIDrawNoteInterface.unregisterCallback(mIDrawPointCallback);
                mIDrawNoteInterface.setEnterNoteModel(false);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }
}
