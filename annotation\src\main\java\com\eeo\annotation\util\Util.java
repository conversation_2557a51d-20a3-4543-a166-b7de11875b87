package com.eeo.annotation.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.Image;
import android.provider.Settings;

import java.nio.ByteBuffer;

public class Util {
    /**
     * 是否处于批注状态
     * 该Flag控制：是否禁止五指聚拢召唤画中画、是否退出批注等
     */
    public static final String KEY_IS_ANNOTATION = "is_annotation";

    public static Bitmap image2Bitmap(Image image) {
        if (image == null) {
            return null;
        }
        int width = image.getWidth();
        int height = image.getHeight();
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer buffer = planes[0].getBuffer();
        int pixelStride = planes[0].getPixelStride();
        int rowStride = planes[0].getRowStride();
        int rowPadding = rowStride - pixelStride * width;
        Bitmap bitmap = Bitmap.createBitmap(width + rowPadding / pixelStride, height, Bitmap.Config.ARGB_8888);
        bitmap.copyPixelsFromBuffer(buffer);
        bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height);
        image.close();
        return bitmap;
    }

    /**
     * 批注状态：0-未处于批注 1-批注 2-熄屏写
     */
    public static void setAnnotation(Context context, int annotationStatus) {
        Settings.Global.putInt(context.getContentResolver(), KEY_IS_ANNOTATION, annotationStatus);
    }
}
