package com.eeo.systemsetting.base;

import android.os.Bundle;
import android.view.Gravity;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;

import butterknife.ButterKnife;

public abstract class BaseActivity extends AppCompatActivity {

    public static final String TAG = "BaseActivity=====";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUtils.updateDensity(this);
        setWindowPosition(false);
        setContentView(getLayout());
        ButterKnife.bind(this);
        initDate();
    }

    abstract public int getLayout();

    abstract public void initDate();


    public void setWindowPosition(boolean isCenter) {
        WindowManager.LayoutParams attributes = getWindow().getAttributes();
        if (isCenter) {
            attributes.x = 0;
            attributes.gravity = Gravity.CENTER_HORIZONTAL | Gravity.BOTTOM;
        } else {
            attributes.gravity = Gravity.END | Gravity.BOTTOM;
            attributes.x = CommonUtils.dp2px(this, Constant.DIALOG_MARIN_END_IN_DP);
        }
        attributes.y = CommonUtils.dp2px(this, Constant.DIALOG_MARIN_BOTTOM_IN_DP);
        getWindow().setAttributes(attributes);

    }
}
