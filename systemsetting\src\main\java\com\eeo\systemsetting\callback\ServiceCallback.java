package com.eeo.systemsetting.callback;

import com.eeo.ota.bean.VersionInfo;

public interface ServiceCallback {
    //检测更新成功，版本号
    void onCheckSuccess(VersionInfo versionInfo);

    void onCheckFail(int errCode, String reason);

    void onDownloadProgress(int progress);

    void onDownloadCompleted(String outputFile);

    void onDownloadFailure(int errCode);

    void onInstallSuccess();

    void onInstallFail(String errMsg);

    void onInstallProgress(float progress);
}
