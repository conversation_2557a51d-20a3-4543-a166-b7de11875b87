2025-07-25 14:38:03.541  6505-6505  ApplicationLoaders      com.eeo.systemsetting                D  Returning zygote-cached class loader: /system/framework/android.test.base.jar
2025-07-25 14:38:03.541  6505-6505  ApplicationLoaders      com.eeo.systemsetting                D  Returning zygote-cached class loader: /system/framework/android.hidl.base-V1.0-java.jar
2025-07-25 14:38:03.541  6505-6505  ApplicationLoaders      com.eeo.systemsetting                D  Returning zygote-cached class loader: /system/framework/android.hidl.manager-V1.0-java.jar
2025-07-25 14:38:03.541  6505-6505  ApplicationLoaders      com.eeo.systemsetting                D  Returning zygote-cached class loader: /system/framework/android.hidl.base-V1.0-java.jar
2025-07-25 14:38:03.546  6505-6505  o.systemsettin          com.eeo.systemsetting                I  The ClassLoaderContext is a special shared library.
2025-07-25 14:38:03.556  6505-6505  o.systemsettin          com.eeo.systemsetting                W  ClassLoaderContext classpath size mismatch. expected=4, found=0 (PCL[/system/framework/com.android.location.provider.jar***********:/system/framework/services.jar***********:/system/framework/services.jar!classes2.dex***********:/system/framework/ethernet-service.jar***********] | PCL[])
2025-07-25 14:38:04.379  6505-6505  NetworkSecurityConfig   com.eeo.systemsetting                D  No Network Security Config specified, using platform default
2025-07-25 14:38:04.381  6505-6505  NetworkSecurityConfig   com.eeo.systemsetting                D  No Network Security Config specified, using platform default
2025-07-25 14:38:04.393  6505-6505  MultiDex                com.eeo.systemsetting                I  VM with version 2.1.0 has multidex support
2025-07-25 14:38:04.393  6505-6505  MultiDex                com.eeo.systemsetting                I  Installing application
2025-07-25 14:38:04.393  6505-6505  MultiDex                com.eeo.systemsetting                I  VM has multidex support, MultiDex support library is disabled.
2025-07-25 14:38:04.393  6505-6505  EeoApplication==        com.eeo.systemsetting                D  === EeoApplication onCreate ====
2025-07-25 14:38:04.483  6505-6529  TvApiSDKManager         com.eeo.systemsetting                D  getTvApi TvApiSDKManager_init getSystemService = true
2025-07-25 14:38:04.491  6505-6505  UdiSdk                  com.eeo.systemsetting                D  start udi client sdk init
2025-07-25 14:38:04.858  6505-6505  EeoApplication==        com.eeo.systemsetting                D  bindGestureService: 
2025-07-25 14:38:04.859  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1748 android.content.ContextWrapper.bindService:756 com.eeo.systemsetting.EeoApplication.bindSystemSettingService:205 com.eeo.systemsetting.EeoApplication.onCreate:123 android.app.Instrumentation.callApplicationOnCreate:1192 
2025-07-25 14:38:04.865  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@addb4b className=com.eeo.systemsetting.service.SystemSettingService packageName=com.eeo.systemsetting intent=null}
2025-07-25 14:38:04.866  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 121 BIND_SERVICE: 0 / BindServiceData{token=android.os.BinderProxy@addb4b intent=Intent { cmp=com.eeo.systemsetting/.service.SystemSettingService }}
2025-07-25 14:38:04.914  6505-6505  android_os_HwBinder     com.eeo.systemsetting                I  HwBinder: Starting thread pool for getting: vendor.amlogic.hardware.systemcontrol@1.1::ISystemControl/default
2025-07-25 14:38:04.943  6505-6505  UdiSdk                  com.eeo.systemsetting                D  register  v1/system/screen/status for com.eeo.udisdk.system.SystemManager$4@9d96928
2025-07-25 14:38:04.985  6505-6505  BinderSocketDns         com.eeo.systemsetting                D  [udi.ifpdos.com] by system server
2025-07-25 14:38:04.987  6505-6505  UdiSdk                  com.eeo.systemsetting                D  sse connect onSuccess
2025-07-25 14:38:05.017  6505-6505  UdiSdk                  com.eeo.systemsetting                D  addEventListener response 200
2025-07-25 14:38:05.022  6505-6505  UdiSdk                  com.eeo.systemsetting                D  addEventListener response 200
2025-07-25 14:38:05.026  6505-6505  UdiSdk                  com.eeo.systemsetting                D  register  v1/source/change for com.eeo.udisdk.source.SourceManager$2@4463540
2025-07-25 14:38:05.030  6505-6505  UdiSdk                  com.eeo.systemsetting                D  addEventListener response 200
2025-07-25 14:38:05.065  6505-6505  android_os_HwBinder     com.eeo.systemsetting                I  HwBinder: Starting thread pool for getting: vendor.amlogic.hardware.tvserver@1.0::ITvServer/default
2025-07-25 14:38:05.073  6505-6505  TvControlManager        com.eeo.systemsetting                I  connect to tvserve HIDL service success
2025-07-25 14:38:05.079  6505-6505  EeoApplication==        com.eeo.systemsetting                I  EeoApplication:onCreate - Sending broadcast to internal ArrayMicUpdateCheckReceiver.
2025-07-25 14:38:05.080  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1111 android.content.ContextWrapper.sendBroadcast:468 com.eeo.systemsetting.EeoApplication.onCreate:164 android.app.Instrumentation.callApplicationOnCreate:1192 android.app.ActivityThread.handleBindApplication:6735 
2025-07-25 14:38:05.082   648-5897  ActivityManager         pid-648                              E  Sending non-protected broadcast com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE from system 6505:com.eeo.systemsetting/1000 pkg com.eeo.systemsetting
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15938)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16640)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15955)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:16807)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2283)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2896)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1154)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:1123)
2025-07-25 14:38:05.082  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@59e5c79 className=com.eeo.systemsetting.service.PopupAlertService packageName=com.eeo.systemsetting intent=null}
2025-07-25 14:38:05.084  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 121 BIND_SERVICE: 0 / BindServiceData{token=android.os.BinderProxy@59e5c79 intent=Intent { cmp=com.eeo.systemsetting/.service.PopupAlertService }}
2025-07-25 14:38:05.085  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: BIND_APPLICATION
2025-07-25 14:38:05.085  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:05.087  6505-6505  ActivityThread          com.eeo.systemsetting                V  handleLaunchActivity 
2025-07-25 14:38:05.091  6505-6530  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 113 RECEIVER: 0 / ReceiverData{intent=Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver } packageName=com.eeo.systemsetting resultCode=-1 resultData=null resultExtras=null}
2025-07-25 14:38:05.095  6505-6505  ActivityThread          com.eeo.systemsetting                V  Handling launch of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}
2025-07-25 14:38:05.101  6505-6505  ActivityThread          com.eeo.systemsetting                V  performLaunchActivity
2025-07-25 14:38:05.115  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing launch of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}
2025-07-25 14:38:05.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}, dir=/data/app/~~36NYr1qcbqDoWx2lYzitGQ==/com.eeo.systemsetting--iAOegXLH63-0Tmytxq_iA==/base.apk
2025-07-25 14:38:05.144  6505-6505  FallbackHome            com.eeo.systemsetting                D  handleBootSource: bootForceSource=E_INPUT_SOURCE_PC ,bootSourceId=8 ,mBootSource=E_INPUT_SOURCE_PC ,mShouldHandleBootSource=false
2025-07-25 14:38:05.153  6505-6505  FallbackHome            com.eeo.systemsetting                D  handleAutoSwitchChannel: autoSwitchChannel=1->0
2025-07-25 14:38:05.173  6505-6505  SubDeviceUpdate         com.eeo.systemsetting                D  VID=3725(e8d) ,PID=30307(7663)
2025-07-25 14:38:05.173  6505-6505  SubDeviceUpdate         com.eeo.systemsetting                D  VID=8183(1ff7) ,PID=3890(f32)
2025-07-25 14:38:05.173  6505-6505  SubDeviceUpdate         com.eeo.systemsetting                D  touch vid = 8183
2025-07-25 14:38:05.176  6505-6505  TouchInterface          com.eeo.systemsetting                D  Init, 1, SDK is already init
2025-07-25 14:38:05.181  6505-6505  ota-ParseUtil           com.eeo.systemsetting                D  readFile:[	{		"name":"2024.03.09.M1427.R30G65N-D-A1-YO-50P.GD415.S1538.50P.V5405.0x9D69.bin",        "version":"5405",        "md5":"5139EEB5B421267ED81150A7CEE34BF2",        "path":"system/ota/touch_R30/2024.03.09.M1427.R30G65N-D-A1-YO-50P.GD415.S1538.50P.V5405.0x9D69.bin"    },    {			"name":"2024.03.09.M1428.R30G75N-D-A1-YO-50P.GD415.S1539.50P.V5406.0xA19B.bin",        "version":"5406",        "md5":"59A58F72F152A200B2698517123FD28F",        "path":"system/ota/touch_R30/2024.03.09.M1428.R30G75N-D-A1-YO-50P.GD415.S1539.50P.V5406.0xA19B.bin"    },	{			"name":"2024.03.09.M1429.R30G86N-D-A1-YO-50P.GD415.S1540.50P.V5405.0x5D59.bin",        "version":"5405",        "md5":"2CBCCFF23FFF561902ABAF15125FC87B",        "path":"system/ota/touch_R30/2024.03.09.M1429.R30G86N-D-A1-YO-50P.GD415.S1540.50P.V5405.0x5D59.bin"    },	{        "name":"2024.03.04.M1360.R30G110N-YO-A1-F-50P.GD415.S1532.50P.V5405.0x3F01.bin",        "version":"5405",        "md5":"0F3789D99426ACAC4387918D8B6F135A",        "path":"system/ota/touch_R30/2024.03.04.M1360.R30G110N-YO-A1-F-50P.GD415.S1532.50P.V5405.0x3F01.bin"    },	{        "name":"2024.12.17.M2424.E30A75N-D-A1-YO-40P.ATF403A.40P.V6202.0x7163.bin",        "version":"6202",        "md5":"2BFDB43615B616D786BBFFB91F3EC183",        "path":"system/ota/touch_E30/2024.12.17.M2424.E30A75N-D-A1-YO-40P.ATF403A.40P.V6202.0x7163.bin"    },	{        "name":"2024.12.17.M2425.E30A86N-D-A1-YO-40P.ATF403A.40P.V6202.0xAE78.bin",        "version":"6202",        "md5":"9DBEEEF11A62D007960002521220CA8D",        "path":"system/ota/touch_E30/2024.12.17.M2425.E30A86N-D-A1-YO-40P.ATF403A.40P.V6202.0xAE78.bin"    }]
2025-07-25 14:38:05.183  6505-6505  TouchZY                 com.eeo.systemsetting                D  parseVersionInfo: VersionInfo{url='null', versionName='5405', fileSize='null', filePath='system/ota/touch_R30/2024.03.09.M1429.R30G86N-D-A1-YO-50P.GD415.S1540.50P.V5405.0x5D59.bin', md5='2CBCCFF23FFF561902ABAF15125FC87B', description='null', descriptionEn='null'}
2025-07-25 14:38:05.206  6505-6505  TouchInterface          com.eeo.systemsetting                D  Firmware version:5406
2025-07-25 14:38:05.206  6505-6505  TouchInterface          com.eeo.systemsetting                D  GetFirmwareVersion: FC A3 85 1E 01 E7 B7 00 00 06 54 00 01 02 A7 53
2025-07-25 14:38:05.227  6505-6505  TouchInterface          com.eeo.systemsetting                D  Firmware checksum:0000B7E7
2025-07-25 14:38:05.227  6505-6505  TouchInterface          com.eeo.systemsetting                D  GetFirmwareCheckSum: FC A3 85 1E 01 E7 B7 00 00 06 54 00 01 02 A7 53
2025-07-25 14:38:05.232  6505-6505  TouchInterface          com.eeo.systemsetting                D  GetProductModel: FC A3 81 13 00 52 33 30 47 38 36 4E 2D 44 2D 41
2025-07-25 14:38:05.232  6505-6505  TouchZY                 com.eeo.systemsetting                D  touchCommunicator get 0 ,currentVersion=5406,checkSum=B7E7 ,productModel=R30G86N-D-A1-YO-50P
2025-07-25 14:38:05.240  6505-6505  TouchZY                 com.eeo.systemsetting                D  VID_TOUCH_ZHONGYUAN,productId:3890
2025-07-25 14:38:05.240  6505-6505  TouchZY                 com.eeo.systemsetting                D  checkVersion: current version=5406,target version=5405
2025-07-25 14:38:05.252  6505-6530  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 159 EXECUTE_TRANSACTION: 0 / android.app.servertransaction.ClientTransaction@8363
2025-07-25 14:38:05.277  6505-6530  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 159 EXECUTE_TRANSACTION: 0 / android.app.servertransaction.ClientTransaction@efd342
2025-07-25 14:38:05.300  6505-6505  DecorViewAndroidR       com.eeo.systemsetting                D  DecorViewAndroidR: 
2025-07-25 14:38:05.332  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing resume of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}} finished=true
2025-07-25 14:38:05.338  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:05.339  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:05.339  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:05.340  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: ENTER_ANIMATION_COMPLETE
2025-07-25 14:38:05.340  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: ENTER_ANIMATION_COMPLETE
2025-07-25 14:38:05.340  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CREATE_SERVICE
2025-07-25 14:38:05.341  6505-6505  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.systemsetting.service.SystemSettingService
2025-07-25 14:38:05.349  6505-6505  UdiSdk                  com.eeo.systemsetting                D  start udi client sdk init
2025-07-25 14:38:05.368  6505-6505  ota-Util                com.eeo.systemsetting                D  serial : BS86A240104027
2025-07-25 14:38:05.377  6505-6505  UdiSdk                  com.eeo.systemsetting                D  register  v1/system/rs232/data for com.eeo.udisdk.system.SystemManager$3@2f16e70
2025-07-25 14:38:05.382  6505-6505  UdiSdk                  com.eeo.systemsetting                D  addEventListener response 200
2025-07-25 14:38:05.389  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1748 android.content.ContextWrapper.bindService:756 com.eeo.systemsetting.rs232.Rs232Manager.bindAccelerateService:382 com.eeo.systemsetting.rs232.Rs232Manager.init:92 com.eeo.systemsetting.rs232.Rs232Manager.<init>:72 
2025-07-25 14:38:05.395  6505-6505  OpsCommManager          com.eeo.systemsetting                D  OpsCommManager initializing with JNI implementation...
2025-07-25 14:38:05.397  6505-6505  MacAddressHandler       com.eeo.systemsetting                D  MacAddressHandler initialized
2025-07-25 14:38:05.397  6505-6505  PowerControlHandler     com.eeo.systemsetting                D  PowerControlHandler initialized
2025-07-25 14:38:05.398  6505-6505  SignalSwitchHandler     com.eeo.systemsetting                D  SignalSwitchHandler initialized
2025-07-25 14:38:05.400  6505-6505  PowerControlHandler     com.eeo.systemsetting                D  PowerControlHandler initialized
2025-07-25 14:38:05.400  6505-6505  SignalSwitchHandler     com.eeo.systemsetting                D  SignalSwitchHandler initialized
2025-07-25 14:38:05.400  6505-6505  ProtocolHandler         com.eeo.systemsetting                D  ProtocolHandler initialized with MAC, PowerControl and SignalSwitch handlers
2025-07-25 14:38:05.405  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  SerialPort JNI library loaded
2025-07-25 14:38:05.405  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  Native library loaded successfully
2025-07-25 14:38:05.405  6505-6505  OpsCommManager          com.eeo.systemsetting                D  OpsCommManager initialized successfully with JNI implementation, PowerControl and SignalSwitch support
2025-07-25 14:38:05.406  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCreate: isAutoUpdate:true
2025-07-25 14:38:05.406  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCreate: downloaded :false
2025-07-25 14:38:05.406  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCreate: startTime : 1753425485406
2025-07-25 14:38:05.421  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCreate: endTime : -1
2025-07-25 14:38:05.421  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCreate: result : 487062.6348352778
2025-07-25 14:38:05.421  6505-6505  ota-Util                com.eeo.systemsetting                D  startAutoUpdateService
2025-07-25 14:38:05.422  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startForegroundService:1675 android.content.ContextWrapper.startForegroundService:725 com.eeo.ota.util.Util.startAutoUpdateService:358 com.eeo.systemsetting.service.SystemSettingService.onCreate:179 android.app.ActivityThread.handleCreateService:4190 
2025-07-25 14:38:05.426  6505-6505  OpsCommManager          com.eeo.systemsetting                D  Starting OpsCommManager with JNI implementation...
2025-07-25 14:38:05.427  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  Starting JniSerialPortManager...
2025-07-25 14:38:05.427  6505-6530  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@831c26e className=com.eeo.ota.service.AutoUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-25 14:38:05.424  6505-6505  o.systemsetting         com.eeo.systemsetting                I  type=1400 audit(0.0:1391): avc: denied { read write } for name="ttyS2" dev="tmpfs" ino=18676 scontext=u:r:system_app:s0 tcontext=u:object_r:hci_attach_dev:s0 tclass=chr_file permissive=1
2025-07-25 14:38:05.424  6505-6505  o.systemsetting         com.eeo.systemsetting                I  type=1400 audit(0.0:1392): avc: denied { open } for path="/dev/ttyS2" dev="tmpfs" ino=18676 scontext=u:r:system_app:s0 tcontext=u:object_r:hci_attach_dev:s0 tclass=chr_file permissive=1
2025-07-25 14:38:05.428  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Opening serial port: /dev/ttyS2
2025-07-25 14:38:05.428  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Serial port opened successfully, fd: 56
2025-07-25 14:38:05.428  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  Serial port opened successfully, fd: 56
2025-07-25 14:38:05.429  6505-6530  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@831c26e startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.service.AutoUpdateService }}
2025-07-25 14:38:05.428  6505-6505  o.systemsetting         com.eeo.systemsetting                I  type=1400 audit(0.0:1393): avc: denied { ioctl } for path="/dev/ttyS2" dev="tmpfs" ino=18676 ioctlcmd=0x5401 scontext=u:r:system_app:s0 tcontext=u:object_r:hci_attach_dev:s0 tclass=chr_file permissive=1
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Configuring serial port fd=56, baud=9600, data=8, stop=1, parity=0
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Serial port configuration verified:
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I    Input flags: 0x00000000
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I    Output flags: 0x00000000
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I    Control flags: 0x00000cbd
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I    Local flags: 0x00000000
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I    VMIN: 0, VTIME: 1
2025-07-25 14:38:05.431  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Serial port configured successfully
2025-07-25 14:38:05.431  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  Serial port configured successfully: 9600 8N1
2025-07-25 14:38:05.432  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Flushing serial port buffers for fd=56
2025-07-25 14:38:05.432  6505-6505  SerialPortJNI           com.eeo.systemsetting                I  Serial port buffers flushed successfully
2025-07-25 14:38:05.432  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  Serial buffers flushed successfully
2025-07-25 14:38:05.433  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  JNI read thread started successfully
2025-07-25 14:38:05.433  6505-6505  JniSerialPortManager    com.eeo.systemsetting                D  JniSerialPortManager started successfully
2025-07-25 14:38:05.433  6505-6505  OpsCommManager          com.eeo.systemsetting                D  OpsCommManager started successfully
2025-07-25 14:38:05.433  6505-6505  SystemSettingService    com.eeo.systemsetting                D  OpsCommManager start result: true
2025-07-25 14:38:05.434  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CREATE_SERVICE
2025-07-25 14:38:05.435  6505-6539  JniSerialPortManager    com.eeo.systemsetting                D  JNI read thread started
2025-07-25 14:38:05.436  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: BIND_SERVICE
2025-07-25 14:38:05.440  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: BIND_SERVICE
2025-07-25 14:38:05.440  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CREATE_SERVICE
2025-07-25 14:38:05.441  6505-6505  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.systemsetting.service.PopupAlertService
2025-07-25 14:38:05.448  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CREATE_SERVICE
2025-07-25 14:38:05.448  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: BIND_SERVICE
2025-07-25 14:38:05.452  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: BIND_SERVICE
2025-07-25 14:38:05.453  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: RECEIVER
2025-07-25 14:38:05.457  6505-6505  o.systemsettin          com.eeo.systemsetting                I  Waiting for a blocking GC ClassLinker
2025-07-25 14:38:05.461  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing receive of Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver }: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver}, dir=/data/app/~~36NYr1qcbqDoWx2lYzitGQ==/com.eeo.systemsetting--iAOegXLH63-0Tmytxq_iA==/base.apk
2025-07-25 14:38:05.462  6505-6505  ArrayMicUp...ckReceiver com.eeo.systemsetting                I  Received action to check Array Microphone update.
2025-07-25 14:38:05.464  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver.onReceive:20 android.app.ActivityThread.handleReceiver:4030 
2025-07-25 14:38:05.466  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: RECEIVER
2025-07-25 14:38:05.466  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:05.467  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:05.468  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:05.468  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing resume of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}} finished=true
2025-07-25 14:38:05.500  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 159 EXECUTE_TRANSACTION: 0 / android.app.servertransaction.ClientTransaction@e3127036
2025-07-25 14:38:05.502  6505-6523  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 159 EXECUTE_TRANSACTION: 0 / android.app.servertransaction.ClientTransaction@8382
2025-07-25 14:38:05.511  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:05.512  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  onReceive: android.net.conn.CONNECTIVITY_CHANGE ,mHasInit=false
2025-07-25 14:38:05.514  6505-6505  TetheringManager        com.eeo.systemsetting                I  registerTetheringEventCallback:com.eeo.systemsetting
2025-07-25 14:38:05.516  6505-6505  ota-Util                com.eeo.systemsetting                D  isNetworkConnect: true
2025-07-25 14:38:05.516  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  init: mIsIniting=false
2025-07-25 14:38:05.517  6505-6505  ota-Util                com.eeo.systemsetting                D  isNetworkConnect: true
2025-07-25 14:38:05.532  6505-6505  Rs232Manager            com.eeo.systemsetting                D  WhiteboardAccelerate onServiceConnected: 
2025-07-25 14:38:05.537  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CREATE_SERVICE
2025-07-25 14:38:05.538  6505-6505  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.service.AutoUpdateService
2025-07-25 14:38:05.541  6505-6505  ota-AutoUpdateService   com.eeo.systemsetting                D  onCreate
2025-07-25 14:38:05.551  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CREATE_SERVICE
2025-07-25 14:38:05.552  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: SERVICE_ARGS
2025-07-25 14:38:05.553  6505-6505  ota-AutoUpdateService   com.eeo.systemsetting                D  onStartCommand
2025-07-25 14:38:05.555  6505-6505  ota-Util                com.eeo.systemsetting                D  isNetworkConnect: true
2025-07-25 14:38:05.567  6505-6523  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 149 ENTER_ANIMATION_COMPLETE: 0 / android.os.BinderProxy@3bb977a
2025-07-25 14:38:05.588  6505-6505  ota-AutoUpdateService   com.eeo.systemsetting                D  setNextAlarm: Sat Jul 26 02:00:00 GMT+08:00 2025
2025-07-25 14:38:05.599  6505-6505  ota-AutoUpdateUtil      com.eeo.systemsetting                D  isNoon: false
2025-07-25 14:38:05.599  6505-6505  ota-AutoUpdateUtil      com.eeo.systemsetting                D  isNight: false
2025-07-25 14:38:05.603  6505-6523  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 116 STOP_SERVICE: 0 / android.os.BinderProxy@831c26e
2025-07-25 14:38:05.607  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: SERVICE_ARGS
2025-07-25 14:38:05.608  6505-6505  EeoApplication==        com.eeo.systemsetting                D  onServiceConnected: 
2025-07-25 14:38:05.610  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:05.611  6505-6505  ActivityThread          com.eeo.systemsetting                V  handleLaunchActivity 
2025-07-25 14:38:05.612  6505-6505  ActivityThread          com.eeo.systemsetting                V  Handling launch of ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}}
2025-07-25 14:38:05.612  6505-6505  ActivityThread          com.eeo.systemsetting                V  performLaunchActivity
2025-07-25 14:38:05.647  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing launch of ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}}
2025-07-25 14:38:05.647  6505-6505  ActivityThread          com.eeo.systemsetting                V  ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}}: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}, dir=/data/app/~~36NYr1qcbqDoWx2lYzitGQ==/com.eeo.systemsetting--iAOegXLH63-0Tmytxq_iA==/base.apk
2025-07-25 14:38:05.652  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  onCreate: 
2025-07-25 14:38:05.658  6505-6505  DecorViewAndroidR       com.eeo.systemsetting                D  DecorViewAndroidR: 
2025-07-25 14:38:05.967  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  getTvApi oK
2025-07-25 14:38:05.967  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                D  bindGestureService: 
2025-07-25 14:38:05.968  6505-6505  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1748 android.content.ContextWrapper.bindService:756 com.eeo.systemsetting.launcher.TifPlayerActivity.bindGestureService:1564 com.eeo.systemsetting.launcher.TifPlayerActivity.onCreate:321 android.app.Activity.performCreate:8010 
2025-07-25 14:38:05.973  6505-6523  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@7727c01 className=com.eeo.systemsetting.projection.GestureDetectorService packageName=com.eeo.systemsetting intent=null}
2025-07-25 14:38:05.974  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 121 BIND_SERVICE: 0 / BindServiceData{token=android.os.BinderProxy@7727c01 intent=Intent { cmp=com.eeo.systemsetting/.projection.GestureDetectorService }}
2025-07-25 14:38:06.018  6505-6505  TouchSliderThread       com.eeo.systemsetting                D  init
2025-07-25 14:38:06.018  6505-6505  TouchSlide...ryHardware com.eeo.systemsetting                I  Starting hardware detection for capacitive sliders...
2025-07-25 14:38:06.019  6505-6505  TouchSlide...ryHardware com.eeo.systemsetting                D  Attempting to read right slider (address: 0x15, register: 0x00)
2025-07-25 14:38:06.022  6505-6505  TouchSlide...ryHardware com.eeo.systemsetting                I  Right slider getI2cData returned: [0]
2025-07-25 14:38:06.022  6505-6505  TouchSlide...sErrorResp com.eeo.systemsetting                D  Right slider: Response is [0]. [Determined as valid, device present]
2025-07-25 14:38:06.022  6505-6505  TouchSlide...ryHardware com.eeo.systemsetting                I  [SUCCESS] Detected response from right capacitive slider.
2025-07-25 14:38:06.022  6505-6505  TouchSlide...ryHardware com.eeo.systemsetting                I  Capacitive slider hardware detection complete. At least one slider responded. hardwarePresent = true
2025-07-25 14:38:06.023  6505-6545  TouchSliderThread_Run   com.eeo.systemsetting                I  TouchSliderThread running...
2025-07-25 14:38:06.025  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  registerTvSettingReceiverAsUser 
2025-07-25 14:38:06.026  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  registerNotifyListener: ok
2025-07-25 14:38:06.026  6505-6545  TouchSliderThread       com.eeo.systemsetting                D  rightTouchStatus=0
2025-07-25 14:38:06.029  6505-6545  TouchSliderThread       com.eeo.systemsetting                D  leftTouchStatus=0
2025-07-25 14:38:06.034  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  onStart: 
2025-07-25 14:38:06.090  6505-6505  Udi-TouchManager        com.eeo.systemsetting                D  enableOsd:enable=false  ,response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/touch/osd/enable}
2025-07-25 14:38:06.093  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing resume of ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}} finished=false
2025-07-25 14:38:06.093  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  onResume: 
2025-07-25 14:38:06.094  6505-6505  ActivityThread          com.eeo.systemsetting                V  Resume ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}} started activity: false, hideForNow: false, finished: false
2025-07-25 14:38:06.108  6505-6505  ActivityThread          com.eeo.systemsetting                V  Resuming ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}} with isForward=true
2025-07-25 14:38:06.109  6505-6505  ActivityThread          com.eeo.systemsetting                V  Scheduling idle handler for ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}}
2025-07-25 14:38:06.114  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:06.115  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:06.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:06.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: ENTER_ANIMATION_COMPLETE
2025-07-25 14:38:06.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: ENTER_ANIMATION_COMPLETE
2025-07-25 14:38:06.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: STOP_SERVICE
2025-07-25 14:38:06.116  6505-6505  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.service.AutoUpdateService@cd3a956
2025-07-25 14:38:06.116  6505-6505  ota-AutoUpdateService   com.eeo.systemsetting                D  onDestroy
2025-07-25 14:38:06.119  6505-6505  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 119 CLEAN_UP_CONTEXT: 0 / android.app.ActivityThread$ContextCleanupInfo@23b7dd7
2025-07-25 14:38:06.122  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: STOP_SERVICE
2025-07-25 14:38:06.122  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  isLauncherGoToTv=false
2025-07-25 14:38:06.123  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  handleMessage: mFirstTuneTime == 0
2025-07-25 14:38:06.135  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  --->sourceId= SOURCE_ID_PC inputId= com.droidlogic.tvinput/.services.Hdmi3InputService/HW7 uri= content://android.media.tv/passthrough/com.droidlogic.tvinput%2F.services.Hdmi3InputService%2FHW7
2025-07-25 14:38:06.140  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  handleMessage: setTifInputSource
2025-07-25 14:38:06.141  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CREATE_SERVICE
2025-07-25 14:38:06.141  6505-6505  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.systemsetting.projection.GestureDetectorService
2025-07-25 14:38:06.145  6505-6505  GestureDetectorService  com.eeo.systemsetting                E  onCreate: 
2025-07-25 14:38:06.153  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CREATE_SERVICE
2025-07-25 14:38:06.154  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: BIND_SERVICE
2025-07-25 14:38:06.156  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: BIND_SERVICE
2025-07-25 14:38:06.201  6505-6546  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-07-25 14:38:06.224  6505-6546  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-07-25 14:38:06.225  6505-6546  TifPlayerActivity===    com.eeo.systemsetting                I  isSourceEnable: availableSource = {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-07-25 14:38:06.255  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CLEAN_UP_CONTEXT
2025-07-25 14:38:06.255  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CLEAN_UP_CONTEXT
2025-07-25 14:38:06.266  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                D  onServiceConnected: 
2025-07-25 14:38:06.300  6505-6531  TvControlManager        com.eeo.systemsetting                I  notifyCallback msg type:553
2025-07-25 14:38:06.304  6505-6505  ActivityThread          com.eeo.systemsetting                V  Reporting idle of ActivityRecord{162db2b token=android.os.BinderProxy@3bb977a {com.eeo.systemsetting/com.eeo.systemsetting.launcher.TifPlayerActivity}} finished=false
2025-07-25 14:38:06.316  6505-6524  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 159 EXECUTE_TRANSACTION: 0 / android.app.servertransaction.ClientTransaction@7fe0
2025-07-25 14:38:06.316  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: EXECUTE_TRANSACTION
2025-07-25 14:38:06.317  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing resume of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}} finished=true
2025-07-25 14:38:06.317  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing stop of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}
2025-07-25 14:38:06.318  6505-6505  ActivityThread          com.eeo.systemsetting                V  Finishing stop of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}: win=null
2025-07-25 14:38:06.318  6505-6505  ActivityThread          com.eeo.systemsetting                V  Performing finish of ActivityRecord{3306dbe token=android.os.BinderProxy@4c0a4d3 {com.eeo.systemsetting/com.eeo.systemsetting.launcher.FallbackHomeActivity}}
2025-07-25 14:38:06.321  6505-6505  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 119 CLEAN_UP_CONTEXT: 0 / android.app.ActivityThread$ContextCleanupInfo@9ba0ee1
2025-07-25 14:38:06.322  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: EXECUTE_TRANSACTION
2025-07-25 14:38:06.326  6505-6505  ActivityThread          com.eeo.systemsetting                V  >>> handling: CLEAN_UP_CONTEXT
2025-07-25 14:38:06.326  6505-6505  ActivityThread          com.eeo.systemsetting                V  <<< done: CLEAN_UP_CONTEXT
2025-07-25 14:38:06.334  6505-6531  TvControlManager        com.eeo.systemsetting                I  notifyCallback msg type:553
2025-07-25 14:38:06.534  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  onPostExecute: result={  "ip": "**************",  "city": "Shenzhen",  "region": "Guangdong",  "country": "CN",  "loc": "22.5455,114.0683",  "org": "AS4134 CHINANET-BACKBONE",  "postal": "518000",  "timezone": "Asia/Shanghai",  "readme": "https://ipinfo.io/missingauth"}
2025-07-25 14:38:06.535  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  init: mIsIniting=false
2025-07-25 14:38:06.539  6505-6505  ota-Util                com.eeo.systemsetting                D  isNetworkConnect: true
2025-07-25 14:38:06.540  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  connect: 
2025-07-25 14:38:06.772  6505-6505  o.systemsetting         com.eeo.systemsetting                I  type=1400 audit(0.0:1407): avc: denied { read write } for name="hidraw0" dev="tmpfs" ino=1822 scontext=u:r:system_app:s0 tcontext=u:object_r:hidraw_audio_device:s0 tclass=chr_file permissive=1
2025-07-25 14:38:06.772  6505-6505  o.systemsetting         com.eeo.systemsetting                I  type=1400 audit(0.0:1408): avc: denied { open } for path="/dev/hidraw0" dev="tmpfs" ino=1822 scontext=u:r:system_app:s0 tcontext=u:object_r:hidraw_audio_device:s0 tclass=chr_file permissive=1
2025-07-25 14:38:06.939  6505-6531  TvControlManager        com.eeo.systemsetting                I  notifyCallback msg type:502
2025-07-25 14:38:06.965  6505-6523  TifPlayerActivity===    com.eeo.systemsetting                I  executeNotifyAction11 action = notifySystemSignalChange
2025-07-25 14:38:06.966  6505-6523  TifPlayerActivity===    com.eeo.systemsetting                I  executeNotifyAction: bundle ! = null
2025-07-25 14:38:06.984  6505-6523  UdiSdk                  com.eeo.systemsetting                D  start udi client sdk init
2025-07-25 14:38:06.997  6505-6523  TifPlayerActivity===    com.eeo.systemsetting                D  mOpsHasSignalCount=1
2025-07-25 14:38:07.052  6505-6505  Compatibil...geReporter com.eeo.systemsetting                D  Compat change id reported: 147798919; UID 1000; state: DISABLED
2025-07-25 14:38:07.058  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  : MSG_REMOVE_NO_SIGNAL
2025-07-25 14:38:07.081  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  same inputid pass
2025-07-25 14:38:07.081  6505-6505  TifPlayerActivity===    com.eeo.systemsetting                I  handleMessage: setTifInputSource
2025-07-25 14:38:07.171  6505-6564  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXMqttConnection info 133  - Start connecting to tcp://WCI40IPKBR.iotcloud.tencentdevices.com:1883
2025-07-25 14:38:07.362  6505-6533  libEGL                  com.eeo.systemsetting                W  EGLNativeWindowType 0xe9dd60b8 disconnect failed
2025-07-25 14:38:07.413  6505-6575  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-07-25 14:38:07.414  6505-6575  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-07-25 14:38:07.712  6505-6564  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXMqttConnection info 133  - wait_for completion return
2025-07-25 14:38:07.716  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXMqttConnection info 133  - onSuccess! hashcode: 192597696
2025-07-25 14:38:07.716  6505-6578  ota-TencentCloud        com.eeo.systemsetting                D  onConnectCompleted: status:OK, msg=connected to tcp://WCI40IPKBR.iotcloud.tencentdevices.com:1883
2025-07-25 14:38:07.719  6505-6578  ota-TencentCloud        com.eeo.systemsetting                D  checkVersion: 
2025-07-25 14:38:07.736  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - Starting subscribe topic: $ota/update/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:07.740  6505-6578  ota-TencentCloud        com.eeo.systemsetting                D  reportCurrentFirmwareVersion: BS86_2_1_26
2025-07-25 14:38:07.742  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - Starting subscribe topic: $ota/update/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:07.746  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - tag OK
2025-07-25 14:38:07.749  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - Starting publish topic: $ota/report/WCI40IPKBR/BS86A240104027 Message: {"type":"report_version","report":{"version":"BS86_2_1_26"}}
2025-07-25 14:38:07.752  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - topic = $ota/report/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:07.754  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - message.toString() = {"type":"report_version","report":{"version":"BS86_2_1_26"}}
2025-07-25 14:38:07.757  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - mMqttClient != null = true
2025-07-25 14:38:07.762  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - reportDevVersion status OK
2025-07-25 14:38:07.765  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - connectComplete. reconnect flag is false
2025-07-25 14:38:07.769  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - onSubscribeCompleted status OK
2025-07-25 14:38:07.771  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - onSubscribeCompleted topic $ota/update/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:07.773  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - onSubscribeCompleted status OK
2025-07-25 14:38:07.775  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 DEBUG TXOTAImpl debug 145  - onSubscribeCompleted topic $ota/update/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:07.777  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - deliveryComplete, token.getMessageId:3
2025-07-25 14:38:07.811  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:07 INFO  TXOTAImpl info 133  - Received topic: $ota/update/WCI40IPKBR/BS86A240104027, id: 0, message: {"type":"report_version_rsp","version":"BS86_2_1_26","result_code":0,"result_msg":"success"}
2025-07-25 14:38:07.811  6505-6578  ota-TencentCloud        com.eeo.systemsetting                D  onReportFirmwareVersion:0, version:BS86_2_1_26, resultMsg:success
2025-07-25 14:38:08.429  6505-6505  ota-Util                com.eeo.systemsetting                D  isNetworkConnect: true
2025-07-25 14:38:08.429  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  checkVersion: 
2025-07-25 14:38:08.430  6505-6505  ota-TencentCloud        com.eeo.systemsetting                D  reportCurrentFirmwareVersion: BS86_2_1_26
2025-07-25 14:38:08.436  6505-6505  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 INFO  TXOTAImpl info 133  - Starting publish topic: $ota/report/WCI40IPKBR/BS86A240104027 Message: {"type":"report_version","report":{"version":"BS86_2_1_26"}}
2025-07-25 14:38:08.438  6505-6505  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 DEBUG TXOTAImpl debug 145  - topic = $ota/report/WCI40IPKBR/BS86A240104027
2025-07-25 14:38:08.440  6505-6505  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 DEBUG TXOTAImpl debug 145  - message.toString() = {"type":"report_version","report":{"version":"BS86_2_1_26"}}
2025-07-25 14:38:08.443  6505-6505  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 DEBUG TXOTAImpl debug 145  - mMqttClient != null = true
2025-07-25 14:38:08.447  6505-6505  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 DEBUG TXOTAImpl debug 145  - reportDevVersion status OK
2025-07-25 14:38:08.450  6505-6581  SystemSettingService    com.eeo.systemsetting                D  handleMessage: MSG_SEND_RS232,mSendRS232PowerOnRetryTimes=0
2025-07-25 14:38:08.458  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 INFO  TXOTAImpl info 133  - deliveryComplete, token.getMessageId:4
2025-07-25 14:38:08.477  6505-6581  Udi-SystemManager       com.eeo.systemsetting                D  sendRS232Data:data=[-86,-69,-52,1,0,0,1,-35,-18,-1]  , response = Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/system/rs232/data}
2025-07-25 14:38:08.492  6505-6578  System.out              com.eeo.systemsetting                I  2025/07/25 14:38:08 INFO  TXOTAImpl info 133  - Received topic: $ota/update/WCI40IPKBR/BS86A240104027, id: 0, message: {"type":"report_version_rsp","version":"BS86_2_1_26","result_code":0,"result_msg":"success"}
2025-07-25 14:38:08.494  6505-6578  ota-TencentCloud        com.eeo.systemsetting                D  onReportFirmwareVersion:0, version:BS86_2_1_26, resultMsg:success
2025-07-25 14:38:10.572  6505-6533  libEGL                  com.eeo.systemsetting                W  EGLNativeWindowType 0xe9dd60b8 disconnect failed
2025-07-25 14:38:18.439  6505-6505  SystemSettingService    com.eeo.systemsetting                I  onCheckFail: errCode:1002