package com.eeo.udisdk;

public class PcKeyboardCode {
    public static final byte KEY_NONE = (byte) 0x00;

    public static final byte CONTROL_KEY_BOARD_CTRL = (byte) 0x01;
    public static final byte CONTROL_KEY_BOARD_SHIFT = (byte) 0x02;
    public static final byte CONTROL_KEY_BOARD_ALT = (byte) 0x04;
    public static final byte CONTROL_KEY_BOARD_WIN = (byte) 0x08
            ;
    public static final byte EVENT_KEY_BOARD_REVERSED = (byte) 0x00;
    public static final byte EVENT_KEY_BOARD_CLICK = (byte) 0x01;
    public static final byte EVENT_KEY_BOARD_DOWN = (byte) 0x02;
    public static final byte EVENT_KEY_BOARD_UP = (byte) 0x03;

    public static final byte FUNCTION_KEY_BOARD_1 = (byte) 0x1E;
    public static final byte FUNCTION_KEY_BOARD_2 = (byte) 0x1F;
    public static final byte FUNCTION_KEY_BOARD_3 = (byte) 0x20;
    public static final byte FUNCTION_KEY_BOARD_4 = (byte) 0x21;
    public static final byte FUNCTION_KEY_BOARD_5 = (byte) 0x22;
    public static final byte FUNCTION_KEY_BOARD_6 = (byte) 0x23;
    public static final byte FUNCTION_KEY_BOARD_7 = (byte) 0x24;
    public static final byte FUNCTION_KEY_BOARD_8 = (byte) 0x25;
    public static final byte FUNCTION_KEY_BOARD_9 = (byte) 0x26;
    public static final byte FUNCTION_KEY_BOARD_0 = (byte) 0x27;
    public static final byte FUNCTION_KEY_BOARD_ENTER = (byte) 0x28;
    public static final byte FUNCTION_KEY_BOARD_ESC = (byte) 0x29;
    public static final byte FUNCTION_KEY_BOARD_BACKSPACE = (byte) 0x2A;
    public static final byte FUNCTION_KEY_BOARD_TAB = (byte) 0x2B;
    public static final byte FUNCTION_KEY_BOARD_SPACE = (byte) 0x2C;
    public static final byte FUNCTION_KEY_BOARD_MINUS = (byte) 0x2D;
    public static final byte FUNCTION_KEY_BOARD_PLUS = (byte) 0x2E;
    public static final byte FUNCTION_KEY_BOARD_LEFT_SQUARE_BRACKET = (byte) 0x2F;
    public static final byte FUNCTION_KEY_BOARD_RIGHT_SQUARE_BRACKET = (byte) 0x30;
    public static final byte FUNCTION_KEY_BOARD_RIGHT_SLASH = (byte) 0x31;
    public static final byte FUNCTION_KEY_BOARD_SEMICOLON = (byte) 0x33;
    public static final byte FUNCTION_KEY_BOARD_SINGLE_QUOTE_MARK = (byte) 0x34;
    public static final byte FUNCTION_KEY_BOARD_GRAVE_ACCENT = (byte) 0x35;
    public static final byte FUNCTION_KEY_BOARD_COMMA = (byte) 0x36;
    public static final byte FUNCTION_KEY_BOARD_STOP = (byte) 0x37;
    public static final byte FUNCTION_KEY_BOARD_LEFT_SLASH = (byte) 0x38;
    public static final byte FUNCTION_KEY_BOARD_CAPS_LOCK = (byte) 0x39;
    public static final byte FUNCTION_KEY_BOARD_F1 = (byte) 0x3A;
    public static final byte FUNCTION_KEY_BOARD_F2 = (byte) 0x3B;
    public static final byte FUNCTION_KEY_BOARD_F3 = (byte) 0x3C;
    public static final byte FUNCTION_KEY_BOARD_F4 = (byte) 0x3D;
    public static final byte FUNCTION_KEY_BOARD_F5 = (byte) 0x3E;
    public static final byte FUNCTION_KEY_BOARD_F6 = (byte) 0x3F;
    public static final byte FUNCTION_KEY_BOARD_F7 = (byte) 0x40;
    public static final byte FUNCTION_KEY_BOARD_F8 = (byte) 0x41;
    public static final byte FUNCTION_KEY_BOARD_F9 = (byte) 0x42;
    public static final byte FUNCTION_KEY_BOARD_F10 = (byte) 0x43;
    public static final byte FUNCTION_KEY_BOARD_F11 = (byte) 0x44;
    public static final byte FUNCTION_KEY_BOARD_F12 = (byte) 0x45;
    public static final byte FUNCTION_KEY_BOARD_A = (byte) 0x04;
    public static final byte FUNCTION_KEY_BOARD_B = (byte) 0x05;
    public static final byte FUNCTION_KEY_BOARD_C = (byte) 0x06;
    public static final byte FUNCTION_KEY_BOARD_D = (byte) 0x07;
    public static final byte FUNCTION_KEY_BOARD_E = (byte) 0x08;
    public static final byte FUNCTION_KEY_BOARD_F = (byte) 0x09;
    public static final byte FUNCTION_KEY_BOARD_G = (byte) 0x0A;
    public static final byte FUNCTION_KEY_BOARD_H = (byte) 0x0B;
    public static final byte FUNCTION_KEY_BOARD_I = (byte) 0x0C;
    public static final byte FUNCTION_KEY_BOARD_J = (byte) 0x0D;
    public static final byte FUNCTION_KEY_BOARD_K = (byte) 0x0E;
    public static final byte FUNCTION_KEY_BOARD_L = (byte) 0x0F;
    public static final byte FUNCTION_KEY_BOARD_M = (byte) 0x10;
    public static final byte FUNCTION_KEY_BOARD_N = (byte) 0x11;
    public static final byte FUNCTION_KEY_BOARD_O = (byte) 0x12;
    public static final byte FUNCTION_KEY_BOARD_P = (byte) 0x13;
    public static final byte FUNCTION_KEY_BOARD_Q = (byte) 0x14;
    public static final byte FUNCTION_KEY_BOARD_R = (byte) 0x15;
    public static final byte FUNCTION_KEY_BOARD_S = (byte) 0x16;
    public static final byte FUNCTION_KEY_BOARD_T = (byte) 0x17;
    public static final byte FUNCTION_KEY_BOARD_U = (byte) 0x18;
    public static final byte FUNCTION_KEY_BOARD_V = (byte) 0x19;
    public static final byte FUNCTION_KEY_BOARD_W = (byte) 0x1A;
    public static final byte FUNCTION_KEY_BOARD_X = (byte) 0x1B;
    public static final byte FUNCTION_KEY_BOARD_Y = (byte) 0x1C;
    public static final byte FUNCTION_KEY_BOARD_Z = (byte) 0x1D;
    public static final byte FUNCTION_KEY_BOARD_PRTSC_SYSRQ = (byte) 0x46;
    public static final byte FUNCTION_KEY_BOARD_SCROLL_LOCK = (byte) 0x47;
    public static final byte FUNCTION_KEY_BOARD_PAUSE_BREAK = (byte) 0x48;
    public static final byte FUNCTION_KEY_BOARD_INSERT = (byte) 0x49;
    public static final byte FUNCTION_KEY_BOARD_HOME = (byte) 0x4A;
    public static final byte FUNCTION_KEY_BOARD_PAGE_UP = (byte) 0x4B;
    public static final byte FUNCTION_KEY_BOARD_DELETE = (byte) 0x4C;
    public static final byte FUNCTION_KEY_BOARD_END = (byte) 0x4D;
    public static final byte FUNCTION_KEY_BOARD_PAGE_DOWN = (byte) 0x4E;
    public static final byte FUNCTION_KEY_BOARD_RIGHT = (byte) 0x4F;
    public static final byte FUNCTION_KEY_BOARD_LEFT = (byte) 0x50;
    public static final byte FUNCTION_KEY_BOARD_DOWN = (byte) 0x51;
    public static final byte FUNCTION_KEY_BOARD_UP = (byte) 0x52;
    public static final byte FUNCTION_KEY_BOARD_VOLUME_UP = (byte) 0x7F;
    public static final byte FUNCTION_KEY_BOARD_VOLUME_DOWN = (byte) 0x80;
    public static final byte FUNCTION_KEY_BOARD_VOLUME_MUTE = (byte) 0x81;
    public static final byte FUNCTION_KEY_SLEEP = (byte) 0xF8;
}
