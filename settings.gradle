pluginManagement {
    repositories {
//        gradlePluginPortal()
//        google()
//        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            //测试版本
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
//        maven { url 'https://jitpack.io' }
//        google()
//        mavenCentral()

//        mavenLocal()
//        maven {
//            url "https://maven.google.com"
//        }
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            //测试版本
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
        maven { url 'https://jitpack.io' }
    }
}
rootProject.name = "amlogic-t982-app"
include ':systemsetting'
include ':libtouchsdk'
include ':commonlibrary'
include ':libudisdk'
include ':annotation'
include ':ota'
include ':setup'
