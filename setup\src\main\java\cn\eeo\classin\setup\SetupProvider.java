package cn.eeo.classin.setup;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.elvishew.xlog.XLog;

import java.util.HashMap;
import java.util.Map;

import cn.eeo.classin.setup.constant.SetupKeyTag;

public class SetupProvider extends ContentProvider {
    // Content Provider的标识符
    public static final String AUTHORITY = "cn.eeo.setup";
    //Cursor
    // URI用于访问信息
    public static final Uri CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/settings");
    private static final UriMatcher sUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
    private static final int SETTING_TABLE_CODE = 1;
    private static final String TAG = "SetupProvider";
    @Override
    public boolean onCreate() {
        return false;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] strings, @Nullable String s, @Nullable String[] strings1, @Nullable String s1) {
        XLog.d("query");
        String[] columnNames = {"Key", "Value"};
        MatrixCursor cursor = new MatrixCursor(columnNames);
        switch (sUriMatcher.match(uri)) {
            case SETTING_TABLE_CODE:
                Map<String, String> map = new HashMap<>();
                SharedPreferences sharedPreferences = getContext().getSharedPreferences(SetupKeyTag.SHAERED_TAG, Context.MODE_PRIVATE);

                String value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_USERTYPE, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_USERTYPE,value);

                value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_LANGUAGE, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_LANGUAGE,value);

                value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_REGION, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_REGION,value);

                value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_ORGANIZATION, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_ORGANIZATION,value);

                value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_ADDRESS, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_ADDRESS,value);

                value = sharedPreferences.getString(SetupKeyTag.SHAERED_KEY_PROJECTION, "unknow");
                map.put(SetupKeyTag.SHAERED_KEY_PROJECTION,value);

                // 遍历 Map 并将键值对添加到 Cursor
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    String key = entry.getKey();
                    String value1 = entry.getValue();
                    cursor.addRow(new Object[]{key, value1});
                }

                break;
            default:
                throw new IllegalArgumentException("Unknown URI: " + uri);
        }

        return cursor;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues contentValues) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues contentValues, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }
}
