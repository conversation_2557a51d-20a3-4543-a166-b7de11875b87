#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Windows与Android串口通信测试脚本
用于验证字节转换问题的修复情况
"""

import serial
import time
import sys
import threading

class SerialTester:
    def __init__(self):
        self.ser = None
        self.is_monitoring = False
        self.received_data = b''
        
    def setup_serial(self, port, baudrate=9600):
        """配置串口"""
        try:
            self.ser = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=0.1,  # 短超时用于非阻塞读取
                rtscts=False,
                dsrdtr=False,
                xonxoff=False
            )
            return True
        except Exception as e:
            print(f"❌ 串口配置失败: {e}")
            return False
    
    def start_monitoring(self):
        """开始监听响应"""
        self.is_monitoring = True
        self.received_data = b''
        
        def monitor_thread():
            print("📡 开始监听Android响应...")
            start_time = time.time()
            
            while self.is_monitoring and (time.time() - start_time) < 10:  # 10秒超时
                try:
                    if self.ser.in_waiting > 0:
                        chunk = self.ser.read(self.ser.in_waiting)
                        self.received_data += chunk
                        
                        # 实时显示接收到的数据
                        chunk_hex = ' '.join([f'{b:02X}' for b in chunk])
                        recv_time = time.time() - start_time
                        print(f"📥 接收 (+{recv_time:.3f}s): {chunk_hex}")
                        
                    time.sleep(0.01)
                except Exception as e:
                    print(f"❌ 监听异常: {e}")
                    break
                    
            self.is_monitoring = False
            print("📡 监听结束")
        
        thread = threading.Thread(target=monitor_thread, daemon=True)
        thread.start()
    
    def send_mac_request(self):
        """发送MAC地址获取命令"""
        cmd = bytes([0x7F, 0x0A, 0x99, 0xA2, 0xB3, 0xC4, 0x02, 0xFF, 0xF1, 0x04, 0xFF, 0xFF, 0xCF])
        
        print(f"\n📤 发送MAC地址获取命令:")
        cmd_hex = ' '.join([f'{b:02X}' for b in cmd])
        print(f"   数据: {cmd_hex}")
        print(f"   长度: {len(cmd)} 字节")
        
        try:
            self.ser.reset_input_buffer()
            self.ser.reset_output_buffer()
            
            bytes_sent = self.ser.write(cmd)
            self.ser.flush()
            
            send_time = time.strftime('%H:%M:%S.%f')[:-3]
            print(f"   时间: {send_time}")
            print(f"   状态: ✅ 发送成功 ({bytes_sent} 字节)")
            return True
            
        except Exception as e:
            print(f"   状态: ❌ 发送失败 - {e}")
            return False
    
    def analyze_response(self):
        """分析接收到的响应"""
        print(f"\n📊 响应分析:")
        print(f"=" * 60)
        
        if not self.received_data:
            print("❌ 未收到任何响应")
            return False
            
        total_hex = ' '.join([f'{b:02X}' for b in self.received_data])
        print(f"✅ 收到响应 ({len(self.received_data)} 字节):")
        print(f"   {total_hex}")
        
        # 字节转换检查
        print(f"\n🔍 字节转换检查:")
        has_c4 = 0xC4 in self.received_data
        has_e4 = 0xE4 in self.received_data
        has_cf = 0xCF in self.received_data
        has_ef = 0xEF in self.received_data
        
        print(f"   C4 存在: {'✅' if has_c4 else '❌'}")
        print(f"   E4 存在: {'⚠️' if has_e4 else '❌'} {'(可能是C4转换后)' if has_e4 else ''}")
        print(f"   CF 存在: {'✅' if has_cf else '❌'}")
        print(f"   EF 存在: {'⚠️' if has_ef else '❌'} {'(可能是CF转换后)' if has_ef else ''}")
        
        # 协议分析
        if len(self.received_data) >= 17:
            print(f"\n🎯 协议格式分析:")
            print(f"   帧头: {self.received_data[0]:02X} {'✅' if self.received_data[0] == 0x7F else '❌'}")
            print(f"   长度: {self.received_data[1]:02X}")
            print(f"   帧尾: {self.received_data[-1]:02X} {'✅' if self.received_data[-1] in [0xCF, 0xEF] else '❌'}")
            
            if len(self.received_data) >= 16:
                mac_bytes = self.received_data[10:16]
                mac_str = ':'.join([f'{b:02X}' for b in mac_bytes])
                print(f"   MAC地址: {mac_str}")
        
        # 转换问题诊断
        print(f"\n🩺 问题诊断:")
        if has_e4 and not has_c4:
            print("   ⚠️ 检测到 C4→E4 字节转换")
        if has_ef and not has_cf:
            print("   ⚠️ 检测到 CF→EF 字节转换")
        if (has_c4 or not has_e4) and (has_cf or not has_ef):
            print("   ✅ 未检测到字节转换问题")
        
        return True
    
    def run_test(self, port):
        """运行完整测试"""
        print("=" * 60)
        print("🚀 增强版串口通信测试")
        print("=" * 60)
        
        if not self.setup_serial(port):
            return False
            
        print(f"✅ 串口 {port} 配置成功")
        print(f"   波特率: 9600")
        print(f"   数据位: 8")
        print(f"   校验位: N")
        print(f"   停止位: 1")
        print(f"   流控制: 禁用")
        
        try:
            input("\n⏸️ 按回车键开始测试...")
            
            # 开始监听
            self.start_monitoring()
            time.sleep(0.5)  # 给监听线程启动时间
            
            # 发送命令
            if not self.send_mac_request():
                return False
            
            # 等待响应
            print(f"\n⏳ 等待响应...")
            for i in range(50):  # 5秒等待
                if self.received_data:
                    break
                time.sleep(0.1)
            
            # 停止监听
            self.is_monitoring = False
            time.sleep(0.5)
            
            # 分析结果
            return self.analyze_response()
            
        finally:
            if self.ser and self.ser.is_open:
                self.ser.close()
                print(f"\n🔒 串口 {port} 已关闭")

def main():
    tester = SerialTester()
    
    # 获取串口号
    port = input("请输入串口号 (例如: COM1, COM3): ").strip()
    if not port:
        port = "COM1"
    
    try:
        success = tester.run_test(port)
        
        print(f"\n" + "=" * 60)
        print("📈 测试结果总结:")
        print("=" * 60)
        
        if success and tester.received_data:
            # 检查字节转换情况
            has_c4 = 0xC4 in tester.received_data
            has_e4 = 0xE4 in tester.received_data
            has_cf = 0xCF in tester.received_data
            has_ef = 0xEF in tester.received_data
            
            if (has_c4 or not has_e4) and (has_cf or not has_ef):
                print("🎉 测试成功: 串口配置修复生效，未检测到字节转换问题！")
            elif has_e4 or has_ef:
                print("⚠️ 测试部分成功: 仍存在字节转换，但Android应该能够处理")
            else:
                print("✅ 测试成功: 收到有效响应")
        else:
            print("❌ 测试失败: 未收到响应或通信异常")
            
        print("\n💡 下一步建议:")
        if success and tester.received_data:
            has_e4 = 0xE4 in tester.received_data
            has_ef = 0xEF in tester.received_data
            if has_e4 or has_ef:
                print("   1. Android代码已更新支持转换后的字节")
                print("   2. 可以继续使用当前配置")
                print("   3. 检查Android日志确认MAC地址获取成功")
            else:
                print("   1. 配置修复成功，可以正常使用")
                print("   2. 建议进行更多轮测试确认稳定性")
        else:
            print("   1. 检查串口连接是否正确")
            print("   2. 确认Android端应用是否运行")
            print("   3. 检查串口号是否正确")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
    
    input("\n⏸️ 按回车键退出...")

if __name__ == "__main__":
    main() 