<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_select"
        style="@style/IP_Dialog_Select_Image"
        android:layout_marginVertical="@dimen/spinner_dropdown_item_iv_margin_vertical"
        android:background="@drawable/set_ic_checkbox"
        />

    <TextView
        android:id="@+id/tv_item"
        style="@style/NetWork_TextView"
        android:layout_width="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/spinner_dropdown_item_tv_margin_start"
        android:layout_marginEnd="@dimen/dialog_network_auto_manual_line_margin_right"
        android:layout_toEndOf="@id/iv_select"
        android:gravity="end"
        android:text="@string/simplified_chinese"
        android:textColor="@color/black_70" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/fragment_about_line_height"
        android:layout_alignParentBottom="true"
        android:background="@color/line1"
        android:layout_marginStart="@dimen/dialog_network_auto_manual_line_margin_left"
        android:layout_marginEnd="@dimen/dialog_network_auto_manual_line_margin_right"
        />

</RelativeLayout>