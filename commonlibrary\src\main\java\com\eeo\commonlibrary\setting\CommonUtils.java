package com.eeo.commonlibrary.setting;

import android.content.Context;
import android.provider.Settings;

public class CommonUtils {

    public static final String LIGHT_VALUE = "com.eeo.light.value";
    public static final String SOUND_VAUE = "com.eeo.sound.value";

    public static boolean setLightValue(Context context,int lightValue){
        return Settings.System.putInt(context.getContentResolver(), LIGHT_VALUE, lightValue);
    }

    public static int getLightValue(Context context){
        try {
            return Settings.System.getInt(context.getContentResolver(),LIGHT_VALUE);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        return -1;
    }


    public static boolean setSoundValue(Context context,int soundValue){
        return Settings.System.putInt(context.getContentResolver(),SOUND_VAUE,soundValue);
    }

    public static int getSoundValue(Context context){
        try {
            return Settings.System.getInt(context.getContentResolver(),SOUND_VAUE);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }

        return -1;
    }


}
