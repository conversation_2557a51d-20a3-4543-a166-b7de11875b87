<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/small_window_shortcut_width"
    android:layout_height="@dimen/small_window_shortcut_height"
    android:background="@drawable/bg_projection_shortcut"
    android:splitMotionEvents="false">

    <FrameLayout
        android:id="@+id/fl_desktop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/small_window_shortcut_tv_desktop_margin_start"
        android:layout_marginEnd="@dimen/small_window_shortcut_iv_screen_margin_end"
        android:paddingVertical="@dimen/fl_desktop_padding_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_desktop"
            style="@style/Projection_ImageView"
            android:src="@drawable/desktop" />

        <TextView
            android:id="@+id/tv_desktop"
            style="@style/Projection_Text_Content"
            android:text="@string/desktop" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_maximize"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/small_window_shortcut_iv_screen_margin_end"
        android:paddingVertical="@dimen/fl_desktop_padding_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/fl_screen"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_maximize"
            style="@style/Projection_ImageView"
            android:src="@drawable/maximize" />

        <TextView
            android:id="@+id/tv_maximize"
            style="@style/Projection_Text_Content"
            android:text="@string/maximize" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_screen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/small_window_shortcut_iv_screen_margin_end"
        android:paddingVertical="@dimen/fl_desktop_padding_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/fl_annotate"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/small_window_shortcut_iv_screen_gone_margin_start">

        <ImageView
            android:id="@+id/iv_screen"
            style="@style/Projection_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/screen" />

        <TextView
            android:id="@+id/tv_screen"
            style="@style/Projection_Text_Content"
            android:text="@string/projection" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_annotate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/small_window_shortcut_iv_screen_margin_end"
        android:paddingVertical="@dimen/fl_desktop_padding_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_volume"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_annotate"
            style="@style/Projection_ImageView"
            android:src="@drawable/annotate" />

        <TextView
            android:id="@+id/tv_annotate"
            style="@style/Projection_Text_Content"
            android:text="@string/annotate" />
    </FrameLayout>

    <ImageView
        android:id="@+id/iv_volume"
        android:layout_width="@dimen/small_window_iv_volume_width"
        android:layout_height="@dimen/small_window_iv_volume_width"
        android:layout_marginEnd="@dimen/small_window_iv_volume_margin_end"
        android:src="@drawable/volume_3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/sb_volume"
        app:layout_constraintTop_toTopOf="parent" />

    <com.eeo.systemsetting.view.CustomerSeekBar
        android:id="@+id/sb_volume"
        android:layout_width="@dimen/small_window_sv_volume_width"
        android:layout_height="@dimen/small_window_sv_volume_height"
        android:background="@null"
        android:max="107"
        android:maxWidth="@dimen/small_window_sv_volume_width"
        android:maxHeight="@dimen/small_window_sv_volume_height"
        android:paddingStart="@dimen/small_window_sv_volume_padding_start"
        android:paddingEnd="@dimen/small_window_sv_volume_padding_end"
        android:layout_marginEnd="@dimen/small_window_sv_volume_margin_end"
        android:progress="60"
        android:progressDrawable="@drawable/progress_projection_shape"
        android:thumb="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
