package com.eeo.ota.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.eeo.ota.R;
import com.eeo.ota.util.Util;

public class UpdateDialog {
    private static final String TAG = "UpdateDialog";
    private static Dialog mUpdatingDialog = null;
    private static Dialog mUpdateSuccessDialog = null;
    private static Dialog mUpdateFailDialog = null;

    public static void showUpdatingDialog(Context context) {
        if (mUpdatingDialog == null) {
            mUpdatingDialog = new Dialog(context, R.style.Dialog);
            mUpdatingDialog.getWindow().setContentView(R.layout.dialog_updating);
            mUpdatingDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            mUpdatingDialog.setCanceledOnTouchOutside(false);
            mUpdatingDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    Util.setIsDialog(context, false);
                    Util.sendOpsTouchEnableBroadcast(context, true);
                }
            });
            WindowManager.LayoutParams layoutParams = mUpdatingDialog.getWindow().getAttributes();
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            mUpdatingDialog.getWindow().setAttributes(layoutParams);
            mUpdatingDialog.getWindow().setDimAmount(0.6f);
        }
        mUpdatingDialog.show();
        Util.setIsDialog(context, true);
        Util.sendOpsTouchEnableBroadcast(context, false);
    }

    public static void dismissUpdatingDialog() {
        if (mUpdatingDialog != null && mUpdatingDialog.isShowing()) {
            mUpdatingDialog.dismiss();
            mUpdatingDialog = null;
        }
    }

    public static void showUpdateSuccessDialog(Context context, boolean showSystem) {
        if (mUpdateSuccessDialog == null) {
            mUpdateSuccessDialog = new Dialog(context, R.style.Dialog);
            mUpdateSuccessDialog.getWindow().setContentView(R.layout.dialog_update_success);
            TextView titleTv = mUpdateSuccessDialog.getWindow().getDecorView().findViewById(R.id.tv_title);
            if (showSystem) {
                titleTv.setText(R.string.update_success);
                //显示版本号
                TextView contentTv = mUpdateSuccessDialog.getWindow().getDecorView().findViewById(R.id.tv_content);
                contentTv.setText(context.getString(R.string.update_success_content) + " ：" + Util.getCurrentVersion());
            } else {
                titleTv.setText(R.string.sub_device_update_success);
            }
            mUpdateSuccessDialog.getWindow().getDecorView().findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismissUpdateSuccessDialog();
                }
            });
            mUpdateSuccessDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            mUpdateSuccessDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    Util.setIsDialog(context, false);
                    Util.sendOpsTouchEnableBroadcast(context, true);
                }
            });
            WindowManager.LayoutParams layoutParams = mUpdateSuccessDialog.getWindow().getAttributes();
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            mUpdateSuccessDialog.getWindow().setAttributes(layoutParams);
            mUpdateSuccessDialog.getWindow().setDimAmount(0.6f);
            mUpdateSuccessDialog.setCanceledOnTouchOutside(true);
        }
        mUpdateSuccessDialog.show();
        Util.setIsDialog(context, true);
        Util.sendOpsTouchEnableBroadcast(context, false);
    }

    public static void dismissUpdateSuccessDialog() {
        if (mUpdateSuccessDialog != null && mUpdateSuccessDialog.isShowing()) {
            mUpdateSuccessDialog.dismiss();
            mUpdateSuccessDialog = null;
        }
    }

    public static void showUpdateFailDialog(Context context, boolean showSystem) {
        if (mUpdateFailDialog == null) {
            mUpdateFailDialog = new Dialog(context, R.style.Dialog);
            mUpdateFailDialog.getWindow().setContentView(R.layout.dialog_update_fail);
            TextView titleTv = mUpdateFailDialog.getWindow().getDecorView().findViewById(R.id.tv_title);
            if (showSystem) {
                titleTv.setText(R.string.update_fail);
            } else {
                titleTv.setText(R.string.sub_device_update_fail);
            }
            mUpdateFailDialog.getWindow().getDecorView().findViewById(R.id.btn_reboot).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismissUpdateFailDialog();
                    Util.reboot(context, true);
                }
            });
            mUpdateFailDialog.getWindow().getDecorView().findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismissUpdateFailDialog();
                }
            });
            mUpdateFailDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            mUpdateFailDialog.setCanceledOnTouchOutside(false);
            mUpdateFailDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    Util.setIsDialog(context, false);
                    Util.sendOpsTouchEnableBroadcast(context, true);
                }
            });
            WindowManager.LayoutParams layoutParams = mUpdateFailDialog.getWindow().getAttributes();
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            mUpdateFailDialog.getWindow().setAttributes(layoutParams);
            mUpdateFailDialog.getWindow().setDimAmount(0.6f);
        }
        mUpdateFailDialog.show();
        Util.setIsDialog(context, true);
        Util.sendOpsTouchEnableBroadcast(context, false);
    }

    public static void dismissUpdateFailDialog() {
        if (mUpdateFailDialog != null && mUpdateFailDialog.isShowing()) {
            mUpdateFailDialog.dismiss();
            mUpdateFailDialog = null;
        }
    }

}
