<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="languages">
        <item>简体中文</item>
        <item>English</item>
    </string-array>
    <string-array name="startup_channels">
        <item>Last Source</item>
        <item>Windows</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>
    <string-array name="startup_channels_windows_disabled">
        <item>Last Source</item>
        <item>USB C</item>
        <item>HDMI 1</item>
        <item>HDMI 2</item>
    </string-array>
    <color name="adb_bg">#FF525D6D</color>
    <color name="adb_btn_default">#991C1F25</color>
    <color name="adb_btn_press">#FF1C1F25</color>
    <color name="adb_item_bg">#331C1F25</color>
    <color name="black">#FF000000</color>
    <color name="black_100">#FF1C1F25</color>
    <color name="black_70">#B31C1F25</color>
    <color name="black_80">#CC1C1F25</color>
    <color name="black_check">#FF696969</color>
    <color name="btn_default_bg_green">#FF009265</color>
    <color name="btn_default_bg_white">#FFFFFFFF</color>
    <color name="btn_dis_click_bg_green">#8002BA8E</color>
    <color name="btn_dis_click_bg_white">#FFDADEE4</color>
    <color name="btn_press_color_green">#FF007F58</color>
    <color name="btn_press_color_white">#FFDADADA</color>
    <color name="error_red">#FFED4747</color>
    <color name="gray_10">#1A525D6D</color>
    <color name="gray_100">#ffd8d8d8</color>
    <color name="gray_50">#D3D1D1</color>
    <color name="gray_60">#992f2f2f</color>
    <color name="gray_80">#CC323232</color>
    <color name="gray_85">#d92f2f2f</color>
    <color name="gray_90">#E62F2F2F</color>
    <color name="green_100">#FF009265</color>
    <color name="grey_10">#1A525D6D</color>
    <color name="line1">#ffb2bdc0</color>
    <color name="line2">#80b2bdc0</color>
    <color name="line_50">#33525d6d</color>
    <color name="main_line">#FFC4CCCE</color>
    <color name="main_shadow_color">#4DA4AFC0</color>
    <color name="press_color">#FF009265</color>
    <color name="progress_bar_background">#FFCBCED3</color>
    <color name="progress_bar_projection_background">#FFCBCED3</color>
    <color name="progress_bar_rgb_background">#801C1F25</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="scrollbar_track_background">#FFBBC8CC</color>
    <color name="shadow_color">#33525D6D</color>
    <color name="shadow_color_middle">#4D525D6D</color>
    <color name="teal_200">#FF02BA8E</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_70">#B31C1F25</color>
    <color name="text_black_100">#FF525D6D</color>
    <color name="text_lock">#FF666666</color>
    <color name="tv_text_normal">#FF525D6D</color>
    <color name="tv_text_pressed">#FF02BA8E</color>
    <color name="white">#FFFFFFFF</color>
    <color name="white_0">#00000000</color>
    <color name="white_10">#1AFFFFFF</color>
    <color name="white_100">#FFFFFFFF</color>
    <color name="white_15">#26FFFFFF</color>
    <color name="white_20">#33FFFFFF</color>
    <color name="white_25">#40FFFFFF</color>
    <color name="white_30">#4DFFFFFF</color>
    <color name="white_35">#59FFFFFF</color>
    <color name="white_40">#66FFFFFF</color>
    <color name="white_45">#73FFFFFF</color>
    <color name="white_5">#0DFFFFFF</color>
    <color name="white_50">#80FFFFFF</color>
    <color name="white_55">#8CFFFFFF</color>
    <color name="white_60">#99FFFFFF</color>
    <color name="white_65">#A6FFFFFF</color>
    <color name="white_70">#B3FFFFFF</color>
    <color name="white_75">#BFFFFFFF</color>
    <color name="white_80">#CCFFFFFF</color>
    <color name="white_85">#D9FFFFFF</color>
    <color name="white_90">#E6FFFFFF</color>
    <color name="white_95">#F2FFFFFF</color>
    <dimen name="adb_btn_height">28dp</dimen>
    <dimen name="adb_btn_margin_bottom">12dp</dimen>
    <dimen name="adb_btn_margin_start">9dp</dimen>
    <dimen name="adb_btn_text_size">9sp</dimen>
    <dimen name="adb_btn_width">70dp</dimen>
    <dimen name="adb_height">384dp</dimen>
    <dimen name="adb_item_adb_margin_top">13dp</dimen>
    <dimen name="adb_item_height">29dp</dimen>
    <dimen name="adb_item_margin_end">16dp</dimen>
    <dimen name="adb_item_margin_start">21dp</dimen>
    <dimen name="adb_item_margin_top">3dp</dimen>
    <dimen name="adb_item_radius">3dp</dimen>
    <dimen name="adb_item_width">232dp</dimen>
    <dimen name="adb_iv_back_height">25dp</dimen>
    <dimen name="adb_iv_back_margin_start">20dp</dimen>
    <dimen name="adb_iv_back_margin_top">20dp</dimen>
    <dimen name="adb_iv_back_width">25dp</dimen>
    <dimen name="adb_iv_close_margin_end">16dp</dimen>
    <dimen name="adb_iv_close_margin_top">11dp</dimen>
    <dimen name="adb_iv_reset_height">20dp</dimen>
    <dimen name="adb_iv_reset_margin_start">220dp</dimen>
    <dimen name="adb_iv_reset_width">20dp</dimen>
    <dimen name="adb_line_margin_top">42dp</dimen>
    <dimen name="adb_scrollview_height">298dp</dimen>
    <dimen name="adb_sw_height">18dp</dimen>
    <dimen name="adb_sw_margin_end">13dp</dimen>
    <dimen name="adb_sw_width">36dp</dimen>
    <dimen name="adb_tv_margin_start">13dp</dimen>
    <dimen name="adb_tv_text_size">9sp</dimen>
    <dimen name="adb_width">264dp</dimen>
    <dimen name="bg_radius">8dp</dimen>
    <dimen name="dialog_factory_reset_btn_cancel_margin_top">24dp</dimen>
    <dimen name="dialog_factory_reset_content_margin_top">16dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_height">20dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_width">20dp</dimen>
    <dimen name="dialog_factory_reset_title_margin_start">8dp</dimen>
    <dimen name="dialog_factory_reset_title_margin_top">25dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_bottom">21dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_start">21dp</dimen>
    <dimen name="dialog_have_update_content_margin_top">8dp</dimen>
    <dimen name="dialog_have_update_content_text_size">9sp</dimen>
    <dimen name="dialog_have_update_height">148dp</dimen>
    <dimen name="dialog_have_update_title_margin_top">19dp</dimen>
    <dimen name="dialog_have_update_title_text_size">9sp</dimen>
    <dimen name="dialog_have_update_width">240dp</dimen>
    <dimen name="dialog_install_fail_tv_title_drawable_padding">8dp</dimen>
    <dimen name="dialog_install_fail_tv_title_margin_top">32dp</dimen>
    <dimen name="dialog_installing_pb_height">21dp</dimen>
    <dimen name="dialog_installing_pb_width">21dp</dimen>
    <dimen name="dialog_installing_text_size">16sp</dimen>
    <dimen name="dialog_installing_tv_margin_start">13dp</dimen>
    <dimen name="dialog_network_auto_manual_height">59dp</dimen>
    <dimen name="dialog_network_auto_manual_item_height">29dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_height">20dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_margin_start">29dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_width">20dp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_left">11dp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_right">11dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_margin_start">3dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_text_size">9sp</dimen>
    <dimen name="dialog_network_auto_manual_width">120dp</dimen>
    <dimen name="dialog_shutdown_content_margin_top">19dp</dimen>
    <dimen name="dialog_shutdown_countdown_content_margin_top">15dp</dimen>
    <dimen name="dialog_shutdown_title_margin_top">23dp</dimen>
    <dimen name="dialog_volume_height">74dp</dimen>
    <dimen name="dialog_volume_iv_margin_start">21dp</dimen>
    <dimen name="dialog_volume_margin_bottom">228dp</dimen>
    <dimen name="dialog_volume_margin_end">80dp</dimen>
    <dimen name="dialog_volume_sb_margin_start">46dp</dimen>
    <dimen name="dialog_volume_tv_text_size">11sp</dimen>
    <dimen name="dialog_volume_width">250dp</dimen>
    <dimen name="fl_desktop_padding_vertical">6dp</dimen>
    <dimen name="fragment_about_content_height">13dp</dimen>
    <dimen name="fragment_about_content_width">160dp</dimen>
    <dimen name="fragment_about_device_name_margin_top">29dp</dimen>
    <dimen name="fragment_about_iv_windows_host_margin_start">81dp</dimen>
    <dimen name="fragment_about_line_height">0.3dp</dimen>
    <dimen name="fragment_about_line_margin_start">21dp</dimen>
    <dimen name="fragment_about_line_margin_top">33dp</dimen>
    <dimen name="fragment_about_padding_bottom">5dp</dimen>
    <dimen name="fragment_about_padding_view_height">52dp</dimen>
    <dimen name="fragment_about_scrollbar_size">4dp</dimen>
    <dimen name="fragment_about_text_size">9sp</dimen>
    <dimen name="fragment_about_title_height">13dp</dimen>
    <dimen name="fragment_about_title_margin_start">21dp</dimen>
    <dimen name="fragment_about_title_margin_top">27dp</dimen>
    <dimen name="fragment_about_title_width">80dp</dimen>
    <dimen name="fragment_about_tv_system_version_margin_start">-19dp</dimen>
    <dimen name="fragment_about_windows_host_width">120dp</dimen>
    <dimen name="fragment_extra_item_margin_top">20dp</dimen>
    <dimen name="fragment_extra_sw_height">20dp</dimen>
    <dimen name="fragment_extra_sw_margin_end">21dp</dimen>
    <dimen name="fragment_extra_sw_width">40dp</dimen>
    <dimen name="fragment_extra_tv_margin_start">21dp</dimen>
    <dimen name="fragment_extra_tv_text_size">9sp</dimen>
    <dimen name="fragment_extra_wireless_screen_margin_top">26dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_end">21dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_top">26dp</dimen>
    <dimen name="fragment_locale_spinner_width">120dp</dimen>
    <dimen name="fragment_locale_tv_language_height">20dp</dimen>
    <dimen name="fragment_locale_tv_language_margin_top">26dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_start">29dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_top">12dp</dimen>
    <dimen name="fragment_network_content_margin_start">1dp</dimen>
    <dimen name="fragment_network_content_width">160dp</dimen>
    <dimen name="fragment_network_dns_content_margin_start">13dp</dimen>
    <dimen name="fragment_network_dns_title_width">60dp</dimen>
    <dimen name="fragment_network_et_height">29dp</dimen>
    <dimen name="fragment_network_et_margin_start">8dp</dimen>
    <dimen name="fragment_network_et_padding_end">7dp</dimen>
    <dimen name="fragment_network_et_text_size">9sp</dimen>
    <dimen name="fragment_network_et_width">140dp</dimen>
    <dimen name="fragment_network_item_margin_top">23dp</dimen>
    <dimen name="fragment_network_iv_ip_height">13dp</dimen>
    <dimen name="fragment_network_iv_ip_margin_start">13dp</dimen>
    <dimen name="fragment_network_iv_ip_width">13dp</dimen>
    <dimen name="fragment_network_ll_ip_margin_top">15dp</dimen>
    <dimen name="fragment_network_ll_mask_margin_top">11dp</dimen>
    <dimen name="fragment_network_network_margin_top">26dp</dimen>
    <dimen name="fragment_network_sw_height">20dp</dimen>
    <dimen name="fragment_network_sw_margin_start">121dp</dimen>
    <dimen name="fragment_network_sw_width">40dp</dimen>
    <dimen name="fragment_network_text_size">9sp</dimen>
    <dimen name="fragment_network_title_margin_start">21dp</dimen>
    <dimen name="fragment_network_title_width">60dp</dimen>
    <dimen name="fragment_network_tv_ip_setting_width">140dp</dimen>
    <dimen name="fragment_update_btn_cancel_margin_top">59dp</dimen>
    <dimen name="fragment_update_btn_install_margin_top">43dp</dimen>
    <dimen name="fragment_update_btn_retry_margin_top">19dp</dimen>
    <dimen name="fragment_update_btn_right_update_gone_margin_top">20dp</dimen>
    <dimen name="fragment_update_btn_right_update_margin_top">121dp</dimen>
    <dimen name="fragment_update_line_margin_top">30dp</dimen>
    <dimen name="fragment_update_pb_check_update_margin_top">77dp</dimen>
    <dimen name="fragment_update_pb_download_margin_end">21dp</dimen>
    <dimen name="fragment_update_pb_download_margin_top">32dp</dimen>
    <dimen name="fragment_update_pb_horizontal_max_height">5dp</dimen>
    <dimen name="fragment_update_pb_horizontal_min_height">5dp</dimen>
    <dimen name="fragment_update_tv_check_network_margin_top">27dp</dimen>
    <dimen name="fragment_update_tv_checking_margin_top">11dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_start">21dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_top">33dp</dimen>
    <dimen name="fragment_update_tv_update_description_height">81dp</dimen>
    <dimen name="fragment_update_tv_update_description_margin_top">13dp</dimen>
    <dimen name="fragment_update_tv_update_description_max_height">80dp</dimen>
    <dimen name="fragment_update_tv_update_description_width">221dp</dimen>
    <dimen name="fragment_update_tv_version_margin_top">33dp</dimen>
    <dimen name="fragment_update_tv_version_text_size">9sp</dimen>
    <dimen name="fragment_wifi_line1_margin_end">0dp</dimen>
    <dimen name="fragment_wifi_line1_margin_start">0dp</dimen>
    <dimen name="fragment_wifi_line1_margin_top">19dp</dimen>
    <dimen name="fragment_wifi_ll_network_gone_margin_top">23dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_start">21dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_top">33dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_height">12dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_margin_start">8dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_width">12dp</dimen>
    <dimen name="fragment_wifi_rv_connected_margin_top">10dp</dimen>
    <dimen name="fragment_wifi_rv_wifi_list_margin_top">13dp</dimen>
    <dimen name="fragment_wifi_sw_margin_start">121dp</dimen>
    <dimen name="fragment_wifi_title_wifi_width">60dp</dimen>
    <dimen name="fragment_wifi_tv_other_network_text_size">9sp</dimen>
    <dimen name="inter_touch_lock_height">98dp</dimen>
    <dimen name="inter_touch_lock_margin_start">8dp</dimen>
    <dimen name="inter_touch_lock_margin_top">8dp</dimen>
    <dimen name="inter_touch_lock_width">220dp</dimen>
    <dimen name="item_wifi_connect_height">25dp</dimen>
    <dimen name="item_wifi_connect_iv_check_height">17dp</dimen>
    <dimen name="item_wifi_connect_iv_check_width">17dp</dimen>
    <dimen name="item_wifi_connect_iv_password_margin_start">182dp</dimen>
    <dimen name="item_wifi_connect_margin_top">18dp</dimen>
    <dimen name="item_wifi_height">41dp</dimen>
    <dimen name="item_wifi_iv_password_margin_end">4dp</dimen>
    <dimen name="item_wifi_iv_rank_height">20dp</dimen>
    <dimen name="item_wifi_iv_rank_margin_end">21dp</dimen>
    <dimen name="item_wifi_iv_rank_width">20dp</dimen>
    <dimen name="item_wifi_line_margin_top">0dp</dimen>
    <dimen name="item_wifi_more_drawable_padding">8dp</dimen>
    <dimen name="item_wifi_more_et_address_margin_top">6dp</dimen>
    <dimen name="item_wifi_more_et_subnet_mask_margin_top">11dp</dimen>
    <dimen name="item_wifi_more_fl_disconnect_margin_top">21dp</dimen>
    <dimen name="item_wifi_more_ip_address_margin_top">8dp</dimen>
    <dimen name="item_wifi_more_item_margin_end">35dp</dimen>
    <dimen name="item_wifi_more_item_margin_top">26dp</dimen>
    <dimen name="item_wifi_more_iv_arrow_margin_top">23dp</dimen>
    <dimen name="item_wifi_more_iv_disconnect_margin_start">21dp</dimen>
    <dimen name="item_wifi_more_iv_ip_marin_end">8dp</dimen>
    <dimen name="item_wifi_more_iv_manual_margin_end">15dp</dimen>
    <dimen name="item_wifi_more_line1_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_line_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_rl_manual_margin_top">18dp</dimen>
    <dimen name="item_wifi_more_title_margin_start">107dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_height">29dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_margin_start">49dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_padding_left">21dp</dimen>
    <dimen name="item_wifi_more_tv_dns_width">47dp</dimen>
    <dimen name="item_wifi_more_tv_ip_setting_width">147dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_bottom">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_end">21dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_start">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_tv_save_text_size">9sp</dimen>
    <dimen name="item_wifi_tv_state_margin_top">1dp</dimen>
    <dimen name="item_wifi_tv_state_text_size">8sp</dimen>
    <dimen name="item_wifi_tv_wifi_height">26dp</dimen>
    <dimen name="item_wifi_tv_wifi_margin_top">7dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_height">13dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_max_width">100dp</dimen>
    <dimen name="iv_back_height">20dp</dimen>
    <dimen name="iv_back_margin_start">13dp</dimen>
    <dimen name="iv_back_margin_top">11dp</dimen>
    <dimen name="iv_back_width">20dp</dimen>
    <dimen name="iv_bright_height">20dp</dimen>
    <dimen name="iv_bright_margin_start">24dp</dimen>
    <dimen name="iv_bright_margin_top">60dp</dimen>
    <dimen name="iv_bright_width">20dp</dimen>
    <dimen name="iv_lock_padding">9dp</dimen>
    <dimen name="iv_screen_height">20dp</dimen>
    <dimen name="iv_screen_margin_top">33dp</dimen>
    <dimen name="iv_screen_width">20dp</dimen>
    <dimen name="iv_shutdown_height">27dp</dimen>
    <dimen name="iv_shutdown_margin_left">103dp</dimen>
    <dimen name="iv_shutdown_margin_top">134dp</dimen>
    <dimen name="iv_shutdown_width">27dp</dimen>
    <dimen name="iv_voice_margin_top">24dp</dimen>
    <dimen name="launcher_main_btn_start_height">40dp</dimen>
    <dimen name="launcher_main_btn_start_margin_top">56dp</dimen>
    <dimen name="launcher_main_btn_start_width">160dp</dimen>
    <dimen name="launcher_main_iv_no_signal_height">304dp</dimen>
    <dimen name="launcher_main_iv_no_signal_margin_top">202dp</dimen>
    <dimen name="launcher_main_iv_no_signal_width">304dp</dimen>
    <dimen name="launcher_main_iv_privacy_height">27dp</dimen>
    <dimen name="launcher_main_iv_privacy_width">27dp</dimen>
    <dimen name="launcher_main_tv_current_signal_margin_top">10dp</dimen>
    <dimen name="launcher_main_tv_current_signal_text_size">12sp</dimen>
    <dimen name="launcher_main_tv_hint_height">37dp</dimen>
    <dimen name="launcher_main_tv_hint_text_size">9sp</dimen>
    <dimen name="launcher_main_tv_hint_width">160dp</dimen>
    <dimen name="launcher_main_tv_hotline_height">40dp</dimen>
    <dimen name="launcher_main_tv_hotline_margin_top">16dp</dimen>
    <dimen name="launcher_main_tv_hotline_width">200dp</dimen>
    <dimen name="launcher_main_tv_no_signal_margin_top">467dp</dimen>
    <dimen name="launcher_main_tv_no_signal_text_size">16sp</dimen>
    <dimen name="launcher_main_tv_privacy_margin_top">30dp</dimen>
    <dimen name="launcher_main_tv_privacy_text_size">18sp</dimen>
    <dimen name="ll_select_width">120dp</dimen>
    <dimen name="ll_touch_lock_margin_bottom">37dp</dimen>
    <dimen name="ll_touch_lock_width">56dp</dimen>
    <dimen name="main_bg_radius">16dp</dimen>
    <dimen name="main_bg_stroke">0.5dp</dimen>
    <dimen name="main_cv_height">394dp</dimen>
    <dimen name="main_cv_width">394dp</dimen>
    <dimen name="main_height">384dp</dimen>
    <dimen name="main_line1_height">0.3dp</dimen>
    <dimen name="main_line1_margin_top">25dp</dimen>
    <dimen name="main_tv_title_margin_top">27dp</dimen>
    <dimen name="main_width">384dp</dimen>
    <dimen name="privacy_height">294dp</dimen>
    <dimen name="privacy_margin_top">17dp</dimen>
    <dimen name="privacy_width">337dp</dimen>
    <dimen name="rgb_btn_reset_width">232dp</dimen>
    <dimen name="rgb_et_height">20dp</dimen>
    <dimen name="rgb_et_width">40dp</dimen>
    <dimen name="rgb_green_gain_margin_top">24dp</dimen>
    <dimen name="rgb_line2_margin_top">25dp</dimen>
    <dimen name="rgb_red_gain_margin_top">23dp</dimen>
    <dimen name="rgb_reset_margin_bottom">12dp</dimen>
    <dimen name="rgb_sb_height">7dp</dimen>
    <dimen name="rgb_sb_margin_start">9dp</dimen>
    <dimen name="rgb_sb_margin_top">-4dp</dimen>
    <dimen name="rgb_sb_padding_end">7dp</dimen>
    <dimen name="rgb_sb_padding_start">7dp</dimen>
    <dimen name="rgb_sb_thumb_height">23dp</dimen>
    <dimen name="rgb_sb_width">195dp</dimen>
    <dimen name="rgb_tv_color_temperature_cold_margin_top">-5dp</dimen>
    <dimen name="rgb_tv_color_temperature_margin_top">19dp</dimen>
    <dimen name="rgb_tv_margin_start">16dp</dimen>
    <dimen name="sb_bright_height">20dp</dimen>
    <dimen name="sb_bright_margin_start">49dp</dimen>
    <dimen name="sb_bright_margin_top">60dp</dimen>
    <dimen name="sb_bright_max_height">40dp</dimen>
    <dimen name="sb_bright_padding_end">0dp</dimen>
    <dimen name="sb_bright_padding_start">0dp</dimen>
    <dimen name="sb_bright_width">151dp</dimen>
    <dimen name="sb_voice_margin_top">24dp</dimen>
    <dimen name="screen_dialog_content_text_size">9sp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_height">75dp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_margin_top">-1dp</dimen>
    <dimen name="screen_dialog_cv_wireless_screen_height">318dp</dimen>
    <dimen name="screen_dialog_iv_back_height">20dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_start">18dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_top">23dp</dimen>
    <dimen name="screen_dialog_iv_back_width">20dp</dimen>
    <dimen name="screen_dialog_iv_code_height">73dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_end">2dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_top">26dp</dimen>
    <dimen name="screen_dialog_iv_code_width">73dp</dimen>
    <dimen name="screen_dialog_iv_step_1_height">15dp</dimen>
    <dimen name="screen_dialog_iv_step_1_margin_top">26dp</dimen>
    <dimen name="screen_dialog_iv_step_1_width">15dp</dimen>
    <dimen name="screen_dialog_iv_step_2_margin_top">12dp</dimen>
    <dimen name="screen_dialog_iv_step_3_margin_top">39dp</dimen>
    <dimen name="screen_dialog_line2_margin_top">8dp</dimen>
    <dimen name="screen_dialog_rl_sub_wired_screen_margin_top">25dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_margin_top">8dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_width">341dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_height">65dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_margin_top">11dp</dimen>
    <dimen name="screen_dialog_rl_wireless_screen_height">308dp</dimen>
    <dimen name="screen_dialog_switch_enable_pin_code_margin_end">1dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_height">20dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_end">23dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_top">14dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_width">40dp</dimen>
    <dimen name="screen_dialog_title_text_size">12sp</dimen>
    <dimen name="screen_dialog_tv_code_line_height">12dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_end">-1dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_top">5dp</dimen>
    <dimen name="screen_dialog_tv_code_text_size">8sp</dimen>
    <dimen name="screen_dialog_tv_enable_pin_code_margin_top">8dp</dimen>
    <dimen name="screen_dialog_tv_margin_start">21dp</dimen>
    <dimen name="screen_dialog_tv_one_margin_start">25dp</dimen>
    <dimen name="screen_dialog_tv_order_height">21dp</dimen>
    <dimen name="screen_dialog_tv_order_width">21dp</dimen>
    <dimen name="screen_dialog_tv_pin_code_margin_top">4dp</dimen>
    <dimen name="screen_dialog_tv_signal_margin_top">11dp</dimen>
    <dimen name="screen_dialog_tv_step_1_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_line_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_start">6dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_top">1dp</dimen>
    <dimen name="screen_dialog_tv_title_margin_top">25dp</dimen>
    <dimen name="screen_offset_tv_margin_bottom">10dp</dimen>
    <dimen name="screen_offset_tv_margin_horizontal">40dp</dimen>
    <dimen name="screen_offset_tv_margin_top">240dp</dimen>
    <dimen name="screen_offset_tv_text_size">12sp</dimen>
    <dimen name="setting_line1_margin_top">11dp</dimen>
    <dimen name="setting_tv_title_margin_top">13dp</dimen>
    <dimen name="shutdown_btn_cancel_margin_left">93dp</dimen>
    <dimen name="shutdown_btn_confirm_height">28dp</dimen>
    <dimen name="shutdown_btn_confirm_margin_left">203dp</dimen>
    <dimen name="shutdown_btn_confirm_margin_top">37dp</dimen>
    <dimen name="shutdown_btn_confirm_text_size">11sp</dimen>
    <dimen name="shutdown_btn_confirm_width">88dp</dimen>
    <dimen name="shutdown_content_margin_top">11dp</dimen>
    <dimen name="shutdown_content_text_size">9sp</dimen>
    <dimen name="shutdown_tittle_margin_left">11dp</dimen>
    <dimen name="shutdown_tittle_margin_top">139dp</dimen>
    <dimen name="shutdown_tittle_text_size">12sp</dimen>
    <dimen name="signal_item_margin_top">11dp</dimen>
    <dimen name="signal_iv_margin_start">139dp</dimen>
    <dimen name="signal_tv_drawable_padding">17dp</dimen>
    <dimen name="signal_tv_height">48dp</dimen>
    <dimen name="signal_tv_margin_start">169dp</dimen>
    <dimen name="signal_tv_text_size">9sp</dimen>
    <dimen name="signal_tv_width">341dp</dimen>
    <dimen name="signal_tv_window_margin_top">21dp</dimen>
    <dimen name="sl_lock_height">37dp</dimen>
    <dimen name="sl_lock_width">37dp</dimen>
    <dimen name="sl_network_height">29dp</dimen>
    <dimen name="sl_network_margin_start">28dp</dimen>
    <dimen name="sl_network_margin_top">21dp</dimen>
    <dimen name="sl_network_shadow_offset_x">0dp</dimen>
    <dimen name="sl_network_shadow_offset_y">1dp</dimen>
    <dimen name="sl_screen_corner_radius">8dp</dimen>
    <dimen name="sl_screen_elevation">5dp</dimen>
    <dimen name="sl_screen_height">114dp</dimen>
    <dimen name="sl_screen_margin_start">16dp</dimen>
    <dimen name="sl_screen_margin_top">64dp</dimen>
    <dimen name="sl_screen_shadow_limit">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_x">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_y">0dp</dimen>
    <dimen name="sl_screen_stroke_width">0.2dp</dimen>
    <dimen name="sl_screen_width">114dp</dimen>
    <dimen name="sl_setting_margin_start">7dp</dimen>
    <dimen name="sl_setting_width">234dp</dimen>
    <dimen name="sl_wifi_margin_top">11dp</dimen>
    <dimen name="sl_write_margin_top">3dp</dimen>
    <dimen name="small_window_iv_home_height">13dp</dimen>
    <dimen name="small_window_iv_home_width">13dp</dimen>
    <dimen name="small_window_iv_volume_height">15dp</dimen>
    <dimen name="small_window_iv_volume_margin_end">5dp</dimen>
    <dimen name="small_window_iv_volume_width">15dp</dimen>
    <dimen name="small_window_shortcut_drawable_padding">3dp</dimen>
    <dimen name="small_window_shortcut_height">29dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_gone_margin_start">111dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_margin_end">21dp</dimen>
    <dimen name="small_window_shortcut_text_size">9sp</dimen>
    <dimen name="small_window_shortcut_tv_desktop_margin_start">11dp</dimen>
    <dimen name="small_window_shortcut_width">427dp</dimen>
    <dimen name="small_window_sv_volume_height">13dp</dimen>
    <dimen name="small_window_sv_volume_margin_end">11dp</dimen>
    <dimen name="small_window_sv_volume_padding_end">0dp</dimen>
    <dimen name="small_window_sv_volume_padding_start">0dp</dimen>
    <dimen name="small_window_sv_volume_width">157dp</dimen>
    <dimen name="small_window_tv_home_margin_start">16dp</dimen>
    <dimen name="spinner_dropdown_item_iv_margin_vertical">5dp</dimen>
    <dimen name="spinner_dropdown_item_tv_margin_start">3dp</dimen>
    <dimen name="toast_height">53dp</dimen>
    <dimen name="toast_msg_margin_start">8dp</dimen>
    <dimen name="toast_msg_text_size">12sp</dimen>
    <dimen name="toast_padding_horizontal">30dp</dimen>
    <dimen name="toast_padding_vertical">19dp</dimen>
    <dimen name="toast_resolution_corner_radius">8dp</dimen>
    <dimen name="toast_resolution_height">53dp</dimen>
    <dimen name="toast_resolution_margin_start">13dp</dimen>
    <dimen name="toast_resolution_tv_text_size">12sp</dimen>
    <dimen name="toast_resolution_width">83dp</dimen>
    <dimen name="toast_shutting_down_ops_height">65dp</dimen>
    <dimen name="toast_shutting_down_ops_iv_margin_top">10dp</dimen>
    <dimen name="toast_shutting_down_ops_tv_margin_top">5dp</dimen>
    <dimen name="toast_shutting_down_ops_width">170dp</dimen>
    <dimen name="toast_uhd_height">77dp</dimen>
    <dimen name="toast_uhd_iv_height">29dp</dimen>
    <dimen name="toast_uhd_iv_margin_start">13dp</dimen>
    <dimen name="toast_uhd_iv_width">59dp</dimen>
    <dimen name="toast_uhd_margin_bottom">94dp</dimen>
    <dimen name="toast_uhd_tv_martin_start">7dp</dimen>
    <dimen name="toast_uhd_tv_text_size">12sp</dimen>
    <dimen name="toast_uhd_width">314dp</dimen>
    <dimen name="toast_width">160dp</dimen>
    <dimen name="tv_desktop_margin_bottom">17dp</dimen>
    <dimen name="tv_desktop_margin_start">36dp</dimen>
    <dimen name="tv_desktop_margin_top">23dp</dimen>
    <dimen name="tv_desktop_text_size">10sp</dimen>
    <dimen name="tv_eye_margin_start">165dp</dimen>
    <dimen name="tv_lock_margin_top">5dp</dimen>
    <dimen name="tv_network_margin_start">55dp</dimen>
    <dimen name="tv_network_text_size">9sp</dimen>
    <dimen name="tv_rest_margin_desktop">14dp</dimen>
    <dimen name="tv_rest_margin_start">15dp</dimen>
    <dimen name="tv_restart_margin_start">44dp</dimen>
    <dimen name="tv_screen_drawable_padding">5dp</dimen>
    <dimen name="tv_screen_height">13dp</dimen>
    <dimen name="tv_screen_margin_top">58dp</dimen>
    <dimen name="tv_screen_text_size">9sp</dimen>
    <dimen name="tv_screen_width">50dp</dimen>
    <dimen name="tv_setting_margin_start">9dp</dimen>
    <dimen name="tv_setting_margin_top">25dp</dimen>
    <dimen name="tv_shutdown_width">50dp</dimen>
    <dimen name="tv_signal_margin_start">61dp</dimen>
    <dimen name="tv_touch_lock_margin_start">113dp</dimen>
    <dimen name="tv_touch_lock_width">80dp</dimen>
    <dimen name="wifi_dialog_input_password_btn_margin_top">20dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_height">29dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_margin_top">11dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_end">30dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_width">197dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_height">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_start">191dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_top">15dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_width">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_wifi_margin_start">21dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_wifi_margin_top">21dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_top">25dp</dimen>
    <dimen name="wifi_dialog_input_password_title_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_height">13dp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_width">100dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_start">24dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_top">8dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_text_size">8sp</dimen>
    <dimen name="window_ruler_iv_ruler_height">572dp</dimen>
    <dimen name="window_ruler_iv_ruler_width">572dp</dimen>
    <string name="DNS">DNS Server</string>
    <string name="DNS1">DNS 1</string>
    <string name="DNS2">DNS 2</string>
    <string name="about">About</string>
    <string name="adb">Adb Switch</string>
    <string name="adb_title">Hidden Secrets</string>
    <string name="android_version">Android Version</string>
    <string name="annotate">Note</string>
    <string name="app_name">SystemSetting</string>
    <string name="auto">Auto</string>
    <string name="auto_update">Auto update</string>
    <string name="behind_hdmi1">HDMI 1</string>
    <string name="behind_hdmi2">HDMI 2</string>
    <string name="breath_led_on">Logo LED always on</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_download">Cancel</string>
    <string name="change_to_pc">Switch to Windows</string>
    <string name="check_network">Please check the cable network or Wi-Fi connection of the large screen</string>
    <string name="check_update">Update</string>
    <string name="checking_update">Checking for updates</string>
    <string name="click_lock">Locked</string>
    <string name="color_temperature">Color Temperature</string>
    <string name="color_temperature_adjust">Color Temperature Adjusting</string>
    <string name="color_temperature_cold">Cold</string>
    <string name="color_temperature_eye_care">In Eye Care mode</string>
    <string name="color_temperature_value_wrong">The value should be %d to %d.</string>
    <string name="color_temperature_warm">Warm</string>
    <string name="company">Manufacturer</string>
    <string name="company_name">Beijing EEO Education Technology \nCo., Ltd.</string>
    <string name="confirm">OK</string>
    <string name="current_sign">Current Channel [Windows]</string>
    <string name="current_sign_hdmi">Current Channel [HDMI]</string>
    <string name="current_sign_hdmi_1">Current Channel [HDMI 1]</string>
    <string name="current_sign_hdmi_2">Current Channel [HDMI 2]</string>
    <string name="current_sign_typec">Current Channel [USB C]</string>
    <string name="current_sign_windows_disabled">Current Channel [Windows](Disabled)</string>
    <string name="debug_menu">Debug Menu</string>
    <string name="desktop">Desktop</string>
    <string name="device_name">Device Name</string>
    <string name="disconnect">Not connected</string>
    <string name="disconnect_network">Disconnect this network</string>
    <string name="download">Downloading, please wait...</string>
    <string name="eeo_screen_move_txt">Click above or swipe up to restore full screen</string>
    <string name="email"><EMAIL></string>
    <string name="english">English</string>
    <string name="extra">More</string>
    <string name="eye">Eye Care</string>
    <string name="factory_menu">Factory Menu</string>
    <string name="factory_reset">Reset</string>
    <string name="finish_lock">Unlocked</string>
    <string name="forget_network">Remove this network</string>
    <string name="front_typec">USB C</string>
    <string name="gateway">Gateway</string>
    <string name="hardware_self_test">Hardware self test</string>
    <string name="hardware_self_test_CPU">CPU</string>
    <string name="hardware_self_test_ethernet">Ethernet</string>
    <string name="hardware_self_test_fail">Fail</string>
    <string name="hardware_self_test_hard_disk">Hard Disk</string>
    <string name="hardware_self_test_memory">Memory</string>
    <string name="hardware_self_test_mic">Mic</string>
    <string name="hardware_self_test_no_test">No Test</string>
    <string name="hardware_self_test_success">Success</string>
    <string name="hardware_self_test_testing">Testing...</string>
    <string name="hardware_self_test_touch">Touch</string>
    <string name="hardware_self_test_wifi">Wifi</string>
    <string name="have_update">New version found</string>
    <string name="have_update_hint">-System update requires a restart\n-This update fixes some major issues. \nPlease update for your normal use.</string>
    <string name="help">Guide</string>
    <string name="hint_no_control">You cannot controll under the small screen for the current signal source.</string>
    <string name="home">Home</string>
    <string name="hotline">400-077-1585</string>
    <string name="install_fail">Installation failed</string>
    <string name="install_fail_reinstall">Installation failed, please try again</string>
    <string name="install_ing">Installing(%d%%), please wait...</string>
    <string name="install_msg">System installation will take a few minutes. \nIt will be temporarily unavailable during the installation.</string>
    <string name="install_right">Install</string>
    <string name="install_title">The latest version is downloaded. Click "Install" to continue the installation.</string>
    <string name="ip_address">IP</string>
    <string name="ip_setting">IP Setting</string>
    <string name="join">Join</string>
    <string name="language_and_locale">Language</string>
    <string name="language_change">Languages</string>
    <string name="lastest_version">. It is the latest version</string>
    <string name="lock">Touch Lock</string>
    <string name="login">Log in</string>
    <string name="login_cancel">Cancel</string>
    <string name="login_title">Login is required</string>
    <string name="mac_address">MAC Address</string>
    <string name="manual">Manual</string>
    <string name="maximize">Maximize</string>
    <string name="network">Ethernet</string>
    <string name="network_error">Network error, \nplease check the cable network or Wi-Fi connection. </string>
    <string name="network_wifi_status_authenticating">Authentication in progress…</string>
    <string name="network_wifi_status_blocked">Blocked</string>
    <string name="network_wifi_status_connected">Connected</string>
    <string name="network_wifi_status_connected_no_internet">Connected but unable to access the internet</string>
    <string name="network_wifi_status_connecting">Connecting…</string>
    <string name="network_wifi_status_disabled">Disactivated</string>
    <string name="network_wifi_status_disconnected">Disconnected</string>
    <string name="network_wifi_status_disconnecting">Disconnecting...</string>
    <string name="network_wifi_status_failed">Failed</string>
    <string name="network_wifi_status_idle"/>
    <string name="network_wifi_status_network_failure">IP confuguration failed</string>
    <string name="network_wifi_status_obtaining_ip_address">Accessing IP address…</string>
    <string name="network_wifi_status_password_failure">Authentication failed</string>
    <string name="network_wifi_status_saved">Saved</string>
    <string name="network_wifi_status_scanning">Scanning…</string>
    <string name="network_wifi_status_suspended">Paused</string>
    <string name="network_wifi_status_verifying_poor_link">Temporarily closed (poor network)</string>
    <string name="network_wifi_status_wifi_failure">Wi-Fi connection failed</string>
    <string name="no_pc_1">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:01)</string>
    <string name="no_pc_2">No computer is detected，Please shut down and check whether the \ncomputer is connected properly ，and then try to restart (error code:02)</string>
    <string name="no_sign">No signal</string>
    <string name="no_sign_hdmi">No signal is recognized, please check \nwhether the signal cable is connected properly</string>
    <string name="no_sign_msg">No signal is recognized, please try to restart</string>
    <string name="no_sign_windows_disabled">Windows is disabled, you can enable it in "Settings-More"</string>
    <string name="not_network">The network is not connected</string>
    <string name="password">Password</string>
    <string name="password_error">* Wrong password</string>
    <string name="privacy">Privacy is under protection</string>
    <string name="privacy_policy">Data Protection and Privacy Policy</string>
    <string name="projection">Mirroring</string>
    <string name="r30_write_speed">R30 Write Speed</string>
    <string name="ready_install">Preparing for installation, please wait...</string>
    <string name="reinstall">Reinstall</string>
    <string name="remaining">    left: </string>
    <string name="reset_ops">This restoration will take about 5 minutes. Please do not cut off the power during the restoration.</string>
    <string name="resolution">Screen Resolution</string>
    <string name="restart">Restart</string>
    <string name="restart_content">Once restarted, all running applications will end</string>
    <string name="restart_countdown_confirm">Restart(%ds)</string>
    <string name="restart_title">Confirm to restart?</string>
    <string name="resting">Sleep</string>
    <string name="retry">Retry</string>
    <string name="rgb_blue_gain">Blue Gain</string>
    <string name="rgb_blue_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="rgb_green_gain">Green Gain</string>
    <string name="rgb_green_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="rgb_red_gain">Red Gain</string>
    <string name="rgb_red_gain_value_wrong">The value should be 0 to 255.</string>
    <string name="rgb_reset">Reset</string>
    <string name="right_update">Update</string>
    <string name="running_memory">Running Memory</string>
    <string name="save">Save</string>
    <string name="saved_network">Network saved</string>
    <string name="screen_activation_status_activated">activated</string>
    <string name="screen_activation_status_getting">getting...</string>
    <string name="screen_activation_status_nonactivated">nonactivated</string>
    <string name="screen_air_play">AirPlay</string>
    <string name="screen_msg1">Scan the QR code or\nenter transcreen.app\nto download the app</string>
    <string name="screen_name">Phone/computer mirroring</string>
    <string name="screen_permission_refuse">REFUSE</string>
    <string name="screen_pin_code">PIN Code：%s</string>
    <string name="screen_pin_code2">PIN Code</string>
    <string name="screen_step_1">Download the Transcreen APP</string>
    <string name="screen_step_2">Connect hotspot\nName：%s\nPassword：%s</string>
    <string name="screen_step_3">Mirror to the board\nOpen Transcreen\nSelect【%s】to mirror</string>
    <string name="screen_text">Mirroring</string>
    <string name="screen_title">Control Panel</string>
    <string name="screen_wifi_connect">Connect the below Wi-Fi:</string>
    <string name="screen_wifi_eeo_guest"> EEO-Guest</string>
    <string name="screen_wifi_eeo_password"> eeoguest123</string>
    <string name="screen_wifi_name">Name: </string>
    <string name="screen_wifi_password">Password: </string>
    <string name="select_network">Select Wi-Fi</string>
    <string name="serial_number">Serial Number</string>
    <string name="service_email">Email</string>
    <string name="service_hotline">Phone</string>
    <string name="setting">Settings</string>
    <string name="shutdown">Shut Down</string>
    <string name="shutdown_content">Once shut down, all running applications will end</string>
    <string name="shutdown_countdown_cancel">Cancel</string>
    <string name="shutdown_countdown_confirm">Shut down(%ds)</string>
    <string name="shutdown_countdown_content">Your Windows has not been shut down properly. \nDo you want to shut it down mandatorily?</string>
    <string name="shutdown_countdown_title">Prompt</string>
    <string name="shutdown_title">Confirm to shut down?</string>
    <string name="sign">Source</string>
    <string name="simplified_chinese">简体中文</string>
    <string name="start_computer">Start Windows</string>
    <string name="startup_channel">Startup Source</string>
    <string name="store_memory">Storage Memory</string>
    <string name="stroke_algorithm">Stroke Algorithm</string>
    <string name="subnet_mask">Subnet Mask</string>
    <string name="switch_wifi">Wi-Fi</string>
    <string name="system_lastest_version">Your version is the latest version</string>
    <string name="system_version">System Version</string>
    <string name="toast_fail">Network connection failed</string>
    <string name="toast_resolution">%s   %dX%d @ %dHz</string>
    <string name="toast_set_ip_fail">Failed to set</string>
    <string name="toast_shutting_down_ops">Windows is shutting down.</string>
    <string name="toast_success">Network connected</string>
    <string name="total">in total: </string>
    <string name="touch_calibration">Touch Calibration</string>
    <string name="touch_slider">Half-Screen Slider</string>
    <string name="touch_unplugged">Touch not recognized</string>
    <string name="touch_version">Touch Version</string>
    <string name="uhd_hdmi">Please use HDMI 2.0 or higher cables \nthat meet the "UHD" signal specifications.</string>
    <string name="uhd_type_c">Please use fully-equipped Type-C cables \nthat meet the "UHD" signal specifications.</string>
    <string name="unlock">Unlock</string>
    <string name="updatable">Available version:</string>
    <string name="update_description">Update Description:</string>
    <string name="update_msg">System update will take a few minutes. \nIt will be temporarily unavailable during the update process.</string>
    <string name="wifi">Wi—Fi</string>
    <string name="wifi_behind_str">\"</string>
    <string name="wifi_device_error">The Wi-Fi device is abnormal, please contact customer service for assistance. </string>
    <string name="wifi_front_str">Please enter the password of \"</string>
    <string name="wifi_more">Wi-Fi Details</string>
    <string name="wifi_network">Network</string>
    <string name="window_factory_reset_content">Once reset, all data in Android will be deleted.</string>
    <string name="window_factory_reset_title">Confirm to reset the system?</string>
    <string name="window_host">System Restore</string>
    <string name="window_host_content">Once restored, all data in C Drive will be deleted.</string>
    <string name="window_host_title">Confirm to restore the Windows system?</string>
    <string name="windows">Windows</string>
    <string name="windows_disable">Windows Disable</string>
    <string name="windows_disabled">Windows (Disabled)</string>
    <string name="windows_task_manager">Windows Task Manager</string>
    <string name="wired_screen_name">Wired mirroring</string>
    <string name="wireless_screen">Wireless mirroring</string>
    <string name="wireless_screen_disabled">Wireless mirroring (Disactivated)</string>
    <string name="wireless_screen_disabled_toast">Wireless mirroring is disactivated. \nPlease activate it in Settings if you need.</string>
    <string name="wireless_screen_enable">Enable wireless mirroring</string>
    <string name="wireless_screen_enable_pin_code">Enable mirroring code</string>
    <string name="wireless_screen_name">Wireless mirroring</string>
    <string name="write_acceleration">Write Acceleration</string>
    <string name="write_text">Note</string>
    <string name="write_without_screen_on">Write without screen on</string>
    <style name="About_Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/fragment_about_line_height</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_line_margin_start</item>
        <item name="android:layout_marginRight">@dimen/fragment_about_line_margin_start</item>
        <item name="android:background">@color/line_50</item>
    </style>
    <style name="About_Text_Content">
        <item name="android:layout_width">@dimen/fragment_about_content_width</item>
        <item name="android:layout_height">@dimen/fragment_about_content_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_about_title_margin_start</item>
        <item name="android:gravity">end</item>
    </style>
    <style name="About_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/fragment_about_title_height</item>
        <item name="android:textSize">@dimen/fragment_about_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginLeft">@dimen/fragment_about_title_margin_start</item>
    </style>
    <style name="Adb_Button">
        <item name="android:layout_width">@dimen/adb_btn_width</item>
        <item name="android:minWidth">@dimen/adb_btn_width</item>
        <item name="android:layout_height">@dimen/adb_btn_height</item>
        <item name="android:minHeight">@dimen/adb_btn_height</item>
        <item name="android:background">@drawable/shape_adb_btn</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/adb_btn_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style>
    <style name="Adb_Switch">
        <item name="android:layout_width">@dimen/adb_sw_width</item>
        <item name="android:layout_height">@dimen/adb_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/adb_sw_margin_end</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/sw_adb_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>
    <style name="Adb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/adb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_centerVertical">true</item>
    </style>
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent
        </item><!-- 设置dialog背景为透明背景 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item><!-- 设置dialog背景变暗 -->
    </style>
    <style name="Dialog_NO_DARK" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@android:color/transparent
        </item><!-- 设置dialog背景为透明背景 -->
        <item name="android:backgroundDimEnabled">false</item><!-- 设置dialog背景不变暗 -->
    </style>
    <style name="EeoDialogStyle" parent="@style/Base.Theme.AppCompat.Dialog">

        <item name="android:colorBackground">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <!--&lt;!&ndash;设置没有窗口标题、dialog标题等各种标题&ndash;&gt;-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="windowNoTitle">true</item>
        <item name="android:dialogTitle">@null</item>
        <!--是否悬浮在activity上-->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        <!--        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>-->
        <!--        <item name="android:windowAnimationStyle">@style/Animation</item>-->
        <item name="android:windowContentTransitions">true</item>

        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>

    </style>
    <style name="Extra_Switch">
        <item name="android:layout_width">@dimen/fragment_extra_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_extra_sw_height</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">@dimen/fragment_extra_sw_margin_end</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>
    <style name="Extra_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_extra_tv_margin_start</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_extra_tv_text_size</item>
    </style>
    <style name="IP_Dialog_Select_Image">
        <item name="android:layout_width">@dimen/dialog_network_auto_manual_iv_width</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_iv_height</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_iv_margin_start
        </item>
    </style>
    <style name="IP_Dialog_Select_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dialog_network_auto_manual_item_height</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="IP_Dialog_Select_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/dialog_network_auto_manual_tv_margin_start
        </item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:textSize">@dimen/dialog_network_auto_manual_tv_text_size</item>
    </style>
    <style name="Line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/main_line1_height</item>
        <item name="android:background">@color/main_line</item>
    </style>
    <style name="Main_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Main_ShadowLayout_Circle_Img">
        <item name="android:layout_width">@dimen/sl_lock_width</item>
        <item name="android:layout_height">@dimen/sl_lock_height</item>
        <item name="android:layout_gravity">center</item>
    </style>
    <style name="Main_ShadowLayout_Circle_Text">
        <item name="android:layout_width">@dimen/tv_screen_width</item>
        <item name="android:layout_height">@dimen/tv_screen_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:textSize">@dimen/tv_screen_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
    </style>
    <style name="Main_Shutdown_TextView">
        <item name="android:layout_width">@dimen/tv_shutdown_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">start</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:textSize">@dimen/tv_desktop_text_size</item>
    </style>
    <style name="Main_YcCardView">
        <item name="ycCardBackgroundColor">@color/white_95</item>
        <item name="ycCardCornerRadius">@dimen/main_bg_radius</item>
        <item name="ycCardElevation">@dimen/sl_screen_elevation</item>
        <item name="ycCardPreventCornerOverlap">false</item>
        <item name="ycStartShadowColor">@color/shadow_color</item>
    </style>
    <style name="NetWork_Linear">
        <item name="android:layout_width">match_parent</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>

    </style>
    <style name="NetWork_Switch">
        <item name="android:layout_width">@dimen/fragment_network_sw_width</item>
        <item name="android:layout_height">@dimen/fragment_network_sw_height</item>
        <item name="android:background">@drawable/network_switch_bg</item>
        <item name="android:thumb">@null</item>
        <item name="android:track">@null</item>
    </style>
    <style name="NetWork_TextView">
        <item name="android:layout_width">@dimen/fragment_network_title_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_title_margin_start</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_network_text_size</item>
    </style>
    <style name="Network_EditText">
        <item name="android:layout_width">@dimen/fragment_network_et_width</item>
        <item name="android:layout_height">@dimen/fragment_network_et_height</item>
        <item name="android:textCursorDrawable">@drawable/shape_network_edt_cursor</item>
        <item name="android:textSize">@dimen/fragment_network_et_text_size</item>
        <item name="android:textColor">@color/color_network_edit</item>
        <item name="android:layout_marginLeft">@dimen/fragment_network_et_margin_start</item>
        <item name="android:paddingEnd">@dimen/fragment_network_et_padding_end</item>
        <item name="android:gravity">center_vertical|end</item>
    </style>
    <style name="Projection_ImageView">
        <item name="android:layout_width">@dimen/small_window_iv_home_width</item>
        <item name="android:layout_height">@dimen/small_window_iv_home_height</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Projection_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/small_window_tv_home_margin_start</item>
        <item name="android:textSize">@dimen/small_window_shortcut_text_size</item>
        <item name="android:textColor">@color/main_shutdown_text_color</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Rgb_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/rgb_tv_margin_start</item>
        <item name="android:textSize">@dimen/adb_tv_text_size</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="SeekBar_Light">
        <item name="android:layout_width">@dimen/sb_bright_width</item>
        <item name="android:layout_height">@dimen/sb_bright_height</item>
        <item name="android:maxWidth">@dimen/sb_bright_width</item>
        <item name="android:maxHeight">@dimen/sb_bright_max_height</item>
        <item name="android:paddingStart">@dimen/sb_bright_padding_start</item>
        <item name="android:paddingEnd">@dimen/sb_bright_padding_start</item>
        <item name="android:progressDrawable">@drawable/progress_vertical_gradient_simple_shape
        </item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@null</item>
        <item name="android:max">113</item>
    </style>
    <style name="SeekBar_Rgb">
        <item name="android:layout_width">@dimen/rgb_sb_width</item>
        <item name="android:layout_height">@dimen/rgb_sb_thumb_height</item>
        <item name="android:maxWidth">@dimen/rgb_sb_width</item>
        <item name="android:maxHeight">@dimen/rgb_sb_height</item>
        <item name="android:paddingStart">@dimen/rgb_sb_padding_start</item>
        <item name="android:paddingEnd">@dimen/rgb_sb_padding_end</item>
        <item name="android:layout_marginStart">@dimen/rgb_sb_margin_start</item>
        <item name="android:layout_marginTop">@dimen/rgb_sb_margin_top</item>
        <item name="android:progressDrawable">
            @drawable/progress_vertical_gradient_simple_shape_rgb
        </item>
        <item name="android:splitTrack">false</item>
        <item name="android:background">@null</item>
        <item name="android:thumb">@drawable/thumb_rgb</item>
        <item name="android:max">255</item>
    </style>
    <style name="Setting_ImageView">
        <item name="android:layout_width">@dimen/iv_bright_width</item>
        <item name="android:layout_height">@dimen/iv_bright_height</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/sl_network_margin_start</item>
    </style>
    <style name="Setting_ShadowLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/sl_network_height</item>
        <item name="hl_layoutBackground_true">@color/press_color</item>
        <item name="hl_layoutBackground">@color/white_0</item>
        <item name="hl_shadowColor">@color/shadow_color</item>
        <item name="hl_shapeMode">selected</item>
        <item name="hl_shadowOffsetX">@dimen/sl_network_shadow_offset_x</item>
        <item name="hl_shadowOffsetY">@dimen/sl_network_shadow_offset_y</item>
        <item name="hl_textColor">@color/text_black_100</item>
        <item name="hl_textColor_true">@color/white_100</item>

    </style>
    <style name="Setting_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginLeft">@dimen/tv_network_margin_start</item>
        <item name="android:textSize">@dimen/tv_network_text_size</item>
    </style>
    <style name="Shutdown_Btn_Cancel">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_white</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_cancel_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="Shutdown_Btn_Confirm">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_btn_confirm_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_btn_confirm_margin_left</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="Shutdown_Icon">
        <item name="android:layout_width">@dimen/iv_shutdown_width</item>
        <item name="android:layout_height">@dimen/iv_shutdown_height</item>
        <item name="android:layout_marginTop">@dimen/iv_shutdown_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/iv_shutdown_margin_left</item>
    </style>
    <style name="Shutdown_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_content_text_size</item>
        <item name="android:textColor">@color/text_black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_content_margin_top</item>
        <item name="android:layout_centerHorizontal">true</item>

    </style>
    <style name="Shutdown_Text_Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:layout_marginTop">@dimen/shutdown_tittle_margin_top</item>
        <item name="android:layout_marginLeft">@dimen/shutdown_tittle_margin_left</item>
    </style>
    <style name="Signal_FrameLayout">
        <item name="android:layout_width">@dimen/signal_tv_width</item>
        <item name="android:layout_height">@dimen/signal_tv_height</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:background">@drawable/shape_signal_bg</item>
    </style>
    <style name="Signal_ImageView">
        <item name="android:layout_width">@dimen/iv_screen_width</item>
        <item name="android:layout_height">@dimen/iv_screen_height</item>
        <item name="android:layout_marginStart">@dimen/signal_iv_margin_start</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Signal_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/signal_tv_margin_start</item>
        <item name="android:textSize">@dimen/signal_tv_text_size</item>
        <item name="android:textColor">@color/main_text_color</item>
    </style>
    <style name="SystemSetting_PopWindow_Host_Btn">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="textAllCaps">false</item>
        <item name="android:outlineProvider">none</item>
    </style>
    <style name="Theme.SystemSetting" parent="Theme.AppCompat.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorControlNormal">@color/white</item>
        
        <item name="colorButtonNormal">@color/teal_200</item>
        <item name="colorSwitchThumbNormal">@color/teal_700</item>
        <item name="colorBackgroundFloating">@color/black</item>
        

        
    </style>
    <style name="Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/shutdown_tittle_text_size</item>
        <item name="android:layout_centerHorizontal">true</item>
    </style>
    <style name="Update_Button">
        <item name="android:layout_width">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:minWidth">@dimen/shutdown_btn_confirm_width</item>
        <item name="android:layout_height">@dimen/shutdown_btn_confirm_height</item>
        <item name="android:background">@drawable/shape_shutdown_btn_green</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/shutdown_btn_confirm_text_size</item>
        <item name="android:textColor">@color/white_100</item>
        <item name="android:outlineProvider">none</item>
    </style>
    <style name="Update_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/fragment_update_tv_version_text_size</item>
    </style>
    <style name="WiFi_Text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/item_wifi_more_tv_disconnect_height</item>
        <item name="android:layout_marginStart">@dimen/item_wifi_more_tv_disconnect_margin_start
        </item>
        <item name="android:textSize">@dimen/item_wifi_more_tv_save_text_size</item>
        <item name="android:textColor">@color/text_press_green_black</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Wifi_Text_NAME">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/wifi_dialog_input_password_title_margin_top
        </item>
        <item name="android:textColor">@color/black_100</item>
        <item name="android:textSize">@dimen/wifi_dialog_input_password_title_text_size</item>
    </style>
    <style name="update_progress_horizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <!--进度条的进度颜色drawable文件-->
        <item name="android:progressDrawable">@drawable/progress_indeterminate_horizontal</item>
        <!--进度条的最小高度-->
        <item name="android:minHeight">@dimen/fragment_update_pb_horizontal_min_height</item>
        <!--进度条的最大高度-->
        <item name="android:maxHeight">@dimen/fragment_update_pb_horizontal_max_height</item>
    </style>
</resources>