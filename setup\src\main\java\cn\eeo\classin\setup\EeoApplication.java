package cn.eeo.classin.setup;

import android.app.Application;

import com.cvte.tv.api.TvApiSDKManager;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.elvishew.xlog.flattener.PatternFlattener;
import com.elvishew.xlog.printer.Printer;
import com.elvishew.xlog.printer.file.FilePrinter;
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator;

public class EeoApplication extends Application {
    public static final String TAG = "EeoApplication==";

    public static Udi udi;
    private static EeoApplication eeoApplication = null;

    public static EeoApplication getApplication() {
        return eeoApplication;
    }

    @Override
    public void onCreate() {
        super.onCreate();

        Printer filePrinter = new FilePrinter                      // 打印日志到文件的打印器
                .Builder(getExternalFilesDir(null).getPath())
                .fileNameGenerator(new DateFileNameGenerator())
                .flattener(new PatternFlattener("{d}: {m}"))
                .build();
        XLog.init(LogLevel.ALL, filePrinter);
        // 设置默认的未捕获异常处理器
        Thread.setDefaultUncaughtExceptionHandler(new MyExceptionHandler());
        //XLog.d("PATH:"+getFilesDir().getPath());
        XLog.d("PATH:" + getExternalFilesDir(null).getPath());
        eeoApplication = this;
        TvApiSDKManager.init(this);
        if (udi == null) {
            udi = new Udi(this, UdiConstant.TOKEN_SETTING);
        }
        /*String test=null;
        test.toCharArray();*/
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        TvApiSDKManager.destroy();
        XLog.i("onTerminate: ");
    }

    // 自定义的未捕获异常处理器
    private class MyExceptionHandler implements Thread.UncaughtExceptionHandler {
        @Override
        public void uncaughtException(Thread thread, Throwable throwable) {
            // 在这里进行崩溃日志的处理，记录到文件
            XLog.e("Uncaught Exception", throwable);

        }
    }
}
