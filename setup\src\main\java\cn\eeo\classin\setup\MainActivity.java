package cn.eeo.classin.setup;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.res.Configuration;
import android.graphics.drawable.AnimationDrawable;
import android.media.tv.TvContract;
import android.media.tv.TvView;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.ui.AppBarConfiguration;
import androidx.navigation.ui.NavigationUI;

import com.cvte.tv.api.TvApiSDKManager;
import com.cvte.tv.api.aidl.EntityInputSource;
import com.cvte.tv.api.aidl.EnumInputSourceId;
import com.cvte.tv.api.aidl.ITVApiSystemInputSourceAidl;
import com.cvte.tv.api.aidl.ITvApiManager;
import com.eeo.ota.arraymic.ArrayMicUpdateService;
import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.dialog.UpdateDialog;
import com.eeo.ota.service.SubDeviceUpdateService;
import com.eeo.ota.touch.SubDeviceUpdate;
import com.eeo.ota.util.Util;
import com.elvishew.xlog.XLog;

import java.lang.ref.WeakReference;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.eeo.classin.setup.databinding.ActivityMainBinding;
import cn.eeo.classin.setup.utils.CommonUtils;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "SetupMainActivity";
    private AppBarConfiguration appBarConfiguration;
    private ActivityMainBinding binding;

    private View mMainView;
    private TvView mTvView;
    private EnumInputSourceId mCurInputSourceId = EnumInputSourceId.SOURCE_ID_MAX;
    public static Handler mMHandler;
    private static final String PRO_COMPLETE_SOURCE_ID = "sys.complete.source_id";
    private final static int MSG_TUNE_TVVIEW = 1;
    private long mFirstTuneTime = 0;
    private ITvApiManager iTvApiManager;
    private ITVApiSystemInputSourceAidl mSourceApi;
    public static AnimationDrawable master_control_animationDrawable1;
    public static AnimationDrawable animationDrawable2;
    public static AnimationDrawable animationDrawable3;

    private final ExecutorService mExecutorService = Executors.newSingleThreadExecutor();

    /**
     * ota子设备更新相关
     * 系统显示前先检测子设备更新
     */
    private boolean mHasBound;
    private SubDeviceUpdateService.MyBinder mBinder;
    private SubDeviceUpdateService mSubDeviceUpdateService;
    private ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mBinder = (SubDeviceUpdateService.MyBinder) service;
            if (mBinder != null) {
                mSubDeviceUpdateService = mBinder.getService();
                mSubDeviceUpdateService.setSubDeviceUpdateListener(new SubDeviceUpdateCallback() {
                    @Override
                    public void onUpdateSuccess() {
                        Log.d("ArrayMicOTA", "Touch update success in setup module");
                        // Touch upgrade success, reboot flag already set in SubDeviceUpdate
                        // Start array microphone upgrade service without checking SP KEY
                        startArrayMicUpdateService();
                    }

                    @Override
                    public void onUpdateFail(String errMsg) {
                        unbindSubDeviceUpdateService();
                        Log.d("ArrayMicOTA", "Touch update failed in setup module");
                        // Touch upgrade failed, no reboot flag set
                        // Still start array microphone upgrade service
                        startArrayMicUpdateService();
                    }

                    @Override
                    public void onAllUpdateFinish() {

                    }
                });
                mSubDeviceUpdateService.updateSubDevice();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mBinder = null;
            mSubDeviceUpdateService = null;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        XLog.d("MainActivity onCreate ");
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        mMainView = findViewById(R.id.fragment_main);

        //开机第一次显示信号源画面要25s左右，这里提前先用1像素的tvView调用
        mTvView = findViewById(R.id.tv_view);
        getTvApi();
        mMHandler = new MHandler(this);
        mMHandler.sendEmptyMessage(MSG_TUNE_TVVIEW);

        //有子设备更新的，先隐藏开机引导界面
        if (checkSubDeviceUpdate()) {
            mMainView.setVisibility(View.GONE);
            bindSubDeviceUpdateService();
        } else {
            // The touch does not require an upgrade, but the array microphone upgrade service needs to be activated
            Log.d("ArrayMicOTA", "Touch update not needed, starting array mic update service");
            mMainView.setVisibility(View.GONE);  // 阻止进入用户引导界面
            startArrayMicUpdateService();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        XLog.d("MainActivity onStart ");
        CommonUtils.setTouchState(getApplicationContext(), false);
        initAnimationDate();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        initAnimationDate();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onSupportNavigateUp() {
        NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        return NavigationUI.navigateUp(navController, appBarConfiguration)
                || super.onSupportNavigateUp();
    }

    @Override
    protected void onStop() {
        super.onStop();
        CommonUtils.setTouchState(getApplicationContext(), true);
    }


    /**
     * 获取TVAPI对象
     */
    private void getTvApi() {
        iTvApiManager = TvApiSDKManager.getInstance().getTvApi();
        if (iTvApiManager == null) {
            XLog.i("can not get tvapi");
            return;
        }
        try {
            mSourceApi = iTvApiManager.getTVApiSystemInputSource();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * Tif切通道,底层信号源切换
     *
     * @param inputSource
     * @param tvPlayer
     */
    public void setTifInputSource(EntityInputSource inputSource, TvView tvPlayer) {
        if (inputSource == null || tvPlayer == null || TextUtils.isEmpty(inputSource.inputId)) {
            XLog.i("error inputSource = " + inputSource + " tvPlayer = " + tvPlayer);
            return;
        }
        if (mCurInputSourceId == inputSource.sourceId) {
            XLog.i("same inputid pass");
            return;
        }
        Uri uri = TvContract.buildChannelUriForPassthroughInput(inputSource.inputId);
        XLog.i("--->" + "sourceId= " + inputSource.sourceId + " inputId= " + inputSource.inputId + " uri= " + uri);
        tvPlayer.reset();
        tvPlayer.tune(inputSource.inputId, uri);
        mCurInputSourceId = inputSource.sourceId;
//		tvPlayer.tune("com.droidlogic.tvinput/.services.Hdmi4InputService/HW8", null);
        XLog.i("--->>>TifController::TvView.setMain() called<<<---");
        SystemProperties.set(PRO_COMPLETE_SOURCE_ID, String.valueOf(inputSource.sourceId.ordinal()));
    }

    private class MHandler extends Handler {
        private final WeakReference<Activity> mTarget;

        private MHandler(Activity target) {
            mTarget = new WeakReference<Activity>(target);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            MainActivity mMainActivity = (MainActivity) mTarget.get();
            switch (msg.what) {
                case MSG_TUNE_TVVIEW:
                    if (mFirstTuneTime == 0) {
                        mFirstTuneTime = System.currentTimeMillis();
                        XLog.i("handleMessage: mFirstTuneTime == 0");
                    }
                    XLog.i("handleMessage: MSG_TUNE_TVVIEW");
                    long curTime = System.currentTimeMillis();
                    if ("0".equals(SystemProperties.get("service.bootvideo.exit")) ||
                            curTime - mFirstTuneTime >= 5000) {
                        if (mSourceApi != null) {
                            try {
                                EntityInputSource inputSource = mSourceApi.eventSystemInputSourceGetInputSource();
                                setTifInputSource(inputSource, mTvView);
                                XLog.i("handleMessage: setTifInputSource");
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        XLog.i("handleMessage: > 5000");
                    } else {
                        XLog.i("boot video not exit yet retry 200ms later!");
                        if (mMHandler.hasMessages(MSG_TUNE_TVVIEW)) {
                            mMHandler.removeMessages(MSG_TUNE_TVVIEW);
                        }
                        mMHandler.sendEmptyMessageDelayed(MSG_TUNE_TVVIEW, 200);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    public void initAnimationDate() {
        XLog.d("initAnimationDate");
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                // 创建第一个帧动画  妙控屏-滑条半屏-大屏控制
                master_control_animationDrawable1 = new AnimationDrawable();
                // 创建第二个帧动画 滑条半屏
                animationDrawable2 = new AnimationDrawable();
                // 创建第三个帧动画 大屏控制
                animationDrawable3 = new AnimationDrawable();
                initAnimationDrawable();
            }
        });
    }

    private void initAnimationDrawable() {
        XLog.d("initDate");
        //master_control_animationDrawable1 添加动画帧
        for (int i = 1; i < 20; i++) {
            // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
            int resourceId;
            if (i < 9) {
                XLog.d("master_control_000" + i);
                resourceId = getResources().getIdentifier("master_control_000" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                }
            } else if (i == 9) {
                XLog.d("master_control_000" + i);
                resourceId = getResources().getIdentifier("master_control_000" + i, "drawable", getPackageName());
                XLog.d("resourceId" + resourceId + "    I=" + i);
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 1500); // 设置每帧的持续时间
                }
            } else if (i > 9) {
                XLog.d("master_control_00" + i);
                resourceId = getResources().getIdentifier("master_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                }
            }
        }
        //初始化第二个帧动画 滑条半屏
        // 遍历添加 21 张帧动画图片
        for (int i = 1; i < 22; i++) {
            // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
            int resourceId;
            if (i < 10) {
                XLog.d("screen_control_000" + i);
                resourceId = getResources().getIdentifier("screen_control_000" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 100);
                }
            } else if (i == 11) {
                XLog.d("screen_control_00" + i);
                resourceId = getResources().getIdentifier("screen_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 1500);
                }
            } else {
                resourceId = getResources().getIdentifier("screen_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 100);
                }
            }
        }

        // 创建第三个帧动画 大屏控制
        // 获取系统属性
        String propName = "persist.sys.boardsn.value";
        String propValue = SystemProperties.get(propName, "unknow");
        Log.d(TAG, propName + " 属性值: " + propValue);
        XLog.d(propName + " 属性值: " + propValue);
        //BS86A,BS86E，BS110A、BSP110A有滑条，其他产品类型都没滑条
        if (propValue.contains("BS86A") || propValue.contains("BS86E") || propValue.contains("BS110A") || propValue.contains("BSP110A")) {
            // 遍历添加 20 张帧动画图片
            for (int i = 1; i < 25; i++) {
                // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
                int resourceId;
                if (i < 10) {
                    XLog.d("half_screen_000" + i);
                    resourceId = getResources().getIdentifier("half_screen_000" + i, "drawable", getPackageName());
                    if (resourceId != 0) {
                        animationDrawable3.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                    }
                } else if (i == 15) {
                    resourceId = getResources().getIdentifier("half_screen_00" + i, "drawable", getPackageName());
                    if (resourceId != 0) {
                        animationDrawable3.addFrame(getResources().getDrawable(resourceId), 1500); // 设置每帧的持续时间
                    }
                } else {
                    XLog.d("half_screen_00" + i);
                    resourceId = getResources().getIdentifier("half_screen_00" + i, "drawable", getPackageName());
                    if (resourceId != 0) {
                        animationDrawable3.addFrame(getResources().getDrawable(resourceId), 100);
                    }
                }
            }
        } else {
            animationDrawable3 = null;
        }
    }

    /**
     * ota子设备更新相关
     */
    private boolean checkSubDeviceUpdate() {
        SubDeviceUpdate subDeviceUpdate = new SubDeviceUpdate(this, null);
        return subDeviceUpdate.checkUpdate();
    }

    private void bindSubDeviceUpdateService() {
        if (mHasBound) {
            return;
        }
        Log.d(TAG, "bindSubDeviceUpdateService: ");
        Intent intent = new Intent(this, SubDeviceUpdateService.class);
        bindService(intent, mServiceConnection, BIND_AUTO_CREATE);
        mHasBound = true;
    }

    private void unbindSubDeviceUpdateService() {
        if (mHasBound) {
            unbindService(mServiceConnection);
            mHasBound = false;
        }
    }

    private void startArrayMicUpdateService() {
        Log.d("ArrayMicOTA", "Starting array mic update service in setup module");

        // Show updating dialog for array mic service
        UpdateDialog.showUpdatingDialog(this);
        Log.d("ArrayMicOTA", "Showing update dialog for array mic service");

        Intent intent = new Intent(this, ArrayMicUpdateService.class);
        ArrayMicUpdateService.setExternalCallback(mArrayMicUpdateCallback);
        startService(intent);
    }

    private SubDeviceUpdateCallback mArrayMicUpdateCallback = new SubDeviceUpdateCallback() {
        @Override
        public void onUpdateSuccess() {
            Log.d("ArrayMicOTA", "Array mic update success in setup module");
            // Set array mic upgrade success flag
            if (mSubDeviceUpdateService != null) {
                SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
                if (subDeviceUpdate != null) {
                    subDeviceUpdate.setArrayMicUpdateSuccess(true);
                }
            }
            // Close update dialog and execute reboot check
            closeUpdateDialogAndExecuteReboot();
        }

        @Override
        public void onUpdateFail(String errMsg) {
            Log.e("ArrayMicOTA", "Array mic update failed in setup module: " + errMsg);
            // Array mic upgrade failed, do not set success flag
            // Close update dialog and execute reboot check
            closeUpdateDialogAndExecuteReboot();
        }

        @Override
        public void onAllUpdateFinish() {
            Log.d("ArrayMicOTA", "Array mic update all finished in setup module");
            // Close update dialog and execute reboot check
            closeUpdateDialogAndExecuteReboot();
        }
    };

    private void closeUpdateDialogAndExecuteReboot() {
        // Close update dialog
        UpdateDialog.dismissUpdatingDialog();
        Log.d("ArrayMicOTA", "Update dialog dismissed after array mic service completion");

        // Execute reboot check
        executeRebootOrProceed();
    }

    private void executeRebootOrProceed() {
        if (mSubDeviceUpdateService != null) {
            SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
            if (subDeviceUpdate != null) {
                if (subDeviceUpdate.shouldRebootAfterAllUpdates()) {
                    Log.d("ArrayMicOTA", "Rebooting due to successful upgrade (touch or array mic)");
                    Util.reboot(this, true);
                } else {
                    Log.d("ArrayMicOTA", "No successful upgrades, setup module should not proceed to normal flow");
                    // In setup module, if no upgrades succeeded, we might need different handling
                    // For now, we'll still reboot to ensure system stability
                    Log.d("ArrayMicOTA", "Setup module: rebooting anyway for system stability");
                    Util.reboot(this, true);
                }
            } else {
                Log.w("ArrayMicOTA", "SubDeviceUpdate instance is null, rebooting for safety");
                Util.reboot(this, true);
            }
        } else {
            Log.w("ArrayMicOTA", "SubDeviceUpdateService is null, rebooting for safety");
            Util.reboot(this, true);
        }
    }

    /**
     * Determine whether to restart based on the upgrade result of the touch
     */
    private void executeDelayedRebootAfterArrayMicService() {
        if (mSubDeviceUpdateService != null) {
            SubDeviceUpdate subDeviceUpdate = mSubDeviceUpdateService.getSubDeviceUpdate();
            if (subDeviceUpdate != null) {
                Log.d("ArrayMicOTA", "Executing delayed reboot check after array mic service completion");
                subDeviceUpdate.executeRebootAfterArrayMicService();
            } else {
                Log.w("ArrayMicOTA", "SubDeviceUpdate instance is null, cannot execute delayed reboot");
            }
        } else {
            Log.w("ArrayMicOTA", "SubDeviceUpdateService is null, cannot execute delayed reboot");
        }
    }
}