package com.eeo.udisdk.network;

public class EthernetConfig {
    private String ip;  //ip地址
    private String gateway;  //默认网关
    private String mask;  //掩码
    private String dns1;
    private String dns2;

    public EthernetConfig(String ip, String gateway, String mask, String dns1, String dns2) {
        this.ip = ip;
        this.gateway = gateway;
        this.mask = mask;
        this.dns1 = dns1;
        this.dns2 = dns2;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getMask() {
        return mask;
    }

    public void setMask(String mask) {
        this.mask = mask;
    }

    public String getDns1() {
        return dns1;
    }

    public void setDns1(String dns1) {
        this.dns1 = dns1;
    }

    public String getDns2() {
        return dns2;
    }

    public void setDns2(String dns2) {
        this.dns2 = dns2;
    }

    @Override
    public String toString() {
        return "EthernetConfig{" +
                "ip='" + ip + '\'' +
                ", gateway='" + gateway + '\'' +
                ", mask='" + mask + '\'' +
                ", dns1='" + dns1 + '\'' +
                ", dns2='" + dns2 + '\'' +
                '}';
    }
}
