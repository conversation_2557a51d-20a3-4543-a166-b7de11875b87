package com.eeo.commonlibrary.source;

import java.util.ArrayList;

public class SourceInterfaceImpl implements SourceInterface {
    @Override
    public int getSignalStatus() {
        return 0;
    }

    @Override
    public ArrayList<Integer> getAvailSourceList() {
        return null;
    }

    @Override
    public int getCurSourceId() {
        return 0;
    }

    @Override
    public boolean changeToSource(int sourceId) {
        return false;
    }
}
