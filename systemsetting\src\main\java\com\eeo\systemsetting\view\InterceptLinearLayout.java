package com.eeo.systemsetting.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class InterceptLinearLayout extends LinearLayout {

    private View view;

    public InterceptLinearLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void setView(View view){
        this.view = view;
    }

    private void setAnim(){
        if (view != null){

            ObjectAnimator animator = ObjectAnimator.ofFloat(view,"translationX",0,15,0,-15,0,-15,0,15,0);
            animator.setDuration(200);
            animator.start();
        }

    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        setAnim();
        return true;
    }
}
