package com.eeo.udisdk;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.eeo.udisdk.network.EthernetConfig;
import com.eeo.udisdk.network.NetworkManager;
import com.eeo.udisdk.picture.PictureManager;
import com.eeo.udisdk.source.SourceChangeListener;
import com.eeo.udisdk.source.SourceManager;
import com.eeo.udisdk.system.HalfScreenChangeListener;
import com.eeo.udisdk.system.OpsShutdownListener;
import com.eeo.udisdk.system.OtaUpgradeListener;
import com.eeo.udisdk.system.RS232DataListener;
import com.eeo.udisdk.system.ScreenStatusChangeListener;
import com.eeo.udisdk.system.ScreenshotListener;
import com.eeo.udisdk.system.SystemManager;
import com.eeo.udisdk.touch.TouchManager;
import com.eeo.udisdk.voice.VoiceManager;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONArray;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;


public class Udi {
    private static final String TAG = "Udi";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;

    private SourceManager mSourceManager;
    private TouchManager mTouchManager;
    private SystemManager mSystemManager;
    private PictureManager mPictureManager;
    private NetworkManager mNetworkManager;
    private VoiceManager mVoiceManager;

    public Udi(Context context, String token) {
        mContext = context;
        if (token == null) {
            token = UdiConstant.TOKEN_SETTING;
        }
        UdiSdk.init(context, token);
        mMediaType = MediaType.parse("application/json;charset=utf-8");
        mClient = UdiSdk.newOkHttpClientBuilder().build();
    }

    private SourceManager getSourceManager() {
        if (mSourceManager == null) {
            mSourceManager = new SourceManager(mContext, mMediaType, mClient);
        }
        return mSourceManager;
    }

    private TouchManager getTouchManager() {
        if (mTouchManager == null) {
            mTouchManager = new TouchManager(mContext, mMediaType, mClient);
        }
        return mTouchManager;
    }

    private SystemManager getSystemManager() {
        if (mSystemManager == null) {
            mSystemManager = new SystemManager(mContext, mMediaType, mClient);
        }
        return mSystemManager;
    }

    private PictureManager getPictureManager() {
        if (mPictureManager == null) {
            mPictureManager = new PictureManager(mContext, mMediaType, mClient);
        }
        return mPictureManager;
    }

    private NetworkManager getNetworkManager() {
        if (mNetworkManager == null) {
            mNetworkManager = new NetworkManager(mContext, mMediaType, mClient);
        }
        return mNetworkManager;
    }

    private VoiceManager getVoiceManager() {
        if (mVoiceManager == null) {
            mVoiceManager = new VoiceManager(mContext, mMediaType, mClient);
        }
        return mVoiceManager;
    }

    /**
     * 获取当前信号源
     *
     * @return
     * @see UdiConstant#SOURCE_PC
     */
    public String getCurrentSource() {
        return getSourceManager().getCurrentSource();
    }

    public String getAvailableSource() {
        return getSourceManager().getAvailableSourceArray();
    }

    /**
     * 切换信号源
     *
     * @param source
     * @see UdiConstant#SOURCE_PC
     */
    public void changeSource(String source) {
        getSourceManager().changeSource(source);
    }

    /**
     * 监听信号源通道变化
     */
    public void registerSourceChangeListener(SourceChangeListener sourceChangeListener) {
        getSourceManager().registerSourceChangeListener(sourceChangeListener);
    }

    /**
     * 取消监听信号源通道变化
     */
    public void unregisterSourceChangeListener() {
        getSourceManager().unregisterSourceChangeListener();
    }

    /**
     * 半屏和画中画坐标偏移接口，后调用的生效
     * 避免同时半屏和画中画时，画中画坐标接口后调用，导致半屏模式坐标偏移
     */
    public void setDisableSmallWindow(boolean disableSmallWindow) {
        getTouchManager().setDisableSmallWindow(disableSmallWindow);
    }

    /**
     * 设置画中画小窗口
     */
    public void setSmallWindow(boolean enable, int left, int top, int right, int bottom, String source) {
        setSmallWindow(enable, left, top, right, bottom, source, true);
    }

    public void setSmallWindow(boolean enable, int left, int top, int right, int bottom, String source, boolean useThread) {
        if (useThread) {
            getTouchManager().setSmallWindowWithThread(enable, left, top, right, bottom, source);
        } else {
            getTouchManager().setSmallWindow(enable, left, top, right, bottom, source);
        }
    }

    /**
     * 设置触摸偏移
     *
     * @param offset 0-2160
     * @param type
     * @see UdiConstant#TYPE_TOUCH_OFFSET_ALL
     */
    public boolean setTouchOffset(String type, int offset) {
        return getTouchManager().setTouchOffset(type, offset);
    }

    public boolean removeTouchArea() {
        return getTouchManager().removeTouchArea();
    }

    public boolean isOsdEnabled() {
        return getTouchManager().isOsdEnabled();
    }

    /**
     * 禁用外部通道触摸
     *
     * @param enable true:禁止外部通道 false:恢复触摸
     */
    public boolean enableOsd(boolean enable) {
        return getTouchManager().enableOsd(enable);
    }

    /**
     * 发送广播通过设置去开关OPS触摸
     * 因为可能有多个应用设置osd，只有全部应用都取消，才能真正取消
     * 统一由设置去控制外部通道触摸
     *
     * @param enable true:恢复外部通道触摸 false:禁用外部通道触摸
     */
    public static void sendOpsTouchEnableBroadcast(Context context, boolean enable) {
        Log.d(TAG, "sendEnableOsdBroadcast : enable=" + enable);
        Intent intent = new Intent("com.eeo.set.usb.enable");
        intent.setPackage("com.eeo.systemsetting");
        intent.putExtra("usbTouchKey", enable ? 0 : 1);  //0可触摸
        context.sendBroadcast(intent);
    }

    /**
     * 整机重启
     * 这个接口只会重启主系统，不会重启模块
     */
    public boolean reboot() {
        return getSystemManager().reboot();
    }

    /**
     * 整机关机
     */
    public boolean shutdown() {
        return getSystemManager().shutdown();
    }

    /**
     * pc开机
     */
    public boolean startOps() {
        return getSystemManager().startOps();
    }

    /**
     * pc关机
     */
    public boolean shutdownOps() {
        return getSystemManager().shutdownOps();
    }

    /**
     * 监听PC关机
     */
    public void registerOpsShutdownListener(OpsShutdownListener opsShutdownListener) {
        getSystemManager().registerOpsShutdownListener(opsShutdownListener);
    }

    /**
     * 取消监听PC关机
     */
    public void unregisterOpsShutdownListener() {
        getSystemManager().unregisterOpsShutdownListener();
    }

    public String getHdmirxEdidType() {
        return getSourceManager().getHdmirxEdidType();
    }

    public String getHdmiOutTimmingFormat() {
        return getSourceManager().getHdmiOutTimmingFormat();
    }

    /**
     * 切换当前输出EDID分辨率
     *
     * @param format 见 {@link UdiConstant}
     * @see UdiConstant#HDMI_OUT_FORMAT_3840X2160_60HZ_REAL_COLOR
     */
    public boolean setHdmiOutTimmingFormat(String format) {
        return getSourceManager().setHdmiOutTimmingFormat(format);
    }

    /**
     * 重置ops
     */
    public boolean resetOps() {
        return getSystemManager().resetOps();
    }

    /**
     * 是否半屏模式
     */
    public boolean isHalfScreen() {
        return getSystemManager().isHalfScreen();
    }

    /**
     * 设置半屏模式
     */
    public boolean setHalfScreen(boolean enable) {
        return getSystemManager().setHalfScreen(enable);
    }

    /**
     * 监听半屏模式变化
     */
    public void registerHalfScreenChangeListener(HalfScreenChangeListener halfScreenChangeListener) {
        getSystemManager().registerHalfScreenChangeListener(halfScreenChangeListener);
    }

    /**
     * 取消监听半屏模式变化
     */
    public void unregisterHalfScreenChangeListener() {
        getSystemManager().unregisterHalfScreenChangeListener();
    }

    /**
     * 发送RS232数据
     *
     * @param data 十六进制数组
     *             JSONArray data = new JSONArray();
     *             data.put(0xAA);
     *             data.put(0xBB);
     *             data.put(0xCC);
     */
    public boolean sendRS232Data(JSONArray data) {
        return getSystemManager().sendRS232Data(data);
    }

    /**
     * 监听RS232数据变化
     */
    public void registerRS232Listener(RS232DataListener mRS232DataListener) {
        getSystemManager().registerRS232Listener(mRS232DataListener);
    }

    /**
     * 取消监听RS232数据变化
     */
    public void unregisterRS232Listener() {
        getSystemManager().unregisterRS232Listener();
    }

    /**
     * 查询PC模块是否插入
     */
    public boolean isOpsConnected() {
        return getSystemManager().isOpsConnected();
    }

    /**
     * 获取ops状态
     *
     * @return
     * @see UdiConstant#OPS_STATUS_ON
     */
    public String getOpsStatus() {
        return getSystemManager().getOpsStatus();
    }

    /**
     * 是否亮屏
     */
    public boolean isScreenOn() {
        return getSystemManager().isScreenOn();
    }

    /**
     * 亮屏/熄屏
     *
     * @param isOn             是否亮屏
     * @param isAllowIntercept ？
     * @param isMute           是否静音
     */
    public boolean changeScreenStatus(boolean isOn, boolean isAllowIntercept, boolean isMute) {
        return getSystemManager().changeScreenStatus(isOn, isAllowIntercept, isMute);
    }

    /**
     * 监听息屏、亮屏模式变化
     */
    public void registerScreenStatusChangeListener(ScreenStatusChangeListener screenStatusChangeListener) {
        getSystemManager().registerScreenStatusChangeListener(screenStatusChangeListener);
    }

    /**
     * 取消监听息屏、亮屏模式变化
     */
    public void unregisterScreenStatusChangeListener() {
        getSystemManager().unregisterScreenStatusChangeListener();
    }

    /**
     * 截屏
     *
     * @param path 图片保存的路径
     */
    public String screenshot(String path) {
        return getSystemManager().screenshot(path);
    }

    /**
     * 截屏
     * screenshotListener.onScreenshotSuccess(Bitmap bitmap)
     * 返回bitmap
     */
    public void screenshot(ScreenshotListener screenshotListener) {
        getSystemManager().screenshot(screenshotListener);
    }

    /**
     * 设置截屏类型
     *
     * @param type
     * @see UdiConstant#TYPE_SHOT_VIDEO_AND_OSD
     */
    public boolean setScreenshotType(String type) {
        return getSystemManager().setScreenshotType(type);
    }

    /**
     * 录屏是否支持
     */
    public boolean isScreenRecordSupport() {
        return getSystemManager().isScreenRecordSupport();
    }

    /**
     * 录屏
     */
    public boolean screenRecord(String path, boolean isRecordAudio, boolean isRecordMicrophone,
                                int systemVolume, int microphoneVolume, int resolution) {
        return getSystemManager().screenRecord(path, isRecordAudio, isRecordMicrophone, systemVolume, microphoneVolume, resolution);
    }

    public boolean stopScreenRecord() {
        return getSystemManager().stopScreenRecord();
    }

    /**
     * ota升级
     */
    public boolean otaUpgrade(String path, OtaUpgradeListener otaUpgradeListener) {
        return getSystemManager().otaUpgrade(path, otaUpgradeListener);
    }

    /**
     * 模拟PC按键
     *
     * @param controlKey  控制键组合（如 CTRL | SHIFT）(CTRL=0x01,SHIFT=0x02,ALT=0x04,WIN=0x08)
     * @param functionKey 功能键码
     * @param event       事件类型 (REVERSED=0x0,CLICK=0x1,DOWN=0x2,UP=0x3)
     * @param duration    按键持续时间（毫秒）
     * @see PcKeyboardCode#CONTROL_KEY_BOARD_ALT
     */
    public boolean sendPcKeyEvent(int controlKey, int functionKey, int event, int duration) {
        return getSystemManager().sendPcKeyEvent(controlKey, functionKey, event, duration);
    }

    public boolean sendPcKeyEvent(int controlKey, int functionKey, int event) {
        return getSystemManager().sendPcKeyEvent(controlKey, functionKey, event, 50);
    }

    /**
     * 是否护眼模式
     */
    public boolean isEyeCareEnabled() {
        return getPictureManager().isEyeCareEnabled();
    }

    /**
     * 设置护眼模式
     */
    public boolean enableEyeCare(boolean enable) {
        return getPictureManager().enableEyeCare(enable);
    }

    /**
     * 获取屏幕亮度
     *
     * @return
     */
    public int getBrightnessValue() {
        return getPictureManager().getBrightnessValue();
    }

    /**
     * 设置屏幕亮度
     *
     * @param value
     * @return
     */
    public boolean setBrightnessValue(int value) {
        return getPictureManager().setBrightnessValue(value);
    }

    /**
     * 整机有线网络开关状态
     */
    public boolean isEthernetEnabled() {
        return getNetworkManager().isEthernetEnabled();
    }

    /**
     * 整机有线网络开关
     */
    public boolean enableEthernet(boolean enable) {
        return getNetworkManager().enableEthernet(enable);
    }

    /**
     * 获取整机有线网络连接模式
     */
    public String getEthernetMode() {
        return getNetworkManager().getEthernetMode();
    }

    /**
     * 设置整机有线网络连接模式
     *
     * @param mode
     * @see UdiConstant#ETHERNET_MODE_DHCP
     */
    public boolean setEthernetMode(String mode) {
        return getNetworkManager().setEthernetMode(mode);
    }

    /**
     * 获取整机有线网络配置
     */
    public EthernetConfig getEthernetConfig() {
        return getNetworkManager().getEthernetConfig();
    }

    /**
     * 设置整机有线网络配置
     */
    public boolean setEthernetConfig(EthernetConfig ethernetConfig) {
        return getNetworkManager().setEthernetConfig(ethernetConfig);
    }

    /**
     * 获取整机IP地址
     */
    public String getIpAddress() {
        return getNetworkManager().getIpAddress();
    }

    /**
     * 获取整机MAC地址
     */
    public String getMacAddress() {
        return getNetworkManager().getMacAddress();
    }

    public boolean isMute() {
        return getVoiceManager().isMute();
    }

    public boolean setMute(boolean isMute) {
        return getVoiceManager().setMute(isMute);
    }
}
