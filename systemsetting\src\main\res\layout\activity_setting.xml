<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/main_width"
    android:layout_height="@dimen/main_height"
    android:layout_alignParentEnd="true"
    android:layout_alignParentBottom="true">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/iv_back_width"
        android:layout_height="@dimen/iv_back_height"
        android:layout_marginStart="@dimen/iv_back_margin_start"
        android:layout_marginTop="@dimen/iv_back_margin_top"
        android:importantForAccessibility="no"
        android:src="@drawable/select_left_icon" />

    <TextView
        android:id="@+id/txt_title"
        style="@style/Title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/setting_tv_title_margin_top"
        android:text="@string/setting" />

    <View
        android:id="@+id/line1"
        style="@style/Line"
        android:layout_below="@id/iv_back"
        android:layout_marginTop="@dimen/setting_line1_margin_top" />

    <LinearLayout
        android:id="@+id/ll_select"
        android:layout_width="@dimen/ll_select_width"
        android:layout_height="match_parent"
        android:layout_below="@id/line1"
        android:orientation="vertical">

        <com.lihang.ShadowLayout
            android:id="@+id/sl_network"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_network_margin_top"
            app:hl_bindTextView="@id/txt_network">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_network_icon" />

            <TextView
                android:id="@+id/txt_network"
                style="@style/Setting_TextView"
                android:text="@string/network" />


        </com.lihang.ShadowLayout>

        <com.lihang.ShadowLayout
            android:id="@+id/sl_wifi"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_wifi_margin_top"
            app:hl_bindTextView="@id/txt_wifi">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_wifi_icon" />

            <TextView
                android:id="@+id/txt_wifi"
                style="@style/Setting_TextView"
                android:text="@string/wifi" />


        </com.lihang.ShadowLayout>

        <com.lihang.ShadowLayout
            android:id="@+id/sl_locale"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_wifi_margin_top"
            app:hl_bindTextView="@id/txt_locale">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_locale_icon" />

            <TextView
                android:id="@+id/txt_locale"
                style="@style/Setting_TextView"
                android:text="@string/language_and_locale" />


        </com.lihang.ShadowLayout>

        <com.lihang.ShadowLayout
            android:id="@+id/sl_extra"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_wifi_margin_top"
            app:hl_bindTextView="@id/txt_extra">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_extra_icon" />

            <TextView
                android:id="@+id/txt_extra"
                style="@style/Setting_TextView"
                android:text="@string/extra" />


        </com.lihang.ShadowLayout>

        <com.lihang.ShadowLayout
            android:id="@+id/sl_about"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_wifi_margin_top"
            app:hl_bindTextView="@id/txt_about">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_about_icon" />

            <TextView
                android:id="@+id/txt_about"
                style="@style/Setting_TextView"
                android:text="@string/about" />


        </com.lihang.ShadowLayout>

        <com.lihang.ShadowLayout
            android:id="@+id/sl_check_update"
            style="@style/Setting_ShadowLayout"
            android:layout_marginTop="@dimen/sl_wifi_margin_top"
            app:hl_bindTextView="@id/txt_update">

            <ImageView
                style="@style/Setting_ImageView"
                android:background="@drawable/select_check_update_icon" />

            <TextView
                android:id="@+id/txt_update"
                style="@style/Setting_TextView"
                android:text="@string/check_update" />


        </com.lihang.ShadowLayout>


    </LinearLayout>

    <View
        android:id="@+id/line2"
        android:layout_width="@dimen/main_line1_height"
        android:layout_height="match_parent"
        android:layout_below="@id/line1"
        android:layout_toEndOf="@id/ll_select"
        android:background="@color/main_line" />

    <FrameLayout
        android:id="@+id/fl_fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/line1"
        android:layout_toEndOf="@id/line2" />

</RelativeLayout>