<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SystemSetting" parent="Theme.AppCompat.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorControlNormal">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorButtonNormal">@color/teal_200</item>
        <item name="colorSwitchThumbNormal">@color/teal_700</item>
        <item name="colorBackgroundFloating">@color/black</item>
        <!-- Status bar color. -->
<!--        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>-->
        <!-- Customize your theme here. -->
    </style>
</resources>