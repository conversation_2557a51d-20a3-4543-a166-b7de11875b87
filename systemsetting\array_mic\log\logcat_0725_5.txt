2025-07-25 16:09:02.703  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Checking for array mic update...
2025-07-25 16:09:02.712  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-07-25 16:09:02.746  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Current version: A009, Target version: A013
2025-07-25 16:09:02.746  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? true. Is specific error version? false
2025-07-25 16:09:02.746  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update...
2025-07-25 16:09:02.749  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-25 16:09:02.749  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-25 16:09:02.751  8492-8537  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side SOC
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 127617064,0,0.
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 16:09:03.791  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 16:09:03.792  8492-8540  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 16:09:03.792  8492-8537  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side SOC] finished with exit code: 0
2025-07-25 16:09:03.793  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-07-25 16:09:03.802  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-07-25 16:09:03.803  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-07-25 16:09:03.803  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-07-25 16:09:03.836  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-07-25 16:09:03.836  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE






2025-07-25 16:09:29.742  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_DISCONNECT
2025-07-25 16:09:29.768  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-25 16:09:29.768  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_CONNECT
2025-07-25 16:10:02.220  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Device reconnected after reboot.
2025-07-25 16:10:07.225  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: FINAL_VERSION_VALIDATION
2025-07-25 16:10:07.271  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Update successful! New version: A013
2025-07-25 16:10:07.271  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: Update success.
2025-07-25 16:10:07.271  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 16:10:07.271  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-25 16:10:07.272  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC and verify.
2025-07-25 16:10:07.273  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 61413416,0,0.
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 16:10:08.313  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 16:10:08.314  8492-8637  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 16:10:08.315  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 16:10:10.342  8492-8492  ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect. Retrying...
2025-07-25 16:10:10.343  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 2/3 to switch USB to PC and verify.
2025-07-25 16:10:10.343  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 112306216,0,0.
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 16:10:11.380  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 16:10:11.381  8492-8642  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 16:10:11.382  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 16:10:13.406  8492-8492  ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect. Retrying...
2025-07-25 16:10:13.406  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 3/3 to switch USB to PC and verify.
2025-07-25 16:10:13.406  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: sample_xml_usbsw s side PC
2025-07-25 16:10:14.441  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 213985320,0,0.
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 16:10:14.442  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 16:10:14.443  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 16:10:14.443  8492-8648  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 16:10:14.445  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 16:10:16.476  8492-8492  ArrayMicOTA             com.eeo.systemsetting                E  Cleanup failed. Could not verify disconnect after 3 retries. Finishing process anyway.
2025-07-25 16:10:16.476  8492-8492  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-25 16:10:16.480  8492-8492  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@6dbacbb
2025-07-25 16:10:16.480  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-25 16:10:16.480  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-25 16:10:16.480  8492-8492  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.