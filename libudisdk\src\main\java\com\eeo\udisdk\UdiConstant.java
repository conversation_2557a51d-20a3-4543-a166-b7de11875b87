package com.eeo.udisdk;

public class UdiConstant {
    /**
     * com.eeo.annotation  ：xZLhfVvV6d1gcql7EmVnPao4eyqhoLDABF18WOdc2VsRpUA=
     * com.eeo.systemsetting： xZLhemyE9X2RUhfedt_9sOsCr4RMn_20wGMhL2Xo1ytWTS7dIo0=
     * com.eeo.projection： xZLhfVvV6d1gcql7A_NmAqGsFoIM9VglUcLiwnhxdAqehCs=
     */
    public static final String TOKEN_ANNOTATION = "xZLhfVvV6d1gcql7EmVnPao4eyqhoLDABF18WOdc2VsRpUA=";
    public static final String TOKEN_SETTING = "xZLhemyE9X2RUhfedt_9sOsCr4RMn_20wGMhL2Xo1ytWTS7dIo0=";
    public static final String TOKEN_PROJECTION = "xZLhfVvV6d1gcql7A_NmAqGsFoIM9VglUcLiwnhxdAqehCs=";

    /**
     * SourceItemEnum
     * 信号源枚举
     */
    public static final String SOURCE_HDMI1 = "HDMI1";
    public static final String SOURCE_HDMI2 = "HDMI2";
    public static final String SOURCE_HDMI3 = "HDMI3";
    public static final String SOURCE_HDMI4 = "HDMI4";
    public static final String SOURCE_DP = "DP";
    public static final String SOURCE_PC = "PC";   //OPS
    public static final String SOURCE_DVD = "DVD";
    public static final String SOURCE_ANDROID = "ANDROID";  //Android
    public static final String SOURCE_ANDROID_SLOT = "ANDROID_SLOT";
    public static final String SOURCE_VGA1 = "VGA1";
    public static final String SOURCE_VGA2 = "VGA2";
    public static final String SOURCE_YPbPr = "YPbPr";
    public static final String SOURCE_AV = "AV";
    public static final String SOURCE_ATV = "ATV";
    public static final String SOURCE_DTV = "DTV";
    public static final String SOURCE_RECORD = "RECORD";
    public static final String SOURCE_TYPE_C1 = "TYPE_C1";
    public static final String SOURCE_TYPE_C2 = "TYPE_C2";
    public static final String SOURCE_DVI = "DVI";
    public static final String SOURCE_NONE = "NONE";

    /**
     * SourceChangeReason
     **/
    public static final String SOURCE_CHANGE_REASON_NORMAL = "NORMAL";
    public static final String SOURCE_CHANGE_REASON_POWER_OFF = "POWER_OFF";


    /**
     * HdmiOutFormatEnum
     */
    public static final String HDMI_OUT_FORMAT_AUTO = "FMT_AUTO";
    public static final String HDMI_OUT_FORMAT_1366_768 = "FMT_1366_768";
    public static final String HDMI_OUT_FORMAT_1080P_50HZ = "FMT_1080P_50HZ";
    public static final String HDMI_OUT_FORMAT_1080P_60HZ = "FMT_1080P_60HZ";
    public static final String HDMI_OUT_FORMAT_3840X2160_30HZ = "FMT_3840X2160_30HZ";
    public static final String HDMI_OUT_FORMAT_3840X2160_50HZ = "FMT_3840X2160_50HZ";
    public static final String HDMI_OUT_FORMAT_3840X2160_60HZ = "FMT_3840X2160_60HZ";
    public static final String HDMI_OUT_FORMAT_480P_60HZ = "FMT_480P_60HZ";
    public static final String HDMI_OUT_FORMAT_720P_60HZ = "FMT_720P_60HZ";
    public static final String HDMI_OUT_FORMAT_3840X2160_60HZ_REAL_COLOR = "FMT_3840X2160_60HZ_REAL_COLOR";
    public static final String HDMI_OUT_FORMAT_3440X1440_60HZ = "FMT_3440X1440_60HZ";

    /**
     * TouchOffsetTypeEnum
     */
    public static final String TYPE_TOUCH_OFFSET_OFF = "OFF"; //关闭
    public static final String TYPE_TOUCH_OFFSET_PC = "PC"; //仅PC通道
    public static final String TYPE_TOUCH_OFFSET_ANDROID = "ANDROID"; //仅安卓通道
    public static final String TYPE_TOUCH_OFFSET_ALL = "ALL"; //全部通道

    /**
     * ShotTypeEnum
     */
    public static final String TYPE_SHOT_VIDEO_ONLY = "VIDEO_ONLY"; //只截取外部通道画面,4K
    public static final String TYPE_SHOT_OSD_ONLY = "OSD_ONLY"; //只截取OSD画面，1080P
    public static final String TYPE_SHOT_VIDEO_AND_OSD = "VIDEO_AND_OSD"; //截取OSD和外部通道的合成，720P，默认值

    /**
     * PcStateEnum
     */
    public static final String OPS_STATUS_OFF = "OFF";
    public static final String OPS_STATUS_SLEEP = "SLEEP";
    public static final String OPS_STATUS_HIBERNATE = "HIBERNATE";
    public static final String OPS_STATUS_ON = "ON";

    /**
     * ConnectModeEnum
     */
    public static final String ETHERNET_MODE_DHCP = "DHCP";
    public static final String ETHERNET_MODE_MANUAL = "MANUAL"; //手动
    public static final String ETHERNET_MODE_UNKNOWN = "UNKNOWN";

    /**
     * RemoveFilePolicy
     * 删除文件策略
     */
    public static final String OTA_POLICY_NEVER = "POLICY_NEVER"; //不删除
    public static final String OTA_POLICY_SUCCESS = "POLICY_SUCCESS"; //成功的时候删除
    public static final String OTA_POLICY_FAILURE = "POLICY_FAILURE"; //失败的时候删除
    public static final String OTA_POLICY_ALWAYS = "POLICY_ALWAYS";

}
