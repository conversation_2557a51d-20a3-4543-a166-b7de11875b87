package com.eeo.systemsetting.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.PixelFormat;
import android.graphics.drawable.AnimationDrawable;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.PowerManager;
import android.os.SystemProperties;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;

import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.dialog.CommonDialog;
import com.eeo.systemsetting.dialog.ShutdownCountdownDialog;
import com.eeo.systemsetting.rs232.Rs232Manager;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.system.OpsShutdownListener;

import java.util.Timer;
import java.util.TimerTask;

public class PowerUtil {
    private static final String TAG = "PowerUtil";
    private static PowerUtil mPowerUtil = null;
    private Context mContext;
    private PowerManager mPowerManager;
    private Udi mUdi;

    private boolean mIsShuttingDown = false;

    /**
     * 无信号关机中标志
     */
    private boolean mIsNoSignalShuttingDown = false;

    /**
     * 设置了该标志，重启也超时关ops
     * 升级完触摸框后要确保重启不被阻止
     */
    private boolean mIsForceReboot = false;

    /**
     * 弱关机：ops关机1分钟超时后，弹窗倒计时15s后，直接关Android
     */
    public static final int FLAG_SHUTDOWN_WEAK = 0;
    /**
     * 硬关机：直接关Android
     */
    public static final int FLAG_SHUTDOWN_HARD = 1;
    /**
     * 软关机：ops关机15s超时后，直接关Android
     */
    public static final int FLAG_SHUTDOWN_SOFT = 2;

    /**
     * 重启、关机弹窗
     */
    private CommonDialog mRestartDialog;
    private CommonDialog mShutdownDialog;
    private Button mRestartConfirmBtn;
    private Button mShutdownConfirmBtn;
    private int mRestartCountDown = 10;
    private int mShutdownCountDown = 10;

    private static final int MSG_SHOW_COUNT_DOWN_DIALOG = 0x001;
    private static final int MSG_SHOW_RESTART_DIALOG = 0x002;
    private static final int MSG_SHOW_SHUTDOWN_DIALOG = 0x003;
    //重启、关机弹窗倒计时
    private static final int MSG_RESTART_COUNT_DOWN = 0x004;
    private static final int MSG_SHUTDOWN_COUNT_DOWN = 0x005;

    private static final int MSG_SHOW_SHUTTING_DOWN_OPS_DIALOG = 0x006;
    private static final int MSG_DISMISS_SHUTTING_DOWN_OPS_DIALOG = 0x007;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_SHOW_COUNT_DOWN_DIALOG:
                    dismissShuttingDownOpsView();
                    ShutdownCountdownDialog shutdownCountdownDialog = new ShutdownCountdownDialog(mContext);
                    shutdownCountdownDialog.show();
                    break;
                case MSG_SHOW_RESTART_DIALOG:
                    showRestartDialogInternal();
                    break;
                case MSG_SHOW_SHUTDOWN_DIALOG:
                    showShutdownDialogInternal();
                    break;
                case MSG_RESTART_COUNT_DOWN:
                    if (mRestartCountDown > 0) {
                        mRestartConfirmBtn.setText(String.format(mContext.getString(R.string.restart_countdown_confirm), mRestartCountDown));
                        mRestartCountDown--;
                        sendEmptyMessageDelayed(MSG_RESTART_COUNT_DOWN, 1000);
                    } else {
                        restart();
                    }
                    break;
                case MSG_SHUTDOWN_COUNT_DOWN:
                    if (mShutdownCountDown > 0) {
                        mShutdownConfirmBtn.setText(String.format(mContext.getString(R.string.shutdown_countdown_confirm), mShutdownCountDown));
                        mShutdownCountDown--;
                        sendEmptyMessageDelayed(MSG_SHUTDOWN_COUNT_DOWN, 1000);
                    } else {
                        shutdown();
                    }
                    break;
                case MSG_SHOW_SHUTTING_DOWN_OPS_DIALOG:
                    showShuttingDownOpsView();
                    break;
                case MSG_DISMISS_SHUTTING_DOWN_OPS_DIALOG:
                    dismissShuttingDownOpsView();
                    break;
            }
        }
    };

    private PowerUtil(Context context) {
        mContext = context;
        mPowerManager = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE);
        mUdi = new Udi(context, UdiConstant.TOKEN_SETTING);
    }

    public static PowerUtil getInstance(Context context) {
        if (mPowerUtil == null) {
            mPowerUtil = new PowerUtil(context);
            CommonUtils.updateDensity(context);
        }
        return mPowerUtil;
    }

    public static void destroy() {
        if (mPowerUtil != null) {
            mPowerUtil = null;
        }
    }

    public boolean isShuttingDown() {
        return mIsShuttingDown;
    }

    public void setShuttingDown(boolean isShuttingDown) {
        mIsShuttingDown = isShuttingDown;
    }

    public boolean isNoSignalShuttingDown() {
        return mIsNoSignalShuttingDown;
    }

    public void cancelNoSignalShutdown() {
        Log.d(TAG, "cancelNoSignalShutdown: ");
        mIsShuttingDown = false;
        mIsNoSignalShuttingDown = false;
        stopShutdownOpsTimer();
        dismissShuttingDownOpsView();
    }


    /**
     * shutdown or reboot both android and ops
     * add by eeo liuxinxi
     */
    public void shutdownOrReboot(boolean reboot, String reason) {
        shutdownOrReboot(reboot, reason, FLAG_SHUTDOWN_WEAK);
    }

    public void shutdownOrReboot(boolean reboot, String reason, int shutdownFlag) {
        shutdownOrReboot(reboot, reason, shutdownFlag, true);
    }

    /**
     * @param shutdownFlag 弱关机、硬关机、软关机
     *                     弱关机：ops关机1分钟超时后，弹窗倒计时15s后，直接关Android
     *                     硬关机：直接关Android
     *                     软关机：ops关机15s超时后，直接关Android
     * @param fromUser     是否主动操作，还是无信号自动关机
     */
    public void shutdownOrReboot(boolean reboot, String reason, int shutdownFlag, boolean fromUser) {
        Log.d(TAG, "shutdownOrReboot: reboot=" + reboot + " ,reason=" + reason + " ,shutdownFlag=" + shutdownFlag);
        mIsShuttingDown = true;
        if (!fromUser) {
            mIsNoSignalShuttingDown = true;
        }

        if (!mUdi.isScreenOn()) {
            //熄屏时先亮屏
            mUdi.changeScreenStatus(true, false, false);
        }
        //退出画中画、批注、控制界面、投屏引导界面等
        CommonUtils.exitAll(mContext);
        //结束投屏
        CommonUtils.finishMultiScreen(mContext);

        try {
            if (isOpsOn()) {
                if (isInOpsSource() || CommonUtils.isOpsDisable()) {
                    shutdownOps(reboot, reason, shutdownFlag, fromUser);
                } else {
                    changeToOps(reboot, reason, shutdownFlag);
                }
            } else {
                shutdownOrRebootAndroid(reboot, reason);
            }
            Log.i(TAG, "shutdownOrReboot: ");
        } catch (Exception e) {
            e.printStackTrace();
            mIsShuttingDown = false;
            Log.e(TAG, "shutdownOrReboot error: " + e);
        }
    }

    private void shutdownOrRebootAndroid(boolean reboot, String reason) {
        Log.e(TAG, "shutdownOrRebootAndroid: reboot=" + reboot + " ,reason=" + reason);
        dismissShuttingDownOpsView();
        SystemControlManager.getInstance().setBreathLedStatus(3); //1：唤醒  2：休眠 3：关机或重启
        //发送RS232指令通知扩展坞已关机
        CommonUtils.sendRS232Data(Rs232Manager.KEY_POWER_OFF);
        if (reboot) {
            mPowerManager.reboot(reason);
        } else {
            try {
                mPowerManager.getClass().getMethod("shutdown", boolean.class, String.class, boolean.class)
                        .invoke(mPowerManager, false, reason, true);
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "shutdown error: " + e);
                mIsShuttingDown = false;
            }
        }
    }

    private boolean isOpsConnected() {
        return mUdi.isOpsConnected();
    }

    private boolean isOpsOn() {
        return UdiConstant.OPS_STATUS_ON.equals(mUdi.getOpsStatus());
    }

    private final static int SOURCE_OPS = 14;
    private static final int DEFAULT_CHANGE_TO_OPS_TIMEOUT = 10 * 1000; //10s
    private Timer mChangeToOpsTimer;
    private TimerTask mChangeToOpsTimerTask = null;
    private int mChangeToOpsTimeout = DEFAULT_CHANGE_TO_OPS_TIMEOUT;
    private int mChangeToOpsTimeoutCount = 0;
    private static final int PERIOD = 1000;

    private void startChangeToOpsTimer(final boolean reboot, final String reason, int shutdownFlag) {
        mChangeToOpsTimeout = SystemProperties.getInt("persist.timeout.ops.change", DEFAULT_CHANGE_TO_OPS_TIMEOUT);
        if (mChangeToOpsTimer == null) {
            mChangeToOpsTimer = new Timer();
        }
        if (mChangeToOpsTimerTask == null) {
            mChangeToOpsTimerTask = new TimerTask() {
                @Override
                public void run() {
                    mChangeToOpsTimeoutCount = mChangeToOpsTimeoutCount + PERIOD;
                    if (mChangeToOpsTimeoutCount >= mChangeToOpsTimeout) {
                        stopChangeToOpsTimer();
                        shutdownOps(reboot, reason, shutdownFlag, true);
                    } else if (isInOpsSource()) {
                        stopChangeToOpsTimer();
                        shutdownOps(reboot, reason, shutdownFlag, true);
                    }
                }
            };
        }
        if (mChangeToOpsTimer != null) {
            Log.d(TAG, "startChangeToOpsTimer ");
            mChangeToOpsTimeoutCount = 0;
            mChangeToOpsTimer.schedule(mChangeToOpsTimerTask, 4000, PERIOD);
        }
    }

    private void stopChangeToOpsTimer() {
        if (mChangeToOpsTimer != null) {
            mChangeToOpsTimer.cancel();
            mChangeToOpsTimer = null;
        }
        if (mChangeToOpsTimerTask != null) {
            mChangeToOpsTimerTask.cancel();
            mChangeToOpsTimerTask = null;
        }
        mChangeToOpsTimeoutCount = 0;
    }

    private boolean isInOpsSource() {
        return UdiConstant.SOURCE_PC.equals(mUdi.getCurrentSource());
    }

    private void changeToOps(boolean reboot, String reason, int shutdownFlag) {
        Log.d(TAG, "changeToOps:reboot=" + reboot + ",reason=" + reason + ",shutdownFlag=" + shutdownFlag);
        changeToSource(mContext, SOURCE_OPS);
        stopChangeToOpsTimer();
        startChangeToOpsTimer(reboot, reason, shutdownFlag);
    }

    private void changeToSource(Context context, int sourceId) {
        mUdi.changeSource(UdiConstant.SOURCE_PC);
    }

    /**
     * @param fromUser 是否需要主动关闭ops
     *                 ops无信号关机时，不再重复执行关闭ops，避免导致ops再次重启
     */
    private void shutdownOps(boolean reboot, String reason, int shutdownFlag, boolean fromUser) {
        Log.d(TAG, "shutdownOps:reboot=" + reboot + ",reason=" + reason
                + ",shutdownFlag=" + shutdownFlag + ",fromUser=" + fromUser);
        if (shutdownFlag == FLAG_SHUTDOWN_HARD) {
            shutdownOrRebootAndroid(reboot, reason);
        } else {
            //ops关机中提示弹窗
            mHandler.sendEmptyMessage(MSG_SHOW_SHUTTING_DOWN_OPS_DIALOG);
            if (fromUser) {
                if (isOpsOn()) {
                    stopShutdownOpsTimer();
                    startShutdownOpsTimer(reboot, reason, shutdownFlag);
                }
                mUdi.shutdownOps();
            } else {
                if (isOpsOn()) {
                    stopShutdownOpsTimer();
                    //ops无信号关机：15s未监听到ops关机完毕，直接关android
                    startShutdownOpsTimer(reboot, reason, FLAG_SHUTDOWN_SOFT);
                }
            }
            mUdi.registerOpsShutdownListener(new OpsShutdownListener() {
                @Override
                public void onOpsShutdown() {
                    stopShutdownOpsTimer();
                    shutdownOrRebootAndroid(reboot, reason);
                }
            });
        }
    }

    private static final int DEFAULT_OPS_SHUTDOWN_TIMEOUT = 15 * 1000; //15s
    private static final int DEFAULT_OPS_SHUTDOWN_WEAK_TIMEOUT = 1 * 60 * 1000; //1min
    private Timer mShutdownOpsTimer;
    private TimerTask mShutdownOpsTimerTask = null;
    private int mShutdownOpsTimeout = DEFAULT_OPS_SHUTDOWN_TIMEOUT;
    private int mShutdownOpsTimeoutCount = 0;

    private void startShutdownOpsTimer(boolean reboot, String reason, int shutDownFlag) {
        if (shutDownFlag == FLAG_SHUTDOWN_WEAK) {
            mShutdownOpsTimeout = SystemProperties.getInt("persist.timeout.ops.shutdown", DEFAULT_OPS_SHUTDOWN_WEAK_TIMEOUT);
        } else {
            mShutdownOpsTimeout = DEFAULT_OPS_SHUTDOWN_TIMEOUT;
        }
        if (mShutdownOpsTimer == null) {
            mShutdownOpsTimer = new Timer();
        }
        if (mShutdownOpsTimerTask == null) {
            mShutdownOpsTimerTask = new TimerTask() {
                @Override
                public void run() {
                    mShutdownOpsTimeoutCount = mShutdownOpsTimeoutCount + PERIOD;
                    if (mShutdownOpsTimeoutCount >= mShutdownOpsTimeout) {
                        if (shutDownFlag == FLAG_SHUTDOWN_WEAK) {
                            Log.e(TAG, "weak shut down ops timeout.");
                            stopShutdownOpsTimer();
                            if (reboot && !mIsForceReboot) {
                                mIsShuttingDown = false;
                                //重启超时不做处理
                                dismissShuttingDownOpsView();
                            } else {
                                //超时后弹窗倒计时再强制关机
                                mHandler.removeMessages(MSG_SHOW_COUNT_DOWN_DIALOG);
                                mHandler.sendEmptyMessage(MSG_SHOW_COUNT_DOWN_DIALOG);
                            }
                        } else if (shutDownFlag == FLAG_SHUTDOWN_SOFT) {
                            Log.e(TAG, "soft shut down ops timeout.");
                            if (reboot && !mIsForceReboot) {
                                mIsShuttingDown = false;
                                //重启超时不做处理
                                dismissShuttingDownOpsView();
                            } else {
                                //15s超时后直接强制关机
                                stopShutdownOpsTimer();
                                shutdownOrRebootAndroid(reboot, reason);
                            }
                        } else {
                            Log.e(TAG, "hard shut down ops timeout,force off ops.");
                            stopShutdownOpsTimer();
                            shutdownOrRebootAndroid(reboot, reason);
                        }
                    } else {
                        if (!isOpsOn()) {
                            shutdownOrRebootAndroid(reboot, reason);
                            stopShutdownOpsTimer();
                        }
                    }
                }
            };
        }
        if (mShutdownOpsTimer != null) {
            Log.d(TAG, "startShutdownOpsTimer ");
            mShutdownOpsTimeoutCount = 0;
            mShutdownOpsTimer.schedule(mShutdownOpsTimerTask, 1000, PERIOD);
        }
    }

    private void stopShutdownOpsTimer() {
        if (mShutdownOpsTimer != null) {
            mShutdownOpsTimer.cancel();
            mShutdownOpsTimer = null;
        }
        if (mShutdownOpsTimerTask != null) {
            mShutdownOpsTimerTask.cancel();
            mShutdownOpsTimerTask = null;
        }
        mShutdownOpsTimeoutCount = 0;
    }

    public void setForceReboot() {
        mIsForceReboot = true;
    }

    /**
     * 确保在主线程show
     */
    public void showRestartDialog() {
        mHandler.sendEmptyMessage(MSG_SHOW_RESTART_DIALOG);
    }

    private void showRestartDialogInternal() {
        if (mRestartDialog == null) {
            mRestartDialog = new CommonDialog.Builder(mContext)
                    .view(R.layout.dialog_restart)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(false) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            dismissRestartDialog();
                        }
                    })
                    .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            restart();
                        }
                    })
                    .build();
            mRestartDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT); //加了这个才能在无activity时显示
            mRestartDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    CommonUtils.setIsDialog(mContext, false);
                    CommonUtils.enableOsd(mContext, false);
                    mHandler.removeMessages(MSG_RESTART_COUNT_DOWN);
                }
            });
        }
        mRestartDialog.show();
        mRestartConfirmBtn = mRestartDialog.findViewById(R.id.btn_confirm);
        mRestartConfirmBtn.setAllCaps(false);   //避免切换英文后，第一次用串口弹窗时出现大写
        CommonUtils.setIsDialog(mContext, true);
        CommonUtils.enableOsd(mContext, true);
        mRestartCountDown = 10;
        mHandler.removeMessages(MSG_RESTART_COUNT_DOWN);
        mHandler.sendEmptyMessage(MSG_RESTART_COUNT_DOWN);

        Window window = mRestartDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            layoutParams.x = CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_MARGIN_END);
            layoutParams.y = CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_MARGIN_BOTTOM);
            window.setAttributes(layoutParams);
        }
    }

    public void dismissRestartDialog() {
        if (mRestartDialog != null && mRestartDialog.isShowing()) {
            mRestartDialog.dismiss();
            mRestartDialog = null;
        }
    }

    /**
     * 确保在主线程show
     */
    public void showShutdownDialog() {
        mHandler.sendEmptyMessage(MSG_SHOW_SHUTDOWN_DIALOG);
    }

    private void showShutdownDialogInternal() {
        if (mShutdownDialog == null) {
            mShutdownDialog = new CommonDialog.Builder(mContext)
                    .view(R.layout.dialog_shutdown)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(false) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            dismissShutdownDialog();
                        }
                    })
                    .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            shutdown();
                        }
                    })
                    .build();
            mShutdownDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT); //加了这个才能在无activity时显示
            mShutdownDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    CommonUtils.setIsDialog(mContext, false);
                    CommonUtils.enableOsd(mContext, false);
                    mHandler.removeMessages(MSG_SHUTDOWN_COUNT_DOWN);
                }
            });
        }
        mShutdownDialog.show();
        mShutdownConfirmBtn = mShutdownDialog.findViewById(R.id.btn_confirm);
        mShutdownConfirmBtn.setAllCaps(false);  //避免切换英文后，第一次用串口弹窗时出现大写
        CommonUtils.setIsDialog(mContext, true);
        CommonUtils.enableOsd(mContext, true);
        mShutdownCountDown = 10;
        mHandler.removeMessages(MSG_SHUTDOWN_COUNT_DOWN);
        mHandler.sendEmptyMessage(MSG_SHUTDOWN_COUNT_DOWN);

        Window window = mShutdownDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            layoutParams.x = CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_MARGIN_END);
            layoutParams.y = CommonUtils.dp2px(mContext, Constant.WINDOW_HOST_MARGIN_BOTTOM);
            window.setAttributes(layoutParams);
        }
    }

    public void dismissShutdownDialog() {
        if (mShutdownDialog != null && mShutdownDialog.isShowing()) {
            mShutdownDialog.dismiss();
            mShutdownDialog = null;
        }
    }

    public void restart() {
        mHandler.removeMessages(MSG_RESTART_COUNT_DOWN);
        dismissRestartDialog();
        CommonUtils.sendExitBroadcast(mContext);
        shutdownOrReboot(true, "");
    }

    private void shutdown() {
        mHandler.removeMessages(MSG_SHUTDOWN_COUNT_DOWN);
        dismissShutdownDialog();
        CommonUtils.sendExitBroadcast(mContext);
        shutdownOrReboot(false, "");
    }

    /**
     * 内置电脑关机中提示
     */
    private View mShuttingDownOpsView;
    private WindowManager.LayoutParams mShuttingDownOpsLayoutParams;
    private boolean mHasShuttingDownOpsViewShown;
    private WindowManager mWindowManager;
    private AnimationDrawable mShuttingDownOpsAnimation;

    private void showShuttingDownOpsView() {
        Log.d(TAG, "showShuttingDownOpsView: ");
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        }
        if (mShuttingDownOpsView == null) {
            mShuttingDownOpsView = LayoutInflater.from(mContext).inflate(R.layout.toast_shutting_down_ops, null);
            mShuttingDownOpsLayoutParams = new WindowManager.LayoutParams();
            mShuttingDownOpsLayoutParams.format = PixelFormat.RGBA_8888;
            mShuttingDownOpsLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            mShuttingDownOpsLayoutParams.gravity = Gravity.CENTER;
            mShuttingDownOpsLayoutParams.width = mContext.getResources().getDimensionPixelSize(R.dimen.toast_shutting_down_ops_width);
            mShuttingDownOpsLayoutParams.height = mContext.getResources().getDimensionPixelSize(R.dimen.toast_shutting_down_ops_height);
            mShuttingDownOpsLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE;
            ImageView shuttingDownOpsIv = mShuttingDownOpsView.findViewById(R.id.iv_shutting_down_ops);
            mShuttingDownOpsAnimation = (AnimationDrawable) shuttingDownOpsIv.getBackground();
        }
        mShuttingDownOpsAnimation.start();
        if (mHasShuttingDownOpsViewShown) {
            mWindowManager.updateViewLayout(mShuttingDownOpsView, mShuttingDownOpsLayoutParams);
        } else {
            mWindowManager.addView(mShuttingDownOpsView, mShuttingDownOpsLayoutParams);
        }
        mHasShuttingDownOpsViewShown = true;
        mHandler.sendEmptyMessageDelayed(MSG_DISMISS_SHUTTING_DOWN_OPS_DIALOG, 120000);
    }

    public void dismissShuttingDownOpsView() {
        if (mWindowManager != null && mShuttingDownOpsView != null && mShuttingDownOpsView.isAttachedToWindow()) {
            Log.d(TAG, "dismissShuttingDownOpsView");
            if (mShuttingDownOpsAnimation != null) {
                mShuttingDownOpsAnimation.stop();
            }
            mWindowManager.removeView(mShuttingDownOpsView);
        }
        mHasShuttingDownOpsViewShown = false;
        mHandler.removeMessages(MSG_DISMISS_SHUTTING_DOWN_OPS_DIALOG);
    }
}
