2025-07-28 11:45:11.805   891-914   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@b651ab0 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-28 11:45:11.825   891-914   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@b651ab0 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-07-28 11:45:11.826   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-07-28 11:45:11.826   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-07-28 11:45:11.827   891-891   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-28 11:45:11.827   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-28 11:45:11.830   891-1425  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-07-28 11:45:16.463   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-07-28 11:45:36.530   891-891   ArrayMicOTA             com.eeo.systemsetting                E  Update failed: USB device detection timed out
2025-07-28 11:45:36.531   891-891   ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: USB device detection timed out
2025-07-28 11:45:36.531   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-28 11:45:36.531   891-891   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-28 11:45:36.531   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-07-28 11:45:36.533   891-2447  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-07-28 11:45:41.870   891-891   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-28 11:45:41.870   891-891   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-07-28 11:45:41.870   891-891   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-28 11:45:41.892   891-891   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@c92542a
2025-07-28 11:45:41.893   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-28 11:45:41.893   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-28 11:45:41.893   891-891   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.