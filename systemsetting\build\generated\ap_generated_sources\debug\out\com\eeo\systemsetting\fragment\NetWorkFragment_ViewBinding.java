// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.CharSequence;
import java.lang.IllegalStateException;
import java.lang.Override;

public class NetWorkFragment_ViewBinding implements Unbinder {
  private NetWorkFragment target;

  private View view7f0801c3;

  private View view7f0800a1;

  private View view7f080074;

  private TextWatcher view7f080074TextWatcher;

  private View view7f080075;

  private TextWatcher view7f080075TextWatcher;

  private View view7f080073;

  private TextWatcher view7f080073TextWatcher;

  private View view7f080070;

  private TextWatcher view7f080070TextWatcher;

  private View view7f080071;

  private TextWatcher view7f080071TextWatcher;

  private View view7f080044;

  private View view7f080045;

  @UiThread
  public NetWorkFragment_ViewBinding(final NetWorkFragment target, View source) {
    this.target = target;

    View view;
    target.scrollview = Utils.findRequiredViewAsType(source, R.id.scrollview, "field 'scrollview'", ScrollView.class);
    target.swNetwork = Utils.findRequiredViewAsType(source, R.id.sw_network, "field 'swNetwork'", Switch.class);
    target.txtMacAddress = Utils.findRequiredViewAsType(source, R.id.txt_mac_address, "field 'txtMacAddress'", TextView.class);
    view = Utils.findRequiredView(source, R.id.txt_ip_setting, "field 'txtIpSetting' and method 'onClick'");
    target.txtIpSetting = Utils.castView(view, R.id.txt_ip_setting, "field 'txtIpSetting'", TextView.class);
    view7f0801c3 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.img_arrow, "field 'imgArrow' and method 'onClick'");
    target.imgArrow = Utils.castView(view, R.id.img_arrow, "field 'imgArrow'", ImageView.class);
    view7f0800a1 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.llMacAddress = Utils.findRequiredViewAsType(source, R.id.ll_mac_address, "field 'llMacAddress'", LinearLayout.class);
    target.llIpMode = Utils.findRequiredViewAsType(source, R.id.ll_ip_mode, "field 'llIpMode'", LinearLayout.class);
    target.llIp = Utils.findRequiredViewAsType(source, R.id.ll_ip, "field 'llIp'", LinearLayout.class);
    target.imgIp = Utils.findRequiredViewAsType(source, R.id.img_ip, "field 'imgIp'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_ip_address, "field 'edtIpAddress', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtIpAddress = Utils.castView(view, R.id.edt_ip_address, "field 'edtIpAddress'", EditText.class);
    view7f080074 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080074TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080074TextWatcher);
    target.llMask = Utils.findRequiredViewAsType(source, R.id.ll_mask, "field 'llMask'", LinearLayout.class);
    target.imgMask = Utils.findRequiredViewAsType(source, R.id.img_mask, "field 'imgMask'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_mask_address, "field 'edtMaskAddress', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtMaskAddress = Utils.castView(view, R.id.edt_mask_address, "field 'edtMaskAddress'", EditText.class);
    view7f080075 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080075TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080075TextWatcher);
    target.llGateway = Utils.findRequiredViewAsType(source, R.id.ll_gateway, "field 'llGateway'", LinearLayout.class);
    target.imgGateway = Utils.findRequiredViewAsType(source, R.id.img_gateway, "field 'imgGateway'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_gateway, "field 'edtGateway', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtGateway = Utils.castView(view, R.id.edt_gateway, "field 'edtGateway'", EditText.class);
    view7f080073 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080073TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080073TextWatcher);
    target.llDns1 = Utils.findRequiredViewAsType(source, R.id.ll_dns1, "field 'llDns1'", LinearLayout.class);
    target.imgDns1 = Utils.findRequiredViewAsType(source, R.id.img_dns1, "field 'imgDns1'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_dns1, "field 'edtDns1', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtDns1 = Utils.castView(view, R.id.edt_dns1, "field 'edtDns1'", EditText.class);
    view7f080070 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080070TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080070TextWatcher);
    target.llDns2 = Utils.findRequiredViewAsType(source, R.id.ll_dns2, "field 'llDns2'", LinearLayout.class);
    target.imgDns2 = Utils.findRequiredViewAsType(source, R.id.img_dns2, "field 'imgDns2'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_dns2, "field 'edtDns2', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtDns2 = Utils.castView(view, R.id.edt_dns2, "field 'edtDns2'", EditText.class);
    view7f080071 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080071TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080071TextWatcher);
    target.llBtn = Utils.findRequiredViewAsType(source, R.id.ll_btn, "field 'llBtn'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.btn_cancel, "field 'btnCancel' and method 'onClick'");
    target.btnCancel = Utils.castView(view, R.id.btn_cancel, "field 'btnCancel'", Button.class);
    view7f080044 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.btn_confirm, "field 'btnConfirm' and method 'onClick'");
    target.btnConfirm = Utils.castView(view, R.id.btn_confirm, "field 'btnConfirm'", Button.class);
    view7f080045 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    NetWorkFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.scrollview = null;
    target.swNetwork = null;
    target.txtMacAddress = null;
    target.txtIpSetting = null;
    target.imgArrow = null;
    target.llMacAddress = null;
    target.llIpMode = null;
    target.llIp = null;
    target.imgIp = null;
    target.edtIpAddress = null;
    target.llMask = null;
    target.imgMask = null;
    target.edtMaskAddress = null;
    target.llGateway = null;
    target.imgGateway = null;
    target.edtGateway = null;
    target.llDns1 = null;
    target.imgDns1 = null;
    target.edtDns1 = null;
    target.llDns2 = null;
    target.imgDns2 = null;
    target.edtDns2 = null;
    target.llBtn = null;
    target.btnCancel = null;
    target.btnConfirm = null;

    view7f0801c3.setOnClickListener(null);
    view7f0801c3 = null;
    view7f0800a1.setOnClickListener(null);
    view7f0800a1 = null;
    view7f080074.setOnFocusChangeListener(null);
    ((TextView) view7f080074).removeTextChangedListener(view7f080074TextWatcher);
    view7f080074TextWatcher = null;
    view7f080074 = null;
    view7f080075.setOnFocusChangeListener(null);
    ((TextView) view7f080075).removeTextChangedListener(view7f080075TextWatcher);
    view7f080075TextWatcher = null;
    view7f080075 = null;
    view7f080073.setOnFocusChangeListener(null);
    ((TextView) view7f080073).removeTextChangedListener(view7f080073TextWatcher);
    view7f080073TextWatcher = null;
    view7f080073 = null;
    view7f080070.setOnFocusChangeListener(null);
    ((TextView) view7f080070).removeTextChangedListener(view7f080070TextWatcher);
    view7f080070TextWatcher = null;
    view7f080070 = null;
    view7f080071.setOnFocusChangeListener(null);
    ((TextView) view7f080071).removeTextChangedListener(view7f080071TextWatcher);
    view7f080071TextWatcher = null;
    view7f080071 = null;
    view7f080044.setOnClickListener(null);
    view7f080044 = null;
    view7f080045.setOnClickListener(null);
    view7f080045 = null;
  }
}
