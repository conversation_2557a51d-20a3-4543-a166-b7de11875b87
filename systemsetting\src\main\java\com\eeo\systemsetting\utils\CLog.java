package com.eeo.systemsetting.utils;

import android.os.SystemProperties;
import android.util.Log;

public class CLog {
    private static final int DEBUG = 2;
    private static String DEF_STR_CDEBUG = SystemProperties.get("persist.def.cdebug", "cdebug");
    private static final int ERROR = 0;
    private static final int INFO = 3;
    private static int LEVEL = 0;
    private static final String TAG = "tvsetting====";
    private static final int WARN = 1;

    static {
        LEVEL = SystemProperties.getInt("persist.tvapi.log.level", DEF_STR_CDEBUG.contains(TAG) ? 3 : 1);
    }

    private static String log(String msg) {
        StackTraceElement line = new Throwable().getStackTrace()[2];
        if (line != null) {
            return "[" + line.getFileName() + "::" + line.getMethodName() + ":" + line.getLineNumber() + "," + "]: " + msg;
        }
        return msg;
    }

    public static void i(String msg) {
        if (LEVEL >= 3) {
            Log.i(TAG, log(msg));
        }
    }

    public static void d(String msg) {
        if (LEVEL >= 2) {
            Log.d(TAG, log(msg));
        }
    }

    public static void w(String msg) {
        if (LEVEL >= 1) {
            Log.w(TAG, log(msg));
        }
    }

    public static void e(String msg) {
        if (LEVEL >= 0) {
            Log.e(TAG, log(msg));
        }
    }

    public static void e(Exception e) {
        if (LEVEL >= 0) {
            Log.e(TAG, log(""), e);
        }
    }

    public static void at(String msg) {
        if (LEVEL >= 3) {
            Log.i(TAG, "[AT]" + log(msg));
        }
    }

    public static void tif(String msg) {
        if (LEVEL >= 3) {
            Log.i(TAG, "[TIF]" + log(msg));
        }
    }
}