t982_ar301:/ # logcat | grep -E "(Udi|UDI|udi|Rs232|ttyS2|OpsCommManager)"
06-25 18:01:54.790   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:01:56.795   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:01:57.651   340  2537 I audio_hw_primary: alsa format =0x1 delay frames =2224 total frames=56832000
06-25 18:01:58.800   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:00.805   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:02.809   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:04.813   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:06.816   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:08.318   340  2537 I audio_hw_primary: alsa format =0x1 delay frames =2208 total frames=57344000
06-25 18:02:08.822   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:10.827   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:12.834   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:14.838   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''
06-25 18:02:16.843   392   392 I hash_map_utils: key: 'HDMIIN Audio Type' value: ''


130|t982_ar301:/ # ls -la /dev/ttyS*
crw------- 1 root   root   236,   0 2025-06-25 09:43 /dev/ttyS0
crw-rw-rw- 1 system system 236,   1 2025-06-25 09:43 /dev/ttyS1
crw-rw-rw- 1 system system 236,   2 2025-06-25 09:43 /dev/ttyS2
crw-rw-rw- 1 system system 236,   3 2025-06-25 17:42 /dev/ttyS3


C:\Users\<USER>