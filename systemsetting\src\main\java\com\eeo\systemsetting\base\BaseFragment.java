package com.eeo.systemsetting.base;

import android.app.Fragment;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import butterknife.ButterKnife;
import butterknife.Unbinder;

public abstract class BaseFragment extends Fragment {

    View view = null;
    private Unbinder unbinder;

    public BaseFragment(){

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (view == null){
            view = LayoutInflater.from(getContext()).inflate(getLayout(),container,false);
            unbinder = ButterKnife.bind(this, view);
            initDate();
        }
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();

    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
//        try {
//            unbinder.unbind();
//        } catch (IllegalStateException e) {
//            e.printStackTrace();
//        }
    }

    public abstract int getLayout();

    public abstract void initDate();
}
