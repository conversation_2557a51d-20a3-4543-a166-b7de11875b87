<?xml version="1.0" encoding="utf-8"?>
<com.zyp.cardview.YcCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Main_YcCardView"
    android:layout_width="@dimen/dialog_volume_width"
    android:layout_height="@dimen/dialog_volume_height">

    <ImageView
        android:id="@+id/iv_volume"
        android:layout_width="@dimen/iv_bright_width"
        android:layout_height="@dimen/iv_bright_height"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dialog_volume_iv_margin_start"
        android:background="@drawable/ic_voice_03" />

    <com.eeo.systemsetting.view.CustomerSeekBar
        android:id="@+id/sb_volume"
        style="@style/SeekBar_Light"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dialog_volume_sb_margin_start"
        android:progress="60" />

    <TextView
        android:id="@+id/tv_volume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:layout_marginEnd="@dimen/dialog_volume_iv_margin_start"
        android:text="100"
        android:textColor="@color/black_100"
        android:textSize="@dimen/dialog_volume_tv_text_size" />

</com.zyp.cardview.YcCardView>