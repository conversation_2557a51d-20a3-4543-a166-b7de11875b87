package com.eeo.ota.update;

import android.content.Context;

import com.eeo.ota.bean.Constant;
import com.eeo.ota.callback.InstallListener;

/**
 * 固件升级
 */
public class FirmwareUpdate {
    private final static String TAG = "FirmwareUpdate";

    /**
     * @param absolutePayloadPath 升级包路径
     */
    public static void updateSystem(Context context, final String absolutePayloadPath, InstallListener installListener) {
        if (Constant.IS_CXD11) {
            FirmwareUpdateHi.updateSystem(context, absolutePayloadPath, installListener);
        } else if (Constant.IS_982) {
            //982方案
            FirmwareUpdateT982.updateSystem(context, absolutePayloadPath, installListener);
        } else {
            FirmwareUpdateRK.updateSystem(context, absolutePayloadPath, installListener);
        }
    }

}
