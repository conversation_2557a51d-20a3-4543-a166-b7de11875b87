package com.eeo.annotation.bean;

import android.os.Build;

public class Constant {

    public static final boolean IS_BS110A = Build.MODEL.contains("BS110A");
    public static final boolean IS_BS86A = Build.MODEL.contains("BS86A");
    public static final boolean IS_BS75A = Build.MODEL.contains("BS75A");
    public static final boolean IS_BS65A = Build.MODEL.contains("BS65A");
    public static final boolean IS_BSP110A = Build.MODEL.contains("BSP110A");
    public static final boolean IS_BSP86A = Build.MODEL.contains("BSP86A");
    public static final boolean IS_BSP75A = Build.MODEL.contains("BSP75A");

    /**
     * 是否使用截屏/透明
     */
    public static final boolean CAPTURE_ENABLE = false;

    /**
     * 是否使用udi截屏
     * 双系统(非纯android）截屏
     */
    public static final boolean UDI_ENABLE = true;

    /**
     * 是否截屏
     */
    public static final String CAPTURE = "capture";

    /**
     * 是否使用书写加速
     */
    public static final boolean ACCELERATE_ENABLE = true;

    /**
     * BM110A屏幕尺寸需要乘以2
     * 读到的是1920x1080,实际是3840x2160
     */
    public final static int SCALE = Build.MODEL.contains("BM110A") ? 2 : 1;

    /**
     * 屏幕宽度
     */
    public static final String WIDTH = "width";
    public static final String HEIGHT = "height";
    public static final String DENSITY = "density";
    public static final String RESULT_CODE = "resultCode";

    /**
     * 笔盘相关
     */
    public static final String X = "x";
    public static final String Y = "y";
    public static final int DEFAULT_X = 2000;
    public static final int DEFAULT_Y = 1900;

    public static final int MENU_WIDTH = 635;
    public static final int MENU_HEIGHT = 140;

    /**
     * 清空View相关
     */
    public static final int CLEAR_ALL_MARGIN_LEFT = 255;
    public static final int CLEAR_ALL_MARGIN_BOTTOM = 32;
    public static final int CLEAR_ALL_WIDTH = 196;
    public static final int CLEAR_ALL_HEIGHT = 112;

    /**
     * 距离屏幕边缘的最小距离
     */
    public final static int MARGIN = 20;

    /**
     * 请求录屏返回的intent
     */
    public static final String DATA = "data";


    /**
     * 画笔大小
     * classIn画笔的是4px/6px/12px/24px
     * <p>
     * 65、75、86、110物理尺寸
     * 1428.48 (H)x803.52(V)  65（0.75）
     * <p>
     * 1650.24(H)x928.26(V)  75（0.87）
     * <p>
     * 1895.04(H)×1065.96(V)  86（1）
     * <p>
     * 2436.48(H) * 1370.52(V)  110（1.28）
     * <p>
     */
    public static final int SIZE_PEN = 7;  //实际像素为 SIZE_PEN / 0.755
    public static final int SIZE_PEN_BS65A = 9;  //12px
    public static final int SIZE_PEN_BS75A = 8;  //10px
    public static final int SIZE_PEN_BS86A = 7;  //9px
    public static final int SIZE_PEN_BS110A = 6; //8px
    public static final int BUTTON_ERASER_SIZE = 112;//按钮板擦的大小
    public static final int AREA_ERASER_SIZE = 250;//按钮板擦的大小
    /**
     * DrawView handler处理消息id
     */
    public static final int HANDLER_ACCELERATE_POINT = 1;
    public static final int HANDLER_CANVAS_CLEAR = 2;
    public static final int HANDLER_START_PAINT = 3;
    /**
     * 息屏写状态定义
     */
    public static final int ENTER_ANNOTATION_WRITE = 1;//进入批注写
    public static final int EXIT_ANNOTATION = 2;//退出批注
    public static final int ENTER_SLEEP_WAIT = 3;//进入息屏等待写
    public static final int ENTER_SLEEP_WRITE = 4;//进入息屏写
}
