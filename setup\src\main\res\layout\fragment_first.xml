<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/activate_bg"
    tools:context=".FirstFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <Button
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="596dp"
            android:layout_marginTop="432dp"
            android:id="@+id/activateBt"
            style="@style/MyButtonStyle"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:text="@string/activate"
            tools:ignore="MissingConstraints" />
        <Button
            android:id="@+id/transparentButton"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="372dp"
            android:layout_marginTop="273dp"
            android:layout_width="94dp"
            android:layout_height="31dp"
            android:alpha="0.0"
            tools:ignore="MissingConstraints" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.core.widget.NestedScrollView>