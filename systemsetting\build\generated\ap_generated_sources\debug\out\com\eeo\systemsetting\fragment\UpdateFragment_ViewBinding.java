// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.constraintlayout.widget.ConstraintLayout;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class UpdateFragment_ViewBinding implements Unbinder {
  private UpdateFragment target;

  private View view7f080044;

  private View view7f080048;

  private View view7f08004d;

  @UiThread
  public UpdateFragment_ViewBinding(final UpdateFragment target, View source) {
    this.target = target;

    View view;
    target.switchAutoUpdate = Utils.findRequiredViewAsType(source, R.id.sw_network, "field 'switchAutoUpdate'", Switch.class);
    target.txtVersionMsg = Utils.findRequiredViewAsType(source, R.id.txt_version_msg, "field 'txtVersionMsg'", TextView.class);
    target.llNotNetwork = Utils.findRequiredViewAsType(source, R.id.ll_not_network, "field 'llNotNetwork'", LinearLayout.class);
    target.llCheckUpdate = Utils.findRequiredViewAsType(source, R.id.ll_check_update, "field 'llCheckUpdate'", LinearLayout.class);
    target.clRightUpdate = Utils.findRequiredViewAsType(source, R.id.cl_right_update, "field 'clRightUpdate'", ConstraintLayout.class);
    target.txtRightUpdate = Utils.findRequiredViewAsType(source, R.id.txt_right_update, "field 'txtRightUpdate'", TextView.class);
    target.txtUpdateDescriptionTitle = Utils.findRequiredViewAsType(source, R.id.txt_title_update_description, "field 'txtUpdateDescriptionTitle'", TextView.class);
    target.txtUpdateDescription = Utils.findRequiredViewAsType(source, R.id.txt_update_description, "field 'txtUpdateDescription'", TextView.class);
    target.progressbarCheckUpdate = Utils.findRequiredViewAsType(source, R.id.progressbar_check_update, "field 'progressbarCheckUpdate'", ProgressBar.class);
    target.llDownload = Utils.findRequiredViewAsType(source, R.id.ll_download, "field 'llDownload'", LinearLayout.class);
    target.progressDownload = Utils.findRequiredViewAsType(source, R.id.progress_download, "field 'progressDownload'", ProgressBar.class);
    view = Utils.findRequiredView(source, R.id.btn_cancel, "field 'btnCancel' and method 'onClick'");
    target.btnCancel = Utils.castView(view, R.id.btn_cancel, "field 'btnCancel'", Button.class);
    view7f080044 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.llInstall = Utils.findRequiredViewAsType(source, R.id.ll_install, "field 'llInstall'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.btn_install, "field 'btnInstall' and method 'onClick'");
    target.btnInstall = Utils.castView(view, R.id.btn_install, "field 'btnInstall'", Button.class);
    view7f080048 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.txtNoNetwork = Utils.findRequiredViewAsType(source, R.id.txt_no_network, "field 'txtNoNetwork'", TextView.class);
    view = Utils.findRequiredView(source, R.id.btn_right_update, "method 'onClick'");
    view7f08004d = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    UpdateFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.switchAutoUpdate = null;
    target.txtVersionMsg = null;
    target.llNotNetwork = null;
    target.llCheckUpdate = null;
    target.clRightUpdate = null;
    target.txtRightUpdate = null;
    target.txtUpdateDescriptionTitle = null;
    target.txtUpdateDescription = null;
    target.progressbarCheckUpdate = null;
    target.llDownload = null;
    target.progressDownload = null;
    target.btnCancel = null;
    target.llInstall = null;
    target.btnInstall = null;
    target.txtNoNetwork = null;

    view7f080044.setOnClickListener(null);
    view7f080044 = null;
    view7f080048.setOnClickListener(null);
    view7f080048 = null;
    view7f08004d.setOnClickListener(null);
    view7f08004d = null;
  }
}
