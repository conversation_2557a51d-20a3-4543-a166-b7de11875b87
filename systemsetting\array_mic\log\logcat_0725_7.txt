2025-07-25 17:56:29.251  9906-9906  EeoApplication==        com.eeo.systemsetting                I  EeoApplication:onCreate - Sending broadcast to internal ArrayMicUpdateCheckReceiver.
2025-07-25 17:56:29.260  9906-9928  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 113 RECEIVER: 0 / ReceiverData{intent=Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver } packageName=com.eeo.systemsetting resultCode=-1 resultData=null resultExtras=null}
2025-07-25 17:56:29.587  9906-9906  ActivityThread          com.eeo.systemsetting                V  Performing receive of Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver }: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver}, dir=/data/app/~~p0cUGZD89QLn_IkG_3d7qA==/com.eeo.systemsetting-2gcL1SOwD_P3n079hsb1sw==/base.apk
2025-07-25 17:56:29.588  9906-9906  ArrayMicUp...ckReceiver com.eeo.systemsetting                I  Received action to check Array Microphone update.
2025-07-25 17:56:29.589  9906-9906  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver.onReceive:20 android.app.ActivityThread.handleReceiver:4030 
2025-07-25 17:56:29.593  9906-9926  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@4d7019c className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-25 17:56:29.595  9906-9926  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@4d7019c startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-07-25 17:56:29.759  9906-9906  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-07-25 17:56:29.761  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-07-25 17:56:29.769  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-07-25 17:56:29.769  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Checking for array mic update...
2025-07-25 17:56:29.772  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-07-25 17:56:29.792  9906-9906  ArrayMicOTA             com.eeo.systemsetting                E  Failed to get current version from device.
2025-07-25 17:56:29.792  9906-9906  ArrayMicOTA             com.eeo.systemsetting                I  No update needed for array microphone.
2025-07-25 17:56:30.298  9906-9906  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@2730b30
2025-07-25 17:56:30.298  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-25 17:56:30.298  9906-9906  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.