package com.eeo.systemsetting.projection;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Outline;
import android.media.AudioManager;
import android.media.tv.TvView;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.bean.AvailableSourceBean;
import com.eeo.systemsetting.screen.ScreenManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.view.CustomerSeekBar;
import com.eeo.udisdk.PcKeyboardCode;
import com.eeo.udisdk.Udi;
import com.eeo.udisdk.UdiConstant;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 画中画相关
 */
public class Projection implements View.OnClickListener {
    private static final String TAG = "Projection";
    private Context mContext;
    private AudioManager mAudioManager;
    private Udi mUdi;
    private boolean mIsPcSource;

    private int mSmallWindowWidth;
    private int mSmallWindowHeight;
    private int mShortcutViewWith;
    private int mShortcutViewHeight;
    private int mProjectionViewEdgeLeftMargin;
    private int mProjectionViewEdgeRightMargin;
    private int mProjectionViewEdgeTopMargin;
    private int mProjectionViewEdgeBottomMargin;
    private float mScale = 1;
    private int mRealLeft;
    private int mRealTop;
    private int mRealRight;
    private int mRealBottom;

    /**
     * 和TifPlayerActivity共用一个TvView
     */
    private RelativeLayout mProjectionLayout;
    private TvView mTvView;
    private TextView mHintTv;
    private RelativeLayout.LayoutParams mProjectionViewLayoutParams;

    /**
     * 父layout
     */
    private RelativeLayout mParentLayout;

    private View mProjectionBgView; //这个用来显示过渡动画

    /**
     * 快捷栏view
     * 包括：主页、投屏、批注、音量条
     */
    private View mShortcutView;
    private RelativeLayout.LayoutParams mShortcutLayoutParams;
    private RelativeLayout.LayoutParams mTvViewLayoutParams;

    private FrameLayout mDesktopLayout;
    private FrameLayout mMaximizeLayout;
    private FrameLayout mScreenLayout;
    private FrameLayout mAnnotateLayout;
    private TextView mDesktopTv;
    private TextView mMaximizeTv;
    private TextView mScreenTv;
    private TextView mAnnotateTv;
    private ImageView mVolumeIv;
    private CustomerSeekBar mVolumeSeekBar;
    private int mVolumeInitProgress;
    private boolean mIsVolumeSeekBarTracking;
    private long mVolumeSeekBarTrackingStopTime = 0;
    //SeekBar滑动时避免耗时卡顿
    private final ExecutorService mExecutorService = Executors.newSingleThreadExecutor();
    private int mVolumeProgress = -1;
    private long mVolumeLastSetTime = 0;
    //由于offset，用来辅助判断是否手动滑动
    private boolean mFromUser = false;

    /**
     * 快捷栏拖动
     */
    private int mTouchStartX;
    private int mTouchStartY;
    private int mTouchCurrentX;
    private int mTouchCurrentY;
    private int mMoveX;
    private int mMoveY;

    /**
     * 屏幕尺寸
     */
    private int mScreenWidth;
    private int mScreenHeight;
    private int mDensity;

    /**
     * 五指聚拢时小窗口的坐标
     */
    private int mX;
    private int mY;

    /**
     * 小窗口是否已显示
     */
    private boolean mHasShown;

    /**
     * 退出背景渐变时，是否已经开始重置小窗口
     */
    private boolean mHasDismissed;

    /**
     * 当前信号和可用信号列表
     * 由于快速显示有线投屏界面
     */
    private List<AvailableSourceBean.SourcesBean> mAvailableSourceList = null;

    private static final int MSG_SET_VOLUME = 0x001;
    private static final int MSG_UPDATE_VOLUME_PROGRESS = 0x002;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_SET_VOLUME:
                    setSystemVolume();
                    break;
                case MSG_UPDATE_VOLUME_PROGRESS:
                    updateVolumeProgress();
                    break;
                default:
                    break;
            }
        }
    };

    public Projection(Context context, int screenWidth, int screenHeight, RelativeLayout projectionLayout, TvView tvView, TextView hintTv, RelativeLayout parentLayout, List<AvailableSourceBean.SourcesBean> sources) {
        mContext = context;
        mScreenWidth = screenWidth;
        mScreenHeight = screenHeight;
        mProjectionLayout = projectionLayout;
        mTvView = tvView;
        mHintTv = hintTv;
        mParentLayout = parentLayout;
        mAvailableSourceList = sources;
        mProjectionBgView = mParentLayout.findViewById(R.id.bg_projection);
        mUdi = EeoApplication.udi;
        mProjectionViewLayoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        init();
    }

    /**
     * 配置不同尺寸机型的宽高
     */
    private void init() {
        if (Constant.IS_BS65A) {
            mScale = Constant.PROJECTION_SCALE_BS65A;
        } else if (Constant.IS_75) {
            mScale = Constant.PROJECTION_SCALE_BS75A;
        } else if (Constant.IS_110) {
            mScale = Constant.PROJECTION_SCALE_BS110A;
        }
        mSmallWindowWidth = ProjectionConstant.SMALL_WINDOW_WIDTH;
        mSmallWindowHeight = ProjectionConstant.SMALL_WINDOW_HEIGHT;
        mShortcutViewWith = ProjectionConstant.SHORTCUT_VIEW_WIDTH;
        mShortcutViewHeight = ProjectionConstant.SHORTCUT_VIEW_HEIGHT;
        mProjectionViewEdgeLeftMargin = (int) (ProjectionConstant.MARGIN + (mScale - 1) * mSmallWindowWidth);
        mProjectionViewEdgeRightMargin = ProjectionConstant.MARGIN;
        mProjectionViewEdgeTopMargin = (int) (ProjectionConstant.MARGIN + (mScale - 1) * (mSmallWindowHeight + mShortcutViewHeight + ProjectionConstant.SHORTCUT_VIEW_MARGIN_TOP));
        mProjectionViewEdgeBottomMargin = ProjectionConstant.MARGIN;
    }

    public boolean isShown() {
        return mHasShown;
    }

    public void show(int x, int y) {
        //画中画召唤过程中不允许触摸，待坐标偏移生效后才恢复触摸
        CommonUtils.enableOsd(mContext, true);
        //退出大屏控制界面
        CommonUtils.sendExitBroadcast(mContext);
        //退出投屏引导界面
        if (EeoApplication.isShowScreenDialog) {
            ScreenManager.getInstance(mContext).dismissScreenDialog();
        }
        mX = x;
        mY = y;
        mHasShown = true;
        showEnterAnimation();
    }

    @SuppressLint("CheckResult")
    public void show() {
        if (EeoApplication.isHalf) {
            mHasShown = true;
            dismiss();
            return;
        }
        //退出大屏控制界面
        CommonUtils.sendExitBroadcast(mContext);
        //退出投屏引导界面
        if (EeoApplication.isShowScreenDialog) {
            ScreenManager.getInstance(mContext).dismissScreenDialog();
        }
//        mIsPcSource = mUdi.getCurrentSource().equals(UdiConstant.SOURCE_PC);
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        setParentView();
        setProjectionView();
        addShortcutView();
        mHasShown = true;
        EeoApplication.isProjection = true;
        EeoApplication.isProjectionEntering = false;
        CommonUtils.setProjection(mContext, true);
        mProjectionLayout.bringToFront();
        mProjectionBgView.setVisibility(View.GONE);
    }

    /**
     * 进入动画
     * 背景图片渐入
     * 因TvView透明度变化过程，画面不显示，TvView就不渐变
     */
    private void showEnterAnimation() {
        EeoApplication.isProjectionEntering = true;
        mProjectionBgView.bringToFront();
        mProjectionBgView.setVisibility(View.VISIBLE);
        mProjectionBgView.setAlpha(0);
        ValueAnimator valueAnimator = ValueAnimator.ofInt(0, 100);
        valueAnimator.setDuration(350);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int progress = (int) valueAnimator.getAnimatedValue();
                mProjectionBgView.setAlpha(progress / 100.0f);
                if (progress >= 100) {
                    show();
                }
            }
        });
        valueAnimator.start();
    }

    public void showExitAnimation() {
        mHasDismissed = false;
        mProjectionBgView.bringToFront();
        mProjectionBgView.setVisibility(View.VISIBLE);
        mProjectionBgView.setAlpha(1.0f);
        ValueAnimator valueAnimator = ValueAnimator.ofInt(100, 0);
        valueAnimator.setDuration(500);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int progress = (int) valueAnimator.getAnimatedValue();
                mProjectionBgView.setAlpha(progress / 100.0f);
                if (progress == 0) {
                    mProjectionBgView.setVisibility(View.GONE);
                    mHasShown = false;
                }
                if (progress <= 90 && !mHasDismissed) {
                    dismissWithoutAnimation();
                }
            }
        });
        valueAnimator.start();
    }

    private void setParentView() {
        mParentLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mParentLayout.setBackground(mContext.getDrawable(R.drawable.bg_projection));
    }

    private void setProjectionView() {
        //tvView圆角
        mTvView.setClipToOutline(true);
        mTvView.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                //bottom+7dp:底部不裁圆角
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight() /*+ CommonUtils.dp2px(mContext, 7)*/, CommonUtils.dp2px(mContext, 8));
            }
        });
        /*if (mIsPcSource) {
            mHintTv.setVisibility(View.GONE);
        } else {
            mHintTv.setVisibility(View.VISIBLE);
        }*/
        mProjectionViewLayoutParams.width = CommonUtils.dp2px(mContext, mSmallWindowWidth);
        mProjectionViewLayoutParams.height = CommonUtils.dp2px(mContext, mSmallWindowHeight + mShortcutViewHeight + ProjectionConstant.SHORTCUT_VIEW_MARGIN_TOP);
        mProjectionViewLayoutParams.leftMargin = mX - CommonUtils.dp2px(mContext, mSmallWindowWidth / 2);
        //屏幕边缘margin
        if (mProjectionViewLayoutParams.leftMargin < CommonUtils.dp2px(mContext, mProjectionViewEdgeLeftMargin)) {
            mProjectionViewLayoutParams.leftMargin = CommonUtils.dp2px(mContext, mProjectionViewEdgeLeftMargin);
        } else if (mProjectionViewLayoutParams.leftMargin + mProjectionViewLayoutParams.width + CommonUtils.dp2px(mContext, mProjectionViewEdgeRightMargin) > mScreenWidth) {
            mProjectionViewLayoutParams.leftMargin = mScreenWidth - mProjectionViewLayoutParams.width - CommonUtils.dp2px(mContext, mProjectionViewEdgeRightMargin);
        }
        mProjectionViewLayoutParams.topMargin = mY - CommonUtils.dp2px(mContext, mSmallWindowHeight / 2);
        if (mProjectionViewLayoutParams.topMargin < CommonUtils.dp2px(mContext, mProjectionViewEdgeTopMargin)) {
            mProjectionViewLayoutParams.topMargin = CommonUtils.dp2px(mContext, mProjectionViewEdgeTopMargin);
        } else if (mProjectionViewLayoutParams.topMargin + mProjectionViewLayoutParams.height + CommonUtils.dp2px(mContext, mProjectionViewEdgeBottomMargin) > mScreenHeight) {
            mProjectionViewLayoutParams.topMargin = mScreenHeight - mProjectionViewLayoutParams.height - CommonUtils.dp2px(mContext, mProjectionViewEdgeBottomMargin);
        }
        mProjectionLayout.setLayoutParams(mProjectionViewLayoutParams);
        //不同机型适配不同的大小
        mProjectionLayout.setPivotX(mProjectionViewLayoutParams.width); //以右下角为基点伸缩
        mProjectionLayout.setPivotY(mProjectionViewLayoutParams.height);
        mProjectionLayout.setScaleX(mScale);
        mProjectionLayout.setScaleY(mScale);
        mRealLeft = (int) (mProjectionViewLayoutParams.leftMargin - (mScale - 1) * mProjectionViewLayoutParams.width);
        mRealTop = (int) (mProjectionViewLayoutParams.topMargin - (mScale - 1) * mProjectionViewLayoutParams.height);
        mRealRight = mRealLeft + (int) (mProjectionViewLayoutParams.width * mScale);
        mRealBottom = mRealTop + (int) (CommonUtils.dp2px(mContext, mSmallWindowHeight) * mScale);
        handleSmallWindowTouch(true);
    }

    private void addShortcutView() {
        if (mShortcutView == null) {
            initShortcutView();
        }
        updateVolumeProgress();
        //添加快捷栏到ProjectionLayout底部
        mShortcutLayoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        //让里面的子控件的比例同步，这里用setScaleX、Y来适配宽高
        mShortcutLayoutParams.width = CommonUtils.dp2px(mContext, mShortcutViewWith);
        mShortcutLayoutParams.height = CommonUtils.dp2px(mContext, mShortcutViewHeight);
        mShortcutLayoutParams.topMargin = CommonUtils.dp2px(mContext, ProjectionConstant.SHORTCUT_VIEW_MARGIN_TOP);
        mShortcutLayoutParams.addRule(RelativeLayout.BELOW, mTvView.getId());
        if (mProjectionLayout.indexOfChild(mShortcutView) == -1) {
            mProjectionLayout.addView(mShortcutView, mShortcutLayoutParams);
        } else {
            mProjectionLayout.updateViewLayout(mShortcutView, mShortcutLayoutParams);
        }
        //重新设置TvView的高度不占满父布局
        mTvViewLayoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        mTvViewLayoutParams.height = CommonUtils.dp2px(mContext, mSmallWindowHeight);
        mProjectionLayout.updateViewLayout(mTvView, mTvViewLayoutParams);
    }

    private void initShortcutView() {
        mShortcutView = LayoutInflater.from(mContext).inflate(R.layout.small_window_shortcut, null);
        mDesktopLayout = mShortcutView.findViewById(R.id.fl_desktop);
        mMaximizeLayout = mShortcutView.findViewById(R.id.fl_maximize);
        mScreenLayout = mShortcutView.findViewById(R.id.fl_screen);
        mAnnotateLayout = mShortcutView.findViewById(R.id.fl_annotate);
        mDesktopTv = mShortcutView.findViewById(R.id.tv_desktop);
        mMaximizeTv = mShortcutView.findViewById(R.id.tv_maximize);
        mScreenTv = mShortcutView.findViewById(R.id.tv_screen);
        mAnnotateTv = mShortcutView.findViewById(R.id.tv_annotate);
        mVolumeIv = mShortcutView.findViewById(R.id.iv_volume);
        mVolumeSeekBar = mShortcutView.findViewById(R.id.sb_volume);

        mDesktopLayout.setOnClickListener(this);
        mMaximizeLayout.setOnClickListener(this);
        mScreenLayout.setOnClickListener(this);
        mAnnotateLayout.setOnClickListener(this);
        mVolumeIv.setOnClickListener(this);
        mVolumeSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (progress < Constant.PROJECTION_SEEK_BAR_PROGRESS_OFFSET) {
                    if (fromUser) {
                        mFromUser = true;
                    }
                    seekBar.setProgress(Constant.PROJECTION_SEEK_BAR_PROGRESS_OFFSET);
                    return;
                }
                progress = progress - Constant.PROJECTION_SEEK_BAR_PROGRESS_OFFSET;
                if (mVolumeProgress != progress) {
                    //设置icon
                    if (progress == 0) {
                        mVolumeIv.setImageResource(R.drawable.volume_0);
                    } else if (progress < 34) {
                        mVolumeIv.setImageResource(R.drawable.volume_1);
                    } else if (progress < 67) {
                        mVolumeIv.setImageResource(R.drawable.volume_2);
                    } else {
                        mVolumeIv.setImageResource(R.drawable.volume_3);
                    }
                    mVolumeProgress = progress;
                    //set system volume
                    if (mFromUser || fromUser) {
                        setSystemVolume();
                    }
                }
                mFromUser = false;
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                mIsVolumeSeekBarTracking = true;
                mVolumeSeekBarTrackingStopTime = 0;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                mIsVolumeSeekBarTracking = false;
                mVolumeSeekBarTrackingStopTime = System.currentTimeMillis();
            }
        });

        mShortcutView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mTouchStartX = (int) event.getRawX();
                    mTouchStartY = (int) event.getRawY();
                    mMoveX = 0;
                    mMoveY = 0;
                } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                    mTouchCurrentX = (int) event.getRawX();
                    mTouchCurrentY = (int) event.getRawY();
                    mMoveX = mTouchCurrentX - mTouchStartX;
                    mMoveY = mTouchCurrentY - mTouchStartY;
                    mTouchStartX = mTouchCurrentX;
                    mTouchStartY = mTouchCurrentY;
                    mProjectionViewLayoutParams.leftMargin += mMoveX;
                    mProjectionViewLayoutParams.topMargin += mMoveY;
                    if (mProjectionViewLayoutParams.leftMargin < CommonUtils.dp2px(mContext, mProjectionViewEdgeLeftMargin)) {
                        //超出屏幕左边缘
                        mProjectionViewLayoutParams.leftMargin = CommonUtils.dp2px(mContext, mProjectionViewEdgeLeftMargin);
                    } else if (mProjectionViewLayoutParams.leftMargin + mProjectionViewLayoutParams.width + CommonUtils.dp2px(mContext, mProjectionViewEdgeRightMargin) > mScreenWidth) {
                        //超出屏幕右边缘
                        mProjectionViewLayoutParams.leftMargin = mScreenWidth - mProjectionViewLayoutParams.width - CommonUtils.dp2px(mContext, mProjectionViewEdgeRightMargin);
                    }
                    if (mProjectionViewLayoutParams.topMargin < CommonUtils.dp2px(mContext, mProjectionViewEdgeTopMargin)) {
                        mProjectionViewLayoutParams.topMargin = CommonUtils.dp2px(mContext, mProjectionViewEdgeTopMargin);
                    } else if (mProjectionViewLayoutParams.topMargin + mProjectionViewLayoutParams.height + CommonUtils.dp2px(mContext, mProjectionViewEdgeBottomMargin) > mScreenHeight) {
                        mProjectionViewLayoutParams.topMargin = mScreenHeight - mProjectionViewLayoutParams.height - CommonUtils.dp2px(mContext, mProjectionViewEdgeBottomMargin);
                    }
                    mRealLeft = (int) (mProjectionViewLayoutParams.leftMargin - (mScale - 1) * mProjectionViewLayoutParams.width);
                    mRealTop = (int) (mProjectionViewLayoutParams.topMargin - (mScale - 1) * mProjectionViewLayoutParams.height);
                    mRealRight = mRealLeft + (int) (mProjectionViewLayoutParams.width * mScale);
                    mRealBottom = mRealTop + (int) (CommonUtils.dp2px(mContext, mSmallWindowHeight) * mScale);
                    mProjectionLayout.setLayoutParams(mProjectionViewLayoutParams);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    handleSmallWindowTouch(true);
                }
                return true;
            }
        });
    }

    public void dismiss() {
        Log.d(TAG, "dismiss: mHasShown=" + mHasShown);
        if (mHasShown) {
            showExitAnimation();
        }
    }

    public void dismissWithoutAnimation() {
        Log.d(TAG, "dismissWithoutAnimation: mHasDismissed=" + mHasDismissed);
        if (!mHasDismissed) {
            mHasDismissed = true;
            resetParentView();
            resetProjectionView();
            if (mProjectionLayout != null && mProjectionLayout.indexOfChild(mShortcutView) != -1) {
                mProjectionLayout.removeView(mShortcutView);
            }
            EeoApplication.isProjection = false;
            EeoApplication.isProjectionEntering = false;
            CommonUtils.setProjection(mContext, false);
        }
    }

    private void resetParentView() {
        mParentLayout.setOnClickListener(null);
        mParentLayout.setBackground(null);
    }

    private void resetProjectionView() {
        Log.e(TAG, "resetProjectionView: ");
        mHintTv.setVisibility(View.GONE);
        //重新设置TvView的高度占满父布局mProjectionLayout
        mTvViewLayoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        mTvViewLayoutParams.height = RelativeLayout.LayoutParams.MATCH_PARENT;
        mProjectionLayout.updateViewLayout(mTvView, mTvViewLayoutParams);
        //重新设置mProjectionLayout的位置和宽高
        mProjectionViewLayoutParams.leftMargin = 0;
        mProjectionViewLayoutParams.topMargin = 0;
        mProjectionViewLayoutParams.width = mScreenWidth;
        mProjectionViewLayoutParams.height = mScreenHeight;
        mProjectionLayout.setLayoutParams(mProjectionViewLayoutParams);
        mProjectionLayout.setScaleX(1);
        mProjectionLayout.setScaleY(1);
        //不在半屏模式才恢复触摸偏移，不然会从画中画时启动半屏模式，会触摸偏移
        if (!CommonUtils.isHalfScreen(mContext)) {
            handleSmallWindowTouch(false);
        } else {
            //画中画召唤过程中不允许触摸，这里要恢复触摸
            if (EeoApplication.mIsFingerDown) {
                EeoApplication.mShouldSetTouchEnable = true;
            } else {
                CommonUtils.enableOsd(mContext, false);
            }
        }
    }

    public void handleSmallWindowTouch(boolean show) {
        if (show) {
            mUdi.setDisableSmallWindow(false);
        }
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                if (Constant.IS_USERDEBUG) {
                    Log.d(TAG, "handleSmallWindowTouch  show=" + show +
                            "  (" + mRealLeft + "," + mRealTop + "," + mRealRight + "," + mRealBottom + ")");
                }
//                if (mIsPcSource) {
                //
                mUdi.setSmallWindow(show, mRealLeft, mRealTop, mRealRight, mRealBottom, /*mUdi.getCurrentSource()*/UdiConstant.SOURCE_NONE, false);
                if (show) {
                    //画中画召唤过程中不允许触摸，待坐标偏移生效后才恢复触摸
                    if (EeoApplication.mIsFingerDown) {
                        EeoApplication.mShouldSetTouchEnable = true;
                    } else {
                        CommonUtils.enableOsd(mContext, false);
                    }
                }
//                } else {
//                    //画中画时禁掉外部信号源的触摸
//                    CommonUtils.enableOsd(mContext, show);
//                }
            }
        });
    }

    private void setSystemVolume() {
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - mVolumeLastSetTime < 200) {
            mHandler.removeMessages(MSG_SET_VOLUME);
            mHandler.sendEmptyMessageDelayed(MSG_SET_VOLUME, 200);
        } else {
            mVolumeLastSetTime = currentTimeMillis;
            mExecutorService.execute(new Runnable() {
                @Override
                public void run() {
                    if (mAudioManager == null) {
                        mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
                    }
                    mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, mVolumeProgress, AudioManager.FLAG_PLAY_SOUND);
                }
            });
        }
    }

    public void updateVolumeProgress() {
        if (mIsVolumeSeekBarTracking || System.currentTimeMillis() - mVolumeSeekBarTrackingStopTime < 500) {
            mHandler.removeMessages(MSG_UPDATE_VOLUME_PROGRESS);
            mHandler.sendEmptyMessageDelayed(MSG_UPDATE_VOLUME_PROGRESS, 500);
            return;
        }
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        mVolumeInitProgress = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (mVolumeSeekBar != null) {
            mVolumeSeekBar.setProgress(mVolumeInitProgress + Constant.PROJECTION_SEEK_BAR_PROGRESS_OFFSET);
        }
    }

    @Override
    public void onClick(View v) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.fl_desktop:
                EeoApplication.udi.sendPcKeyEvent(PcKeyboardCode.CONTROL_KEY_BOARD_WIN,
                        PcKeyboardCode.FUNCTION_KEY_BOARD_D, PcKeyboardCode.EVENT_KEY_BOARD_CLICK);
                dismiss();
                break;
            case R.id.fl_maximize:
                dismiss();
                break;
            case R.id.fl_screen:
                ScreenManager.getInstance(mContext).showScreenDialog(mAvailableSourceList);
                dismiss();
                break;
            case R.id.fl_annotate:
                int[] location = new int[2];
                v.getLocationOnScreen(location);
                CommonUtils.startAnnotation(mContext, 1, location[0], location[1]);
                dismiss();
                break;
            case R.id.iv_volume:
                //点击音量图标静音
                mFromUser = true;
                mVolumeSeekBar.setProgress(Constant.PROJECTION_SEEK_BAR_PROGRESS_OFFSET);
                break;
        }
    }

    public void onConfigurationChanged() {
        if (mScreenTv != null) {
            mDesktopTv.setText(mContext.getString(R.string.desktop));
            mMaximizeTv.setText(mContext.getString(R.string.maximize));
            mScreenTv.setText(mContext.getString(R.string.projection));
            mAnnotateTv.setText(mContext.getString(R.string.annotate));
        }
    }
}
