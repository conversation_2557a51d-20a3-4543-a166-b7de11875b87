{"logs": [{"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values-zh-rCN\\string.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,162,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,600,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8787,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,634,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8823,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,13,481,482,483,511,512,513,514,515,516,518,519,520,521,522,523,524,526,527,528,529,530,531,532,533,534,535,536,537,538,539,543,544,545,546,547,548,549,550,551,552,553,554,556,557,558,560,561,562,563,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,627,628,629,630,631,632,634,635,636,637,638,639,640,641,642,643,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,701,702,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,726,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,417,28895,28934,28973,31569,31608,31646,31689,31738,31778,31874,31910,31955,32003,32051,32101,32139,32238,32287,32350,32396,32447,32492,32541,32599,32652,32714,32782,32835,32875,32929,33135,33188,33246,33308,33370,33430,33504,33548,33587,33632,33675,33728,33824,33884,33938,34035,34072,34107,34153,34256,34300,34349,34395,34434,34488,34543,34604,34659,34719,34776,34831,34890,34948,35010,35067,35124,35177,35270,35306,35364,35400,35449,35497,35564,35623,35693,35740,35805,35849,35893,35929,35980,36029,36081,36118,36155,36199,36244,36290,36328,36369,36410,36478,36551,36611,36673,36756,36821,36884,36951,37021,37079,37125,37199,37278,37354,37412,37475,37537,37618,37735,37840,37945,37985,38073,38158,38318,38364,38404,38454,38496,38557,38599,38651,38707,38750,38855,38914,38959,38998,39060,39122,39172,39211,39248,39297,39360,39410,39474,39522,39584,39629,39675,39723,39759,39807,39874,39942,40012,40064,40136,40184,40240,40291,40340,40400,40463,40535,40578,40624,40683,40744,40810,40859,40991,41039,41085,41132,41181,41220,41260,41323,41380,41443,41542,41600,41650,41687,41739,41789,41940,41986,42151,42196,42244,42307,42355,42401,42464,42515,42575,42624,42663,42715,42761,42813,42861,42929,42998,43038,43082,43267,43553,43592,43641,43704,43752,43798,43842,43922,43989,44042,44115,44178,44219,44272,44326,44388,44439,44488,44551,44630,44688,44754,44808,44860,44902", "endLines": "5,12,18,481,482,483,511,512,513,514,515,516,518,519,520,521,522,523,524,526,527,528,529,530,531,532,533,534,535,536,537,538,539,543,544,545,546,547,548,549,550,551,552,553,554,556,557,558,560,561,562,563,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,627,628,629,630,631,632,634,635,636,637,638,639,640,641,642,643,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,701,702,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,726,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755", "endColumns": "19,19,19,38,38,38,38,37,42,48,39,50,35,44,47,47,49,37,48,48,62,45,50,44,48,57,52,61,67,52,39,53,38,52,57,61,61,59,73,43,38,44,42,52,48,59,53,43,36,34,45,46,43,48,45,38,53,54,60,54,59,56,54,58,57,61,56,56,52,92,35,57,35,48,47,66,58,69,46,64,43,43,35,50,48,51,36,36,43,44,45,37,40,40,67,72,59,61,82,64,62,66,69,57,45,73,78,75,57,62,61,80,72,104,104,39,87,84,102,45,39,49,41,60,41,51,55,42,46,58,44,38,61,61,49,38,36,48,62,49,63,47,61,44,45,47,35,47,66,67,69,51,71,47,55,50,48,59,62,71,42,45,58,60,65,48,52,47,45,46,48,38,39,62,56,62,98,57,49,36,51,49,49,45,49,44,47,62,47,45,62,50,59,48,38,51,45,51,47,67,68,39,43,52,68,38,48,62,47,45,43,79,66,52,72,62,40,52,53,61,50,48,62,78,57,65,53,51,41,57", "endOffsets": "211,412,604,28929,28968,29007,31603,31641,31684,31733,31773,31824,31905,31950,31998,32046,32096,32134,32183,32282,32345,32391,32442,32487,32536,32594,32647,32709,32777,32830,32870,32924,32963,33183,33241,33303,33365,33425,33499,33543,33582,33627,33670,33723,33772,33879,33933,33977,34067,34102,34148,34195,34295,34344,34390,34429,34483,34538,34599,34654,34714,34771,34826,34885,34943,35005,35062,35119,35172,35265,35301,35359,35395,35444,35492,35559,35618,35688,35735,35800,35844,35888,35924,35975,36024,36076,36113,36150,36194,36239,36285,36323,36364,36405,36473,36546,36606,36668,36751,36816,36879,36946,37016,37074,37120,37194,37273,37349,37407,37470,37532,37613,37686,37835,37940,37980,38068,38153,38256,38359,38399,38449,38491,38552,38594,38646,38702,38745,38792,38909,38954,38993,39055,39117,39167,39206,39243,39292,39355,39405,39469,39517,39579,39624,39670,39718,39754,39802,39869,39937,40007,40059,40131,40179,40235,40286,40335,40395,40458,40530,40573,40619,40678,40739,40805,40854,40907,41034,41080,41127,41176,41215,41255,41318,41375,41438,41537,41595,41645,41682,41734,41784,41834,41981,42031,42191,42239,42302,42350,42396,42459,42510,42570,42619,42658,42710,42756,42808,42856,42924,42993,43033,43077,43130,43331,43587,43636,43699,43747,43793,43837,43917,43984,44037,44110,44173,44214,44267,44321,44383,44434,44483,44546,44625,44683,44749,44803,44855,44897,44955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,683", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29012,29107,29202,29302,29384,29481,29587,29664,29739,29830,29923,30020,30116,30210,30303,30398,30490,30581,30672,30750,30846,30941,31036,31133,31229,31327,31475,40912", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "29102,29197,29297,29379,29476,29582,29659,29734,29825,29918,30015,30111,30205,30298,30393,30485,30576,30667,30745,30841,30936,31031,31128,31224,31322,31470,31564,40986"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\ota\\build\\intermediates\\packaged_res\\debug\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "517,525,540,541,542,555,559,564,626,633,644,703,704,724,725,727,728,729,730", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31829,32188,32968,33021,33077,33777,33982,34200,37691,38261,38797,42036,42092,43135,43182,43336,43386,43448,43496", "endColumns": "44,49,52,55,57,46,52,55,43,56,57,55,58,46,84,49,61,47,56", "endOffsets": "31869,32233,33016,33072,33130,33819,34030,34251,37730,38313,38850,42087,42146,43177,43262,43381,43443,43491,43548"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values-zh-rCN\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,418,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,53,54,56,-1,-1,-1,-1,58,57,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24000,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2762,2652,2708,2819,-1,-1,-1,-1,2925,2872,2980,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,54,52,51,-1,-1,-1,-1,53,51,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2646,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24072,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2813,2702,2756,2866,-1,-1,-1,-1,2974,2919,3030,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "609,655,708,759,808,853,896,951,998,1049,1102,1152,1198,1245,1295,1351,1405,1454,1509,1564,1615,1673,1723,1774,1828,1873,1922,1966,2017,2064,2106,2146,2220,2291,2358,2424,2494,2563,2638,2712,2780,2847,2905,2972,3037,3094,3170,3241,3300,3358,3417,3482,3547,3617,3685,3759,3826,3901,3977,4050,4120,4185,4251,4327,4391,4443,4504,4564,4620,4681,4739,4791,4849,4910,4971,5040,5116,5175,5239,5301,5361,5427,5487,5542,5601,5666,5729,5787,5863,5928,5990,6046,6106,6161,6223,6281,6354,6424,6494,6556,6622,6692,6765,6836,6905,6968,7041,7105,7163,7226,7288,7348,7406,7470,7531,7598,7658,7723,7790,7857,7915,7980,8037,8094,8161,8221,8290,8359,8429,8497,8577,8653,8716,8790,8860,8930,9001,9072,9147,9217,9291,9363,9439,9519,9599,9675,9744,9811,9872,9935,9997,10069,10138,10205,10273,10346,10413,10482,10551,10613,10675,10746,10801,10861,10919,10974,11030,11095,11159,11234,11294,11342,11405,11461,11521,11576,11632,11694,11761,11833,11904,11971,12033,12095,12161,12234,12295,12362,12425,12487,12554,12620,12687,12760,12833,12892,12959,13027,13092,13159,13224,13287,13347,13406,13462,13523,13588,13634,13686,13736,13781,13829,13883,13935,13982,14030,14082,14129,14179,14235,14290,14339,14390,14452,14518,14580,14646,14716,14781,14844,14906,14980,15053,15113,15175,15235,15298,15365,15428,15498,15566,15633,15699,15747,15806,15857,15903,15950,15997,16043,16087,16137,16190,16246,16289,16336,16386,16432,16484,16529,16573,16630,16682,16737,16792,16836,16886,16935,16984,17035,17086,17131,17203,17270,17321,17369,17423,17475,17527,17579,17633,17681,17732,17794,17862,17934,18006,18066,18132,18196,18255,18315,18378,18442,18501,18563,18629,18690,18756,18822,18883,18959,19037,19112,19180,19252,19324,19402,19484,19570,19656,19737,19798,19863,19927,19990,20052,20126,20187,20252,20313,20373,20440,20506,20568,20635,20702,20767,20832,20894,20960,21020,21078,21134,21193,21256,21315,21380,21443,21505,21563,21622,21679,21738,21797,21854,21908,21963,22021,22069,22124,22174,22222,22281,22327,22372,22421,22476,22529,22586,22643,22697,22747,22796,22850,22902,22955,23011,23067,23122,23170,23224,23273,23323,23373,23432,23490,23551,23615,23675,23744,23804,23886,23960,24022,24099,24159,24220,24285,24350,24417,24478,24543,24614,24682,24726,24779,24830,24886,24940,25001,25056,25117,25178,25232,25294,25363,25431,25493,25541,25592,25649,25699,25754,25810,25864,25912,25956,26012,26067,26120,26172,26224,26273,26328,26379,26433,26485,26540,26597,26645,26697,26747,26794,26848,26901,26950,27004,27063,27114,27187,27264,27345,27427,27510,27589,27666,27738,27817,27893,27964,28043,28120,28196,28271,28344,28429,28514,28603,28689,28774,28835", "endColumns": "45,52,50,48,44,42,54,46,50,52,49,45,46,49,55,53,48,54,54,50,57,49,50,53,44,48,43,50,46,41,39,73,70,66,65,69,68,74,73,67,66,57,66,64,56,75,70,58,57,58,64,64,69,67,73,66,74,75,72,69,64,65,75,63,51,60,59,55,60,57,51,57,60,60,68,75,58,63,61,59,65,59,54,58,64,62,57,75,64,61,55,59,54,61,57,72,69,69,61,65,69,72,70,68,62,72,63,57,62,61,59,57,63,60,66,59,64,66,66,57,64,56,56,66,59,68,68,69,67,79,75,62,73,69,69,70,70,74,69,73,71,75,79,79,75,68,66,60,62,61,71,68,66,67,72,66,68,68,61,61,70,54,59,57,54,55,64,63,74,59,47,62,55,59,54,55,61,66,71,70,66,61,61,65,72,60,66,62,61,66,65,66,72,72,58,66,67,64,66,64,62,59,58,55,60,64,45,51,49,44,47,53,51,46,47,51,46,49,55,54,48,50,61,65,61,65,69,64,62,61,73,72,59,61,59,62,66,62,69,67,66,65,47,58,50,45,46,46,45,43,49,52,55,42,46,49,45,51,44,43,56,51,54,54,43,49,48,48,50,50,44,71,66,50,47,53,51,51,51,53,47,50,61,67,71,71,59,65,63,58,59,62,63,58,61,65,60,65,65,60,75,77,74,67,71,71,77,81,85,85,80,60,64,63,62,61,73,60,64,60,59,66,65,61,66,66,64,64,61,65,59,57,55,58,62,58,64,62,61,57,58,56,58,58,56,53,54,57,47,54,49,47,58,45,44,48,54,52,56,56,53,49,48,53,51,52,55,55,54,47,53,48,49,49,58,57,60,63,59,68,59,81,73,61,76,59,60,64,64,66,60,64,70,67,43,52,50,55,53,60,54,60,60,53,61,68,67,61,47,50,56,49,54,55,53,47,43,55,54,52,51,51,48,54,50,53,51,54,56,47,51,49,46,53,52,48,53,58,50,72,76,80,81,82,78,76,71,78,75,70,78,76,75,74,72,84,84,88,85,84,60,59", "endOffsets": "650,703,754,803,848,891,946,993,1044,1097,1147,1193,1240,1290,1346,1400,1449,1504,1559,1610,1668,1718,1769,1823,1868,1917,1961,2012,2059,2101,2141,2215,2286,2353,2419,2489,2558,2633,2707,2775,2842,2900,2967,3032,3089,3165,3236,3295,3353,3412,3477,3542,3612,3680,3754,3821,3896,3972,4045,4115,4180,4246,4322,4386,4438,4499,4559,4615,4676,4734,4786,4844,4905,4966,5035,5111,5170,5234,5296,5356,5422,5482,5537,5596,5661,5724,5782,5858,5923,5985,6041,6101,6156,6218,6276,6349,6419,6489,6551,6617,6687,6760,6831,6900,6963,7036,7100,7158,7221,7283,7343,7401,7465,7526,7593,7653,7718,7785,7852,7910,7975,8032,8089,8156,8216,8285,8354,8424,8492,8572,8648,8711,8785,8855,8925,8996,9067,9142,9212,9286,9358,9434,9514,9594,9670,9739,9806,9867,9930,9992,10064,10133,10200,10268,10341,10408,10477,10546,10608,10670,10741,10796,10856,10914,10969,11025,11090,11154,11229,11289,11337,11400,11456,11516,11571,11627,11689,11756,11828,11899,11966,12028,12090,12156,12229,12290,12357,12420,12482,12549,12615,12682,12755,12828,12887,12954,13022,13087,13154,13219,13282,13342,13401,13457,13518,13583,13629,13681,13731,13776,13824,13878,13930,13977,14025,14077,14124,14174,14230,14285,14334,14385,14447,14513,14575,14641,14711,14776,14839,14901,14975,15048,15108,15170,15230,15293,15360,15423,15493,15561,15628,15694,15742,15801,15852,15898,15945,15992,16038,16082,16132,16185,16241,16284,16331,16381,16427,16479,16524,16568,16625,16677,16732,16787,16831,16881,16930,16979,17030,17081,17126,17198,17265,17316,17364,17418,17470,17522,17574,17628,17676,17727,17789,17857,17929,18001,18061,18127,18191,18250,18310,18373,18437,18496,18558,18624,18685,18751,18817,18878,18954,19032,19107,19175,19247,19319,19397,19479,19565,19651,19732,19793,19858,19922,19985,20047,20121,20182,20247,20308,20368,20435,20501,20563,20630,20697,20762,20827,20889,20955,21015,21073,21129,21188,21251,21310,21375,21438,21500,21558,21617,21674,21733,21792,21849,21903,21958,22016,22064,22119,22169,22217,22276,22322,22367,22416,22471,22524,22581,22638,22692,22742,22791,22845,22897,22950,23006,23062,23117,23165,23219,23268,23318,23368,23427,23485,23546,23610,23670,23739,23799,23881,23955,24017,24094,24154,24215,24280,24345,24412,24473,24538,24609,24677,24721,24774,24825,24881,24935,24996,25051,25112,25173,25227,25289,25358,25426,25488,25536,25587,25644,25694,25749,25805,25859,25907,25951,26007,26062,26115,26167,26219,26268,26323,26374,26428,26480,26535,26592,26640,26692,26742,26789,26843,26896,26945,26999,27058,27109,27182,27259,27340,27422,27505,27584,27661,27733,27812,27888,27959,28038,28115,28191,28266,28339,28424,28509,28598,28684,28769,28830,28890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "700", "startColumns": "4", "startOffsets": "41839", "endColumns": "100", "endOffsets": "41935"}}]}]}