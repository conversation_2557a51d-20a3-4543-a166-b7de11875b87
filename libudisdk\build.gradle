//apply plugin: 'com.android.application'
apply plugin: 'com.android.library'

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"
    defaultConfig {
//        applicationId "com.eeo.udisdk"
        minSdkVersion 26
        targetSdkVersion 30
        versionCode 1
        versionName "1.0"
        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    compileOnly(fileTree("src/main/libs"))
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'com.google.code.gson:gson:2.8.6'
}
