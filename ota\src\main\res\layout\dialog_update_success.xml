<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_update_width"
    android:layout_height="@dimen/dialog_update_height"
    android:layout_gravity="center"
    android:background="@drawable/bg_shortcut">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_update_success_tv_title_margin_top"
        android:drawableStart="@drawable/ota_ic_success"
        android:drawablePadding="@dimen/dialog_update_fail_tv_title_drawable_padding"
        android:gravity="center_vertical"
        android:text="@string/update_success"
        android:textColor="@color/black"
        android:textSize="@dimen/dialog_update_fail_tv_title_text_size" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_update_fail_tv_content_margin_top"
        android:text="@string/update_success_content"
        android:textColor="@color/text_black_100"
        android:textSize="@dimen/dialog_update_fail_tv_content_text_size" />

    <Button
        android:id="@+id/btn_confirm"
        style="@style/Ota_Update_Button"
        android:layout_width="@dimen/dialog_update_fail_btn_width"
        android:layout_height="@dimen/dialog_update_fail_btn_height"
        android:layout_below="@id/tv_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_update_success_btn_confirm_margin_top"
        android:background="@drawable/bg_btn_confirm"
        android:text="@string/confirm_update_success"
        android:textColor="@color/white_100"
        android:textSize="@dimen/dialog_update_fail_btn_confirm_text_size" />


</RelativeLayout>