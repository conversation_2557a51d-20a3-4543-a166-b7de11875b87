plugins {
//    id 'com.android.application'
    id 'com.android.library'
}

android {
    compileSdk 28

    defaultConfig {
//        applicationId "com.eeo.ota"
        minSdk 19
        targetSdk 26
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true // 启用 MultiDex
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    /*signingConfigs {
        release {
            keyAlias 'android'
            keyPassword 'android'
            storeFile file('../appKey/xbh.jks')
            storePassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }
        debug {
            keyAlias 'android'
            keyPassword 'android'
            storeFile file('../appKey/xbh.jks')
            storePassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }*/
    // 自定义apk命名
    /*applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def fileName = "Ota.apk"
                    outputFileName = fileName
            }
    }*/
}

dependencies {
    implementation 'com.android.support:appcompat-v7:28.0.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    //tencent iot sdk 远程构建
    //最新版本见：https://github.com/tencentyun/iot-device-java/releases
//    implementation 'com.tencent.iot.explorer:explorer-device-android:3.3.10'
    implementation 'com.tencent.iot.explorer:explorer-device-android:3.3.22'

    //海思
    compileOnly files('libs/Hiframework.jar')
    api files('libs/HiSysManager.jar')
    implementation files('libs/Hitv.jar')

    implementation files('../commonlib/droidlogic.jar')

    //众远touch
    implementation project(':libtouchsdk')
}