//apply plugin: 'com.android.application'
apply plugin: 'com.android.library'

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"
    defaultConfig {
//        applicationId "com.example.touchsdk"
        minSdkVersion 15
        targetSdkVersion 30
        versionCode 1
        versionName "1.2"
        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets{
        main{
            jni.srcDirs = []
            jniLibs.srcDirs "src/main/libs"
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}
