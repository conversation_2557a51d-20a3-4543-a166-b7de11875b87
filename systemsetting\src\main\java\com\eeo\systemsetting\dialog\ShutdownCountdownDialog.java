package com.eeo.systemsetting.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.PowerUtil;

public class ShutdownCountdownDialog extends Dialog {
    public static final String TAG = "ShutdownCountdownDialog";
    private static final int MSG_COUNT_DOWN = 0x001;

    private Context mContext;
    private Button mConfirmBtn;
    private Button mCancelBtn;
    private int mCountDown = 15;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == MSG_COUNT_DOWN) {
                if (mCountDown > 0) {
                    mConfirmBtn.setText(String.format(mContext.getString(R.string.shutdown_countdown_confirm), mCountDown));
                    mCountDown--;
                    sendEmptyMessageDelayed(MSG_COUNT_DOWN, 1000);
                } else {
                    dismiss();
                    PowerUtil.getInstance(mContext).shutdownOrReboot(false, "", PowerUtil.FLAG_SHUTDOWN_HARD); //硬关机
                }
            }
        }
    };

    public ShutdownCountdownDialog(Context context) {
        super(context, R.style.Dialog);
        init();
        mContext = context;
    }

    public ShutdownCountdownDialog(Context context, int themeResId) {
        super(context, themeResId);
        init();
        mContext = context;
    }

    private void init() {
        getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_shutdown_countdown);
        Log.i(TAG, "onCreate: ");

        mConfirmBtn = findViewById(R.id.btn_confirm);
        mCancelBtn = findViewById(R.id.btn_cancel);

        mConfirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
                PowerUtil.getInstance(mContext).shutdownOrReboot(false, "", PowerUtil.FLAG_SHUTDOWN_HARD); //硬关机
            }
        });


        mCancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
                PowerUtil.getInstance(mContext).setShuttingDown(false);
            }
        });
    }

    @Override
    public void show() {
        super.show();
        mCountDown = 15;
        mHandler.removeMessages(MSG_COUNT_DOWN);
        mHandler.sendEmptyMessage(MSG_COUNT_DOWN);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        mHandler.removeMessages(MSG_COUNT_DOWN);
    }
}
