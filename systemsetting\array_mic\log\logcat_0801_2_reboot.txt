2025-08-01 14:56:07.643   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-01 14:56:07.643   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-01 14:56:07.643   845-845   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service
2025-08-01 14:56:07.643   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-01 14:56:07.645   845-845   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:283 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:148 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:119 
2025-08-01 14:56:07.663   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity
2025-08-01 14:56:07.664   845-867   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@723e00e className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-01 14:56:07.666   845-876   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@723e00e startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-01 14:56:08.015   845-845   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-01 14:56:08.020   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-01 14:56:08.120   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-01 14:56:08.120   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-01 14:56:08.141   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-08-01 14:56:08.141   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-01 14:56:08.149   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-01 14:56:11.527   845-1405  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=500, message=Internal Server Error, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 14:56:17.283   845-1633  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 14:56:17.289   845-1633  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":false,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 14:56:18.150   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-01 14:56:18.150   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-01 14:56:18.154   845-1940  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-01 14:56:20.516   845-1308  o.systemsettin          com.eeo.systemsetting                W  Long monitor contention with owner Thread-9 (1940) at void com.droidlogic.app.SystemControlManager.systemCmd(java.lang.String)(SystemControlManager.java:1354) waiters=0 in java.util.ArrayList com.droidlogic.app.SystemControlManager.getI2cData(int, int, int, int) for 2.357s
2025-08-01 14:56:22.164   845-2215  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 14:56:22.166   845-2215  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 14:56:22.171   845-1405  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 14:56:22.172   845-1405  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 14:56:22.518   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-01 14:56:26.927   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-08-01 14:56:26.927   845-845   ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-08-01 14:56:26.927   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 14:56:28.086   845-1405  Udi-SourceManager       com.eeo.systemsetting                D  getAvailableSourceArray: response=Response{protocol=http/1.0, code=200, message=OK, url=http://udi.ifpdos.com/v1/source/list/available}
2025-08-01 14:56:28.086   845-1405  Udi-SourceManager       com.eeo.systemsetting                E  getSourceArray: {"sources":[{"hasSignal":true,"hideWhenNoSignal":false,"name":"PC","originName":"PC","sourceItem":"PC","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI1","originName":"HDMI1","sourceItem":"HDMI1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"HDMI2","originName":"HDMI2","sourceItem":"HDMI2","visible":true},{"hasSignal":true,"hideWhenNoSignal":false,"name":"Type-C1","originName":"Type-C1","sourceItem":"TYPE_C1","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"Type-C2","originName":"Type-C2","sourceItem":"TYPE_C2","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"DP","originName":"DP","sourceItem":"DP","visible":true},{"hasSignal":false,"hideWhenNoSignal":false,"name":"VGA","originName":"VGA","sourceItem":"VGA1","visible":true}]}
2025-08-01 14:56:30.065   845-845   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-01 14:56:30.065   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-01 14:56:30.070   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-01 14:56:30.114   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-08-01 14:56:30.114   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-08-01 14:56:30.114   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-08-01 14:56:30.114   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-08-01 14:56:30.117   845-2476  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh
2025-08-01 14:56:30.231   845-2478  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: qpid ==>1379
2025-08-01 14:56:30.236   845-2476  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh] finished with exit code: 0
2025-08-01 14:56:32.263   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-08-01 14:56:32.264   845-2493  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-08-01 14:56:32.430   845-2493  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 0
2025-08-01 14:56:34.432   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-08-01 14:56:34.434   845-2510  ArrayMicOTA             com.eeo.systemsetting                D  Executing command: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-08-01 14:56:34.458   845-2514  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: error: device '303_usb_device' not found
2025-08-01 14:56:34.461   845-2510  ArrayMicOTA             com.eeo.systemsetting                D  Command [adb -s 303_usb_device shell rm -rf /mnt/UDISK/*] finished with exit code: 1
2025-08-01 14:56:34.461   845-845   ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Command failed: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-08-01 14:56:34.461   845-845   ArrayMicOTA             com.eeo.systemsetting                D  ArrayMicOTA: Setting SP key to not completed (failed)
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Command failed: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                E  Array mic update failed in systemsetting module: Command failed: adb -s 303_usb_device shell rm -rf /mnt/UDISK/*
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-08-01 14:56:34.463   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-08-01 14:56:34.465   845-2516  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side PC
2025-08-01 14:56:36.736   845-1308  o.systemsettin          com.eeo.systemsetting                W  Long monitor contention with owner Thread-26 (2516) at void com.droidlogic.app.SystemControlManager.systemCmd(java.lang.String)(SystemControlManager.java:1354) waiters=0 in java.util.ArrayList com.droidlogic.app.SystemControlManager.getI2cData(int, int, int, int) for 2.268s
2025-08-01 14:56:36.762   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-08-01 14:56:36.762   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Cleanup successful. USB switched to PC and device disconnected.
2025-08-01 14:56:36.762   845-845   ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-08-01 14:56:36.762   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Array mic update all finished in systemsetting module
2025-08-01 14:56:36.762   845-845   ArrayMicOTA             com.eeo.systemsetting                W  SubDeviceUpdateService is null, cannot execute delayed reboot
2025-08-01 14:56:36.766   845-845   ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@5ff2808
2025-08-01 14:56:36.766   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-08-01 14:56:36.767   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-08-01 14:56:36.767   845-845   ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.