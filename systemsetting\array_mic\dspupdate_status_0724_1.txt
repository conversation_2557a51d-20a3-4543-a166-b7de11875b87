commit 097efef35553d90f04d98f23fdefa97ec8be9feb
Author: chensib<PERSON> <<EMAIL>>
Date:   Mon Jun 23 15:21:58 2025 +0800

    [ota]新增DSP固件升级功能

:100644 100644 dafc942 0b3a83e M	ota/src/main/AndroidManifest.xml
:000000 100644 0000000 dacdec9 A	ota/src/main/java/com/eeo/ota/dsp/DspFirmwareUpdater.java
:000000 100644 0000000 c1b6893 A	ota/src/main/java/com/eeo/ota/dsp/DspUpdate.java
:000000 100644 0000000 ad6c7df A	ota/src/main/java/com/eeo/ota/dsp/IDspCommunicationManager.java
:000000 100644 0000000 26054d0 A	ota/src/main/java/com/eeo/ota/dsp/UpdateStepException.java
:000000 100644 0000000 f233485 A	ota/src/main/java/com/eeo/ota/service/DspUpdateService.java
:100644 100644 6d34944 bd13d4b M	setup/src/main/java/cn/eeo/classin/setup/MainActivity.java
:100644 100644 ab91fd1 bf0e56d M	systemsetting/src/main/AndroidManifest.xml
:100644 100644 e9b9b6d 2a4c21a M	systemsetting/src/main/java/com/eeo/systemsetting/EeoApplication.java
:000000 100644 0000000 84e2653 A	systemsetting/src/main/java/com/eeo/systemsetting/receiver/DspUpdateCheckReceiver.java
:000000 100644 0000000 b492142 A	systemsetting/src/main/java/com/eeo/systemsetting/usbserial/DspUpgradeFrameParser.java
:100644 100644 36b4f35 c12b407 M	systemsetting/src/main/java/com/eeo/systemsetting/usbserial/UsbSerialManager.java
