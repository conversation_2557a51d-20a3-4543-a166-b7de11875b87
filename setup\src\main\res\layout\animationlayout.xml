<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/blur_background">

    <LinearLayout
        android:id="@+id/ll_system_init"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="235dp"
        android:orientation="horizontal"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/progressbar_init"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:indeterminateTint="@color/press_color" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13dp"
            android:text="@string/system_initialization"
            android:textColor="@color/white_100"
            android:textSize="16sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="400dp"
        android:layout_height="400dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="211dp" />


</RelativeLayout>