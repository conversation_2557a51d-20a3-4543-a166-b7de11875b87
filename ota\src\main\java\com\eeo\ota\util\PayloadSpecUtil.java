package com.eeo.ota.util;

import com.eeo.ota.bean.Constant;
import com.eeo.ota.bean.PayloadSpec;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class PayloadSpecUtil {

    /**
     * 解析update.zip
     */
    public static PayloadSpec forNonStreaming(File packageFile) throws IOException {
        Throwable th;
        Enumeration<? extends ZipEntry> entries;
        long payloadSize;
        InputStream inputStream;
        boolean payloadFound = false;
        long payloadOffset = 0;
        long payloadSize2 = 0;
        List<String> properties = new ArrayList<>();
        ZipFile zip = new ZipFile(packageFile);
        try {
            Enumeration<? extends ZipEntry> entries2 = zip.entries();
            long offset = 0;
            while (entries2.hasMoreElements()) {
                try {
                    ZipEntry entry = (ZipEntry) entries2.nextElement();
                    String name = entry.getName();
                    long extraSize = entry.getExtra() == null ? 0L : entry.getExtra().length;
                    long payloadSize3 = name.length() + 30;
                    offset += payloadSize3 + extraSize;
                    try {
                        if (entry.isDirectory()) {
                            payloadSize2 = payloadSize2;
                        } else {
                            long length = entry.getCompressedSize();
                            if (!Constant.PAYLOAD_BINARY_FILE_NAME.equals(name)) {
                                entries = entries2;
                                if (Constant.PAYLOAD_PROPERTIES_FILE_NAME.equals(name) && (inputStream = zip.getInputStream(entry)) != null) {
                                    BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
                                    while (true) {
                                        String line = br.readLine();
                                        if (line == null) {
                                            break;
                                        }
                                        properties.add(line);
                                    }
                                }
                                payloadSize = payloadSize2;
                            } else if (entry.getMethod() == 0) {
                                payloadFound = true;
                                payloadOffset = offset;
                                payloadSize = length;
                                entries = entries2;
                            } else {
                                throw new IOException("Invalid compression method.");
                            }
                            offset += length;
                            payloadSize2 = payloadSize;
                            entries2 = entries;
                        }
                    } catch (Throwable th2) {
                        th = th2;
                        try {
                            zip.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                        throw th;
                    }
                } catch (Throwable th4) {
                    th = th4;
                }
            }
            zip.close();
            if (payloadFound) {
                return PayloadSpec.newBuilder()
                        .url("file://" + packageFile.getAbsolutePath())
                        .offset(payloadOffset)
                        .size(payloadSize2)
                        .properties(properties)
                        .build();
            }
            throw new IOException("Failed to find payload entry in the given package.");
        } catch (Throwable th5) {
            th = th5;
        }
        return null;
    }
}