package cn.eeo.classin.setup.utils;

import android.os.SystemProperties;

public class Constant {

    //退出二级页面
    public static final String ACTION_OUT_PAGE = "com.eeo.out.page";

    //wifi详情页面
    public static final String ACTION_WIFI_MORE = "com.eeo.wifi.more";
    //wifi详情页的bean
    public static final String ACTION_WIFI_KEY = "com.eeo.wifi.key";
    //退出wifi详情页
    public static final String ACTION_OUT_WIFI_MORE_PAGE = "com.eeo.out.wifi.more.page";
    //退出大屏控制界面
    public static final String ACTION_EXIT = "com.eeo.systemsetting.exit";

    //飞图投屏
    public static final String MULTI_SCREEN_PKG_NAME = "com.android.toofifi";
    public static final String MULTI_SCREEN_TOP_ACTIVITY_NAME = "com.android.toofifi.ui.activity.MultiScreenActivity";
    public static final String ACTION_MULTI_SCREEN_OS_MARK_OPEN = "com.toofifi.action.OS_MARK_OPEN"; //该广播允许投屏时弹窗


    //触摸广播
    public static final String SET_USB_ENABLE_ACTION = "com.eeo.set.usb.enable";
    public static final String USB2_TOUCH_KEY = "usbTouchKey";
    public static final int TOUCH = 0;
    public static final int UN_TOUCH = 1;

    //广播来源
    public static final String USB2_TOUCH_MODULE = "usbTouchModuleKey";
    //设置页面
    public static final int MODULE_SETTING = 0;
    //其他页面
    public static final int MODULE_OTHER = 1;

    public static final String KEYCODE_POWER_ACTION = "com.android.eeo.SendHotKey";
    public static final String HOT_KEY = "hotkey";
    public static final String KEY_EVENT = "event";
    public static final int KEYCODE_SEEWO_POWER = 5000;  // KeyEvent.KEYCODE_SEEWO_POWER = 5000;

    /**
     * 长按电源键广播，采用强关机
     */
    public static final String ACTION_EEO_LONG_PRESS_POWER_OFF = "com.eeo.action.LONG_PRESS_POWER_OFF";
    /**
     * 其它应用通过广播来统一让设置控制关机、重启
     */
    public static final String ACTION_EEO_POWER_OFF = "com.eeo.action.POWER_OFF";
    public static final String ACTION_EEO_REBOOT = "com.eeo.action.REBOOT";

    public static final String CONFIGURED_NETWORKS_CHANGE = "android.net.wifi.CONFIGURED_NETWORKS_CHANGE";
    public static final String LINK_CONFIGURATION_CHANGED = "android.net.wifi.LINK_CONFIGURATION_CHANGED";

    public static final int DIALOG_WIDTH = 240;
    public static final int DIALOG_HEIGHT = 148;

    public static final int DIALOG_X = 1305;
    public static final int DIALOG_Y = 573;

    public static final boolean CVT_EN_TV_SOURCE = SystemProperties.getBoolean("ro.en.tvsource", false);
}
