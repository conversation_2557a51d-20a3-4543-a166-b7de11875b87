<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="bg_radius">8dp</dimen>

    <dimen name="main_cv_width">394dp</dimen>
    <dimen name="main_cv_height">394dp</dimen>
    <dimen name="main_width">384dp</dimen>
    <dimen name="main_height">384dp</dimen>
    <dimen name="main_bg_radius">16dp</dimen>
    <dimen name="main_bg_stroke">0.5dp</dimen>
    <dimen name="main_tv_title_margin_top">27dp</dimen>
    <dimen name="sl_screen_width">114dp</dimen>
    <dimen name="sl_screen_height">114dp</dimen>
    <dimen name="sl_screen_margin_start">16dp</dimen>
    <dimen name="sl_screen_margin_top">64dp</dimen>
    <dimen name="sl_screen_corner_radius">8dp</dimen>
    <dimen name="sl_screen_elevation">5dp</dimen>
    <dimen name="sl_screen_shadow_limit">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_x">0dp</dimen>
    <dimen name="sl_screen_shadow_offset_y">0dp</dimen>
    <dimen name="sl_screen_stroke_width">0.2dp</dimen>
    <dimen name="iv_screen_width">20dp</dimen>
    <dimen name="iv_screen_height">20dp</dimen>
    <dimen name="tv_screen_width">50dp</dimen>
    <dimen name="tv_screen_height">13dp</dimen>
    <dimen name="tv_screen_text_size">9sp</dimen>
    <dimen name="iv_screen_margin_top">33dp</dimen>
    <dimen name="tv_screen_margin_top">58dp</dimen>
    <dimen name="tv_shutdown_width">50dp</dimen>
    <dimen name="tv_setting_margin_top">25dp</dimen>
    <dimen name="tv_screen_drawable_padding">5dp</dimen>
    <dimen name="sl_write_margin_top">3dp</dimen>
    <dimen name="sl_setting_width">234dp</dimen>
    <dimen name="sl_setting_margin_start">7dp</dimen>
    <dimen name="tv_setting_margin_start">9dp</dimen>
    <dimen name="tv_signal_margin_start">61dp</dimen>
    <dimen name="tv_touch_lock_width">80dp</dimen>
    <dimen name="tv_touch_lock_margin_start">113dp</dimen>
    <dimen name="tv_eye_margin_start">165dp</dimen>
    <dimen name="iv_bright_width">20dp</dimen>
    <dimen name="iv_bright_height">20dp</dimen>
    <dimen name="iv_bright_margin_start">24dp</dimen>
    <dimen name="iv_bright_margin_top">60dp</dimen>
    <dimen name="iv_voice_margin_top">24dp</dimen>
    <dimen name="sb_bright_width">151dp</dimen>
    <dimen name="sb_bright_height">20dp</dimen>
    <dimen name="sb_bright_max_height">40dp</dimen>
    <dimen name="sb_bright_padding_start">0dp</dimen>
    <dimen name="sb_bright_padding_end">0dp</dimen>
    <dimen name="sb_bright_margin_start">49dp</dimen>
    <dimen name="sb_bright_margin_top">60dp</dimen>
    <dimen name="sb_voice_margin_top">24dp</dimen>
    <dimen name="fl_desktop_padding_vertical">6dp</dimen>
    <dimen name="tv_desktop_margin_start">36dp</dimen>
    <dimen name="tv_desktop_margin_top">23dp</dimen>
    <dimen name="tv_desktop_margin_bottom">17dp</dimen>
    <dimen name="tv_desktop_text_size">10sp</dimen>
    <dimen name="tv_rest_margin_start">15dp</dimen>
    <dimen name="tv_rest_margin_desktop">14dp</dimen>
    <dimen name="tv_restart_margin_start">44dp</dimen>
    <dimen name="main_line1_height">0.3dp</dimen>
    <dimen name="main_line1_margin_top">25dp</dimen>
    <dimen name="ll_touch_lock_width">56dp</dimen>
    <dimen name="ll_touch_lock_margin_bottom">37dp</dimen>
    <dimen name="sl_lock_width">37dp</dimen>
    <dimen name="sl_lock_height">37dp</dimen>
    <dimen name="tv_lock_margin_top">5dp</dimen>
    <dimen name="inter_touch_lock_width">220dp</dimen>
    <dimen name="inter_touch_lock_height">98dp</dimen>
    <dimen name="inter_touch_lock_margin_start">8dp</dimen>
    <dimen name="inter_touch_lock_margin_top">8dp</dimen>

    <dimen name="iv_back_margin_top">11dp</dimen>
    <dimen name="iv_back_width">20dp</dimen>
    <dimen name="iv_back_height">20dp</dimen>
    <dimen name="iv_back_margin_start">13dp</dimen>
    <dimen name="setting_tv_title_margin_top">13dp</dimen>
    <dimen name="setting_line1_margin_top">11dp</dimen>
    <dimen name="ll_select_width">120dp</dimen>
    <dimen name="sl_network_height">29dp</dimen>
    <dimen name="sl_network_margin_start">28dp</dimen>
    <dimen name="sl_network_margin_top">21dp</dimen>
    <dimen name="tv_network_margin_start">55dp</dimen>
    <dimen name="tv_network_text_size">9sp</dimen>

    <dimen name="sl_network_shadow_offset_x">0dp</dimen>
    <dimen name="sl_network_shadow_offset_y">1dp</dimen>
    <dimen name="sl_wifi_margin_top">11dp</dimen>

    <dimen name="iv_shutdown_width">27dp</dimen>
    <dimen name="iv_shutdown_height">27dp</dimen>
    <dimen name="iv_shutdown_margin_top">134dp</dimen>
    <dimen name="iv_shutdown_margin_left">103dp</dimen>
    <dimen name="shutdown_tittle_text_size">12sp</dimen>
    <dimen name="shutdown_tittle_margin_top">139dp</dimen>
    <dimen name="shutdown_tittle_margin_left">11dp</dimen>
    <dimen name="shutdown_content_text_size">9sp</dimen>
    <dimen name="shutdown_content_margin_top">11dp</dimen>
    <dimen name="shutdown_btn_confirm_width">88dp</dimen>
    <dimen name="shutdown_btn_confirm_height">28dp</dimen>
    <dimen name="shutdown_btn_confirm_text_size">11sp</dimen>
    <dimen name="shutdown_btn_confirm_margin_top">37dp</dimen>
    <dimen name="shutdown_btn_confirm_margin_left">203dp</dimen>
    <dimen name="shutdown_btn_cancel_margin_left">93dp</dimen>

    <dimen name="signal_tv_width">341dp</dimen>
    <dimen name="signal_tv_height">48dp</dimen>
    <dimen name="signal_tv_text_size">9sp</dimen>
    <dimen name="signal_tv_drawable_padding">17dp</dimen>
    <dimen name="signal_tv_margin_start">169dp</dimen>
    <dimen name="signal_tv_window_margin_top">21dp</dimen>
    <dimen name="signal_iv_margin_start">139dp</dimen>
    <dimen name="signal_item_margin_top">11dp</dimen>

    <dimen name="adb_width">264dp</dimen>
    <dimen name="adb_height">384dp</dimen>
    <dimen name="adb_scrollview_height">298dp</dimen>
    <dimen name="adb_iv_back_width">25dp</dimen>
    <dimen name="adb_iv_back_height">25dp</dimen>
    <dimen name="adb_iv_back_margin_start">20dp</dimen>
    <dimen name="adb_iv_back_margin_top">20dp</dimen>
    <dimen name="adb_iv_close_margin_top">11dp</dimen>
    <dimen name="adb_iv_close_margin_end">16dp</dimen>
    <dimen name="adb_line_margin_top">42dp</dimen>
    <dimen name="adb_item_width">232dp</dimen>
    <dimen name="adb_item_height">29dp</dimen>
    <dimen name="adb_item_radius">3dp</dimen>
    <dimen name="adb_item_margin_top">3dp</dimen>
    <dimen name="adb_item_adb_margin_top">13dp</dimen>
    <dimen name="adb_item_margin_start">21dp</dimen>
    <dimen name="adb_item_margin_end">16dp</dimen>
    <dimen name="adb_sw_width">36dp</dimen>
    <dimen name="adb_sw_height">18dp</dimen>
    <dimen name="adb_sw_margin_end">13dp</dimen>
    <dimen name="adb_iv_reset_width">20dp</dimen>
    <dimen name="adb_iv_reset_height">20dp</dimen>
    <dimen name="adb_iv_reset_margin_start">220dp</dimen>
    <dimen name="adb_tv_margin_start">13dp</dimen>
    <dimen name="adb_tv_text_size">9sp</dimen>
    <dimen name="adb_btn_text_size">11sp</dimen>
    <dimen name="adb_btn_width">70dp</dimen>
    <dimen name="adb_btn_height">28dp</dimen>
    <dimen name="adb_btn_margin_start">9dp</dimen>
    <dimen name="adb_btn_margin_bottom">12dp</dimen>

    <dimen name="rgb_tv_margin_start">16dp</dimen>
    <dimen name="rgb_tv_color_temperature_margin_top">19dp</dimen>
    <dimen name="rgb_sb_width">195dp</dimen>
    <dimen name="rgb_sb_height">7dp</dimen>
    <dimen name="rgb_sb_padding_start">7dp</dimen>
    <dimen name="rgb_sb_padding_end">7dp</dimen>
    <dimen name="rgb_sb_margin_start">9dp</dimen>
    <dimen name="rgb_sb_thumb_height">23dp</dimen>
    <dimen name="rgb_et_width">40dp</dimen>
    <dimen name="rgb_et_height">20dp</dimen>
    <dimen name="rgb_sb_margin_top">-4dp</dimen>
    <dimen name="rgb_tv_color_temperature_cold_margin_top">-5dp</dimen>
    <dimen name="rgb_line2_margin_top">25dp</dimen>
    <dimen name="rgb_red_gain_margin_top">23dp</dimen>
    <dimen name="rgb_green_gain_margin_top">24dp</dimen>
    <dimen name="rgb_reset_margin_bottom">12dp</dimen>
    <dimen name="rgb_btn_reset_width">232dp</dimen>

    <dimen name="dialog_have_update_width">240dp</dimen>
    <dimen name="dialog_have_update_height">148dp</dimen>
    <dimen name="dialog_have_update_title_margin_top">19dp</dimen>
    <dimen name="dialog_have_update_title_text_size">9sp</dimen>
    <dimen name="dialog_have_update_content_text_size">9sp</dimen>
    <dimen name="dialog_have_update_content_margin_top">8dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_start">21dp</dimen>
    <dimen name="dialog_have_update_btn_cancel_margin_bottom">21dp</dimen>

    <dimen name="dialog_shutdown_countdown_content_margin_top">15dp</dimen>

    <dimen name="dialog_shutdown_title_margin_top">23dp</dimen>
    <dimen name="dialog_shutdown_content_margin_top">19dp</dimen>

    <dimen name="dialog_factory_reset_title_margin_top">25dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_width">20dp</dimen>
    <dimen name="dialog_factory_reset_iv_warn_height">20dp</dimen>
    <dimen name="dialog_factory_reset_content_margin_top">16dp</dimen>
    <dimen name="dialog_factory_reset_title_margin_start">8dp</dimen>
    <dimen name="dialog_factory_reset_btn_cancel_margin_top">24dp</dimen>

    <dimen name="dialog_installing_pb_width">21dp</dimen>
    <dimen name="dialog_installing_pb_height">21dp</dimen>
    <dimen name="dialog_installing_tv_margin_start">13dp</dimen>
    <dimen name="dialog_installing_text_size">16sp</dimen>

    <dimen name="dialog_install_fail_tv_title_margin_top">32dp</dimen>
    <dimen name="dialog_install_fail_tv_title_drawable_padding">8dp</dimen>

    <dimen name="dialog_network_auto_manual_width">120dp</dimen>
    <dimen name="dialog_network_auto_manual_height">59dp</dimen>
    <dimen name="dialog_network_auto_manual_item_height">29dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_width">20dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_height">20dp</dimen>
    <dimen name="dialog_network_auto_manual_iv_margin_start">29dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_margin_start">3dp</dimen>
    <dimen name="dialog_network_auto_manual_tv_text_size">9sp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_left">11dp</dimen>
    <dimen name="dialog_network_auto_manual_line_margin_right">11dp</dimen>

    <dimen name="fragment_about_scrollbar_size">4dp</dimen>
    <dimen name="fragment_about_padding_bottom">5dp</dimen>
    <dimen name="fragment_about_device_name_margin_top">29dp</dimen>
    <dimen name="fragment_about_text_size">9sp</dimen>
    <dimen name="fragment_about_title_width">60dp</dimen>
    <dimen name="fragment_about_title_height">13dp</dimen>
    <dimen name="fragment_about_title_margin_top">27dp</dimen>
    <dimen name="fragment_about_title_margin_start">21dp</dimen>
    <dimen name="fragment_about_content_width">160dp</dimen>
    <dimen name="fragment_about_content_height">13dp</dimen>
    <dimen name="fragment_about_line_height">0.3dp</dimen>
    <dimen name="fragment_about_line_margin_start">21dp</dimen>
    <dimen name="fragment_about_line_margin_top">33dp</dimen>
    <dimen name="fragment_about_tv_system_version_margin_start">1dp</dimen>
    <dimen name="fragment_about_windows_host_width">80dp</dimen>
    <dimen name="fragment_about_iv_windows_host_margin_start">121dp</dimen>
    <dimen name="fragment_about_padding_view_height">52dp</dimen>

    <dimen name="fragment_extra_wireless_screen_margin_top">26dp</dimen>
    <dimen name="fragment_extra_item_margin_top">20dp</dimen>
    <dimen name="fragment_extra_sw_width">40dp</dimen>
    <dimen name="fragment_extra_sw_height">20dp</dimen>
    <dimen name="fragment_extra_sw_margin_end">21dp</dimen>
    <dimen name="fragment_extra_tv_margin_start">21dp</dimen>
    <dimen name="fragment_extra_tv_text_size">9sp</dimen>

    <dimen name="fragment_network_network_margin_top">26dp</dimen>
    <dimen name="fragment_network_sw_margin_start">141dp</dimen>
    <dimen name="fragment_network_text_size">9sp</dimen>
    <dimen name="fragment_network_item_margin_top">23dp</dimen>
    <dimen name="fragment_network_title_width">40dp</dimen>
    <dimen name="fragment_network_title_margin_start">21dp</dimen>
    <dimen name="fragment_network_content_width">160dp</dimen>
    <dimen name="fragment_network_content_margin_start">21dp</dimen>
    <dimen name="fragment_network_tv_ip_setting_width">140dp</dimen>
    <dimen name="fragment_network_ll_ip_margin_top">15dp</dimen>
    <dimen name="fragment_network_iv_ip_width">13dp</dimen>
    <dimen name="fragment_network_iv_ip_height">13dp</dimen>
    <dimen name="fragment_network_iv_ip_margin_start">33dp</dimen>
    <dimen name="fragment_network_ll_mask_margin_top">11dp</dimen>
    <dimen name="fragment_network_dns_title_width">50dp</dimen>
    <dimen name="fragment_network_dns_content_margin_start">23dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_start">29dp</dimen>
    <dimen name="fragment_network_btn_confirm_margin_top">12dp</dimen>
    <dimen name="fragment_network_sw_width">40dp</dimen>
    <dimen name="fragment_network_sw_height">20dp</dimen>
    <dimen name="fragment_network_et_width">140dp</dimen>
    <dimen name="fragment_network_et_height">29dp</dimen>
    <dimen name="fragment_network_et_text_size">9sp</dimen>
    <dimen name="fragment_network_et_margin_start">8dp</dimen>
    <dimen name="fragment_network_et_padding_end">7dp</dimen>

    <dimen name="fragment_wifi_title_wifi_width">47dp</dimen>
    <dimen name="fragment_wifi_sw_margin_start">134dp</dimen>
    <dimen name="fragment_wifi_rv_connected_margin_top">10dp</dimen>
    <dimen name="fragment_wifi_line1_margin_top">19dp</dimen>
    <dimen name="fragment_wifi_line1_margin_start">0dp</dimen>
    <dimen name="fragment_wifi_line1_margin_end">0dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_start">21dp</dimen>
    <dimen name="fragment_wifi_ll_network_margin_top">33dp</dimen>
    <dimen name="fragment_wifi_ll_network_gone_margin_top">23dp</dimen>
    <dimen name="fragment_wifi_tv_other_network_text_size">9sp</dimen>
    <dimen name="fragment_wifi_pb_check_update_width">12dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_height">12dp</dimen>
    <dimen name="fragment_wifi_pb_check_update_margin_start">8dp</dimen>
    <dimen name="fragment_wifi_rv_wifi_list_margin_top">13dp</dimen>

    <dimen name="fragment_update_line_margin_top">30dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_start">21dp</dimen>
    <dimen name="fragment_update_tv_no_network_margin_top">33dp</dimen>
    <dimen name="fragment_update_tv_check_network_margin_top">27dp</dimen>
    <dimen name="fragment_update_btn_retry_margin_top">19dp</dimen>
    <dimen name="fragment_update_tv_version_margin_top">33dp</dimen>
    <dimen name="fragment_update_pb_check_update_margin_top">77dp</dimen>
    <dimen name="fragment_update_tv_checking_margin_top">11dp</dimen>
    <dimen name="fragment_update_tv_version_text_size">9sp</dimen>
    <dimen name="fragment_update_btn_right_update_margin_top">121dp</dimen>
    <dimen name="fragment_update_btn_right_update_gone_margin_top">20dp</dimen>
    <dimen name="fragment_update_btn_install_margin_top">43dp</dimen>
    <dimen name="fragment_update_pb_download_margin_top">32dp</dimen>
    <dimen name="fragment_update_pb_download_margin_end">21dp</dimen>
    <dimen name="fragment_update_pb_horizontal_min_height">5dp</dimen>
    <dimen name="fragment_update_pb_horizontal_max_height">5dp</dimen>
    <dimen name="fragment_update_btn_cancel_margin_top">59dp</dimen>
    <dimen name="fragment_update_tv_update_description_width">221dp</dimen>
    <dimen name="fragment_update_tv_update_description_height">81dp</dimen>
    <dimen name="fragment_update_tv_update_description_margin_top">13dp</dimen>
    <dimen name="fragment_update_tv_update_description_max_height">80dp</dimen>

    <dimen name="fragment_locale_tv_language_margin_top">26dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_top">26dp</dimen>
    <dimen name="fragment_locale_iv_language_margin_end">21dp</dimen>
    <dimen name="fragment_locale_tv_language_height">20dp</dimen>
    <dimen name="fragment_locale_spinner_width">120dp</dimen>


    <dimen name="item_wifi_height">41dp</dimen>
    <dimen name="item_wifi_tv_wifi_height">26dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_height">13dp</dimen>
    <dimen name="item_wifi_tv_wifi_name_max_width">100dp</dimen>
    <dimen name="item_wifi_tv_state_margin_top">1dp</dimen>
    <dimen name="item_wifi_tv_state_text_size">8sp</dimen>
    <dimen name="item_wifi_iv_rank_width">20dp</dimen>
    <dimen name="item_wifi_iv_rank_height">20dp</dimen>
    <dimen name="item_wifi_iv_rank_margin_end">21dp</dimen>
    <dimen name="item_wifi_iv_password_margin_end">4dp</dimen>
    <dimen name="item_wifi_line_margin_top">0dp</dimen>

    <dimen name="item_wifi_connect_height">25dp</dimen>
    <dimen name="item_wifi_connect_margin_top">18dp</dimen>
    <dimen name="item_wifi_connect_iv_check_width">17dp</dimen>
    <dimen name="item_wifi_connect_iv_check_height">17dp</dimen>
    <dimen name="item_wifi_connect_iv_password_margin_start">182dp</dimen>

    <dimen name="item_wifi_more_title_margin_start">107dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_start">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_end">21dp</dimen>
    <dimen name="item_wifi_more_tv_save_margin_bottom">18dp</dimen>
    <dimen name="item_wifi_more_tv_save_text_size">9sp</dimen>
    <dimen name="item_wifi_more_line_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_drawable_padding">8dp</dimen>
    <dimen name="item_wifi_more_line1_margin_top">14dp</dimen>
    <dimen name="item_wifi_more_item_margin_top">26dp</dimen>
    <dimen name="item_wifi_more_tv_ip_setting_width">147dp</dimen>
    <dimen name="item_wifi_more_item_margin_end">35dp</dimen>
    <dimen name="item_wifi_more_iv_arrow_margin_top">23dp</dimen>
    <dimen name="item_wifi_more_rl_manual_margin_top">18dp</dimen>
    <dimen name="item_wifi_more_iv_manual_margin_end">15dp</dimen>
    <dimen name="item_wifi_more_ip_address_margin_top">8dp</dimen>
    <dimen name="item_wifi_more_et_address_margin_top">6dp</dimen>
    <dimen name="item_wifi_more_iv_ip_marin_end">8dp</dimen>
    <dimen name="item_wifi_more_et_subnet_mask_margin_top">11dp</dimen>
    <dimen name="item_wifi_more_tv_dns_width">47dp</dimen>
    <dimen name="item_wifi_more_fl_disconnect_margin_top">21dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_height">29dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_margin_start">49dp</dimen>
    <dimen name="item_wifi_more_tv_disconnect_padding_left">21dp</dimen>
    <dimen name="item_wifi_more_iv_disconnect_margin_start">21dp</dimen>

    <dimen name="launcher_main_iv_privacy_width">27dp</dimen>
    <dimen name="launcher_main_iv_privacy_height">27dp</dimen>
    <dimen name="launcher_main_tv_privacy_margin_top">30dp</dimen>
    <dimen name="launcher_main_tv_privacy_text_size">18sp</dimen>
    <dimen name="launcher_main_tv_hint_width">160dp</dimen>
    <dimen name="launcher_main_tv_hint_height">37dp</dimen>
    <dimen name="launcher_main_tv_hint_text_size">9sp</dimen>
    <dimen name="launcher_main_iv_no_signal_width">304dp</dimen>
    <dimen name="launcher_main_iv_no_signal_height">304dp</dimen>
    <dimen name="launcher_main_iv_no_signal_margin_top">202dp</dimen>
    <dimen name="launcher_main_tv_no_signal_margin_top">467dp</dimen>
    <dimen name="launcher_main_tv_no_signal_text_size">16sp</dimen>
    <dimen name="launcher_main_tv_current_signal_margin_top">10dp</dimen>
    <dimen name="launcher_main_tv_current_signal_text_size">12sp</dimen>
    <dimen name="launcher_main_btn_start_width">160dp</dimen>
    <dimen name="launcher_main_btn_start_height">40dp</dimen>
    <dimen name="launcher_main_btn_start_margin_top">56dp</dimen>
    <dimen name="launcher_main_tv_hotline_width">200dp</dimen>
    <dimen name="launcher_main_tv_hotline_height">40dp</dimen>
    <dimen name="launcher_main_tv_hotline_margin_top">16dp</dimen>

    <dimen name="screen_dialog_cv_wireless_screen_height">318dp</dimen>
    <dimen name="screen_dialog_rl_wireless_screen_height">308dp</dimen>
    <dimen name="screen_dialog_iv_back_width">20dp</dimen>
    <dimen name="screen_dialog_iv_back_height">20dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_start">18dp</dimen>
    <dimen name="screen_dialog_iv_back_margin_top">23dp</dimen>

    <dimen name="screen_dialog_tv_title_margin_top">25dp</dimen>
    <dimen name="screen_dialog_title_text_size">12sp</dimen>
    <dimen name="screen_dialog_content_text_size">9sp</dimen>
    <dimen name="screen_dialog_tv_margin_start">21dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_width">40dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_height">20dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_top">14dp</dimen>
    <dimen name="screen_dialog_switch_enable_wireless_screen_margin_end">23dp</dimen>
    <dimen name="screen_dialog_line2_margin_top">8dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_width">341dp</dimen>
    <dimen name="screen_dialog_rl_sub_wireless_screen_margin_top">8dp</dimen>
    <dimen name="screen_dialog_tv_enable_pin_code_margin_top">8dp</dimen>
    <dimen name="screen_dialog_switch_enable_pin_code_margin_end">1dp</dimen>
    <dimen name="screen_dialog_tv_order_width">21dp</dimen>
    <dimen name="screen_dialog_tv_order_height">21dp</dimen>
    <dimen name="screen_dialog_tv_one_margin_start">25dp</dimen>
    <dimen name="screen_dialog_iv_step_1_width">15dp</dimen>
    <dimen name="screen_dialog_iv_step_1_height">15dp</dimen>
    <dimen name="screen_dialog_iv_step_1_margin_top">26dp</dimen>
    <dimen name="screen_dialog_tv_step_1_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_line_height">13dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_start">6dp</dimen>
    <dimen name="screen_dialog_tv_step_1_margin_top">1dp</dimen>
    <dimen name="screen_dialog_iv_step_2_margin_top">12dp</dimen>
    <dimen name="screen_dialog_iv_step_3_margin_top">39dp</dimen>
    <dimen name="screen_dialog_tv_pin_code_margin_top">4dp</dimen>
    <dimen name="screen_dialog_iv_code_width">73dp</dimen>
    <dimen name="screen_dialog_iv_code_height">73dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_top">29dp</dimen>
    <dimen name="screen_dialog_iv_code_margin_end">1dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_top">5dp</dimen>
    <dimen name="screen_dialog_tv_code_margin_end">-2dp</dimen>
    <dimen name="screen_dialog_tv_code_text_size">9sp</dimen>
    <dimen name="screen_dialog_tv_code_line_height">13dp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_margin_top">-1dp</dimen>
    <dimen name="screen_dialog_cv_wired_screen_height">75dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_height">65dp</dimen>
    <dimen name="screen_dialog_rl_wired_screen_margin_top">11dp</dimen>
    <dimen name="screen_dialog_rl_sub_wired_screen_margin_top">25dp</dimen>
    <dimen name="screen_dialog_tv_signal_margin_top">11dp</dimen>
    <dimen name="screen_offset_tv_text_size">12sp</dimen>
    <dimen name="screen_offset_tv_margin_top">240dp</dimen>
    <dimen name="screen_offset_tv_margin_horizontal">40dp</dimen>
    <dimen name="screen_offset_tv_margin_bottom">10dp</dimen>

    <dimen name="small_window_shortcut_width">427dp</dimen>
    <dimen name="small_window_shortcut_height">29dp</dimen>
    <dimen name="small_window_shortcut_tv_desktop_margin_start">11dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_margin_end">21dp</dimen>
    <dimen name="small_window_shortcut_iv_screen_gone_margin_start">131dp</dimen>
    <dimen name="small_window_iv_home_width">13dp</dimen>
    <dimen name="small_window_iv_home_height">13dp</dimen>
    <dimen name="small_window_iv_volume_width">15dp</dimen>
    <dimen name="small_window_iv_volume_height">15dp</dimen>
    <dimen name="small_window_iv_volume_margin_end">5dp</dimen>
    <dimen name="small_window_sv_volume_width">157dp</dimen>
    <dimen name="small_window_sv_volume_height">13dp</dimen>
    <dimen name="small_window_sv_volume_margin_end">11dp</dimen>
    <dimen name="small_window_sv_volume_padding_start">0dp</dimen>
    <dimen name="small_window_sv_volume_padding_end">0dp</dimen>
    <dimen name="small_window_shortcut_text_size">9sp</dimen>
    <dimen name="small_window_shortcut_drawable_padding">3dp</dimen>
    <dimen name="small_window_tv_home_margin_start">16dp</dimen>

    <dimen name="dialog_volume_width">250dp</dimen>
    <dimen name="dialog_volume_height">74dp</dimen>
    <dimen name="dialog_volume_margin_end">80dp</dimen>
    <dimen name="dialog_volume_margin_bottom">228dp</dimen>
    <dimen name="dialog_volume_iv_margin_start">21dp</dimen>
    <dimen name="dialog_volume_sb_margin_start">46dp</dimen>
    <dimen name="dialog_volume_tv_text_size">11sp</dimen>

    <dimen name="toast_width">160dp</dimen>
    <dimen name="toast_height">53dp</dimen>
    <dimen name="toast_padding_horizontal">30dp</dimen>
    <dimen name="toast_padding_vertical">19dp</dimen>
    <dimen name="toast_msg_margin_start">8dp</dimen>
    <dimen name="toast_msg_text_size">12sp</dimen>

    <dimen name="wifi_dialog_input_password_iv_wifi_margin_start">21dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_wifi_margin_top">21dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_title_margin_top">25dp</dimen>
    <dimen name="wifi_dialog_input_password_title_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_width">100dp</dimen>
    <dimen name="wifi_dialog_input_password_title_wifi_name_max_height">13dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_width">197dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_height">29dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_margin_top">11dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_start">8dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_padding_end">30dp</dimen>
    <dimen name="wifi_dialog_input_password_et_password_text_size">9sp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_width">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_height">20dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_top">15dp</dimen>
    <dimen name="wifi_dialog_input_password_iv_eye_margin_start">191dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_text_size">8sp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_start">24dp</dimen>
    <dimen name="wifi_dialog_input_password_tv_password_error_margin_top">8dp</dimen>
    <dimen name="wifi_dialog_input_password_btn_margin_top">20dp</dimen>

    <dimen name="window_ruler_iv_ruler_width">572dp</dimen>
    <dimen name="window_ruler_iv_ruler_height">572dp</dimen>

    <dimen name="spinner_dropdown_item_tv_margin_start">3dp</dimen>
    <dimen name="spinner_dropdown_item_iv_margin_vertical">5dp</dimen>

    <dimen name="privacy_width">337dp</dimen>
    <dimen name="privacy_height">294dp</dimen>
    <dimen name="privacy_margin_top">21dp</dimen>

    <dimen name="toast_resolution_width">83dp</dimen>
    <dimen name="toast_resolution_height">53dp</dimen>
    <dimen name="toast_resolution_margin_start">13dp</dimen>
    <dimen name="toast_resolution_corner_radius">8dp</dimen>
    <dimen name="toast_resolution_tv_text_size">12sp</dimen>

    <dimen name="toast_uhd_width">240dp</dimen>
    <dimen name="toast_uhd_height">77dp</dimen>
    <dimen name="toast_uhd_margin_bottom">94dp</dimen>
    <dimen name="toast_uhd_iv_width">59dp</dimen>
    <dimen name="toast_uhd_iv_height">29dp</dimen>
    <dimen name="toast_uhd_iv_margin_start">13dp</dimen>
    <dimen name="toast_uhd_tv_martin_start">7dp</dimen>
    <dimen name="toast_uhd_tv_text_size">12sp</dimen>

    <dimen name="toast_shutting_down_ops_width">120dp</dimen>
    <dimen name="toast_shutting_down_ops_height">65dp</dimen>
    <dimen name="toast_shutting_down_ops_iv_margin_top">10dp</dimen>
    <dimen name="toast_shutting_down_ops_tv_margin_top">5dp</dimen>
</resources>