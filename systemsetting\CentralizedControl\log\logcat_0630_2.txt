06-30 10:47:04.155   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:47:04.239   342  1902 I chatty  : uid=1000(system) HwBinder:342_3 identical 5 lines
06-30 10:47:04.255   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:47:04.271   650   723 D system_server: process[77] device8 mBtnTouch=1
06-30 10:47:04.272   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:47:04.274   360   360 I android.hardware.power-service.libperfmgr: Power setBoost: INTERACTION duration: 0
06-30 10:47:04.274   360   360 I libperfmgr: Do Powerhint: INTERACTION
06-30 10:47:04.275   360   360 I android.hardware.power-service.libperfmgr: Power setBoost: INTERACTION duration: 0
06-30 10:47:04.275   360   360 I libperfmgr: Do Powerhint: INTERACTION
06-30 10:47:04.281  1565  1588 E RemoteLockCount_: remotex app disable
06-30 10:47:04.284  1155  1246 D UdiServer-SDKService: com.seewo.osservice sendMessage {"paramsJson":"{\"isAllowIntercept\":true,\"on\":true}","requestName":"com.seewo.sdk.internal.command.device.CmdSetScreenStatus"}
06-30 10:47:04.286  1155  1246 D UdiServer-App: onRequest: POST v1/system/screen/status
06-30 10:47:04.287  1155  1246 D UdiServer-Transfer: before process request: com.ifpdos.udi.base.UdiRequest@3aedf1d@v1/system/screen/status[c9d08e1f-4342-4c56-a8d6-04f11f3b9896]
06-30 10:47:04.287  1155  1246 D UdiServer-Transfer-BinderHttpd: process POST v1/system/screen/status/set
06-30 10:47:04.289   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:47:04.290  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.290  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: │ on POST : v1/system/screen/status/set
06-30 10:47:04.290  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.292  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.293  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: │ com.seewo.osservice isMute: SetScreenStatusBody(isOn=true, isAllowIntercept=true, isMute=null)
06-30 10:47:04.294  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.296   331   501 I SystemControl: getPanelPower: panelstatus = 1
06-30 10:47:04.298  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.299  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: │ on POST v1/system/screen/status/set : response(200,) , duration 8
06-30 10:47:04.299  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:47:04.301  1155  1246 D UdiServer-Transfer: after process request: com.ifpdos.udi.base.UdiRequest@3aedf1d@v1/system/screen/status[c9d08e1f-4342-4c56-a8d6-04f11f3b9896], response: UdiResponse{OK, }, duration: 15
06-30 10:47:04.303  1155  1246 D UdiServer-SDKService: {"paramsJson":"{\"result\":true}","responseName":"RespBooleanResult","status":"SUCCESS"}