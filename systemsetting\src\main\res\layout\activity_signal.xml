<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/main_width"
    android:layout_height="@dimen/main_height"
    android:minHeight="@dimen/main_height">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/iv_back_width"
        android:layout_height="@dimen/iv_back_height"
        android:layout_marginStart="@dimen/iv_back_margin_start"
        android:layout_marginTop="@dimen/iv_back_margin_top"
        android:importantForAccessibility="no"
        android:src="@drawable/select_left_icon" />

    <TextView
        android:id="@+id/txt_title"
        style="@style/Title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/setting_tv_title_margin_top"
        android:text="@string/sign" />

    <View
        android:id="@+id/line1"
        style="@style/Line"
        android:layout_below="@id/iv_back"
        android:layout_marginTop="@dimen/setting_line1_margin_top" />

    <FrameLayout
        android:id="@+id/fl_windows"
        style="@style/Signal_FrameLayout"
        android:layout_below="@id/line1"
        android:layout_marginTop="@dimen/signal_tv_window_margin_top">

        <ImageView
            android:id="@+id/iv_windows"
            style="@style/Signal_ImageView"
            android:src="@drawable/select_signal_windows_icon" />

        <TextView
            android:id="@+id/txt_windows"
            style="@style/Signal_Text"
            android:text="@string/windows" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_typec"
        style="@style/Signal_FrameLayout"
        android:layout_below="@id/fl_windows"
        android:layout_marginTop="@dimen/signal_item_margin_top">

        <ImageView
            android:id="@+id/iv_typec"
            style="@style/Signal_ImageView"
            android:src="@drawable/select_signal_typec_icon" />

        <TextView
            android:id="@+id/txt_typec"
            style="@style/Signal_Text"
            android:text="@string/front_typec" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_behind_hdmi1"
        style="@style/Signal_FrameLayout"
        android:layout_below="@id/fl_typec"
        android:layout_marginTop="@dimen/signal_item_margin_top">

        <ImageView
            android:id="@+id/iv_behind_hdmi1"
            style="@style/Signal_ImageView"
            android:src="@drawable/select_signal_hdmi_icon" />

        <TextView
            android:id="@+id/txt_behind_hdmi1"
            style="@style/Signal_Text"
            android:text="@string/behind_hdmi1" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_behind_hdmi2"
        style="@style/Signal_FrameLayout"
        android:layout_below="@id/fl_behind_hdmi1"
        android:layout_marginTop="@dimen/signal_item_margin_top">

        <ImageView
            android:id="@+id/iv_behind_hdmi2"
            style="@style/Signal_ImageView"
            android:src="@drawable/select_signal_hdmi_icon" />

        <TextView
            android:id="@+id/txt_behind_hdmi2"
            style="@style/Signal_Text"
            android:text="@string/behind_hdmi2" />
    </FrameLayout>
</RelativeLayout>