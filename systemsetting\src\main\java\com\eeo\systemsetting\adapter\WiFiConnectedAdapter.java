package com.eeo.systemsetting.adapter;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.wifi.AccessPoint;

import java.lang.reflect.Method;
import java.util.List;

public class WiFiConnectedAdapter extends RecyclerView.Adapter<WiFiConnectedAdapter.ConnectViewHolder> {
    public static final String TAG = "ConnectedAdapter===";
    private List<WifiInfo> list;
    private NetworkInfo networkInfo;
    private Context context;

    private WifiManager mWifiManager;

    private long mLastConnectedNoInternetTime = 0;
    private String mSsid;

    public WiFiConnectedAdapter(Context context) {
        this.context = context;
        mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
    }


    public void setConnectedWifi(List<WifiInfo> list, NetworkInfo networkInfo) {
        this.list = list;
        this.networkInfo = networkInfo;
        notifyDataSetChanged();
    }


    @NonNull
    @Override
    public ConnectViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ConnectViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ConnectViewHolder holder, int position) {
        WifiInfo wifiInfo = list.get(position);
        if (mSsid == null || !mSsid.equals(wifiInfo.getSSID())) {
            mSsid = wifiInfo.getSSID();
            mLastConnectedNoInternetTime = 0;
        }
        holder.txtWifiName.setText(mSsid.replaceAll("\"", ""));
        holder.tvState.setVisibility(View.VISIBLE);
        holder.tvState.setTextColor(context.getColor(R.color.press_color));
//        networkInfo.getState()
//        holder.tvState.setText(R.string.network_wifi_status_connected);
        if (networkInfo != null) {
            holder.tvState.setText(getConnectedState(networkInfo.getState()));
        } else {
            holder.tvState.setText(context.getString(R.string.disconnect));
        }

        if (havePassword(wifiInfo)) {
            holder.imgPassword.setVisibility(View.VISIBLE);
        } else {
            holder.imgPassword.setVisibility(View.GONE);
        }

        //信号强度
        int level = AccessPoint.calculateSignalLevel(wifiInfo.getRssi(), AccessPoint.SIGNAL_LEVELS);

        if (level <= 1) {
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_1);
        } else if (level == 2) {
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_2);
        } else if (level == 3) {
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_3);
        } else {
            holder.imgRank.setBackgroundResource(R.drawable.ic_wifi_1);
        }

        holder.imgMore.setOnClickListener(v -> {
            enterToWiFiMore(wifiInfo);
        });

        holder.line.setVisibility(View.INVISIBLE);

    }

    public void enterToWiFiMore(WifiInfo wifiInfo) {
        Log.i(TAG, "enterToWiFiMore: ");
        Intent intent = new Intent();
        intent.setAction(Constant.ACTION_WIFI_MORE);
        intent.putExtra(Constant.ACTION_WIFI_KEY, wifiInfo);
        context.sendBroadcast(intent);
    }

    /**
     * 判断当前是否有网络连接,但是如果该连接的网络无法上网，也会返回true
     *
     * @param context
     * @return
     */
    private boolean isNetConnection(Context context, NetworkInfo networkInfo) {
        if (context != null) {
            ConnectivityManager cm = (ConnectivityManager)
                    context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            Network network;
            try {
                Method getCurrentNetwork = WifiManager.class.getMethod("getCurrentNetwork");
                network = (Network) getCurrentNetwork.invoke(mWifiManager);
            } catch (Exception e) {
                network = null;
            }
            NetworkCapabilities networkCapabilities = cm.getNetworkCapabilities(network);
            if (networkCapabilities != null && !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                return false;
            }
        }
        return true;
    }

    private boolean havePassword(WifiInfo wifiInfo) {
        List<ScanResult> scanResults = mWifiManager.getScanResults();
        String ssid = wifiInfo.getSSID().replaceAll("\"", "");
        for (ScanResult scanResult : scanResults) {
            if (scanResult.SSID.equals(ssid)) {
                if (!scanResult.capabilities.contains("WEP")
                        && !scanResult.capabilities.contains("PSK")
                        && !scanResult.capabilities.contains("EAP")) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    private String getConnectedState(NetworkInfo.State state) {
        String msg = "";
        if (state == NetworkInfo.State.CONNECTING) {
            msg = context.getString(R.string.network_wifi_status_connecting);
            mLastConnectedNoInternetTime = 0;
        } else if (state == NetworkInfo.State.CONNECTED) {
            if (isNetConnection(context, networkInfo)) {
                mLastConnectedNoInternetTime = 0;
                msg = context.getString(R.string.network_wifi_status_connected);
            } else {
                long currentTime = System.currentTimeMillis();
                if (mLastConnectedNoInternetTime != 0 && currentTime - mLastConnectedNoInternetTime > 3000) {
                    msg = context.getString(R.string.network_wifi_status_connected_no_internet);
                } else {
                    mLastConnectedNoInternetTime = currentTime;
                    msg = context.getString(R.string.network_wifi_status_connected);
                }
            }
        } else {
            mLastConnectedNoInternetTime = 0;
            msg = context.getString(R.string.network_wifi_status_connecting);
        }
        return msg;
    }


    static class ConnectViewHolder extends RecyclerView.ViewHolder {
        TextView txtWifiName;
        ImageView imgPassword;
        ImageView imgRank;
        ImageView imgMore;
        TextView tvState;
        View line;

        public ConnectViewHolder(View itemView) {
            super(itemView);
            txtWifiName = itemView.findViewById(R.id.txt_wifi_name);
            imgPassword = itemView.findViewById(R.id.img_password);
            imgRank = itemView.findViewById(R.id.img_rank);
            imgMore = itemView.findViewById(R.id.img_more);
            tvState = itemView.findViewById(R.id.txt_state);
            line = itemView.findViewById(R.id.line);
        }
    }
}
