package com.eeo.ota.arraymic;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.util.SharedPreferencesUtil;
import java.io.File;
import java.util.HashMap;

// Use the correct SystemControlManager as specified by the user.
import com.droidlogic.app.SystemControlManager;

public class ArrayMicFirmwareUpdater {

    // Interface for polling conditions to avoid lambda issues
    private interface PollingCondition {
        boolean check();
    }

    private static final String TAG = "ArrayMicOTA"; // Unified TAG

    // Constants for device detection
    private static final int USB_VENDOR_ID = 0x2207;
    private static final int USB_PRODUCT_ID = 0x0019;
    private static final String ADB_DEVICE_SERIAL = "303_usb_device";

    // Constants for polling mechanism
    private static final int POLL_INTERVAL_MS = 2000; // 2 seconds
    private static final int USB_DETECT_TIMEOUT_MS = 20000; // 20 seconds
    private static final int ADB_DETECT_TIMEOUT_MS = 10000; // 10 seconds
    private static final int REBOOT_DETECT_TIMEOUT_MS = 70000; // 70 seconds

    // Switch to enable/disable the check for a specific problematic firmware version.
    private static final boolean ENABLE_SPECIFIC_VERSION_CHECK = true;
    // The specific firmware version that requires a forced update.
    private static final String SPECIFIC_ERROR_VERSION = "QH303_QSOUND_20231110001";

    private final Context mContext;
    private final Handler mHandler;
    private final SubDeviceUpdateCallback mCallback;
    private final AdbHelper mAdbHelper;

    private String mTargetVersion;
    private String mFirmwarePath;
    private String mFirmwareFileName;

    private int mPushRetryCount = 0;
    private static final int MAX_PUSH_RETRIES = 3;
    private int mSwitchRetryCount = 0;
    private static final int MAX_SWITCH_RETRIES = 2;

    // ADB detection retry counter
    private int mAdbDetectRetryCount = 0;
    private static final int MAX_ADB_DETECT_RETRIES = 2;

    // Overall upgrade retry counter
    private int mOverallRetryCount = 0;
    private static final int MAX_OVERALL_RETRIES = 3;

    // ADB detection retry delay
    private static final int ADB_RETRY_DELAY_MS = 3000; // 3 seconds

    private enum UpdateState {
        IDLE,
        INITIAL_DELAY,
        SWITCHING_USB,
        DETECTING_USB,
        DETECTING_ADB,
        CHECKING_VERSION,
        STOPPING_SERVICE,
        CLEANING_REMOTE_DIR,
        PUSHING_FIRMWARE,
        VALIDATING_FIRMWARE,
        DELETING_USER_DATA,
        EXECUTING_UPGRADE,
        MONITORING_REBOOT_DISCONNECT,
        MONITORING_REBOOT_CONNECT,
        FINAL_VERSION_VALIDATION,
        CLEANUP
    }

    private UpdateState mCurrentState = UpdateState.IDLE;

    public ArrayMicFirmwareUpdater(Context context, SubDeviceUpdateCallback callback) {
        this.mContext = context.getApplicationContext();
        this.mCallback = callback;
        this.mHandler = new Handler(Looper.getMainLooper());
        this.mAdbHelper = new AdbHelper(context);
    }

    public void startUpdate() {
        startUpdateWithRetry();
    }

    public void startUpdateWithRetry() {
        mOverallRetryCount = 0;
        startUpdateInternal();
    }

    private void startUpdateInternal() {
        mOverallRetryCount++;
        Log.i(TAG, "Starting Array Mic update process... (Overall attempt " + mOverallRetryCount + "/" + MAX_OVERALL_RETRIES + ")");
        mPushRetryCount = 0;
        mSwitchRetryCount = 0;
        mAdbDetectRetryCount = 0;

        // Only wait 10 seconds for the first attempt, then skip the delay and go directly to USB switch.
        if (mOverallRetryCount == 1) {
            mCurrentState = UpdateState.INITIAL_DELAY;
        } else {
            Log.i(TAG, "Retry attempt, skipping initial delay and going directly to USB switch...");
            mCurrentState = UpdateState.SWITCHING_USB;
        }
        executeNextStep();
    }

    private void executeNextStep() {
        Log.d(TAG, "Executing state: " + mCurrentState);
        switch (mCurrentState) {
            case INITIAL_DELAY:
                Log.i(TAG, "Initial 10-second delay before starting USB switch...");
                mHandler.postDelayed(() -> {
                    mCurrentState = UpdateState.SWITCHING_USB;
                    executeNextStep();
                }, 10000);
                break;
            case SWITCHING_USB:
                mSwitchRetryCount++;
                Log.i(TAG, "Attempt " + mSwitchRetryCount + "/" + MAX_SWITCH_RETRIES + " to switch USB to SOC...");
                new Thread(() -> {
                    try {
                        Log.d(TAG, "Executing system command: sample_xml_usbsw s side SOC");
                        SystemControlManager.getInstance().systemCmd("sample_xml_usbsw s side SOC");
                        mHandler.postDelayed(() -> {
                            mCurrentState = UpdateState.DETECTING_USB;
                            executeNextStep();
                        }, 2000); 
                    } catch (Exception e) {
                        Log.e(TAG, "System command failed: sample_xml_usbsw s side SOC", e);
                        fail("Command failed: sample_xml_usbsw s side SOC");
                    }
                }).start();
                break;
            case DETECTING_USB:
                pollWithTimeout(USB_DETECT_TIMEOUT_MS, this::detectUsbDevice, () -> {
                    mCurrentState = UpdateState.DETECTING_ADB;
                    executeNextStep();
                }, () -> {
                    if (mSwitchRetryCount < MAX_SWITCH_RETRIES) {
                        Log.w(TAG, "USB detection failed on attempt " + mSwitchRetryCount + ". Retrying immediately...");
                        mCurrentState = UpdateState.SWITCHING_USB; 
                        executeNextStep();
                    } else {
                        fail("USB device detection failed after " + MAX_SWITCH_RETRIES + " attempts.");
                    }
                });
                break;
            case DETECTING_ADB:
                pollWithTimeout(ADB_DETECT_TIMEOUT_MS, this::detectAdbDevice, () -> {
                    mCurrentState = UpdateState.CHECKING_VERSION;
                    executeNextStep();
                }, () -> {
                    mAdbDetectRetryCount++;
                    if (mAdbDetectRetryCount < MAX_ADB_DETECT_RETRIES) {
                        Log.w(TAG, "ADB detection failed on attempt " + mAdbDetectRetryCount + "/" + MAX_ADB_DETECT_RETRIES + ". Retrying after delay...");
                        mHandler.postDelayed(() -> {
                            mCurrentState = UpdateState.DETECTING_ADB;
                            executeNextStep();
                        }, ADB_RETRY_DELAY_MS);
                    } else {
                        Log.w(TAG, "ADB detection failed after " + MAX_ADB_DETECT_RETRIES + " attempts. Checking USB device status...");
                        // Check if the USB device still exists
                        if (detectUsbDevice()) {
                            Log.w(TAG, "USB device still exists, but ADB detection failed. This may be a device issue.");
                            retryOverallUpdate("ADB device detection failed after " + MAX_ADB_DETECT_RETRIES + " attempts, but USB device exists");
                        } else {
                            Log.w(TAG, "USB device lost. Array mic may have been switched by other code.");
                            retryOverallUpdate("USB device lost during ADB detection");
                        }
                    }
                });
                break;
            case CHECKING_VERSION:
                checkVersionAndDecide();
                break;
            case STOPPING_SERVICE:
                executeAdbCommandInThread("shell /usr/bin/qdreamer/qsound/kill_sound.sh", UpdateState.DELETING_USER_DATA, 2000);
                break;
            case DELETING_USER_DATA:
                executeAdbCommandInThread("shell rm -rf /overlay/upper/usr/bin/qdreamer/*", UpdateState.CLEANING_REMOTE_DIR, 2000);
                break;
            case CLEANING_REMOTE_DIR:
                executeAdbCommandInThread("shell rm -rf /mnt/UDISK/*", UpdateState.PUSHING_FIRMWARE, 2000);
                break;
            case PUSHING_FIRMWARE:
                executeAdbCommandInThread("push " + mFirmwarePath + " /mnt/UDISK/", UpdateState.VALIDATING_FIRMWARE, 0);
                break;
            case VALIDATING_FIRMWARE:
                validateFirmware();
                break;
            case EXECUTING_UPGRADE:
                String upgradeCmd = "shell swupdate_cmd.sh -i /mnt/UDISK/" + mFirmwareFileName + " -e stable,upgrade_recovery";
                executeAdbCommandInThread(upgradeCmd, UpdateState.MONITORING_REBOOT_DISCONNECT, 0);
                break;
            
            case MONITORING_REBOOT_DISCONNECT:
                pollWithTimeout(REBOOT_DETECT_TIMEOUT_MS, new PollingCondition() {
                    @Override
                    public boolean check() {
                        return monitorDisconnect();
                    }
                }, new Runnable() {
                    @Override
                    public void run() {
                        mCurrentState = UpdateState.MONITORING_REBOOT_CONNECT;
                        executeNextStep();
                    }
                }, new Runnable() {
                    @Override
                    public void run() {
                        fail("Device did not disconnect for reboot within timeout");
                    }
                });
                break;

            case MONITORING_REBOOT_CONNECT:
                pollWithTimeout(REBOOT_DETECT_TIMEOUT_MS, this::monitorConnect, () -> {
                    mCurrentState = UpdateState.FINAL_VERSION_VALIDATION;
                    // Device reconnected, validate immediately.
                    executeNextStep();
                }, () -> fail("Device did not reconnect after reboot within timeout"));
                break;

            case FINAL_VERSION_VALIDATION:
                validateFinalVersion();
                break;

            case CLEANUP:
                Log.i(TAG, "Update process finished. Starting cleanup...");
                cleanupAndFinish();
                break;
        }
    }

    private void executeAdbCommandInThread(final String command, final UpdateState nextState, final long delayAfter) {
        new Thread(() -> {
            String fullCommand = "adb -s " + ADB_DEVICE_SERIAL + " " + command;
            final boolean success = mAdbHelper.executeCommandAndWait(fullCommand);
            mHandler.post(() -> {
                if (success) {
                    mHandler.postDelayed(() -> {
                        mCurrentState = nextState;
                        executeNextStep();
                    }, delayAfter);
                } else {
                    // Check if the USB device still exists
                    Log.w(TAG, "ADB command failed: " + fullCommand + ". Checking USB device status...");
                    boolean usbExists = detectUsbDevice();
                    Log.i(TAG, "USB device exists after ADB command failure: " + usbExists);

                    if (usbExists) {
                        retryOverallUpdate("ADB command failed but USB device exists: " + fullCommand);
                    } else {
                        retryOverallUpdate("ADB command failed and USB device lost: " + fullCommand);
                    }
                }
            });
        }).start();
    }

    private boolean isVersionLower(String current, String target) {
        // Only compare versions that follow the expected "A<number>" format.
        if (current == null || !current.startsWith("A") || target == null || !target.startsWith("A")) {
            return false;
        }
        try {
            // Safely extract numbers after 'A' and compare.
            int currentNum = Integer.parseInt(current.replaceAll("^A", ""));
            int targetNum = Integer.parseInt(target.replaceAll("^A", ""));
            return currentNum < targetNum;
        } catch (NumberFormatException e) {
            Log.e(TAG, "Failed to parse version number for comparison: " + current + " vs " + target, e);
            return false;
        }
    }
    
    private void checkVersionAndDecide() {
        if (!parseConfig()) {
            fail("Failed to parse config file.");
            return;
        }

        String currentVersion = mAdbHelper.executeShellCommandWithOutput("adb -s " + ADB_DEVICE_SERIAL + " shell cat /usr/bin/qdreamer/qsound/version.txt");

        if (currentVersion == null || currentVersion.trim().isEmpty()) {
            fail("Failed to get current version from device.");
            return;
        }
        currentVersion = currentVersion.trim();

        boolean isLower = isVersionLower(currentVersion, mTargetVersion);
        
        boolean isSpecificError = false;
        if (ENABLE_SPECIFIC_VERSION_CHECK) {
            isSpecificError = SPECIFIC_ERROR_VERSION.equals(currentVersion);
        }

        Log.i(TAG, "Current version: " + currentVersion + ", Target version: " + mTargetVersion);
        Log.i(TAG, "Is version lower? " + isLower + ". Is specific error version? " + isSpecificError);

        if (isLower || isSpecificError) {
            Log.i(TAG, "Update required. Proceeding with update...");
            mCurrentState = UpdateState.STOPPING_SERVICE;
            executeNextStep();
        } else {
            Log.i(TAG, "No update required. Cleaning up...");
            // No upgrade required, set SP Key to successful status
            Log.d(TAG, "ArrayMicOTA: Setting SP key to completed (no update needed)");
            SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, true);
            mCurrentState = UpdateState.CLEANUP;
            executeNextStep();
        }
    }

    private boolean parseConfig() {
        try {
            java.io.InputStream is = new java.io.FileInputStream("/system/ota/mic_qdreamer/mic_config.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            String json = new String(buffer, "UTF-8");
            
            org.json.JSONArray jsonArray = new org.json.JSONArray(json);
            if (jsonArray.length() > 0) {
                // Always use the last entry in the JSON array, as it is the latest version.
                org.json.JSONObject config = jsonArray.getJSONObject(jsonArray.length() - 1);
                mTargetVersion = config.getString("version");
                mFirmwareFileName = config.getString("firmware_file");
                mFirmwarePath = config.getString("firmware_path");
                
                Log.d(TAG, "Config parsed: version=" + mTargetVersion + ", file=" + mFirmwareFileName);
                return mTargetVersion != null && mFirmwarePath != null && mFirmwareFileName != null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse /system/ota/mic_qdreamer/mic_config.json", e);
        }
        return false;
    }

    private boolean detectUsbDevice() {
        UsbManager usbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        if (usbManager == null) {
            Log.e(TAG, "UsbManager not available for device detection");
            return false;
        }

        HashMap<String, UsbDevice> deviceHashMap = usbManager.getDeviceList();
        Log.d(TAG, "Checking USB devices. Total devices found: " + deviceHashMap.size());

        for (UsbDevice usbDevice : deviceHashMap.values()) {
            if (usbDevice.getVendorId() == USB_VENDOR_ID && usbDevice.getProductId() == USB_PRODUCT_ID) {
                Log.i(TAG, "Found target device with VID: " + USB_VENDOR_ID + ", PID: " + USB_PRODUCT_ID);
                return true;
            }
        }
        Log.w(TAG, "Target USB device not found (VID: " + USB_VENDOR_ID + ", PID: " + USB_PRODUCT_ID + ")");
        return false;
    }

    private boolean detectAdbDevice() {
        String result = mAdbHelper.executeShellCommandWithOutput("adb devices");
        if (result != null && result.contains(ADB_DEVICE_SERIAL)) {
            Log.i(TAG, "ADB device detected.");
            return true;
        }
        return false;
    }
    
    private void validateFirmware() {
        File localFile = new File(mFirmwarePath);
        if (!localFile.exists()) {
            fail("Local firmware file not found: " + mFirmwarePath);
            return;
        }
        long localSize = localFile.length();
        long remoteSize = mAdbHelper.getRemoteFileSize("/mnt/UDISK/" + mFirmwareFileName);

        Log.d(TAG, "Validating firmware size. Local: " + localSize + ", Remote: " + remoteSize);

        if (localSize == remoteSize && localSize > 0) {
            Log.i(TAG, "Firmware validation successful.");
            mCurrentState = UpdateState.EXECUTING_UPGRADE;
            executeNextStep();
        } else {
            mPushRetryCount++;
            if (mPushRetryCount >= MAX_PUSH_RETRIES) {
                fail("Firmware validation failed after " + MAX_PUSH_RETRIES + " retries.");
            } else {
                Log.w(TAG, "Firmware validation failed. Retrying push... (Attempt " + (mPushRetryCount + 1) + ")");
                mCurrentState = UpdateState.CLEANING_REMOTE_DIR;
                executeNextStep();
            }
        }
    }

    private boolean monitorDisconnect() {
        String result = mAdbHelper.executeShellCommandWithOutput("adb devices");
        if (result == null || !result.contains(ADB_DEVICE_SERIAL)) {
            Log.i(TAG, "Device disconnected for reboot.");
            return true;
        }
        return false;
    }

    private boolean monitorConnect() {
        String result = mAdbHelper.executeShellCommandWithOutput("adb devices");
        if (result != null && result.contains(ADB_DEVICE_SERIAL)) {
            Log.i(TAG, "Device reconnected after reboot.");
            return true;
        }
        return false;
    }

    private void validateFinalVersion() {
        Log.d(TAG, "Validating final version...");
        String newVersion = mAdbHelper.executeShellCommandWithOutput("adb -s " + ADB_DEVICE_SERIAL + " shell cat /usr/bin/qdreamer/qsound/version.txt");

        if (newVersion != null && newVersion.trim().equals(mTargetVersion)) {
            Log.i(TAG, "Update successful! New version: " + newVersion.trim());
            // Set SP Key to successful status
            Log.d(TAG, "ArrayMicOTA: Setting SP key to completed (success)");
            SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, true);
            if (mCallback != null) {
                mCallback.onUpdateSuccess();
            }
        } else {
            fail("Final version validation failed. Expected " + mTargetVersion + ", but got " + (newVersion != null ? newVersion.trim() : "null"));
        }
        mCurrentState = UpdateState.CLEANUP;
        executeNextStep();
    }


    private void pollWithTimeout(long timeout, PollingCondition condition, Runnable onSuccess, Runnable onFail) {
        final long startTime = System.currentTimeMillis();

        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (mCurrentState == UpdateState.IDLE) { // Stop polling if updater was released
                    return;
                }

                if (System.currentTimeMillis() - startTime > timeout) {
                    if (onFail != null) {
                        onFail.run();
                    }
                    return;
                }

                if (condition.check()) {
                    onSuccess.run();
                } else {
                    mHandler.postDelayed(this, POLL_INTERVAL_MS);
                }
            }
        });
    }

    private void cleanupAndFinish() {
        final int maxRetries = 3;
        final int[] retryCount = {0};
        attemptSwitchAndPoll(retryCount, maxRetries);
    }

    private void attemptSwitchAndPoll(final int[] retryCount, final int maxRetries) {
        retryCount[0]++;
        Log.d(TAG, "Attempt " + retryCount[0] + "/" + maxRetries + " to switch USB to PC.");

        new Thread(() -> {
            try {

                Log.d(TAG, "Executing system command: sample_xml_usbsw s side PC");
                SystemControlManager.getInstance().systemCmd("sample_xml_usbsw s side PC");

                // Switch back to the main thread to start polling for disconnect.
                mHandler.post(() -> {
                    pollWithTimeout(
                        10000, // 10-second timeout for verification.
                        new PollingCondition() { // Condition: Check if device is gone
                            @Override
                            public boolean check() {
                                return monitorDisconnect();
                            }
                        },
                        new Runnable() { // onSuccess:
                            @Override
                            public void run() {
                                Log.i(TAG, "Cleanup successful. USB switched to PC and device disconnected.");
                                if (mCallback != null) {
                                    mCallback.onAllUpdateFinish();
                                }
                            }
                        },
                        new Runnable() { // onFail (polling timed out):
                            @Override
                            public void run() {
                                Log.w(TAG, "Failed to verify disconnect on attempt " + retryCount[0]);
                                if (retryCount[0] < maxRetries) {
                                    // Retry by calling the wrapper method again. This is safe and robust.
                                    attemptSwitchAndPoll(retryCount, maxRetries);
                                } else {
                                    Log.e(TAG, "Cleanup failed. Could not verify disconnect after " + maxRetries + " attempts.");
                                    if (mCallback != null) {
                                        // Still call finish to stop the service
                                        mCallback.onAllUpdateFinish();
                                    }
                                }
                            }
                        }
                    );
                });
            } catch (Exception e) {
                // If the command itself fails, we should stop and report failure.
                Log.e(TAG, "Failed to execute command to switch to PC on attempt " + retryCount[0], e);
                fail("Failed to execute cleanup command: sample_xml_usbsw s side PC");
            }
        }).start();
    }

    private void retryOverallUpdate(String reason) {
        if (mOverallRetryCount < MAX_OVERALL_RETRIES) {
            Log.w(TAG, "Overall update attempt " + mOverallRetryCount + " failed: " + reason + ". Retrying...");
            // Delay by 2 seconds and retry the overall upgrade process
            mHandler.postDelayed(() -> {
                startUpdateInternal();
            }, 2000);
        } else {
            fail("Update failed after " + MAX_OVERALL_RETRIES + " overall attempts. Last reason: " + reason);
        }
    }

    private void fail(String message) {
        // Prevent fail() from triggering cleanup recursively
        if (mCurrentState != UpdateState.CLEANUP) {
            Log.e(TAG, "Update failed: " + message);
            // Set SP Key to failed status
            Log.d(TAG, "ArrayMicOTA: Setting SP key to not completed (failed)");
            SharedPreferencesUtil.setArrayMicUpdateCompleted(mContext, false);
            if (mCallback != null) {
                mCallback.onUpdateFail(message);
            }
            mCurrentState = UpdateState.CLEANUP;
            executeNextStep();
        }
    }

    public void release() {
        Log.d(TAG, "Releasing resources.");
        mHandler.removeCallbacksAndMessages(null);
        mCurrentState = UpdateState.IDLE;
    }
}
