package cn.eeo.classin.setup;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.CompoundButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;

import com.elvishew.xlog.XLog;

import cn.eeo.classin.setup.constant.SetupKeyTag;
import cn.eeo.classin.setup.databinding.BasicSettingBinding;

public class BasicSettingFragment extends Fragment {

    private static final String TAG = "BasicSettingFragment";
    SharedPreferences preferences;
    SharedPreferences.Editor editor;
    private @NonNull
    BasicSettingBinding binding;
    RadioButton radioButton;

    public BasicSettingFragment() {
        // Required empty public constructor
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment

        binding = BasicSettingBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        preferences = getContext().getSharedPreferences(SetupKeyTag.SHAERED_TAG, Context.MODE_PRIVATE);
        editor = preferences.edit();
        binding.backIc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Navigation.findNavController(view).navigateUp();
            }
        });

        binding.confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //隐藏键盘
                InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                if (editor != null) {
                    String organization_txt = binding.organizationEtId.getText().toString();
                    String addr_txt = binding.deviceAddrEtId.getText().toString();
                    if (radioButton == null) {
                        Toast.makeText(getContext(), getString(R.string.no_select_type_tips), Toast.LENGTH_SHORT).show();
                        return;
                    }
                    if (organization_txt != null && !organization_txt.isEmpty()) {
                        // 字符串不为空
                        if (organization_txt.length() > 20) {
                            //字符串长度不能大于20
                            Toast.makeText(getContext(), getString(R.string.long_name_tips), Toast.LENGTH_SHORT).show();
                            return;
                        }else if (containsSpecialCharacter(organization_txt)) {
                            //包含特殊字符串
                            Toast.makeText(getContext(), getString(R.string.special_character_tips), Toast.LENGTH_SHORT).show();
                            return;
                        }
                    } else {
                        // 字符串为空
                        //没有填写名称提示
                        Toast.makeText(getContext(), getString(R.string.no_name_tips), Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (addr_txt != null && !addr_txt.isEmpty()) {
                        if (addr_txt.length() > 10) {
                            Toast.makeText(getContext(), getString(R.string.long_string_tips), Toast.LENGTH_SHORT).show();
                            return;
                        } else if (containsSpecialCharacter(addr_txt)) {
                            //包含特殊字符串
                            Toast.makeText(getContext(), getString(R.string.special_character_tips), Toast.LENGTH_SHORT).show();
                            return;
                        }
                    } else {
                        //没有填写地址提示
                        Toast.makeText(getContext(), getString(R.string.no_address_tips), Toast.LENGTH_SHORT).show();
                        return;

                    }

                    editor.putString(SetupKeyTag.SHAERED_KEY_ORGANIZATION, organization_txt);
                    editor.putString(SetupKeyTag.SHAERED_KEY_ADDRESS, addr_txt);
                    editor.apply();
                    NavHostFragment.findNavController(BasicSettingFragment.this)
                            .navigate(R.id.action_BasicSettingFragment_to_UserAgreementFragment);

                    //startSetting();
                    //finishSetupWizard();
                    //getActivity().
                }
                //requireActivity().finish();
            }
        });
        binding.educationRadio.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int checkedId) {
                radioButton = getView().findViewById(checkedId); // 获取选中的 RadioButton ID
                if (radioButton != null) {
                    // 执行相应的操作
                    // 例如，获取选中的 RadioButton 文本
                    String selectedText = radioButton.getText().toString();
                    XLog.d( "selectedText:" + selectedText);

                    // 或执行其他逻辑
                    // 例如，根据选中的 RadioButton 执行相应的操作
                }
                switch (checkedId) {
                    case R.id.education_id:
                        editor.putString(SetupKeyTag.SHAERED_KEY_USERTYPE, "education");
                        break;
                    case R.id.meetting_id:
                        editor.putString(SetupKeyTag.SHAERED_KEY_USERTYPE, "meetting");
                        break;
                    default:
                        editor.putString(SetupKeyTag.SHAERED_KEY_USERTYPE, "unknow");
                        break;
                }
            }
        });
        //设置投屏开关的默认状态
        Settings.System.putInt(getContext().getContentResolver(), "projection_status", 1);
        binding.projectionButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    Settings.System.putInt(getContext().getContentResolver(), "projection_status", 1);
                    XLog.d("GET projection_status:" + Settings.System.getInt(getContext().getContentResolver(), "projection_status", 1));
                    XLog.d( "GET projection_status:" + Settings.System.getInt(getContext().getContentResolver(), "projection_status", 1));
                } else {
                    Settings.System.putInt(getContext().getContentResolver(), "projection_status", 0);
                    XLog.d( "GET projection_status:" + Settings.System.getInt(getContext().getContentResolver(), "projection_status", 1));
                }
            }
        });
    }

    private void setprojection() {

        Settings.System.putInt(this.getContext().getContentResolver(), "xxx", 1);
    }


    public static boolean containsSpecialCharacter(String str) {
        // 正则表达式，表示除了字母和数字之外的任意字符
        String regex = "[^a-zA-Z0-9\\u4E00-\\u9FA5]";

        // 判断字符串中是否包含特殊字符
        return str.matches(".*" + regex + ".*");
    }

}