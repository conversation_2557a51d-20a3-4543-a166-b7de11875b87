2025-08-01 18:50:57.256   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Touch update not needed, checking SP key for array mic update
2025-08-01 18:50:57.256   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Checking SP key: array mic update completed = false
2025-08-01 18:50:57.256   868-868   ArrayMicOTA             com.eeo.systemsetting                D  SP key indicates array mic update needed, starting service
2025-08-01 18:50:57.256   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update service in systemsetting module
2025-08-01 18:50:57.259   868-868   ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.launcher.FallbackHomeActivity.startArrayMicUpdateService:283 com.eeo.systemsetting.launcher.FallbackHomeActivity.maybeFinish:149 com.eeo.systemsetting.launcher.FallbackHomeActivity.onCreate:120 
2025-08-01 18:50:57.307   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Proceeding to TifPlayerActivity
2025-08-01 18:50:57.310   868-929   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@fff97b6 className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-08-01 18:50:57.313   868-896   ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@fff97b6 startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-08-01 18:50:57.596   868-868   ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-08-01 18:50:57.597   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-08-01 18:50:57.634   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-08-01 18:50:57.636   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update process via updater...
2025-08-01 18:50:57.735   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process... (Overall attempt 1/3)
2025-08-01 18:50:57.736   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: INITIAL_DELAY
2025-08-01 18:50:57.743   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Initial 10-second delay before starting USB switch...
2025-08-01 18:51:07.861   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-08-01 18:51:07.861   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Attempt 1/2 to switch USB to SOC...
2025-08-01 18:51:07.874   868-2065  ArrayMicOTA             com.eeo.systemsetting                D  Executing system command: sample_xml_usbsw s side SOC
2025-08-01 18:51:12.386   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_USB
2025-08-01 18:51:12.426   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 4
2025-08-01 18:51:12.426   868-868   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 18:51:14.433   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-01 18:51:14.433   868-868   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 18:51:18.961   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 5
2025-08-01 18:51:18.961   868-868   ArrayMicOTA             com.eeo.systemsetting                W  Target USB device not found (VID: 8711, PID: 25)
2025-08-01 18:51:20.967   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Checking USB devices. Total devices found: 7
2025-08-01 18:51:20.968   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Found target device with VID: 8711, PID: 25
2025-08-01 18:51:20.968   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-08-01 18:51:24.021   868-868   ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-08-01 18:51:24.021   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CHECKING_VERSION
2025-08-01 18:51:24.025   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-08-01 18:51:24.075   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Current version: QH303_QSOUND_20231110001, Target version: A013
2025-08-01 18:51:24.076   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? false. Is specific error version? true
2025-08-01 18:51:24.076   868-868   ArrayMicOTA             com.eeo.systemsetting                I  Update required. Proceeding with update...
2025-08-01 18:51:24.076   868-868   ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE