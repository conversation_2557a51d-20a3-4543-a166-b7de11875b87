package com.eeo.systemsetting.view;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ScrollView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;

import java.util.Locale;

@SuppressLint("ViewConstructor")
public class PrivacyFrameLayout extends FrameLayout {
    public static final String TAG = "PrivacyFrameLayout===";
    View rootView = null;
    Context mContext;

    private ImageView ivBack;
    private ScrollView scrollView;
    private WebView webView;

    public PrivacyFrameLayout(Context context) {
        super(context);
        mContext = context;
        if (rootView == null) {
            rootView = LayoutInflater.from(context).inflate(R.layout.activity_privacy, null);
        }

        findView();
        addView(rootView);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void findView() {
        ivBack = rootView.findViewById(R.id.iv_back);
        ivBack.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.back((Activity) mContext);
            }
        });
        scrollView = rootView.findViewById(R.id.sv_privacy);
        webView = rootView.findViewById(R.id.webView);
        webView.setBackgroundColor(Color.TRANSPARENT);
        webView.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //避免webView里面的超链接被点击
                return true;
            }
        });
        String htmlContent = "<html><head><style>" +
                "body {" +
                "   color: #FFFFFF; /* 设置文字颜色为白色 */" +
                "   font-size: 14dp; /* 设置字体大小为9sp */" +
                "}" +
                "</style></head></html>";
        webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null);
        // 设置初始缩放级别
        webView.setInitialScale(100); // 设置为50%，可以根据需要调整
        if (Locale.CHINA.equals(Locale.getDefault())) {
            webView.loadUrl("file:///android_asset/user_agreement.html");
        } else {
            webView.loadUrl("file:///android_asset/user_agreement_english.html");
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        //WebView初始化的时候会还原density的值，这里需要重新update
        CommonUtils.updateDensity(mContext);
        if (scrollView != null) {
            scrollView.scrollTo(0, 0);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }
}
