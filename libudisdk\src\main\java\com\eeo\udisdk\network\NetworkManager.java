package com.eeo.udisdk.network;

import android.content.Context;
import android.util.Log;

import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.system.ScreenStatusChangeListener;
import com.ifpdos.udi.sdk.IEventHandler;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Arrays;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 网络相关
 */
public class NetworkManager {
    private static final String TAG = "Udi-NetworkManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;

    public NetworkManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    /**
     * 整机有线网络开关状态
     */
    public boolean isEthernetEnabled() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/enable"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "isEthernetEnabled:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "isEthernetEnabled body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                    return jsonObject.optBoolean("enable");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean enableEthernet(boolean enable) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("enable", enable);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/enable"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "enableEthernet:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取整机有线网络连接模式
     */
    public String getEthernetMode() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/mode"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getEthernetMode:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getEthernetMode body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                    return jsonObject.optString("value");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 设置整机有线网络连接模式
     *
     * @param mode
     * @see UdiConstant#ETHERNET_MODE_DHCP
     */
    public boolean setEthernetMode(String mode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("value", mode);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/mode"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setEthernetMode:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取整机有线网络配置
     */
    public EthernetConfig getEthernetConfig() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/config"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getIpAddress:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getIpAddress body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                    String ip = jsonObject.optString("ip");
                    String gateway = jsonObject.optString("gateway");
                    String mask = jsonObject.optString("mask");
                    String dns1 = jsonObject.optString("dns1");
                    String dns2 = jsonObject.optString("dns2");
                    return new EthernetConfig(ip, gateway, mask, dns1, dns2);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 设置整机有线网络配置
     */
    public boolean setEthernetConfig(EthernetConfig config) {
        if (config == null) {
            return false;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("ip", config.getIp());
            jsonObject.put("gateway", config.getGateway());
            jsonObject.put("mask", config.getMask());
            jsonObject.put("dns1", config.getDns1());
            jsonObject.put("dns2", config.getDns2());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/config"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setEthernetMode:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取整机IP地址
     */
    public String getIpAddress() {
        /*Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ip"))  //这个读的值不对，改用从config里读
                .build();*/
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/ethernet/config"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getIpAddress:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getIpAddress body:" + body);
                try {
                    JSONObject jsonObject = new JSONObject(body);
                    return jsonObject.optString("ip");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取整机MAC地址
     */
    public String getMacAddress() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/network/mac"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getMacAddress:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getMacAddress body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                    return jsonObject.optString("value");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
