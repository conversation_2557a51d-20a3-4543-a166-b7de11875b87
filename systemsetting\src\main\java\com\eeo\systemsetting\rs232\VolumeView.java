package com.eeo.systemsetting.rs232;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.view.CustomerSeekBar;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class VolumeView implements View.OnTouchListener, View.OnClickListener, SeekBar.OnSeekBarChangeListener {
    private final static String TAG = "VolumeView";
    private Context mContext;

    private View mVolumeView;
    private ImageView mVolumeIv;
    private CustomerSeekBar mVolumeSeekBar;
    private TextView mVolumeTv;
    private WindowManager.LayoutParams mVolumeLayoutParams;
    private boolean mHasVolumeViewShown;
    private WindowManager mWindowManager;
    private AudioManager mAudioManager;

    private boolean mIsVolumeSeekBarTracking;
    //SeekBar滑动时避免耗时卡顿
    private final ExecutorService mExecutorService = Executors.newSingleThreadExecutor();
    private int mVolume;
    private int mVolumeProgress = -1;
    private boolean isVolumeSetting;
    private final int VOICE_VALUE_0 = 0;
    private final int VOICE_VALUE_33 = 33;
    private final int VOICE_VALUE_66 = 66;
    private int mVoiceLevel;
    private int mLastVoiceLevel = -1;
    //由于offset，用来辅助判断是否手动滑动
    private boolean mFromUser = false;
    private static final int DURATION = 3000;

    private static final int MSG_SHOW_VOLUME = 0x001;
    private static final int MSG_DISMISS_VOLUME = 0x002;
    private static final int MSG_SET_VOLUME = 0x003;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_SHOW_VOLUME:
                    showVolumeView();
                    break;
                case MSG_DISMISS_VOLUME:
                    dismissVolumeView();
                    break;
                case MSG_SET_VOLUME:
                    setSystemVolume(mVolumeProgress);
                    break;
            }
        }
    };

    public VolumeView(Context context) {
        mContext = context;
    }

    /**
     * @param volume 负数时，从系统获取最新音量显示
     *               如mute和unmute时传入-1
     */
    public void showVolumeView(int volume) {
        if ((EeoApplication.isShowMainDialog && EeoApplication.currentPage == EeoApplication.PAGE_MAIN)
                || EeoApplication.isProjection) {
            //大屏控制菜单或画中画界面有音量调，这里不重复显示
            return;
        }
        if (volume < 0) {
            if (mAudioManager == null) {
                mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
            }
            mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        } else {
            mVolume = volume;
        }
        mHandler.sendEmptyMessage(MSG_SHOW_VOLUME);
    }

    public void showVolumeView() {
        if (mWindowManager == null) {
            mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        }
        if (mVolumeView == null) {
            mVolumeView = LayoutInflater.from(mContext).inflate(R.layout.dialog_volume, null);
            mVolumeView.setOnTouchListener(this);
            mVolumeLayoutParams = new WindowManager.LayoutParams();
            mVolumeLayoutParams.format = PixelFormat.RGBA_8888;
            mVolumeLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            mVolumeLayoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            mVolumeLayoutParams.width = mContext.getResources().getDimensionPixelSize(R.dimen.dialog_volume_width);
            mVolumeLayoutParams.height = mContext.getResources().getDimensionPixelSize(R.dimen.dialog_volume_height);
            mVolumeLayoutParams.x = mContext.getResources().getDimensionPixelSize(R.dimen.dialog_volume_margin_end);
            mVolumeLayoutParams.y = mContext.getResources().getDimensionPixelSize(R.dimen.dialog_volume_margin_bottom);
            mVolumeLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL /*|
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE*/
                    | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
            mVolumeIv = mVolumeView.findViewById(R.id.iv_volume);
            mVolumeIv.setOnClickListener(this);
            mVolumeSeekBar = mVolumeView.findViewById(R.id.sb_volume);
            mVolumeSeekBar.setOnSeekBarChangeListener(this);
            mVolumeTv = mVolumeView.findViewById(R.id.tv_volume);
        }
        mVolumeSeekBar.setProgress(mVolume + Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
        mVolumeTv.setText(String.valueOf(mVolume));
        if (mHasVolumeViewShown) {
            mWindowManager.updateViewLayout(mVolumeView, mVolumeLayoutParams);
        } else {
            mWindowManager.addView(mVolumeView, mVolumeLayoutParams);
            CommonUtils.enableOsd(mContext, true);
        }
        mHasVolumeViewShown = true;
        mHandler.removeMessages(MSG_DISMISS_VOLUME);
        mHandler.sendEmptyMessageDelayed(MSG_DISMISS_VOLUME, DURATION);
    }

    public void dismissVolumeView() {
        if (mWindowManager != null && mVolumeView != null && mVolumeView.isAttachedToWindow()) {
            Log.d(TAG, "dismissVolumeView");
            mWindowManager.removeView(mVolumeView);
            CommonUtils.enableOsd(mContext, false);
        }
        mHasVolumeViewShown = false;
    }

    /**
     * 触控时不消失
     */
    private void keepVolumeView() {
        mHandler.removeMessages(MSG_DISMISS_VOLUME);
    }

    /**
     * 重新倒计时消失
     */
    private void resetVolumeViewDuration() {
        mHandler.removeMessages(MSG_DISMISS_VOLUME);
        mHandler.sendEmptyMessageDelayed(MSG_DISMISS_VOLUME, DURATION);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            keepVolumeView();
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            resetVolumeViewDuration();
        } else if (event.getAction() == MotionEvent.ACTION_OUTSIDE) {
            dismissVolumeView();
        }
        return false;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_volume:
                //点击音量图标静音
                mFromUser = true;
                mVolumeSeekBar.setProgress(Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
                resetVolumeViewDuration();
                break;
        }
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (progress < Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET) {
            if (fromUser) {
                mFromUser = true;
            }
            seekBar.setProgress(Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET);
            return;
        }
        progress = progress - Constant.SETTING_SEEK_BAR_PROGRESS_OFFSET;
        if (mVolumeProgress != progress) {
            mVolumeProgress = progress;
            setVoiceIcon(progress);
            mVolumeTv.setText(String.valueOf(progress));
            //set system volume
            if (mFromUser || fromUser) {
                //在子线程设置，避免造成滑条卡顿
                if (isVolumeSetting) {
                    //最后再delay设置，避免最后一次漏掉设置
                    mHandler.removeMessages(MSG_SET_VOLUME);
                    mHandler.sendEmptyMessageDelayed(MSG_SET_VOLUME, 50);
                } else {
                    mExecutorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            isVolumeSetting = true;
                            setSystemVolume(mVolumeProgress);
                            isVolumeSetting = false;
                        }
                    });
                }
            }
        }
        mFromUser = false;
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        mIsVolumeSeekBarTracking = true;
        keepVolumeView();
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        mIsVolumeSeekBarTracking = false;
        resetVolumeViewDuration();
    }

    private void setSystemVolume(int volume) {
        Log.d(TAG, "setSystemVolume: " + volume);
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, AudioManager.FLAG_PLAY_SOUND);
    }

    /**
     * 设置音量图标icon
     *
     * @param voiceValue
     */
    private void setVoiceIcon(int voiceValue) {
        if (VOICE_VALUE_0 == voiceValue) {
            mVoiceLevel = 0;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                mVolumeIv.setBackground(mContext.getDrawable(R.drawable.ic_voice_00));
            }
            return;
        }

        if (VOICE_VALUE_0 < voiceValue && voiceValue < VOICE_VALUE_33) {
            mVoiceLevel = 1;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                mVolumeIv.setBackground(mContext.getDrawable(R.drawable.ic_voice_01));
            }
            return;
        }

        if (VOICE_VALUE_33 <= voiceValue && voiceValue < VOICE_VALUE_66) {
            mVoiceLevel = 2;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                mVolumeIv.setBackground(mContext.getDrawable(R.drawable.ic_voice_02));
            }
            return;
        }

        if (VOICE_VALUE_66 <= voiceValue) {
            mVoiceLevel = 3;
            if (mVoiceLevel != mLastVoiceLevel) {
                mLastVoiceLevel = mVoiceLevel;
                mVolumeIv.setBackground(mContext.getDrawable(R.drawable.ic_voice_03));
            }
            return;
        }
    }
}
