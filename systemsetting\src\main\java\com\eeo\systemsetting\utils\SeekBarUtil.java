package com.eeo.systemsetting.utils;

import android.content.Context;
import android.widget.SeekBar;

import androidx.core.content.ContextCompat;

import com.eeo.systemsetting.R;

public class SeekBarUtil {

    /**
     * 通过scale实现进度末端带圆角，在进度很小时会不显示圆角
     * progress<11时通过动态修改竖直方向的padding来实现动画效果
     */
    public static void setSeekBarProgressDrawable(Context context, SeekBar seekBar, int lastProgress, int progress) {
        if (progress >= 11 && lastProgress < 11) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape));
        } else if (progress == 1) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape1));
        } else if (progress == 2) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape2));
        } else if (progress == 3) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape3));
        } else if (progress == 4) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape4));
        } else if (progress == 5) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape5));
        } else if (progress == 6) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape6));
        } else if (progress == 7) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape7));
        } else if (progress == 8) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape8));
        } else if (progress == 9) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape9));
        } else if (progress == 10) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape10));
        }
    }

    /**
     * 通过scale实现进度末端带圆角，在进度很小时会不显示圆角
     * progress<7时通过动态修改竖直方向的padding来实现动画效果
     */
    public static void setProjectionSeekBarProgressDrawable(Context context, SeekBar seekBar, int lastProgress, int progress) {
        if (progress >= 5 && lastProgress < 5) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape));
        } else if (progress == 1) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape7));
        } else if (progress == 2) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape8));
        } else if (progress == 3) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape9));
        } else if (progress == 4) {
            seekBar.setProgressDrawable(ContextCompat.getDrawable(context, R.drawable.progress_vertical_gradient_simple_shape10));
        }
    }
}
