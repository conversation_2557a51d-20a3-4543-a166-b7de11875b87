package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.util.Log;

/**
 * HHT协议处理器
 * 负责解析Windows发送的HHT协议数据包
 * 当前阶段：专注于以太网MAC地址获取指令的解析和响应
 * 
 * HHT协议格式：7F 长度 数据... CF
 * MAC地址获取请求：7F 0A 99 A2 B3 C4 02 FF F1 04 FF FF CF (13字节)
 * MAC地址响应：7F 0E 99 A2 B3 C4 02 FF F3 04 以太网MAC地址6字节 CF (17字节)
 */
public class ProtocolHandler {
    private static final String TAG = "ProtocolHandler";
    
    // HHT协议常量
    private static final byte FRAME_HEADER = (byte) 0x7F;
    private static final byte FRAME_TAIL = (byte) 0xCF;
    
    // MAC地址获取指令特征码（原始版本）
    private static final byte[] MAC_REQUEST_PATTERN = {
        (byte) 0x7F, (byte) 0x0A, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0xF1, (byte) 0x04, (byte) 0xFF, (byte) 0xFF, (byte) 0xCF
    };
    
    // MAC地址获取指令特征码（转换后版本，C4→E4, CF→EF）
    private static final byte[] MAC_REQUEST_PATTERN_CONVERTED = {
        (byte) 0x7F, (byte) 0x0A, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xE4,
        (byte) 0x02, (byte) 0xFF, (byte) 0xF1, (byte) 0x04, (byte) 0xFF, (byte) 0xFF, (byte) 0xEF
    };
    
    // 响应帧前缀（不包含MAC地址和尾部）
    private static final byte[] MAC_RESPONSE_PREFIX = {
        (byte) 0x7F, (byte) 0x0E, (byte) 0x99, (byte) 0xA2, (byte) 0xB3, (byte) 0xC4,
        (byte) 0x02, (byte) 0xFF, (byte) 0xF3, (byte) 0x04
    };
    

    
    private Context mContext;
    private MacAddressHandler mMacAddressHandler;
    private PowerControlHandler mPowerControlHandler;
    private SignalSwitchHandler mSignalSwitchHandler;
    private OpsCommManager mOpsCommManager;
    private JniSerialPortManager mSerialPortManager;
    
    // 数据缓冲区，用于处理粘包和拆包
    private final StringBuilder mDataBuffer = new StringBuilder();
    private static final int MAX_BUFFER_SIZE = 2048;

    public ProtocolHandler(Context context, MacAddressHandler macAddressHandler) {
        mContext = context;
        mMacAddressHandler = macAddressHandler;
        mPowerControlHandler = new PowerControlHandler(context);
        mSignalSwitchHandler = new SignalSwitchHandler(context);
        Log.d(TAG, "ProtocolHandler initialized with MAC, PowerControl and SignalSwitch handlers");
    }

    /**
     * 设置OpsCommManager引用（用于统计）
     */
    public void setOpsCommManager(OpsCommManager opsCommManager) {
        mOpsCommManager = opsCommManager;
    }

    /**
     * 设置JniSerialPortManager引用（用于发送数据）
     */
    public void setJniSerialPortManager(JniSerialPortManager jniSerialPortManager) {
        mSerialPortManager = jniSerialPortManager;
    }

    /**
     * 处理接收到的原始数据
     */
    public void handleReceivedData(byte[] data) {
        if (data == null || data.length == 0) {
            Log.w(TAG, "Received empty data");
            return;
        }

        try {
            // 将字节数据转换为十六进制字符串
            String hexString = bytesToHexString(data);
            // Log.d(TAG, "Processing received data: " + hexString);

            // 添加到缓冲区
            synchronized (mDataBuffer) {
                mDataBuffer.append(hexString.replace(" ", ""));
                
                // 防止缓冲区过大
                if (mDataBuffer.length() > MAX_BUFFER_SIZE) {
                    Log.w(TAG, "Buffer overflow, clearing buffer");
                    mDataBuffer.setLength(0);
                    return;
                }
                
                // 尝试解析完整的数据包
                parseCompletePackets();
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while handling received data", e);
        }
    }

    /**
     * 从缓冲区解析完整的数据包
     */
    private void parseCompletePackets() {
        String buffer = mDataBuffer.toString();
        
        // 查找帧头7F的位置
        int headerIndex = buffer.indexOf("7F");
        
        while (headerIndex != -1 && headerIndex + 4 < buffer.length()) {
            try {
                // 获取长度字节（帧头后的第一个字节）
                String lengthHex = buffer.substring(headerIndex + 2, headerIndex + 4);
                int packetLength = Integer.parseInt(lengthHex, 16);
                
                // 计算完整包的长度（包括帧头和帧尾）
                int totalLength = (packetLength + 3) * 2; // *2因为是十六进制字符串
                
                // 检查缓冲区是否包含完整的包
                if (headerIndex + totalLength <= buffer.length()) {
                    String packetHex = buffer.substring(headerIndex, headerIndex + totalLength);
                    
                    // 验证帧尾
                    if (packetHex.endsWith("CF")) {
                        Log.d(TAG, "Found complete packet: " + packetHex);
                        
                        // 解析数据包
                        parsePacket(hexStringToBytes(packetHex));
                        
                        // 从缓冲区移除已处理的数据
                        mDataBuffer.delete(0, headerIndex + totalLength);
                        buffer = mDataBuffer.toString();
                        headerIndex = buffer.indexOf("7F");
                    } else {
                        Log.w(TAG, "Invalid packet: frame tail not found");
                        headerIndex = buffer.indexOf("7F", headerIndex + 2);
                    }
                } else {
                    // 数据包不完整，等待更多数据
                    break;
                }
                
            } catch (NumberFormatException e) {
                Log.w(TAG, "Invalid length byte in packet");
                headerIndex = buffer.indexOf("7F", headerIndex + 2);
            } catch (Exception e) {
                Log.e(TAG, "Exception while parsing packet", e);
                headerIndex = buffer.indexOf("7F", headerIndex + 2);
            }
        }
    }

    /**
     * 解析单个数据包
     */
    private void parsePacket(byte[] packet) {
        if (packet == null || packet.length < 3) {
            Log.w(TAG, "Invalid packet: too short");
            return;
        }

        try {
            // 验证帧头和帧尾
            if (packet[0] != FRAME_HEADER || packet[packet.length - 1] != FRAME_TAIL) {
                Log.w(TAG, "Invalid packet: invalid frame header or tail");
                return;
            }

            // 验证长度字段
            int declaredLength = packet[1] & 0xFF;
            int actualLength = packet.length - 3; // 减去帧头、长度、帧尾
            
            if (declaredLength != actualLength) {
                Log.w(TAG, "Invalid packet: length mismatch. Declared: " + declaredLength + ", Actual: " + actualLength);
                return;
            }

            String packetHex = bytesToHexString(packet);
            Log.d(TAG, "Parsing valid packet: " + packetHex);

            // 检查是否为MAC地址获取请求
            if (isMacAddressRequest(packet)) {
                Log.d(TAG, "Detected MAC address request");
                handleMacAddressRequest();
            } 
            // 检查是否为关机控制请求
            else if (mPowerControlHandler.isPowerOffCommand(packet)) {
                Log.d(TAG, "Detected power off command");
                handlePowerOffRequest();
            } 
            // 检查是否为信号源切换请求
            else {
                int signalType = mSignalSwitchHandler.getSignalSwitchType(packet);
                if (signalType != -1) {
                    Log.d(TAG, "Detected signal switch command, type: " + signalType);
                    handleSignalSwitchRequest(signalType);
                } else {
                    Log.d(TAG, "Unknown packet type, ignoring");
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while parsing packet", e);
        }
    }

    /**
     * 检查是否为MAC地址获取请求（支持原始和转换后的字节模式）
     */
    private boolean isMacAddressRequest(byte[] packet) {
        if (packet.length != MAC_REQUEST_PATTERN.length) {
            return false;
        }

        // 首先检查原始模式
        boolean isOriginalPattern = true;
        for (int i = 0; i < MAC_REQUEST_PATTERN.length; i++) {
            if (packet[i] != MAC_REQUEST_PATTERN[i]) {
                isOriginalPattern = false;
                break;
            }
        }
        
        if (isOriginalPattern) {
            Log.i(TAG, "✅ Detected ORIGINAL MAC request pattern (no byte conversion)");
            return true;
        }
        
        // 检查转换后的模式
        boolean isConvertedPattern = true;
        for (int i = 0; i < MAC_REQUEST_PATTERN_CONVERTED.length; i++) {
            if (packet[i] != MAC_REQUEST_PATTERN_CONVERTED[i]) {
                isConvertedPattern = false;
                break;
            }
        }
        
        if (isConvertedPattern) {
            Log.w(TAG, "⚠️ Detected CONVERTED MAC request pattern (C4→E4, CF→EF conversion detected)");
            Log.w(TAG, "Original pattern:  " + bytesToHexString(MAC_REQUEST_PATTERN));
            Log.w(TAG, "Converted pattern: " + bytesToHexString(MAC_REQUEST_PATTERN_CONVERTED));
            Log.w(TAG, "Received packet:   " + bytesToHexString(packet));
            return true;
        }
        
        // 尝试修复可能的其他转换模式
        byte[] repairedPacket = repairByteConversions(packet);
        if (repairedPacket != null) {
            boolean isRepairedPattern = true;
            for (int i = 0; i < MAC_REQUEST_PATTERN.length; i++) {
                if (repairedPacket[i] != MAC_REQUEST_PATTERN[i]) {
                    isRepairedPattern = false;
                    break;
                }
            }
            
            if (isRepairedPattern) {
                Log.w(TAG, "🔧 Successfully repaired byte conversions in MAC request");
                Log.w(TAG, "Original packet: " + bytesToHexString(packet));
                Log.w(TAG, "Repaired packet: " + bytesToHexString(repairedPacket));
                return true;
            }
        }
        
        Log.d(TAG, "❌ Unknown packet pattern: " + bytesToHexString(packet));
        return false;
    }
    
    /**
     * 尝试修复可能的字节转换问题
     */
    private byte[] repairByteConversions(byte[] packet) {
        if (packet == null || packet.length != MAC_REQUEST_PATTERN.length) {
            return null;
        }
        
        byte[] repaired = new byte[packet.length];
        System.arraycopy(packet, 0, repaired, 0, packet.length);
        
        boolean hadConversions = false;
        
        // 修复已知的字节转换模式
        for (int i = 0; i < repaired.length; i++) {
            int originalByte = packet[i] & 0xFF;
            
            // 检查是否是C4→E4的转换
            if (originalByte == 0xE4) {
                // 检查上下文，看是否应该是C4
                if (shouldBeC4(i, packet)) {
                    repaired[i] = (byte) 0xC4;
                    hadConversions = true;
                    Log.d(TAG, "Repaired byte[" + i + "]: E4 → C4");
                }
            }
            // 检查是否是CF→EF的转换
            else if (originalByte == 0xEF) {
                // 检查上下文，看是否应该是CF
                if (shouldBeCF(i, packet)) {
                    repaired[i] = (byte) 0xCF;
                    hadConversions = true;
                    Log.d(TAG, "Repaired byte[" + i + "]: EF → CF");
                }
            }
        }
        
        return hadConversions ? repaired : null;
    }
    
    /**
     * 检查指定位置的字节是否应该是C4
     */
    private boolean shouldBeC4(int position, byte[] packet) {
        // 在MAC请求模式中，C4出现在位置5
        return position == 5;
    }
    
    /**
     * 检查指定位置的字节是否应该是CF
     */
    private boolean shouldBeCF(int position, byte[] packet) {
        // 在MAC请求模式中，CF出现在最后一个位置
        return position == packet.length - 1;
    }

    /**
     * 处理关机控制请求
     */
    private void handlePowerOffRequest() {
        Log.d(TAG, "Handling power off request...");

        try {
            // 增加指令计数
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementCommandCount();
            }

            // 处理关机请求
            byte[] response = mPowerControlHandler.handlePowerOffCommand();
            if (response != null) {
                sendResponse(response);
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementSuccessCount();
                }
            } else {
                Log.e(TAG, "Failed to build power off response");
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementErrorCount();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while handling power off request", e);
            
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementErrorCount();
            }
        }
    }

    /**
     * 处理信号源切换请求
     */
    private void handleSignalSwitchRequest(int signalType) {
        Log.d(TAG, "Handling signal switch request, type: " + signalType);

        try {
            // 增加指令计数
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementCommandCount();
            }

            // 处理信号源切换请求
            byte[] response = mSignalSwitchHandler.handleSignalSwitchCommand(signalType);
            if (response != null) {
                sendResponse(response);
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementSuccessCount();
                }
            } else {
                Log.e(TAG, "Failed to build signal switch response");
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementErrorCount();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while handling signal switch request", e);
            
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementErrorCount();
            }
        }
    }

    /**
     * 处理MAC地址获取请求
     */
    private void handleMacAddressRequest() {
        Log.d(TAG, "Handling MAC address request...");

        try {
            // 增加指令计数
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementCommandCount();
            }

            // 只获取以太网MAC地址（根据用户要求，不需要WiFi MAC地址）
            String ethMac = mMacAddressHandler.getEthMacAddress();

            Log.d(TAG, "Retrieved Ethernet MAC address: " + ethMac);

            // 构建响应
            byte[] response = buildMacAddressResponse(ethMac);
            if (response != null) {
                sendResponse(response);
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementSuccessCount();
                }
            } else {
                Log.e(TAG, "Failed to build MAC address response");
                
                if (mOpsCommManager != null) {
                    mOpsCommManager.incrementErrorCount();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while handling MAC address request", e);
            
            if (mOpsCommManager != null) {
                mOpsCommManager.incrementErrorCount();
            }
        }
    }

    /**
     * 构建MAC地址响应帧（仅以太网MAC地址）
     */
    private byte[] buildMacAddressResponse(String ethMac) {
        try {
            if (ethMac == null || ethMac.isEmpty()) {
                Log.e(TAG, "No valid Ethernet MAC address available");
                return null;
            }

            // 转换MAC地址为字节数组
            byte[] macBytes = macStringToBytes(ethMac);
            if (macBytes == null || macBytes.length != 6) {
                Log.e(TAG, "Invalid Ethernet MAC address format: " + ethMac);
                return null;
            }

            return buildSingleMacResponse(macBytes);

        } catch (Exception e) {
            Log.e(TAG, "Exception while building MAC response", e);
            return null;
        }
    }

    /**
     * 构建以太网MAC地址响应（17字节）
     */
    private byte[] buildSingleMacResponse(byte[] macBytes) {
        byte[] response = new byte[MAC_RESPONSE_PREFIX.length + macBytes.length + 1];
        
        // 复制前缀
        System.arraycopy(MAC_RESPONSE_PREFIX, 0, response, 0, MAC_RESPONSE_PREFIX.length);
        
        // 复制MAC地址
        System.arraycopy(macBytes, 0, response, MAC_RESPONSE_PREFIX.length, macBytes.length);
        
        // 添加帧尾
        response[response.length - 1] = FRAME_TAIL;
        
        Log.d(TAG, "Built single MAC response: " + bytesToHexString(response));
        return response;
    }



    /**
     * 发送响应数据
     */
    private void sendResponse(byte[] response) {
        Log.d(TAG, "Sending response: " + bytesToHexString(response));
        
        if (mSerialPortManager != null) {
            boolean success = mSerialPortManager.sendData(response);
            if (success) {
                Log.d(TAG, "Response sent successfully via JNI implementation");
            } else {
                Log.e(TAG, "Failed to send response via JNI implementation");
            }
        } else {
            Log.e(TAG, "JNI SerialPortManager is null, cannot send response");
        }
    }

    /**
     * MAC地址字符串转字节数组
     */
    private byte[] macStringToBytes(String macAddress) {
        if (macAddress == null || macAddress.isEmpty()) {
            return null;
        }

        try {
            // 移除分隔符
            String cleanMac = macAddress.replace(":", "").replace("-", "").replace(" ", "");
            
            if (cleanMac.length() != 12) {
                Log.e(TAG, "Invalid MAC address length: " + macAddress);
                return null;
            }

            byte[] bytes = new byte[6];
            for (int i = 0; i < 6; i++) {
                bytes[i] = (byte) Integer.parseInt(cleanMac.substring(i * 2, (i + 1) * 2), 16);
            }

            return bytes;

        } catch (NumberFormatException e) {
            Log.e(TAG, "Invalid MAC address format: " + macAddress, e);
            return null;
        }
    }

    /**
     * 十六进制字符串转字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        String cleanHex = hexString.replace(" ", "");
        int len = cleanHex.length();
        byte[] data = new byte[len / 2];
        
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
                                + Character.digit(cleanHex.charAt(i + 1), 16));
        }
        
        return data;
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b & 0xFF));
        }
        return sb.toString().trim();
    }
} 