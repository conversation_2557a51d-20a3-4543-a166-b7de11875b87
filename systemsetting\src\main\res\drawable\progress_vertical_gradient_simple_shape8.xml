<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="10dp" />
            <solid android:color="@color/progress_bar_background" />
            <padding android:left="1dp" />
            <padding android:top="2.5dp" />
            <padding android:right="1dp" />
            <padding android:bottom="2.5dp" />
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <scale android:scaleWidth="100%">
            <shape>
                <corners android:radius="10dp" />
                <solid android:color="@color/white_100" />
            </shape>
        </scale>
        <!--        进度调直角-->
        <!--        <clip>-->
        <!--            <shape>-->
        <!--                <corners android:radius="10dp" />-->
        <!--                <gradient-->
        <!--                    android:angle="0"-->
        <!--                    android:endColor="@color/white_100"-->
        <!--                    android:startColor="@color/white_100"-->
        <!--                    android:type="radial" />-->
        <!--            </shape>-->
        <!--        </clip>-->
    </item>
</layer-list>