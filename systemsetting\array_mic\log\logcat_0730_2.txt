07-30 15:42:53.366  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.409  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:42:53.430   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:53.430   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:53.430   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:53.430   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:53.457  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.567  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.568  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.672  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.761  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.775  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.775  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.875  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.917  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.919  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.931   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:53.931   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:53.931   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:53.931   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:53.949   787   800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:53.950  1769  1784 E com.android.nf: failed to ection refused
07-30 15:42:53.998  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refuseE o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.077  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.145  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.197   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:54.223   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.246   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:54.247   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:54.247   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:54.247   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:54.248   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:54.248   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:54.248   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:54.248   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:54.248   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:54.268  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.367  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) enter, args=[-1]
07-30 15:42:54.368  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) exit, rst=2
07-30 15:42:54.368  2100  2433 I AppServ_LicenseCheck: getWzAuthStatus() status=2
07-30 15:42:54.431   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:54.431   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.431   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:54.432   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:54.432   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:54.471  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.494  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.499  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.505  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.513  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.556  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.592  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.644  1004  1016 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.650  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.773   963   975 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.797  1870  1883 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:42:54.932   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:54.932   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:54.932   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offs 451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:54.961  2257  2270 E sservice:daerol socket: Connection refused
07-30 15:42:54.978  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.080  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:42:55.080  1838  1838 D UserGuideAnimationFragment: 结束第二个动画！！！！！！！！！
07-30 15:42:55.080  1838  1838 D UserGuideAnimationFragment: 再次第二个动画！！！！！！！！！
07-30 15:42:55.080  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:42:55.081  1838  1838 D UserGuideAnimationFragment: 结束第二个动画！！！！！！！！！
07-30 15:42:55.081  1838  1838 D UserGuideAnimationFragment: 再次第二个动画！！！！！！！！！
07-30 15:42:55.127  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.201   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:55.240   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.249   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:55.250   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:55.250   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:55.250   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:55.251   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:55.251   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:55.251   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:55.251   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:55.251   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:55.263  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.406  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.409  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:42:55.432   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:55.433   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:55.433   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:55.433   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:55.497  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.567  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.568  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.713  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.762  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.776  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.776  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.876  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.920  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.934   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:55.934   329   451 D SystemControl:= 1784, data = 62.
07-30 15:42:55.934   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0 SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:55.950  1769  1784 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.951   787   800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.958  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:42:55.998  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.063   809   825 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.117  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.186  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.203   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:56.223   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.252   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:56.253   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:56.253   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:56.253   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:56.254   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:56.254   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:56.254   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:56.254   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:56.254   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:56.268  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.432   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.435   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:56.435   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:56.435   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:56.435   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:56.471  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.500  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.514  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.534  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.544  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.557  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.592  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.651  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.685  1004  1016 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.774   963   975 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.837  1870  1883 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:42:56.936   329   451 D STypes: id = 168, len = 1, offset = 0
07-30 15:42:56.936   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr .936   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:56.936   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:56.979  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.001  2257  2270 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.168  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.206   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:57.241   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.256   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:57.256   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:57.256   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:57.256   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:57.257   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:57.257   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:57.257   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:57.258   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:57.258   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:57.300  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.369  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) enter, args=[-1]
07-30 15:42:57.370  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) exit, rst=2
07-30 15:42:57.370  2100  2433 I AppServ_LicenseCheck: getWzAuthStatus() status=2
07-30 15:42:57.410  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:42:57.436   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:57.436   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:57.436   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:57.436   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:57.445  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.536  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.568  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.568  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.752  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.762  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.776  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.776  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.876  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.920  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.936   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:57.937   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784,329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:57.937   329   451 D System: actualAddr = 2103, data = 0.
07-30 15:42:57.951  1769  1784 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.951   787   800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.997  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:42:57.999  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.064   809   825 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.156  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.209   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:58.224   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.226  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.259   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:58.260   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:58.260   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:58.260   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:58.260   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:58.261   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:58.261   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:58.261   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:58.261   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:58.269  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.432   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.437   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:58.437   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:58.437   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:58.437   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:58.472  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.480  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:42:58.480  1838  1838 D UserGuideAnimationFragment: 结束第二个动画！！！！！！！！！
07-30 15:42:58.481  1838  1838 D UserGuideAnimationFragment: 启动第三个动画
07-30 15:42:58.500  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.514  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.557  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.574  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.585  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.593  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.652  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.725  1004  1016 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:58.774   963   wp control socket: Connection refused
07-30 15:42:58.877  1870  1883 E d.process.medi: failed to connect to jdwp control so42:58.938   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:58.938   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:58.938   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:58.938   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:58.979  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.040  2257  2270 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.209  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.211   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:42:59.241   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.262   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:42:59.263   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:42:59.263   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:42:59.263   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:42:59.264   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:42:59.264   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:42:59.264   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:42:59.265   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:42:59.265   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:42:59.341  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.410  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:42:59.438   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:59.438   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:59.438   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:42:59.438   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:42:59.485  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.569  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.569  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.576  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.763  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.776  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.776  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.793  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.876  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.920  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.939   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:42:59.939   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:42:59.939   329   451 D SystemCon = 287, len = 1, offset = 0
07-30 15:42:59.939   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, d  800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.951  1769  1784 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:42:59.999  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.036  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.064   809   825 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.195  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.216   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:00.224   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.264  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.266   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:00.266   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:00.266   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:00.266   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:00.269   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:00.269   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:00.269   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:00.269  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.270   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:00.270   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:00.371  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) enter, args=[-1]
07-30 15:43:00.372  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) exit, rst=2
07-30 15:43:00.372  2100  2433 I AppServ_LicenseCheck: getWzAuthStatus() status=2
07-30 15:43:00.433   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.439   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:00.439   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:00.440   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:00.440   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:00.472  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.501  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.515  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.557  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.593  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.616  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.623  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.652  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.765  1004  1016 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.775   963   975 E rkstack.proces: failed to connect to jdwp control 15:43:00.919  1870  1883 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:00.940Action]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:00.940   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:00.940   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:00.940   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:00.980  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.080  2257  2270 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.219   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:01.242   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.249  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.271   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:01.272   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:01.273   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:01.273   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:01.380  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.410  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:43:01.441   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:01.441   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:01.441   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:01.441   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:01.523  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.569  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.569  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.617  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.763  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.777  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.777  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.832  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.877  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.921  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:01.941   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:01.941   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:01.941   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, .941   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:01.952  1769  1784 E to jdwp control socket: Connection refused
07-30 15:43:01.952   787   800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.000  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.067   809   825 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.074  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.180  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:43:02.181  1838  1838 D UserGuideAnimationFragment: 开始第三个动画
07-30 15:43:02.181  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:43:02.181  1838  1838 D UserGuideAnimationFragment: 开始第三个动画
07-30 15:43:02.221   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:02.225   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.233  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.269  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.274   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:02.275   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:02.275   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:02.275   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:02.276   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:02.276   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:02.276   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:02.281   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:02.281   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:02.304  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.435   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.442   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:02.442   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:02.442   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:02.442   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:02.473  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.502  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.515  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.558  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.594  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.653  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.656  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.663  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.775   963   975 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.804  1004  1016 E ssioncontrolle: failed to connect to jdwp con 168, len = 1, offset = 0
07-30 15:43:02.942   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, dat  451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:02.942   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:02.956  1870  1883 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:02.980  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.120  2257  2270 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.224   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:03.242   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.286   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:03.288   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:03.288   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:03.288   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:03.289  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.290   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:03.290   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:03.290   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:03.291   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:03.291   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:03.373  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) enter, args=[-1]
07-30 15:43:03.374  2100  2433 I main_taskProc: callServerApi(getWzAuthStatus) exit, rst=2
07-30 15:43:03.374  2100  2433 I AppServ_LicenseCheck: getWzAuthStatus() status=2
07-30 15:43:03.410  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:43:03.421  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.443   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:03.443   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:03.443   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:03.443   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:03.564  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.569  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.569  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.657  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.763  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.777  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.777  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.873  1979  1991 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.877  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.921  1163  1182 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.943   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offseta = 62.
07-30 15:43:03.943   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15trol: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:03.952   787   800 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:03.952  1769  1784 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.000  1790  1803 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.067   809   825 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.114  2011  2023 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.225   839   861 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.226   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:04.270  1838  1856 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.273  2038  2050 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.292   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:04.293   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:04.293   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:04.293   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:04.294   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:04.294   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:04.294   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:04.295   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:04.295   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:04.342  2871  2884 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.436   904   921 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.443   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:04.444   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:04.444   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:04.444   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:04.473  2121  2133 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.502  1251  1265 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.515  2141  2155 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.558  2100  2112 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.594  2488  2500 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.653  2235  2248 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.695  2069  2088 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.703  1815  1828 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.775   963   975 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.844  1004  1016 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.944   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:04.944   329   451 D SystemControl: [r = 1784, data = 62.
07-30 15:43:04.944   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:04.984  2187  2201 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:43:04.996  1870  1883 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.160  2257  2270 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.230   637   652 I RunningTasks: call getRunningTasks is1000
07-30 15:43:05.243   637   645 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.297   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:43:05.297   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:43:05.298   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:43:05.298   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:43:05.298   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:43:05.298   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:43:05.298   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:43:05.299   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:43:05.299   405   608 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:43:05.329  2285  2297 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.410  2834  2845 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:43:05.445   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:43:05.445   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:43:05.445   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:43:05.445   329   451 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:43:05.461  1927  1941 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.570  1328  1350 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.570  1083  1102 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.604  2312  2324 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.698  1950  1957 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.764  1700  1714 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.778  1721  1734 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.778  1130  1146 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.878  1744  1758 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:43:05.880  1838  1838 D UserGuideAnimationFragment: 最后一帧
07-30 15:43:05.880  1838  1838 D UserGuideAnimationFragment: 开始第三个动画
07-30 15:43:05.880  1838  1838 D UserGuideAnimationFragment: 第三个动画结束
07-30 15:43:05.886   637   652 I ActivityTaskManager: START u0 {act=com.cvte.intent.ACTION_TIF_PLAYER_ACTIVITY cat=[android.intent.category.DEFAULT] flg=0x10000020 cmp=com.eeo.systemsetting/.launcher.FallbackHomeActivity (has extras)} from uid 1000
07-30 15:43:05.895   637   652 D ActivityInfo: Debug:isFixedOrientationLandscape orientation = 0
07-30 15:43:05.898   637  1314 W ContextImpl: Calling a method in the system process without a qualified user: ast:1111 com.ifpdos.freeformframework.utils.FreeFormHelper$2.run:554 java.util.concurrent.ThreadPoolExecutor.runWorker:1167 jutor$Worker.run:641 java.lang.Thread.run:923
07-30 15:43:05.895   637   652 I chatty  : uid=1000(system) Binder:637_2 identical 1 line
07-30 15:43:05.895   637   652 D ActivityInfo: Debug:isFixedOrientationLandscape orientation = 0
