# 查看adb设备列表
adb devices

# 查看升级前固件版本号
adb -s 303_usb_device shell cat /usr/bin/qdreamer/qsound/version.txt

# 停止算法程序
adb -s 303_usb_device shell /usr/bin/qdreamer/qsound/kill_sound.sh

# 删除旧用户数据 【【重要步骤】】
adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*

# 删除/mnt/UDISK/目录下所有内容以腾出空间 【【重要步骤】】
adb -s 303_usb_device shell rm -rf /mnt/UDISK/*

# 将固件包swu文件包push到/mnt/UDISK/目录
adb -s 303_usb_device push /system/ota/QH303_V197_20250718.swu  /mnt/UDISK/

# 执行ota升级命令，执行完该命令设备会重启，执行命令与设备重启就绪总共时间约50秒
adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20250718.swu -e stable,upgrade_recovery

# 查看升级后固件版本号：
adb -s 303_usb_device shell cat /usr/bin/qdreamer/qsound/version.txt