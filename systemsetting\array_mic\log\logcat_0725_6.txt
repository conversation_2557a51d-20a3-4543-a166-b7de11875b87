2025-07-25 17:51:23.530  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DELETING_USER_DATA
2025-07-25 17:51:23.532  9689-9796  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*
2025-07-25 17:51:25.887  9689-9796  ArrayMicOTA             com.eeo.systemsetting                D  Local command [adb -s 303_usb_device shell rm -rf /overlay/upper/usr/bin/qdreamer/*] finished with exit code: 0
2025-07-25 17:51:27.891  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: EXECUTING_UPGRADE
2025-07-25 17:51:27.893  9689-9801  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery
2025-07-25 17:51:27.921  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: config new swupdate
2025-07-25 17:51:27.923  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_input: ##-i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery##
2025-07-25 17:51:28.761  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## set swupdate_param done ##
2025-07-25 17:51:28.866  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## Error: "swu_version" not defined
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_param: ##-i /mnt/UDISK/QH303_V197_20240712.swu##
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_software: ##stable##
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_mode: ##upgrade_recovery##
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###now do swupdate###
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ###log in /mnt/UDISK/swupdate.log###
2025-07-25 17:51:28.868  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: ## swupdate -v  -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery ##
2025-07-25 17:51:32.961  9689-9803  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: swu_next: ##reboot##
2025-07-25 17:51:33.561  9689-9801  ArrayMicOTA             com.eeo.systemsetting                D  Local command [adb -s 303_usb_device shell swupdate_cmd.sh -i /mnt/UDISK/QH303_V197_20240712.swu -e stable,upgrade_recovery] finished with exit code: 0
2025-07-25 17:51:33.562  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_DISCONNECT
2025-07-25 17:51:33.612  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Device disconnected for reboot.
2025-07-25 17:51:33.612  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: MONITORING_REBOOT_CONNECT
2025-07-25 17:52:06.098  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Device reconnected after reboot.
2025-07-25 17:52:11.104  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: FINAL_VERSION_VALIDATION
2025-07-25 17:52:11.149  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Update successful! New version: A013
2025-07-25 17:52:11.150  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: Update success.
2025-07-25 17:52:11.150  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 17:52:11.150  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Starting cleanup...
2025-07-25 17:52:11.150  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 1/3 to switch USB to PC.
2025-07-25 17:52:11.152  9689-9837  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: sample_xml_usbsw s side PC
2025-07-25 17:52:12.176  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 17:52:12.177  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 17:52:12.177  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 72529960,0,0.
2025-07-25 17:52:12.177  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 17:52:12.177  9689-9837  ArrayMicOTA             com.eeo.systemsetting                D  Local command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 17:52:12.178  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 17:52:12.179  9689-9839  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 17:52:22.319  9689-9689  ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect on attempt 1
2025-07-25 17:52:22.319  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 2/3 to switch USB to PC.
2025-07-25 17:52:22.322  9689-9847  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: sample_xml_usbsw s side PC
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 206755880,0,0.
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 17:52:23.350  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 17:52:23.351  9689-9849  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 17:52:23.352  9689-9847  ArrayMicOTA             com.eeo.systemsetting                D  Local command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 17:52:33.484  9689-9689  ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect on attempt 2
2025-07-25 17:52:33.484  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Attempt 3/3 to switch USB to PC.
2025-07-25 17:52:33.485  9689-9857  ArrayMicOTA             com.eeo.systemsetting                D  Executing local command: sample_xml_usbsw s side PC
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO start
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: open SWOGPIO err
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_in line:117 params error 231381032,0,0.
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_out line:159 params error.
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [sample-xml-usbsw][e] liufeng begin start resource!
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: [myfifo][e] myfifo_free line:245 params error.
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: version: v1.0.0 
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: help
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: : input "sample_xml_usbsw s xxx yyy" to set the xxx object to yyy channel
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: xxx:    side , front_obj  
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: yyy:    SOC  , PC , OUT1 - OUT6
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: /*                             
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT1 : soc touch               
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT2 : soc type c              
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT3 : front touch             
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: OUT4 : front type c            
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: */
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: example: sample_xml_usbsw s side SOC 
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer: 
2025-07-25 17:52:34.514  9689-9859  ArrayMicOTA             com.eeo.systemsetting                V  Stream consumer:  
2025-07-25 17:52:34.515  9689-9857  ArrayMicOTA             com.eeo.systemsetting                D  Local command [sample_xml_usbsw s side PC] finished with exit code: 0
2025-07-25 17:52:44.649  9689-9689  ArrayMicOTA             com.eeo.systemsetting                W  Failed to verify disconnect on attempt 3
2025-07-25 17:52:44.649  9689-9689  ArrayMicOTA             com.eeo.systemsetting                E  Cleanup failed. Could not verify disconnect after 3 attempts.
2025-07-25 17:52:44.649  9689-9689  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-25 17:52:44.656  9689-9689  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@731ab6d
2025-07-25 17:52:44.656  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-25 17:52:44.656  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-25 17:52:44.656  9689-9689  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
