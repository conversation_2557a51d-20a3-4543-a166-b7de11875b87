<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_have_update_width"
    android:layout_height="@dimen/dialog_have_update_height"
    android:background="@drawable/shape_windows_host_bg"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_factory_reset_title_margin_top"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="@dimen/dialog_factory_reset_iv_warn_width"
            android:layout_height="@dimen/dialog_factory_reset_iv_warn_height"
            android:background="@drawable/ic_warn" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dialog_factory_reset_title_margin_start"
            android:text="@string/window_host_title"
            android:textColor="@color/black_100"
            android:textSize="@dimen/dialog_have_update_title_text_size" />

    </LinearLayout>


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_factory_reset_content_margin_top"
        android:text="@string/window_host_content"
        android:textColor="@color/text_black_100"
        android:textSize="@dimen/dialog_have_update_content_text_size"

        />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_factory_reset_btn_cancel_margin_top"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/SystemSetting_PopWindow_Host_Btn"
            android:background="@drawable/shape_shutdown_btn_white"
            android:text="@string/cancel" />

        <Button
            android:id="@+id/btn_confirm"
            style="@style/SystemSetting_PopWindow_Host_Btn"
            android:layout_marginStart="@dimen/dialog_have_update_btn_cancel_margin_start"
            android:background="@drawable/shape_shutdown_btn_green"
            android:text="@string/confirm"
            android:textColor="@color/white_100" />
    </LinearLayout>


</LinearLayout>