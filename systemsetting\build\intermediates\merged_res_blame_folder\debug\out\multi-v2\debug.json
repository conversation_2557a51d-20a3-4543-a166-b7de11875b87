{"logs": [{"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2904", "endColumns": "100", "endOffsets": "3000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2816", "endColumns": "100", "endOffsets": "2912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2905", "endColumns": "100", "endOffsets": "3001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2860", "endColumns": "100", "endOffsets": "2956"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2897", "endColumns": "100", "endOffsets": "2993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v23_values-v23.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b71206ba2da564120cdfd0a6235d6d6f\\transformed\\jetified-cardview-1.0.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "206"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "3674", "endLines": "55", "endColumns": "12", "endOffsets": "3825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1371,2267,2386,2513,2735,2959,3074,3181,3294", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1366,2262,2381,2508,2730,2954,3069,3176,3289,3519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631c254547bfc5df4d5287473bf942d8\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3524", "endLines": "52", "endColumns": "12", "endOffsets": "3669"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2883", "endColumns": "100", "endOffsets": "2979"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2764", "endColumns": "100", "endOffsets": "2860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2770", "endColumns": "100", "endOffsets": "2866"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2845", "endColumns": "100", "endOffsets": "2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2935", "endColumns": "100", "endOffsets": "3031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2568,2751,2851", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,107,182,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2563,2746,2846,2930"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values-zh-rCN\\string.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,162,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,600,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8787,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,634,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8823,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,13,481,482,483,511,512,513,514,515,516,518,519,520,521,522,523,524,526,527,528,529,530,531,532,533,534,535,536,537,538,539,543,544,545,546,547,548,549,550,551,552,553,554,556,557,558,560,561,562,563,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,627,628,629,630,631,632,634,635,636,637,638,639,640,641,642,643,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,701,702,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,726,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,417,28895,28934,28973,31569,31608,31646,31689,31738,31778,31874,31910,31955,32003,32051,32101,32139,32238,32287,32350,32396,32447,32492,32541,32599,32652,32714,32782,32835,32875,32929,33135,33188,33246,33308,33370,33430,33504,33548,33587,33632,33675,33728,33824,33884,33938,34035,34072,34107,34153,34256,34300,34349,34395,34434,34488,34543,34604,34659,34719,34776,34831,34890,34948,35010,35067,35124,35177,35270,35306,35364,35400,35449,35497,35564,35623,35693,35740,35805,35849,35893,35929,35980,36029,36081,36118,36155,36199,36244,36290,36328,36369,36410,36478,36551,36611,36673,36756,36821,36884,36951,37021,37079,37125,37199,37278,37354,37412,37475,37537,37618,37735,37840,37945,37985,38073,38158,38318,38364,38404,38454,38496,38557,38599,38651,38707,38750,38855,38914,38959,38998,39060,39122,39172,39211,39248,39297,39360,39410,39474,39522,39584,39629,39675,39723,39759,39807,39874,39942,40012,40064,40136,40184,40240,40291,40340,40400,40463,40535,40578,40624,40683,40744,40810,40859,40991,41039,41085,41132,41181,41220,41260,41323,41380,41443,41542,41600,41650,41687,41739,41789,41940,41986,42151,42196,42244,42307,42355,42401,42464,42515,42575,42624,42663,42715,42761,42813,42861,42929,42998,43038,43082,43267,43553,43592,43641,43704,43752,43798,43842,43922,43989,44042,44115,44178,44219,44272,44326,44388,44439,44488,44551,44630,44688,44754,44808,44860,44902", "endLines": "5,12,18,481,482,483,511,512,513,514,515,516,518,519,520,521,522,523,524,526,527,528,529,530,531,532,533,534,535,536,537,538,539,543,544,545,546,547,548,549,550,551,552,553,554,556,557,558,560,561,562,563,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,627,628,629,630,631,632,634,635,636,637,638,639,640,641,642,643,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,701,702,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,726,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755", "endColumns": "19,19,19,38,38,38,38,37,42,48,39,50,35,44,47,47,49,37,48,48,62,45,50,44,48,57,52,61,67,52,39,53,38,52,57,61,61,59,73,43,38,44,42,52,48,59,53,43,36,34,45,46,43,48,45,38,53,54,60,54,59,56,54,58,57,61,56,56,52,92,35,57,35,48,47,66,58,69,46,64,43,43,35,50,48,51,36,36,43,44,45,37,40,40,67,72,59,61,82,64,62,66,69,57,45,73,78,75,57,62,61,80,72,104,104,39,87,84,102,45,39,49,41,60,41,51,55,42,46,58,44,38,61,61,49,38,36,48,62,49,63,47,61,44,45,47,35,47,66,67,69,51,71,47,55,50,48,59,62,71,42,45,58,60,65,48,52,47,45,46,48,38,39,62,56,62,98,57,49,36,51,49,49,45,49,44,47,62,47,45,62,50,59,48,38,51,45,51,47,67,68,39,43,52,68,38,48,62,47,45,43,79,66,52,72,62,40,52,53,61,50,48,62,78,57,65,53,51,41,57", "endOffsets": "211,412,604,28929,28968,29007,31603,31641,31684,31733,31773,31824,31905,31950,31998,32046,32096,32134,32183,32282,32345,32391,32442,32487,32536,32594,32647,32709,32777,32830,32870,32924,32963,33183,33241,33303,33365,33425,33499,33543,33582,33627,33670,33723,33772,33879,33933,33977,34067,34102,34148,34195,34295,34344,34390,34429,34483,34538,34599,34654,34714,34771,34826,34885,34943,35005,35062,35119,35172,35265,35301,35359,35395,35444,35492,35559,35618,35688,35735,35800,35844,35888,35924,35975,36024,36076,36113,36150,36194,36239,36285,36323,36364,36405,36473,36546,36606,36668,36751,36816,36879,36946,37016,37074,37120,37194,37273,37349,37407,37470,37532,37613,37686,37835,37940,37980,38068,38153,38256,38359,38399,38449,38491,38552,38594,38646,38702,38745,38792,38909,38954,38993,39055,39117,39167,39206,39243,39292,39355,39405,39469,39517,39579,39624,39670,39718,39754,39802,39869,39937,40007,40059,40131,40179,40235,40286,40335,40395,40458,40530,40573,40619,40678,40739,40805,40854,40907,41034,41080,41127,41176,41215,41255,41318,41375,41438,41537,41595,41645,41682,41734,41784,41834,41981,42031,42191,42239,42302,42350,42396,42459,42510,42570,42619,42658,42710,42756,42808,42856,42924,42993,43033,43077,43130,43331,43587,43636,43699,43747,43793,43837,43917,43984,44037,44110,44173,44214,44267,44321,44383,44434,44483,44546,44625,44683,44749,44803,44855,44897,44955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,683", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29012,29107,29202,29302,29384,29481,29587,29664,29739,29830,29923,30020,30116,30210,30303,30398,30490,30581,30672,30750,30846,30941,31036,31133,31229,31327,31475,40912", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "29102,29197,29297,29379,29476,29582,29659,29734,29825,29918,30015,30111,30205,30298,30393,30485,30576,30667,30745,30841,30936,31031,31128,31224,31322,31470,31564,40986"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\ota\\build\\intermediates\\packaged_res\\debug\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "517,525,540,541,542,555,559,564,626,633,644,703,704,724,725,727,728,729,730", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31829,32188,32968,33021,33077,33777,33982,34200,37691,38261,38797,42036,42092,43135,43182,43336,43386,43448,43496", "endColumns": "44,49,52,55,57,46,52,55,43,56,57,55,58,46,84,49,61,47,56", "endOffsets": "31869,32233,33016,33072,33130,33819,34030,34251,37730,38313,38850,42087,42146,43177,43262,43381,43443,43491,43548"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values-zh-rCN\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,418,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,53,54,56,-1,-1,-1,-1,58,57,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24000,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2762,2652,2708,2819,-1,-1,-1,-1,2925,2872,2980,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,54,52,51,-1,-1,-1,-1,53,51,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2646,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24072,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2813,2702,2756,2866,-1,-1,-1,-1,2974,2919,3030,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "609,655,708,759,808,853,896,951,998,1049,1102,1152,1198,1245,1295,1351,1405,1454,1509,1564,1615,1673,1723,1774,1828,1873,1922,1966,2017,2064,2106,2146,2220,2291,2358,2424,2494,2563,2638,2712,2780,2847,2905,2972,3037,3094,3170,3241,3300,3358,3417,3482,3547,3617,3685,3759,3826,3901,3977,4050,4120,4185,4251,4327,4391,4443,4504,4564,4620,4681,4739,4791,4849,4910,4971,5040,5116,5175,5239,5301,5361,5427,5487,5542,5601,5666,5729,5787,5863,5928,5990,6046,6106,6161,6223,6281,6354,6424,6494,6556,6622,6692,6765,6836,6905,6968,7041,7105,7163,7226,7288,7348,7406,7470,7531,7598,7658,7723,7790,7857,7915,7980,8037,8094,8161,8221,8290,8359,8429,8497,8577,8653,8716,8790,8860,8930,9001,9072,9147,9217,9291,9363,9439,9519,9599,9675,9744,9811,9872,9935,9997,10069,10138,10205,10273,10346,10413,10482,10551,10613,10675,10746,10801,10861,10919,10974,11030,11095,11159,11234,11294,11342,11405,11461,11521,11576,11632,11694,11761,11833,11904,11971,12033,12095,12161,12234,12295,12362,12425,12487,12554,12620,12687,12760,12833,12892,12959,13027,13092,13159,13224,13287,13347,13406,13462,13523,13588,13634,13686,13736,13781,13829,13883,13935,13982,14030,14082,14129,14179,14235,14290,14339,14390,14452,14518,14580,14646,14716,14781,14844,14906,14980,15053,15113,15175,15235,15298,15365,15428,15498,15566,15633,15699,15747,15806,15857,15903,15950,15997,16043,16087,16137,16190,16246,16289,16336,16386,16432,16484,16529,16573,16630,16682,16737,16792,16836,16886,16935,16984,17035,17086,17131,17203,17270,17321,17369,17423,17475,17527,17579,17633,17681,17732,17794,17862,17934,18006,18066,18132,18196,18255,18315,18378,18442,18501,18563,18629,18690,18756,18822,18883,18959,19037,19112,19180,19252,19324,19402,19484,19570,19656,19737,19798,19863,19927,19990,20052,20126,20187,20252,20313,20373,20440,20506,20568,20635,20702,20767,20832,20894,20960,21020,21078,21134,21193,21256,21315,21380,21443,21505,21563,21622,21679,21738,21797,21854,21908,21963,22021,22069,22124,22174,22222,22281,22327,22372,22421,22476,22529,22586,22643,22697,22747,22796,22850,22902,22955,23011,23067,23122,23170,23224,23273,23323,23373,23432,23490,23551,23615,23675,23744,23804,23886,23960,24022,24099,24159,24220,24285,24350,24417,24478,24543,24614,24682,24726,24779,24830,24886,24940,25001,25056,25117,25178,25232,25294,25363,25431,25493,25541,25592,25649,25699,25754,25810,25864,25912,25956,26012,26067,26120,26172,26224,26273,26328,26379,26433,26485,26540,26597,26645,26697,26747,26794,26848,26901,26950,27004,27063,27114,27187,27264,27345,27427,27510,27589,27666,27738,27817,27893,27964,28043,28120,28196,28271,28344,28429,28514,28603,28689,28774,28835", "endColumns": "45,52,50,48,44,42,54,46,50,52,49,45,46,49,55,53,48,54,54,50,57,49,50,53,44,48,43,50,46,41,39,73,70,66,65,69,68,74,73,67,66,57,66,64,56,75,70,58,57,58,64,64,69,67,73,66,74,75,72,69,64,65,75,63,51,60,59,55,60,57,51,57,60,60,68,75,58,63,61,59,65,59,54,58,64,62,57,75,64,61,55,59,54,61,57,72,69,69,61,65,69,72,70,68,62,72,63,57,62,61,59,57,63,60,66,59,64,66,66,57,64,56,56,66,59,68,68,69,67,79,75,62,73,69,69,70,70,74,69,73,71,75,79,79,75,68,66,60,62,61,71,68,66,67,72,66,68,68,61,61,70,54,59,57,54,55,64,63,74,59,47,62,55,59,54,55,61,66,71,70,66,61,61,65,72,60,66,62,61,66,65,66,72,72,58,66,67,64,66,64,62,59,58,55,60,64,45,51,49,44,47,53,51,46,47,51,46,49,55,54,48,50,61,65,61,65,69,64,62,61,73,72,59,61,59,62,66,62,69,67,66,65,47,58,50,45,46,46,45,43,49,52,55,42,46,49,45,51,44,43,56,51,54,54,43,49,48,48,50,50,44,71,66,50,47,53,51,51,51,53,47,50,61,67,71,71,59,65,63,58,59,62,63,58,61,65,60,65,65,60,75,77,74,67,71,71,77,81,85,85,80,60,64,63,62,61,73,60,64,60,59,66,65,61,66,66,64,64,61,65,59,57,55,58,62,58,64,62,61,57,58,56,58,58,56,53,54,57,47,54,49,47,58,45,44,48,54,52,56,56,53,49,48,53,51,52,55,55,54,47,53,48,49,49,58,57,60,63,59,68,59,81,73,61,76,59,60,64,64,66,60,64,70,67,43,52,50,55,53,60,54,60,60,53,61,68,67,61,47,50,56,49,54,55,53,47,43,55,54,52,51,51,48,54,50,53,51,54,56,47,51,49,46,53,52,48,53,58,50,72,76,80,81,82,78,76,71,78,75,70,78,76,75,74,72,84,84,88,85,84,60,59", "endOffsets": "650,703,754,803,848,891,946,993,1044,1097,1147,1193,1240,1290,1346,1400,1449,1504,1559,1610,1668,1718,1769,1823,1868,1917,1961,2012,2059,2101,2141,2215,2286,2353,2419,2489,2558,2633,2707,2775,2842,2900,2967,3032,3089,3165,3236,3295,3353,3412,3477,3542,3612,3680,3754,3821,3896,3972,4045,4115,4180,4246,4322,4386,4438,4499,4559,4615,4676,4734,4786,4844,4905,4966,5035,5111,5170,5234,5296,5356,5422,5482,5537,5596,5661,5724,5782,5858,5923,5985,6041,6101,6156,6218,6276,6349,6419,6489,6551,6617,6687,6760,6831,6900,6963,7036,7100,7158,7221,7283,7343,7401,7465,7526,7593,7653,7718,7785,7852,7910,7975,8032,8089,8156,8216,8285,8354,8424,8492,8572,8648,8711,8785,8855,8925,8996,9067,9142,9212,9286,9358,9434,9514,9594,9670,9739,9806,9867,9930,9992,10064,10133,10200,10268,10341,10408,10477,10546,10608,10670,10741,10796,10856,10914,10969,11025,11090,11154,11229,11289,11337,11400,11456,11516,11571,11627,11689,11756,11828,11899,11966,12028,12090,12156,12229,12290,12357,12420,12482,12549,12615,12682,12755,12828,12887,12954,13022,13087,13154,13219,13282,13342,13401,13457,13518,13583,13629,13681,13731,13776,13824,13878,13930,13977,14025,14077,14124,14174,14230,14285,14334,14385,14447,14513,14575,14641,14711,14776,14839,14901,14975,15048,15108,15170,15230,15293,15360,15423,15493,15561,15628,15694,15742,15801,15852,15898,15945,15992,16038,16082,16132,16185,16241,16284,16331,16381,16427,16479,16524,16568,16625,16677,16732,16787,16831,16881,16930,16979,17030,17081,17126,17198,17265,17316,17364,17418,17470,17522,17574,17628,17676,17727,17789,17857,17929,18001,18061,18127,18191,18250,18310,18373,18437,18496,18558,18624,18685,18751,18817,18878,18954,19032,19107,19175,19247,19319,19397,19479,19565,19651,19732,19793,19858,19922,19985,20047,20121,20182,20247,20308,20368,20435,20501,20563,20630,20697,20762,20827,20889,20955,21015,21073,21129,21188,21251,21310,21375,21438,21500,21558,21617,21674,21733,21792,21849,21903,21958,22016,22064,22119,22169,22217,22276,22322,22367,22416,22471,22524,22581,22638,22692,22742,22791,22845,22897,22950,23006,23062,23117,23165,23219,23268,23318,23368,23427,23485,23546,23610,23670,23739,23799,23881,23955,24017,24094,24154,24215,24280,24345,24412,24473,24538,24609,24677,24721,24774,24825,24881,24935,24996,25051,25112,25173,25227,25289,25358,25426,25488,25536,25587,25644,25694,25749,25805,25859,25907,25951,26007,26062,26115,26167,26219,26268,26323,26374,26428,26480,26535,26592,26640,26692,26742,26789,26843,26896,26945,26999,27058,27109,27182,27259,27340,27422,27505,27584,27661,27733,27812,27888,27959,28038,28115,28191,28266,28339,28424,28509,28598,28684,28769,28830,28890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "700", "startColumns": "4", "startOffsets": "41839", "endColumns": "100", "endOffsets": "41935"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2750", "endColumns": "100", "endOffsets": "2846"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2934", "endColumns": "100", "endOffsets": "3030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2930", "endColumns": "100", "endOffsets": "3026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-large-v4_values-large-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2929", "endColumns": "100", "endOffsets": "3025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2904", "endColumns": "100", "endOffsets": "3000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2883", "endColumns": "100", "endOffsets": "2979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2958", "endColumns": "100", "endOffsets": "3054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,2953"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2869", "endColumns": "100", "endOffsets": "2965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2894", "endColumns": "100", "endOffsets": "2990"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2958", "endColumns": "100", "endOffsets": "3054"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2856", "endColumns": "100", "endOffsets": "2952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2854", "endColumns": "100", "endOffsets": "2950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2803", "endColumns": "100", "endOffsets": "2899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2860", "endColumns": "100", "endOffsets": "2956"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2917", "endColumns": "100", "endOffsets": "3013"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2837", "endColumns": "100", "endOffsets": "2933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2903", "endColumns": "100", "endOffsets": "2999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-hdpi-v4_values-hdpi-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "13", "endOffsets": "327"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values_values.arsc.flat", "map": [{"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\ota\\build\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "171,173,176,213,221,222,223,225,263,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,1031,1039,1054,1055,1056,1069,1073,1078,1140,1147,1158,1217,1218,1238,1239,1241,1242,1243,1244,2558,3033", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8045,8136,8271,10609,10957,10999,11041,11132,13363,25855,25930,26002,26063,26123,26194,26264,26337,26408,26478,26553,26623,26691,26744,26820,26893,26945,27002,27058,27121,60557,60942,61918,61978,62037,62932,63185,63453,67669,68448,69116,72914,72982,74324,74388,74721,74792,74869,74946,157229,190789", "endLines": "171,173,176,213,221,222,223,225,263,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,1031,1039,1054,1055,1056,1069,1073,1078,1140,1147,1158,1217,1218,1238,1239,1241,1242,1243,1244,2564,3036", "endColumns": "44,44,44,40,41,41,44,47,45,74,71,60,59,70,69,72,70,69,74,69,67,52,75,72,51,56,55,62,59,44,53,59,58,112,59,85,96,60,59,73,67,74,63,190,70,76,76,101,12,12", "endOffsets": "8085,8176,8311,10645,10994,11036,11081,11175,13404,25925,25997,26058,26118,26189,26259,26332,26403,26473,26548,26618,26686,26739,26815,26888,26940,26997,27053,27116,27176,60597,60991,61973,62032,62145,62987,63266,63545,67725,68503,69185,72977,73052,74383,74574,74787,74864,74941,75043,157595,190931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\023b8ab3be5e105d1ca8b1fc8939c68c\\transformed\\jetified-glide-4.9.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "970", "startColumns": "4", "startOffsets": "57006", "endColumns": "57", "endOffsets": "57059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631c254547bfc5df4d5287473bf942d8\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "28,192,193,194,195,424,425,426,1350,2407,2409,2412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "972,9353,9414,9476,9538,22952,23011,23068,81941,148872,148936,149062", "endLines": "28,192,193,194,195,424,425,426,1356,2408,2411,2414", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1019,9409,9471,9533,9597,23006,23063,23117,82350,148931,149057,149185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,39,40,149,150,151,152,153,154,155,156,157,158,159,166,167,168,169,178,179,180,181,182,183,190,191,204,205,206,207,208,209,211,212,227,228,234,235,236,237,238,239,240,241,242,243,244,245,249,250,251,252,253,254,255,256,264,265,267,268,269,270,273,274,275,276,282,283,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,494,495,592,593,594,595,596,597,598,875,876,877,878,879,880,881,882,966,967,968,969,971,975,976,977,989,990,991,992,993,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1197,1324,1325,1326,1327,1328,1329,1337,1338,1342,1346,1357,1362,1368,1375,1379,1383,1388,1392,1396,1400,1404,1408,1412,1418,1422,1428,1432,1438,1442,1447,1451,1454,1458,1464,1468,1474,1478,1484,1487,1491,1495,1499,1503,1507,1508,1509,1510,1513,1516,1519,1522,1526,1527,1528,1529,1530,1533,1535,1537,1539,1544,1545,1549,1555,1559,1560,1562,1573,1574,1578,1584,1588,1589,1590,1594,1621,1625,1626,1630,1658,1827,1853,2022,2048,2079,2087,2093,2107,2129,2134,2139,2149,2158,2167,2171,2178,2186,2193,2194,2203,2206,2209,2213,2217,2221,2224,2225,2230,2235,2245,2250,2257,2263,2264,2267,2271,2276,2278,2280,2283,2286,2288,2292,2295,2302,2305,2308,2312,2314,2318,2320,2322,2324,2328,2336,2344,2356,2362,2371,2374,2385,2388,2389,2394,2395,2565,2634,2704,2705,2715,2724,2746,2748,2752,2755,2758,2761,2764,2767,2770,2773,2777,2780,2783,2786,2790,2793,2797,2930,2931,2932,2933,2934,2935,2936,2937,2938,2939,2940,2941,2942,2943,2944,2945,2946,2947,2948,2949,2950,2952,2954,2955,2956,2957,2958,2959,2960,2961,2963,2964,2966,2967,2969,2971,2972,2974,2975,2976,2977,2978,2979,2981,2982,2983,2984,2985,2997,2999,3001,3003,3004,3005,3006,3007,3008,3009,3010,3011,3012,3013,3014,3015,3017,3018,3019,3020,3021,3022,3023,3025,3029,3050,3051,3052,3053,3054,3055,3059,3060,3061,3095,3097,3099,3101,3103,3105,3106,3107,3108,3110,3112,3114,3115,3116,3117,3118,3119,3120,3121,3122,3123,3124,3125,3128,3129,3130,3131,3133,3135,3136,3138,3139,3141,3143,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3156,3158,3159,3160,3161,3163,3164,3165,3166,3167,3169,3171,3173,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,1474,1519,6613,6654,6709,6768,6830,6894,6964,7025,7100,7176,7253,7684,7769,7851,7927,8364,8441,8519,8625,8731,8810,9238,9295,10017,10091,10166,10231,10297,10357,10464,10536,11224,11291,11587,11646,11705,11764,11823,11882,11936,11990,12043,12097,12151,12205,12439,12513,12592,12665,12739,12810,12882,12954,13409,13466,13587,13660,13734,13808,13988,14060,14133,14203,14505,14565,15976,16045,16114,16184,16258,16334,16398,16475,16551,16628,16693,16762,16839,16914,16983,17051,17128,17194,17255,17352,17417,17486,17585,17656,17715,17773,17830,17889,17953,18024,18096,18168,18240,18312,18379,18447,18515,18574,18637,18701,18791,18882,18942,19008,19075,19141,19211,19275,19328,19395,19456,19523,19636,19694,19757,19822,19887,19962,20035,20107,20156,20217,20278,20339,20401,20465,20529,20593,20658,20721,20781,20842,20908,20967,21027,21089,21160,21220,27581,27667,33980,34070,34157,34245,34327,34410,34500,51107,51159,51217,51262,51328,51392,51449,51506,56801,56858,56906,56955,57064,57234,57281,57330,57936,57968,58032,58094,58154,58402,58476,58546,58624,58678,58748,58833,58881,58927,58988,59051,59117,59181,59252,59315,59380,59444,59505,59566,59618,59691,59765,59834,59909,59983,60057,60198,71667,80048,80126,80216,80304,80400,80490,81072,81161,81408,81689,82355,82640,83033,83510,83732,83954,84230,84457,84687,84917,85147,85377,85604,86023,86249,86674,86904,87332,87551,87834,88042,88173,88400,88826,89051,89478,89699,90124,90244,90520,90821,91145,91436,91750,91887,92018,92123,92365,92532,92736,92944,93215,93327,93439,93544,93661,93875,94021,94161,94247,94595,94683,94929,95347,95596,95678,95776,96368,96468,96720,97144,97399,97493,97582,97819,99843,100085,100187,100440,102596,113037,114553,125092,126620,128377,129003,129423,130484,131749,132005,132241,132788,133282,133887,134085,134665,135229,135604,135722,136260,136417,136613,136886,137142,137312,137453,137517,137882,138249,138925,139189,139527,139880,139974,140160,140466,140728,140853,140980,141219,141430,141549,141742,141919,142374,142555,142677,142936,143049,143236,143338,143445,143574,143849,144357,144853,145730,146024,146594,146743,147475,147647,147731,148067,148159,157600,162846,168235,168297,168875,169459,170775,170888,171117,171277,171429,171600,171766,171935,172102,172265,172508,172678,172851,173022,173296,173495,173700,181681,181765,181861,181957,182055,182155,182257,182359,182461,182563,182665,182765,182861,182973,183102,183225,183356,183487,183585,183699,183793,183933,184067,184163,184275,184375,184491,184587,184699,184799,184939,185075,185239,185369,185527,185677,185818,185962,186097,186209,186359,186487,186615,186751,186883,187013,187143,187255,188153,188299,188443,188581,188647,188737,188813,188917,189007,189109,189217,189325,189425,189505,189597,189695,189805,189857,189935,190041,190133,190237,190347,190469,190632,191438,191518,191618,191708,191818,191908,192149,192243,192349,194349,194449,194561,194675,194791,194907,195001,195115,195227,195329,195449,195571,195653,195757,195877,196003,196101,196195,196283,196395,196511,196633,196745,196920,197036,197122,197214,197326,197450,197517,197643,197711,197839,197983,198111,198180,198275,198390,198503,198602,198711,198822,198933,199034,199139,199239,199369,199460,199583,199677,199789,199875,199979,200075,200163,200281,200385,200489,200615,200703,200811,200911,201001,201111,201195,201297,201381,201435,201499,201605,201691,201801,201885", "endLines": "34,39,40,149,150,151,152,153,154,155,156,157,158,159,166,167,168,169,178,179,180,181,182,183,190,191,204,205,206,207,208,209,211,212,227,228,234,235,236,237,238,239,240,241,242,243,244,245,249,250,251,252,253,254,255,256,264,265,267,268,269,270,273,274,275,276,282,283,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,494,495,592,593,594,595,596,597,598,875,876,877,878,879,880,881,882,966,967,968,969,971,975,976,977,989,990,991,992,993,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1197,1324,1325,1326,1327,1328,1336,1337,1341,1345,1349,1361,1367,1374,1378,1382,1387,1391,1395,1399,1403,1407,1411,1417,1421,1427,1431,1437,1441,1446,1450,1453,1457,1463,1467,1473,1477,1483,1486,1490,1494,1498,1502,1506,1507,1508,1509,1512,1515,1518,1521,1525,1526,1527,1528,1529,1532,1534,1536,1538,1543,1544,1548,1554,1558,1559,1561,1572,1573,1577,1583,1587,1588,1589,1593,1620,1624,1625,1629,1657,1826,1852,2021,2047,2078,2086,2092,2106,2128,2133,2138,2148,2157,2166,2170,2177,2185,2192,2193,2202,2205,2208,2212,2216,2220,2223,2224,2229,2234,2244,2249,2256,2262,2263,2266,2270,2275,2277,2279,2282,2285,2287,2291,2294,2301,2304,2307,2311,2313,2317,2319,2321,2323,2327,2335,2343,2355,2361,2370,2373,2384,2387,2388,2393,2394,2399,2633,2703,2704,2714,2723,2724,2747,2751,2754,2757,2760,2763,2766,2769,2772,2776,2779,2782,2785,2789,2792,2796,2800,2930,2931,2932,2933,2934,2935,2936,2937,2938,2939,2940,2941,2942,2943,2944,2945,2946,2947,2948,2949,2951,2953,2954,2955,2956,2957,2958,2959,2960,2962,2963,2965,2966,2968,2970,2971,2973,2974,2975,2976,2977,2978,2980,2981,2982,2983,2984,2985,2998,3000,3002,3003,3004,3005,3006,3007,3008,3009,3010,3011,3012,3013,3014,3016,3017,3018,3019,3020,3021,3022,3024,3028,3032,3050,3051,3052,3053,3054,3058,3059,3060,3061,3096,3098,3100,3102,3104,3105,3106,3107,3109,3111,3113,3114,3115,3116,3117,3118,3119,3120,3121,3122,3123,3124,3127,3128,3129,3130,3132,3134,3135,3137,3138,3140,3142,3144,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3157,3158,3159,3160,3162,3163,3164,3165,3166,3168,3170,3172,3174,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "1342,1514,1563,6649,6704,6763,6825,6889,6959,7020,7095,7171,7248,7326,7764,7846,7922,7998,8436,8514,8620,8726,8805,8885,9290,9348,10086,10161,10226,10292,10352,10413,10531,10604,11286,11354,11641,11700,11759,11818,11877,11931,11985,12038,12092,12146,12200,12254,12508,12587,12660,12734,12805,12877,12949,13022,13461,13519,13655,13729,13803,13878,14055,14128,14198,14269,14560,14621,16040,16109,16179,16253,16329,16393,16470,16546,16623,16688,16757,16834,16909,16978,17046,17123,17189,17250,17347,17412,17481,17580,17651,17710,17768,17825,17884,17948,18019,18091,18163,18235,18307,18374,18442,18510,18569,18632,18696,18786,18877,18937,19003,19070,19136,19206,19270,19323,19390,19451,19518,19631,19689,19752,19817,19882,19957,20030,20102,20151,20212,20273,20334,20396,20460,20524,20588,20653,20716,20776,20837,20903,20962,21022,21084,21155,21215,21283,27662,27749,34065,34152,34240,34322,34405,34495,34586,51154,51212,51257,51323,51387,51444,51501,51555,56853,56901,56950,57001,57093,57276,57325,57371,57963,58027,58089,58149,58206,58471,58541,58619,58673,58743,58828,58876,58922,58983,59046,59112,59176,59247,59310,59375,59439,59500,59561,59613,59686,59760,59829,59904,59978,60052,60193,60263,71715,80121,80211,80299,80395,80485,81067,81156,81403,81684,81936,82635,83028,83505,83727,83949,84225,84452,84682,84912,85142,85372,85599,86018,86244,86669,86899,87327,87546,87829,88037,88168,88395,88821,89046,89473,89694,90119,90239,90515,90816,91140,91431,91745,91882,92013,92118,92360,92527,92731,92939,93210,93322,93434,93539,93656,93870,94016,94156,94242,94590,94678,94924,95342,95591,95673,95771,96363,96463,96715,97139,97394,97488,97577,97814,99838,100080,100182,100435,102591,113032,114548,125087,126615,128372,128998,129418,130479,131744,132000,132236,132783,133277,133882,134080,134660,135224,135599,135717,136255,136412,136608,136881,137137,137307,137448,137512,137877,138244,138920,139184,139522,139875,139969,140155,140461,140723,140848,140975,141214,141425,141544,141737,141914,142369,142550,142672,142931,143044,143231,143333,143440,143569,143844,144352,144848,145725,146019,146589,146738,147470,147642,147726,148062,148154,148432,162841,168230,168292,168870,169454,169545,170883,171112,171272,171424,171595,171761,171930,172097,172260,172503,172673,172846,173017,173291,173490,173695,174025,181760,181856,181952,182050,182150,182252,182354,182456,182558,182660,182760,182856,182968,183097,183220,183351,183482,183580,183694,183788,183928,184062,184158,184270,184370,184486,184582,184694,184794,184934,185070,185234,185364,185522,185672,185813,185957,186092,186204,186354,186482,186610,186746,186878,187008,187138,187250,187390,188294,188438,188576,188642,188732,188808,188912,189002,189104,189212,189320,189420,189500,189592,189690,189800,189852,189930,190036,190128,190232,190342,190464,190627,190784,191513,191613,191703,191813,191903,192144,192238,192344,192436,194444,194556,194670,194786,194902,194996,195110,195222,195324,195444,195566,195648,195752,195872,195998,196096,196190,196278,196390,196506,196628,196740,196915,197031,197117,197209,197321,197445,197512,197638,197706,197834,197978,198106,198175,198270,198385,198498,198597,198706,198817,198928,199029,199134,199234,199364,199455,199578,199672,199784,199870,199974,200070,200158,200276,200380,200484,200610,200698,200806,200906,200996,201106,201190,201292,201376,201430,201494,201600,201686,201796,201880,202000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b71206ba2da564120cdfd0a6235d6d6f\\transformed\\jetified-cardview-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "308,309,310,311,928,929,930,2400,3203,3205,3208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15715,15779,15844,15909,54499,54561,54621,148437,202732,202800,202933", "endLines": "308,309,310,311,928,929,930,2406,3204,3207,3210", "endColumns": "63,64,64,66,61,59,56,12,12,12,12", "endOffsets": "15774,15839,15904,15971,54556,54616,54673,148867,202795,202928,203063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\27f02ae307087a092deaed406cbb04d7\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "499,500,501,603,604,605,972", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27861,27920,27968,34819,34894,34970,57098", "endColumns": "58,47,55,74,75,71,65", "endOffsets": "27915,27963,28019,34889,34965,35037,57159"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "160,161,162,163,170,172,174,175,177,184,185,186,187,188,189,210,214,215,216,217,218,219,220,224,226,229,230,231,232,233,248,257,258,259,260,261,262,266,271,272,277,278,279,280,281,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7331,7374,7426,7476,8003,8090,8181,8226,8316,8890,8947,9004,9063,9122,9180,10418,10650,10694,10739,10781,10825,10869,10913,11086,11180,11359,11401,11443,11487,11533,12391,13027,13087,13158,13222,13269,13316,13524,13883,13932,14274,14319,14364,14408,14459,14626,14677,14729,14771,14815,14860,14906,14951,14996,15041,15086,15131,15176,15221,15265,15310,15355,15400,15445,15490,15535,15580,15625,15670", "endColumns": "42,51,49,47,41,45,44,44,47,56,56,58,58,57,57,45,43,44,41,43,43,43,43,45,43,41,41,43,45,53,47,59,70,63,46,46,46,62,48,55,44,44,43,50,45,50,51,41,43,44,45,44,44,44,44,44,44,44,43,44,44,44,44,44,44,44,44,44,44", "endOffsets": "7369,7421,7471,7519,8040,8131,8221,8266,8359,8942,8999,9058,9117,9175,9233,10459,10689,10734,10776,10820,10864,10908,10952,11127,11219,11396,11438,11482,11528,11582,12434,13082,13153,13217,13264,13311,13358,13582,13927,13983,14314,14359,14403,14454,14500,14672,14724,14766,14810,14855,14901,14946,14991,15036,15081,15126,15171,15216,15260,15305,15350,15395,15440,15485,15530,15575,15620,15665,15710"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,162,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,742,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10849,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,45,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,781,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10890,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,13,995,996,997,1025,1026,1027,1028,1029,1030,1032,1033,1034,1035,1036,1037,1038,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1070,1071,1072,1074,1075,1076,1077,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1141,1142,1143,1144,1145,1146,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1215,1216,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1240,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,424,58281,58324,58363,60268,60308,60351,60404,60464,60506,60602,60640,60692,60740,60788,60849,60891,60996,61055,61168,61216,61281,61327,61391,61472,61528,61600,61688,61744,61793,61879,62150,62217,62286,62359,62432,62503,62597,62647,62691,62743,62796,62867,62992,63087,63141,63271,63310,63351,63405,63550,63599,63662,63708,63752,63818,63873,63938,63995,64062,64123,64178,64241,64304,64370,64429,64486,64544,64699,64738,64856,64894,64943,65004,65093,65166,65313,65363,65485,65527,65577,65615,65672,65726,65797,65841,65882,65930,65988,66040,66082,66128,66173,66285,66376,66440,66508,66622,66693,66763,66837,66916,66978,67024,67112,67203,67290,67350,67417,67482,67584,67730,67903,68076,68122,68250,68337,68508,68577,68623,68683,68747,68825,68874,68934,69019,69067,69190,69328,69385,69429,69523,69590,69652,69694,69734,69786,69870,69924,70009,70059,70142,70186,70234,70292,70330,70386,70459,70531,70610,70662,70765,70830,70890,70946,71000,71070,71150,71250,71300,71355,71428,71489,71555,71607,71720,71776,71832,71880,71930,71975,72022,72117,72178,72248,72392,72452,72517,72557,72609,72666,72796,72852,73057,73109,73155,73241,73299,73364,73427,73487,73565,73625,73670,73734,73794,73859,73915,74034,74158,74200,74257,74579,75048,75087,75134,75259,75334,75386,75435,75541,75625,75680,75780,75865,75909,75969,76033,76103,76165,76228,76315,76456,76533,76615,76683,76749,76793", "endLines": "5,12,18,995,996,997,1025,1026,1027,1028,1029,1030,1032,1033,1034,1035,1036,1037,1038,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1070,1071,1072,1074,1075,1076,1077,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1141,1142,1143,1144,1145,1146,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1215,1216,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1240,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269", "endColumns": "19,19,19,42,38,38,39,42,52,59,41,50,37,51,47,47,60,41,50,58,112,47,64,45,63,80,55,71,87,55,48,85,38,66,68,72,72,70,93,49,43,51,52,70,64,94,53,43,38,40,53,47,48,62,45,43,65,54,64,56,66,60,54,62,62,65,58,56,57,154,38,117,37,48,60,88,72,146,49,121,41,49,37,56,53,70,43,40,47,57,51,41,45,44,111,90,63,67,113,70,69,73,78,61,45,87,90,86,59,66,64,101,84,172,172,45,127,86,110,68,45,59,63,77,48,59,84,47,48,137,56,43,93,66,61,41,39,51,83,53,84,49,82,43,47,57,37,55,72,71,78,51,102,64,59,55,53,69,79,99,49,54,72,60,65,51,59,55,55,47,49,44,46,94,60,69,143,59,64,39,51,56,58,55,61,51,45,85,57,64,62,59,77,59,44,63,59,64,55,118,123,41,56,66,141,38,46,124,74,51,48,105,83,54,99,84,43,59,63,69,61,62,86,140,76,81,67,65,43,75", "endOffsets": "211,419,615,58319,58358,58397,60303,60346,60399,60459,60501,60552,60635,60687,60735,60783,60844,60886,60937,61050,61163,61211,61276,61322,61386,61467,61523,61595,61683,61739,61788,61874,61913,62212,62281,62354,62427,62498,62592,62642,62686,62738,62791,62862,62927,63082,63136,63180,63305,63346,63400,63448,63594,63657,63703,63747,63813,63868,63933,63990,64057,64118,64173,64236,64299,64365,64424,64481,64539,64694,64733,64851,64889,64938,64999,65088,65161,65308,65358,65480,65522,65572,65610,65667,65721,65792,65836,65877,65925,65983,66035,66077,66123,66168,66280,66371,66435,66503,66617,66688,66758,66832,66911,66973,67019,67107,67198,67285,67345,67412,67477,67579,67664,67898,68071,68117,68245,68332,68443,68572,68618,68678,68742,68820,68869,68929,69014,69062,69111,69323,69380,69424,69518,69585,69647,69689,69729,69781,69865,69919,70004,70054,70137,70181,70229,70287,70325,70381,70454,70526,70605,70657,70760,70825,70885,70941,70995,71065,71145,71245,71295,71350,71423,71484,71550,71602,71662,71771,71827,71875,71925,71970,72017,72112,72173,72243,72387,72447,72512,72552,72604,72661,72720,72847,72909,73104,73150,73236,73294,73359,73422,73482,73560,73620,73665,73729,73789,73854,73910,74029,74153,74195,74252,74319,74716,75082,75129,75254,75329,75381,75430,75536,75620,75675,75775,75860,75904,75964,76028,76098,76160,76223,76310,76451,76528,76610,76678,76744,76788,76864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "164,165,246,247,427,428,429,430,431,432,433,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,973,974,978,979,980,981,982,983,984,985,986,987,988,994,1214,2986,2987,2991,2992,2996,3190,3191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7524,7596,12259,12328,23122,23192,23260,23332,23402,23463,23537,40462,40523,40584,40646,40710,40772,40833,40901,41001,41061,41127,41200,41269,41326,41378,54678,54750,54826,54891,54950,55009,55069,55129,55189,55249,55309,55369,55429,55489,55549,55609,55668,55728,55788,55848,55908,55968,56028,56088,56148,56208,56268,56327,56387,56447,56506,56565,56624,56683,56742,57164,57199,57376,57431,57494,57549,57607,57664,57714,57775,57832,57866,57901,58211,72725,187395,187512,187713,187823,188024,202005,202077", "endLines": "164,165,246,247,427,428,429,430,431,432,433,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,973,974,978,979,980,981,982,983,984,985,986,987,988,994,1214,2986,2990,2991,2995,2996,3190,3191", "endColumns": "71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "7591,7679,12323,12386,23187,23255,23327,23397,23458,23532,23605,40518,40579,40641,40705,40767,40828,40896,40996,41056,41122,41195,41264,41321,41373,41435,54745,54821,54886,54945,55004,55064,55124,55184,55244,55304,55364,55424,55484,55544,55604,55663,55723,55783,55843,55903,55963,56023,56083,56143,56203,56263,56322,56382,56442,56501,56560,56619,56678,56737,56796,57194,57229,57426,57489,57544,57602,57659,57709,57770,57827,57861,57896,57931,58276,72791,187507,187708,187818,188019,188148,202072,202139"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\style.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,29,38,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,68,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,362", "startColumns": "-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,1402,1846,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,3329,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19030", "endLines": "-1,-1,-1,-1,-1,-1,35,46,26,-1,-1,-1,-1,-1,-1,-1,-1,-1,74,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,370", "endColumns": "-1,-1,-1,-1,-1,-1,12,12,12,-1,-1,-1,-1,-1,-1,-1,-1,-1,12,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,12", "endOffsets": "-1,-1,-1,-1,-1,-1,1803,2361,1359,-1,-1,-1,-1,-1,-1,-1,-1,-1,3723,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19568"}, "to": {"startLines": "1270,1277,1286,1293,1305,1315,2415,2422,2431,2455,2464,2472,2478,2484,2492,2497,2502,2507,2514,2521,2528,2534,2541,2548,2725,2730,2739,2801,2814,2831,2837,2850,2857,2867,2877,2883,2892,2900,2906,2913,2921,3062,3069,3080,3086,3195,3211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "76869,77309,77877,78308,78993,79543,149190,149590,150102,151384,151914,152384,152738,153071,153527,153765,154014,154275,154702,155095,155477,155706,156090,156526,169550,169827,170393,174030,174779,175752,176100,176823,177222,177915,178610,178974,179454,179948,180283,180686,181148,192441,192810,193493,193818,202289,203068", "endLines": "1276,1285,1292,1304,1314,1323,2421,2430,2454,2463,2471,2477,2483,2491,2496,2501,2506,2513,2520,2527,2533,2540,2547,2557,2729,2738,2745,2813,2830,2836,2849,2856,2866,2876,2882,2891,2899,2905,2912,2920,2929,3068,3079,3085,3094,3202,3219", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "77304,77872,78303,78988,79538,80043,149585,150097,151379,151909,152379,152733,153066,153522,153760,154009,154270,154697,155090,155472,155701,156085,156521,157224,169822,170388,170770,174774,175747,176095,176818,177217,177910,178605,178969,179449,179943,180278,180681,181143,181676,192805,193488,193813,194344,202727,203598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25062f21db2e09c0f5c1d6cca5fceda8\\transformed\\constraintlayout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,29,30,31,32,35,41,42,43,44,47,48,51,54,55,56,57,58,61,64,65,66,67,72,75,78,79,80,85,86,87,90,93,94,97,100,103,106,107,110,113,114,119,120,125,128,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "620,681,1024,1072,1124,1185,1347,1568,1629,1689,1759,1892,1960,2089,2215,2277,2342,2410,2477,2600,2725,2792,2857,2922,3103,3224,3345,3411,3478,3688,3757,3823,3948,4074,4141,4267,4394,4519,4646,4711,4837,4960,5025,5233,5300,5480,5600,5720,5785,5847,5909,5971,6030,6090,6151,6212,6271", "endLines": "19,27,29,30,31,32,38,41,42,43,46,47,50,53,54,55,56,57,60,63,64,65,66,71,74,77,78,79,84,85,86,89,92,93,96,99,102,105,106,109,112,113,118,119,124,127,130,131,132,133,134,135,136,137,138,139,148", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11", "endOffsets": "676,967,1067,1119,1180,1226,1469,1624,1684,1754,1887,1955,2084,2210,2272,2337,2405,2472,2595,2720,2787,2852,2917,3098,3219,3340,3406,3473,3683,3752,3818,3943,4069,4136,4262,4389,4514,4641,4706,4832,4955,5020,5228,5295,5475,5595,5715,5780,5842,5904,5966,6025,6085,6146,6207,6266,6608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e57b8fb71f4a475f9f21d587a549e7c\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "33,3192", "startColumns": "4,4", "startOffsets": "1231,202144", "endLines": "33,3194", "endColumns": "60,12", "endOffsets": "1287,202284"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3037", "startColumns": "4", "startOffsets": "190936", "endLines": "3049", "endColumns": "12", "endOffsets": "191433"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\libtouchsdk\\build\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "196,197,198,199,200,421,422,423", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9602,9648,9695,9746,9798,22824,22866,22911", "endColumns": "45,46,50,51,56,41,44,40", "endOffsets": "9643,9690,9741,9793,9850,22861,22906,22947"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,420,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,53,54,56,-1,-1,-1,-1,58,57,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24107,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2762,2652,2708,2819,-1,-1,-1,-1,2925,2872,2980,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,54,52,51,-1,-1,-1,-1,53,51,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2646,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24179,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2813,2702,2756,2866,-1,-1,-1,-1,2974,2919,3030,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,487,488,489,490,491,492,493,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,599,600,601,602,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21288,21334,21387,21438,21486,21531,21574,21629,21676,21727,21780,21830,21876,21923,21973,22029,22083,22132,22187,22242,22293,22351,22401,22452,22506,22551,22600,22644,22695,22742,22784,23610,23684,23755,23822,23888,23958,24027,24102,24176,24244,24311,24369,24436,24501,24558,24634,24705,24764,24822,24881,24946,25011,25081,25149,25223,25290,25365,25441,25514,25584,25649,25715,25791,27181,27233,27294,27354,27410,27471,27529,28024,28082,28143,28204,28273,28348,28407,28471,28533,28593,28659,28719,28774,28833,28898,28961,29019,29097,29163,29225,29281,29341,29396,29458,29516,29589,29659,29729,29791,29857,29927,30000,30071,30139,30202,30275,30339,30397,30460,30522,30582,30640,30704,30765,30832,30892,30957,31024,31091,31149,31214,31271,31328,31395,31455,31524,31593,31663,31731,31811,31887,31950,32024,32094,32164,32235,32306,32381,32451,32525,32597,32673,32753,32833,32909,32978,33045,33106,33169,33231,33303,33372,33439,33507,33580,33647,33716,33785,33847,33909,34591,34646,34706,34764,35042,35098,35163,35227,35302,35362,35410,35473,35529,35589,35644,35700,35762,35829,35901,35972,36039,36101,36163,36229,36302,36363,36430,36493,36555,36622,36688,36755,36828,36901,36960,37027,37095,37160,37227,37292,37355,37415,37474,37530,37589,37650,37715,37761,37813,37863,37908,37956,38010,38062,38109,38155,38203,38255,38302,38352,38408,38463,38512,38563,38625,38691,38753,38819,38889,38954,39017,39079,39153,39226,39286,39348,39408,39471,39538,39601,39671,39739,39806,39872,39920,39979,40030,40076,40123,40170,40216,40260,40310,40363,40419,41440,41487,41537,41583,41635,41680,41724,41781,41833,41888,41943,41987,42037,42086,42135,42186,42237,42282,42354,42421,42472,42520,42574,42626,42678,42730,42784,42832,42883,42945,43013,43085,43157,43217,43283,43347,43406,43466,43529,43593,43652,43714,43780,43841,43907,43973,44034,44110,44188,44263,44331,44403,44475,44553,44635,44721,44807,44888,44949,45014,45078,45141,45203,45277,45338,45403,45464,45524,45591,45657,45719,45786,45853,45918,45983,46045,46111,46171,46229,46285,46344,46407,46466,46531,46594,46656,46714,46773,46830,46889,46948,47005,47059,47114,47172,47220,47275,47325,47373,47432,47478,47523,47572,47627,47680,47737,47794,47848,47898,47947,48001,48053,48106,48162,48218,48273,48321,48375,48424,48474,48524,48583,48641,48702,48766,48826,48895,48955,49037,49111,49173,49250,49310,49371,49436,49501,49568,49629,49694,49765,49833,49877,49930,49981,50037,50091,50152,50207,50268,50329,50383,50445,50514,50582,50644,50692,50743,50800,50850,50905,50961,51015,51063,51560,51616,51671,51724,51776,51828,51877,51932,51983,52037,52089,52144,52201,52249,52301,52351,52398,52452,52505,52554,52608,52667,52718,52791,52868,52949,53031,53114,53193,53270,53342,53421,53497,53568,53647,53724,53800,53875,53948,54033,54118,54207,54293,54378,54439", "endColumns": "45,52,50,47,44,42,54,46,50,52,49,45,46,49,55,53,48,54,54,50,57,49,50,53,44,48,43,50,46,41,39,73,70,66,65,69,68,74,73,67,66,57,66,64,56,75,70,58,57,58,64,64,69,67,73,66,74,75,72,69,64,65,75,63,51,60,59,55,60,57,51,57,60,60,68,74,58,63,61,59,65,59,54,58,64,62,57,77,65,61,55,59,54,61,57,72,69,69,61,65,69,72,70,67,62,72,63,57,62,61,59,57,63,60,66,59,64,66,66,57,64,56,56,66,59,68,68,69,67,79,75,62,73,69,69,70,70,74,69,73,71,75,79,79,75,68,66,60,62,61,71,68,66,67,72,66,68,68,61,61,70,54,59,57,54,55,64,63,74,59,47,62,55,59,54,55,61,66,71,70,66,61,61,65,72,60,66,62,61,66,65,66,72,72,58,66,67,64,66,64,62,59,58,55,58,60,64,45,51,49,44,47,53,51,46,45,47,51,46,49,55,54,48,50,61,65,61,65,69,64,62,61,73,72,59,61,59,62,66,62,69,67,66,65,47,58,50,45,46,46,45,43,49,52,55,42,46,49,45,51,44,43,56,51,54,54,43,49,48,48,50,50,44,71,66,50,47,53,51,51,51,53,47,50,61,67,71,71,59,65,63,58,59,62,63,58,61,65,60,65,65,60,75,77,74,67,71,71,77,81,85,85,80,60,64,63,62,61,73,60,64,60,59,66,65,61,66,66,64,64,61,65,59,57,55,58,62,58,64,62,61,57,58,56,58,58,56,53,54,57,47,54,49,47,58,45,44,48,54,52,56,56,53,49,48,53,51,52,55,55,54,47,53,48,49,49,58,57,60,63,59,68,59,81,73,61,76,59,60,64,64,66,60,64,70,67,43,52,50,55,53,60,54,60,60,53,61,68,67,61,47,50,56,49,54,55,53,47,43,55,54,52,51,51,48,54,50,53,51,54,56,47,51,49,46,53,52,48,53,58,50,72,76,80,81,82,78,76,71,78,75,70,78,76,75,74,72,84,84,88,85,84,60,59", "endOffsets": "21329,21382,21433,21481,21526,21569,21624,21671,21722,21775,21825,21871,21918,21968,22024,22078,22127,22182,22237,22288,22346,22396,22447,22501,22546,22595,22639,22690,22737,22779,22819,23679,23750,23817,23883,23953,24022,24097,24171,24239,24306,24364,24431,24496,24553,24629,24700,24759,24817,24876,24941,25006,25076,25144,25218,25285,25360,25436,25509,25579,25644,25710,25786,25850,27228,27289,27349,27405,27466,27524,27576,28077,28138,28199,28268,28343,28402,28466,28528,28588,28654,28714,28769,28828,28893,28956,29014,29092,29158,29220,29276,29336,29391,29453,29511,29584,29654,29724,29786,29852,29922,29995,30066,30134,30197,30270,30334,30392,30455,30517,30577,30635,30699,30760,30827,30887,30952,31019,31086,31144,31209,31266,31323,31390,31450,31519,31588,31658,31726,31806,31882,31945,32019,32089,32159,32230,32301,32376,32446,32520,32592,32668,32748,32828,32904,32973,33040,33101,33164,33226,33298,33367,33434,33502,33575,33642,33711,33780,33842,33904,33975,34641,34701,34759,34814,35093,35158,35222,35297,35357,35405,35468,35524,35584,35639,35695,35757,35824,35896,35967,36034,36096,36158,36224,36297,36358,36425,36488,36550,36617,36683,36750,36823,36896,36955,37022,37090,37155,37222,37287,37350,37410,37469,37525,37584,37645,37710,37756,37808,37858,37903,37951,38005,38057,38104,38150,38198,38250,38297,38347,38403,38458,38507,38558,38620,38686,38748,38814,38884,38949,39012,39074,39148,39221,39281,39343,39403,39466,39533,39596,39666,39734,39801,39867,39915,39974,40025,40071,40118,40165,40211,40255,40305,40358,40414,40457,41482,41532,41578,41630,41675,41719,41776,41828,41883,41938,41982,42032,42081,42130,42181,42232,42277,42349,42416,42467,42515,42569,42621,42673,42725,42779,42827,42878,42940,43008,43080,43152,43212,43278,43342,43401,43461,43524,43588,43647,43709,43775,43836,43902,43968,44029,44105,44183,44258,44326,44398,44470,44548,44630,44716,44802,44883,44944,45009,45073,45136,45198,45272,45333,45398,45459,45519,45586,45652,45714,45781,45848,45913,45978,46040,46106,46166,46224,46280,46339,46402,46461,46526,46589,46651,46709,46768,46825,46884,46943,47000,47054,47109,47167,47215,47270,47320,47368,47427,47473,47518,47567,47622,47675,47732,47789,47843,47893,47942,47996,48048,48101,48157,48213,48268,48316,48370,48419,48469,48519,48578,48636,48697,48761,48821,48890,48950,49032,49106,49168,49245,49305,49366,49431,49496,49563,49624,49689,49760,49828,49872,49925,49976,50032,50086,50147,50202,50263,50324,50378,50440,50509,50577,50639,50687,50738,50795,50845,50900,50956,51010,51058,51102,51611,51666,51719,51771,51823,51872,51927,51978,52032,52084,52139,52196,52244,52296,52346,52393,52447,52500,52549,52603,52662,52713,52786,52863,52944,53026,53109,53188,53265,53337,53416,53492,53563,53642,53719,53795,53870,53943,54028,54113,54202,54288,54373,54434,54494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d39ef44050431162b870de0e584b2aa2\\transformed\\jetified-ShadowLayout-3.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "201,202,203,496,497,498", "startColumns": "4,4,4,4,4,4", "startOffsets": "9855,9912,9968,27754,27789,27826", "endColumns": "56,55,48,34,36,34", "endOffsets": "9907,9963,10012,27784,27821,27856"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-xlarge-v4_values-xlarge-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2923", "endColumns": "100", "endOffsets": "3019"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2922", "endColumns": "100", "endOffsets": "3018"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2900", "endColumns": "100", "endOffsets": "2996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v22_values-v22.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2881", "endColumns": "100", "endOffsets": "2977"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,903,995,1089,1184,1278,1378,1472,1568,1663,1755,1847,1929,2040,2143,2242,2357,2471,2574,2729,2832", "endColumns": "117,104,106,85,107,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,516,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827,2910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2915", "endColumns": "100", "endOffsets": "3011"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ldltr-v21_values-ldltr-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2942", "endColumns": "100", "endOffsets": "3038"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v24_values-v24.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2972", "endColumns": "100", "endOffsets": "3068"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2877", "endColumns": "100", "endOffsets": "2973"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v28_values-v28.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v26_values-v26.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,431,657,896", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,426,652,891,984"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-h720dp-v13_values-h720dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-h720dp-v13\\values-h720dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "66", "endOffsets": "117"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2917", "endColumns": "100", "endOffsets": "3013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2843", "endColumns": "100", "endOffsets": "2939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2877", "endColumns": "100", "endOffsets": "2973"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v16_values-v16.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-land_values-land.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2878", "endColumns": "100", "endOffsets": "2974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-port_values-port.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-port\\values-port.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2914", "endColumns": "100", "endOffsets": "3010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2897", "endColumns": "100", "endOffsets": "2993"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2817", "endColumns": "100", "endOffsets": "2913"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v18_values-v18.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2900", "endColumns": "100", "endOffsets": "2996"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,905,996,1089,1185,1280,1380,1473,1568,1664,1755,1845,1934,2044,2148,2254,2365,2469,2587,2750,2856", "endColumns": "118,109,106,85,103,119,77,75,90,92,95,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,824,900,991,1084,1180,1275,1375,1468,1563,1659,1750,1840,1929,2039,2143,2249,2360,2464,2582,2745,2851,2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2946", "endColumns": "100", "endOffsets": "3042"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2943", "endColumns": "100", "endOffsets": "3039"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2858", "endColumns": "100", "endOffsets": "2954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,264,265,266,267,268,271", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,19284,19400,19526,19652,19780,19952", "endLines": "2,3,4,5,264,265,266,267,270,275", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,19395,19521,19647,19775,19947,20299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8499,8684,11670,11867,12066,12189,12312,12425,12608,12863,13064,13153,13264,13497,13598,13693,13816,13945,14062,14239,14338,14473,14616,14751,14870,15071,15190,15283,15394,15450,15557,15752,15863,15996,16091,16182,16273,16366,16483,16622,16693,16776,17456,17513,17571,18265", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8494,8679,11665,11862,12061,12184,12307,12420,12603,12858,13059,13148,13259,13492,13593,13688,13811,13940,14057,14234,14333,14468,14611,14746,14865,15066,15185,15278,15389,15445,15552,15747,15858,15991,16086,16177,16268,16361,16478,16617,16688,16771,17451,17508,17566,18260,18966"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,110,113,156,159,162,164,166,168,171,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,224,226,227,228,238,239,240,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "368,459,562,665,770,877,986,1095,1204,1313,1422,1529,1632,1751,1906,2061,2166,2287,2388,2535,2676,2779,2898,3005,3108,3263,3434,3583,3748,3905,4056,4175,4526,4675,4824,4936,5083,5236,5383,5458,5547,5634,5735,5838,8812,8997,11983,12180,12379,12502,12625,12738,12921,13176,13377,13466,13577,13810,13911,14006,14129,14258,14375,14552,14651,14786,14929,15064,15183,15384,15503,15596,15707,15763,15870,16065,16176,16309,16404,16495,16586,16679,16796,16935,17006,17089,17769,17826,17884,18578", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,109,112,155,158,161,163,165,167,170,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,223,225,226,227,237,238,239,251,263", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "454,557,660,765,872,981,1090,1199,1308,1417,1524,1627,1746,1901,2056,2161,2282,2383,2530,2671,2774,2893,3000,3103,3258,3429,3578,3743,3900,4051,4170,4521,4670,4819,4931,5078,5231,5378,5453,5542,5629,5730,5833,8807,8992,11978,12175,12374,12497,12620,12733,12916,13171,13372,13461,13572,13805,13906,14001,14124,14253,14370,14547,14646,14781,14924,15059,15178,15379,15498,15591,15702,15758,15865,16060,16171,16304,16399,16490,16581,16674,16791,16930,17001,17084,17764,17821,17879,18573,19279"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2934", "endColumns": "100", "endOffsets": "3030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,905,997,1091,1188,1282,1381,1475,1571,1666,1758,1850,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,170,98,85", "endOffsets": "208,314,421,510,611,735,820,900,992,1086,1183,1277,1376,1470,1566,1661,1753,1845,1930,2037,2148,2250,2358,2466,2573,2744,2843,2929"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2910", "endColumns": "100", "endOffsets": "3006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v25_values-v25.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2877", "endColumns": "100", "endOffsets": "2973"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,820,900,992,1086,1183,1277,1377,1471,1567,1662,1754,1846,1927,2035,2142,2249,2358,2463,2577,2754,2853", "endColumns": "103,103,107,84,100,127,84,79,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,815,895,987,1081,1178,1272,1372,1466,1562,1657,1749,1841,1922,2030,2137,2244,2353,2458,2572,2749,2848,2931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2936", "endColumns": "100", "endOffsets": "3032"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2929", "endColumns": "100", "endOffsets": "3025"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-watch-v20_values-watch-v20.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2911", "endColumns": "100", "endOffsets": "3007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2875", "endColumns": "100", "endOffsets": "2971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,95,108,101,113,156,102,79", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2205,2314,2416,2530,2687,2790,2870"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2942", "endColumns": "100", "endOffsets": "3038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sw600dp-v13_values-sw600dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2847", "endColumns": "100", "endOffsets": "2943"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2745", "endColumns": "100", "endOffsets": "2841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2884", "endColumns": "100", "endOffsets": "2980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2869", "endColumns": "100", "endOffsets": "2965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2933", "endColumns": "100", "endOffsets": "3029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "3072", "endColumns": "100", "endOffsets": "3168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "203", "endOffsets": "254"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "5714", "endColumns": "203", "endOffsets": "5913"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2845", "endColumns": "100", "endOffsets": "2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2881", "endColumns": "100", "endOffsets": "2977"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2899", "endColumns": "100", "endOffsets": "2995"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2937", "endColumns": "100", "endOffsets": "3033"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,879,970,1063,1155,1249,1349,1442,1537,1630,1721,1815,1894,1999,2097,2195,2303,2403,2506,2661,2758", "endColumns": "107,103,106,81,100,113,79,77,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,874,965,1058,1150,1244,1344,1437,1532,1625,1716,1810,1889,1994,2092,2190,2298,2398,2501,2656,2753,2835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2840", "endColumns": "100", "endOffsets": "2936"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2845", "endColumns": "100", "endOffsets": "2941"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2912", "endColumns": "100", "endOffsets": "3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2845", "endColumns": "100", "endOffsets": "2941"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,2921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2926", "endColumns": "100", "endOffsets": "3022"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2920", "endColumns": "100", "endOffsets": "3016"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2919", "endColumns": "100", "endOffsets": "3015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2878", "endColumns": "100", "endOffsets": "2974"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "211,322,430,521,628,755,839,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1941,2054,2155,2251,2364,2474,2598,2772,2883,2963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2968", "endColumns": "100", "endOffsets": "3064"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2890", "endColumns": "100", "endOffsets": "2986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2935", "endColumns": "100", "endOffsets": "3031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2901", "endColumns": "100", "endOffsets": "2997"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2918", "endColumns": "100", "endOffsets": "3014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2953", "endColumns": "100", "endOffsets": "3049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2881", "endColumns": "100", "endOffsets": "2977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,885,976,1069,1164,1258,1352,1445,1540,1637,1728,1819,1903,2007,2119,2218,2324,2435,2537,2700,2798", "endColumns": "106,101,108,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,418,504,609,726,804,880,971,1064,1159,1253,1347,1440,1535,1632,1723,1814,1898,2002,2114,2213,2319,2430,2532,2695,2793,2876"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-watch-v21_values-watch-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2924", "endColumns": "100", "endOffsets": "3020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,2841", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,2919"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-v17_values-v17.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,25,29,33,37,40,43,46,50,53,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1161,1331,1559,1783,2025,2196,2370,2539,2812,3012,3216", "endLines": "4,8,11,14,17,21,24,28,32,36,39,42,45,49,52,56,60", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1156,1326,1554,1778,2020,2191,2365,2534,2807,3007,3211,3540"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2858", "endColumns": "100", "endOffsets": "2954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2938", "endColumns": "100", "endOffsets": "3034"}}]}, {"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\merged_res\\debug\\values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2873", "endColumns": "100", "endOffsets": "2969"}}]}]}