package com.eeo.systemsetting.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;

import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.touchlock.TouchLockManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;

public class SystemSettingBroadcast extends BroadcastReceiver {
    public static final String TAG = "SystemSettingBroadcast=";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "onReceive: getAction:" + intent.getAction());
        if (intent.getAction().equals(Constant.KEYCODE_POWER_ACTION) && intent.getIntExtra(Constant.HOT_KEY, -1) == Constant.KEYCODE_SEEWO_POWER) {
            KeyEvent event = intent.getParcelableExtra(Constant.KEY_EVENT);
            if (!Constant.IS_USERDEBUG && event == null) {
                return;
            }
            if (EeoApplication.udi.isScreenOn()) {
                if (EeoApplication.isShowInstallingDialog) {
                    Log.d(TAG, "onReceive KEYCODE_POWER_ACTION :do nothing because of installing.");
                    return;
                }
                if (EeoApplication.isProjectionEntering) {
                    Log.d(TAG, "onReceive KEYCODE_POWER_ACTION :do nothing because of entering projection.");
                    return;
                }
                if (CommonUtils.isSetup(context)) {
                    Log.d(TAG, "onReceive KEYCODE_POWER_ACTION :do nothing because of setup.");
                    return;
                }
                CommonUtils.startMainActivity(context);
            } else {
                //熄屏时亮屏
                EeoApplication.udi.changeScreenStatus(true, false, false);
            }
        } else if (intent.getAction().equals(Constant.ACTION_EEO_LONG_PRESS_POWER_OFF)) {
            PowerUtil.getInstance(context).shutdownOrReboot(false, "", PowerUtil.FLAG_SHUTDOWN_SOFT); //长按使用软关机：15s超时直接强制ops关机
        } else if (intent.getAction().equals(Constant.ACTION_EEO_POWER_OFF)) {
            PowerUtil.getInstance(context).shutdownOrReboot(false, "");
        } else if (intent.getAction().equals(Constant.ACTION_EEO_REBOOT)) {
            if (intent.getIntExtra(Constant.FORCE_REBOOT, 0) == 1) {
                PowerUtil.getInstance(context).setForceReboot();
                PowerUtil.getInstance(context).shutdownOrReboot(true, "", PowerUtil.FLAG_SHUTDOWN_SOFT);
            } else {
                PowerUtil.getInstance(context).shutdownOrReboot(true, "");
            }
        } else if (intent.getAction().equals(Constant.SET_USB_ENABLE_ACTION)) { //触控是否禁止
            int touchResult = intent.getIntExtra(Constant.USB2_TOUCH_KEY, -1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    CommonUtils.enableOsd(context, touchResult != Constant.TOUCH);
                }
            }).start();
        } else if (intent.getAction().equals(Constant.ACTION_MULTI_SCREEN_START_MIRROR)) {
            //飞图投屏开始,打开投屏授权(多人抢占投屏时授权)
            CommonUtils.enableMirrorPermission(context, true);
            //亮屏、退出熄屏写
            CommonUtils.screenOnAndExitAnnotation(context);
            //投屏开始时，自动打开触控锁
            if (EeoApplication.isLock) {
                EeoApplication.isLockBeforeMirror = true;
            } else {
                EeoApplication.isLockBeforeMirror = false;
                EeoApplication.isLock = true;
                TouchLockManager.getInstance(context).showTouchLockView(true);
            }
        } else if (intent.getAction().equals(Constant.ACTION_MULTI_SCREEN_STOP_MIRROR)) {
            //飞图投屏结束,关闭投屏授权
            CommonUtils.enableMirrorPermission(context, false);
            if (!EeoApplication.isLockBeforeMirror) {
                EeoApplication.isLock = false;
                TouchLockManager.getInstance(context).dismissTouchLockView();
            }
        }
    }
}
