package com.eeo.ota.server;

import com.eeo.ota.callback.CheckVersionCallback;
import com.eeo.ota.callback.DownloadListener;

public abstract class Server {
    /**
     * 检测更新
     */
    public abstract void checkVersion(CheckVersionCallback checkVersionCallback);

    /**
     * 下载
     * 支持断点续传
     */
    public abstract void download(DownloadListener downloadListener);

    /**
     * 停止下载
     */
    public abstract void stopDownload();
}
