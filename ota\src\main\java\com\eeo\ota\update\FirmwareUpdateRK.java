package com.eeo.ota.update;

import android.content.Context;
import android.os.Build;
import android.os.RecoverySystem;
import android.util.Log;

import com.eeo.ota.callback.InstallListener;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.concurrent.Executors;

public class FirmwareUpdateRK {
    private final static String TAG = "FirmwareUpdateRK";

    private static File RECOVERY_DIR = new File("/cache/recovery");
    private static File UPDATE_FLAG_FILE = new File(RECOVERY_DIR, "last_flag");

    public static boolean updateSystem(Context context, final String absolutePayloadPath, InstallListener installListener) {
        File file = new File(absolutePayloadPath);
        if (!file.exists()) {
            if (installListener != null) {
                installListener.onInstallFail("update.zip path is invalid");
            }
            Log.d(TAG, "updateSystemNormal Error!!! absolutePayloadPath is invalid. it is not a file.");
            return false;
        }

        Thread updateThread = new Thread() {
            @Override
            public void run() {
                try {
                    //verify package
                    RecoverySystem.verifyPackage(file, new RecoverySystem.ProgressListener() {
                        @Override
                        public void onProgress(int progress) {
                            Log.d(TAG, "verifyPackage onProgress:" + progress);
                        }
                    }, null);

                    //install package
                    installPackage(context, file);
                } catch (Exception e) {
                    Log.e(TAG, "update exception:" + e);
                    if (installListener != null) {
                        installListener.onInstallFail(e.toString());
                    }
                }
            }
        };
        Executors.newSingleThreadExecutor().submit(updateThread);
        return true;
    }

    public static void installPackage(Context context, File packageFile) throws IOException {
        String filename = packageFile.getCanonicalPath();
        writeFlagCommand(filename);
        String oriPath = packageFile.getCanonicalPath();
        String convertPath = null;
        if (Build.VERSION.SDK_INT >= 30) {
            Log.d(TAG, "installPackage SDK version >= Build.VERSION_CODES.R,should convert update file path.");
            if (oriPath.startsWith("/storage/emulated/0")) {
                convertPath = oriPath.replace("/storage/emulated/0", "/data/media/0");
            } else if (oriPath.startsWith("/storage")) {
                convertPath = oriPath.replace("/storage", "/mnt/media_rw");
            }
            Log.d(TAG, "installPackage update " + oriPath + " to " + convertPath);
            File convertFile = new File(convertPath);
            RecoverySystem.installPackage(context, convertFile);
            return;
        }
        RecoverySystem.installPackage(context, packageFile);
    }

    /* JADX WARN: Finally extract failed */
    public static String readFlagCommand() {
        if (!UPDATE_FLAG_FILE.exists()) {
            return null;
        }
        Log.d(TAG, "UPDATE_FLAG_FILE is exists");
        char[] buf = new char[128];
        int readCount = 0;
        try {
            try {
                FileReader reader = new FileReader(UPDATE_FLAG_FILE);
                readCount = reader.read(buf, 0, buf.length);
                Log.d(TAG, "readCount = " + readCount + " buf.length = " + buf.length);
            } catch (IOException e) {
                Log.e(TAG, "can not read /cache/recovery/last_flag!");
            }
            UPDATE_FLAG_FILE.delete();
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < readCount && buf[i] != 0; i++) {
                sBuilder.append(buf[i]);
            }
            return sBuilder.toString();
        } catch (Throwable th) {
            UPDATE_FLAG_FILE.delete();
            throw th;
        }
    }

    public static void writeFlagCommand(String path) throws IOException {
        RECOVERY_DIR.mkdirs();
        UPDATE_FLAG_FILE.delete();
        FileWriter writer = new FileWriter(UPDATE_FLAG_FILE);
        try {
            writer.write("updating$path=" + path);
        } finally {
            writer.close();
        }
    }
}
