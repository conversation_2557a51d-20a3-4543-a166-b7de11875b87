package com.eeo.systemsetting.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemProperties;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;

import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;


import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;

import com.android.toofifi.IRemoteInterface;
import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.base.BaseFragment;
import com.eeo.systemsetting.rgb.RgbManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.dialog.CommonDialog;
import com.eeo.udisdk.UdiConstant;
import com.example.touchsdk.TouchCommunicator;

import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;

public class AboutFragment extends BaseFragment {
    public static final String TAG = "AboutFragment===";
    @BindView(R.id.txt_name)
    TextView txtName;
    @BindView(R.id.txt_serial)
    TextView txtSerial;
    @BindView(R.id.txt_resolution)
    TextView txtResolution;
    @BindView(R.id.txt_total)
    TextView txtTotal;
    @BindView(R.id.txt_store_total)
    TextView txtStoreTotal;
    @BindView(R.id.txt_android_version)
    TextView txtAndroidVersion;
    @BindView(R.id.txt_system_version)
    TextView txtSystemVersion;
    @BindView(R.id.txt_touch_version)
    TextView txtTouchVersion;
    @BindView(R.id.txt_hotline_title)
    TextView txtHotlineTitle;
    @BindView(R.id.txt_hotline)
    TextView txtHotline;
    @BindView(R.id.ll_windows_host)
    LinearLayout llWindowsHost;
    @BindView(R.id.ll_system_version)
    LinearLayout llSystemVersion;
    @BindView(R.id.rl_privacy_policy)
    RelativeLayout rlPrivacy;
    CommonDialog mWindowHostDialog;
    CommonDialog mResetFactoryDialog;

    private int defValue = 0;
    private CommonDialog mAdbDialog;

    /**
     * 后门输入密码进入工厂菜单等
     */
    private static final String PWD_FACTORY_MENU = "eeofactorymenu";
    private static final String PWD_DEBUG_MENU = "eeodebugmenu";
    private static final String PWD_DEBUG_MENU_2 = "202505";
    private static final String PWD_CALIBRATION = "eeocalibration";
    private EditText mPasswordEdt;

    private Dialog mResetOpsDialog;

    public static final int MSG_DISMISS_RESET_OPS_DIALOG = 0x001;
    public static final int MSG_RESET_OPS_TIMEOUT = 0x002;
    public static final int MSG_GET_SCREEN_ACTIVATION_STATUS = 0x003;
    public static final int MSG_DISMISS_PASSWORD_EDITTEXT = 0x004;
    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_DISMISS_RESET_OPS_DIALOG:
                    dismissResetOpsDialog();
                    break;
                case MSG_RESET_OPS_TIMEOUT:
                    EeoApplication.isResettingOps = false;
                    break;
                case MSG_GET_SCREEN_ACTIVATION_STATUS:
                    getScreenActivationStatus();
                    break;
                case MSG_DISMISS_PASSWORD_EDITTEXT:
                    dismissPasswordEditText();
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public int getLayout() {
        return R.layout.fragment_about;
    }

    @Override
    public void initDate() {
        //ClassIn_Board_S86_Pro去掉下划线
        String productName = SystemProperties.get("ro.eeo.product.name", "ClassIn Board S86 Pro").replace("_", " ");
        txtName.setText(productName);
        String serial = SystemProperties.get("persist.sys.boardsn.value", "unknown");
        if ("unknown".equals(serial)) {
            serial = Build.getSerial();
        }
        txtSerial.setText(serial);

        WindowManager windowManager = getActivity().getWindow().getWindowManager();
        DisplayMetrics metrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getRealMetrics(metrics);
        //屏幕实际宽度（像素个数）
        int width = metrics.widthPixels;
        //屏幕实际高度（像素个数）
        int height = metrics.heightPixels;
        txtResolution.setText(width + "x" + height);

        String language = Locale.getDefault().getLanguage();
        //获取总运行内存
        String totalMemory = CommonUtils.getTotalMemory(getContext());
        //获取剩余运行内存
        String availMemory = CommonUtils.getAvailMemory(getContext());
        //获取总内存
//        String totalInternalMemorySize = CommonUtils.getTotalInternalMemorySize(getContext());
        //获取剩余内容
        String availableInternalMemorySize = CommonUtils.getAvailableInternalMemorySize(getContext());
        if (language.equals(Constant.LANGUAGE_ZH)) {
            txtTotal.setText(getActivity().getString(R.string.total) + totalMemory + getActivity().getString(R.string.remaining) + availMemory);
            txtStoreTotal.setText(getActivity().getString(R.string.total) + "32 GB" + getActivity().getString(R.string.remaining) + availableInternalMemorySize);
        } else {
            txtTotal.setText(availMemory + " Left/" + totalMemory);
            txtStoreTotal.setText(availableInternalMemorySize + " Left/32 GB");
            //英文版隐藏服务热线
            txtHotlineTitle.setVisibility(View.GONE);
            txtHotline.setVisibility(View.GONE);
        }

        //android版本
        String sdkVersion = CommonUtils.getSdkVersion();
        txtAndroidVersion.setText(sdkVersion);

        //系统版本
        txtSystemVersion.setText(Build.ID);

        //触摸框版本
        if (TouchCommunicator.isPlugged(getContext())) {
            //众远的用checkSum
            String version = null;
            String checkSum = null;
            for (int i = 0; i < 10; i++) {
                version = EeoApplication.touchCommunicator.GetFirmwareVersion();
                checkSum = EeoApplication.touchCommunicator.GetFirmwareCheckSum();
                Log.d(TAG, "touchCommunicator get " + i + " ,currentVersion=" + version + ",checkSum=" + checkSum);
                if ("error".equals(version) || "error".equals(checkSum)
                        || "0000".equals(version) || "0000".equals(checkSum)
                ) {
                    //触摸框滑动过程中有时候读到error,再重新读
                    EeoApplication.touchCommunicator.InitSDK();
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException e) {
                        Log.e(TAG, "checkVersion InterruptedException " + e);
                        e.printStackTrace();
                    }
                } else {
                    break;
                }
            }
            txtTouchVersion.setText(version);
        } else {
            txtTouchVersion.setText(getText(R.string.touch_unplugged));
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        defValue = 0;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (CommonUtils.isOpsInserted() && UdiConstant.SOURCE_PC.equals(EeoApplication.mCurrentSource)) {
            llWindowsHost.setVisibility(View.VISIBLE);
        } else {
            llWindowsHost.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.ll_windows_host, R.id.ll_system_version, R.id.rl_privacy_policy})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_windows_host:
                setWindowHostDialog();
                break;

            case R.id.ll_system_version:
                defValue++;
                if (defValue == 10) {
                    showAdbDialog();
                }
                break;
            case R.id.rl_privacy_policy:
                CommonUtils.sendPrivacyBroadcast(getContext());
                break;

            default:
                break;
        }
    }

    private void showAdbDialog() {
        bindScreenService();
        if (mAdbDialog == null) {
            mAdbDialog = new CommonDialog.Builder(getContext())
                    .view(R.layout.dialog_adb)
                    .style(R.style.Dialog)
                    .cancelTouchout(false)
                    .addViewOnclick(R.id.img_back, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mAdbDialog.dismiss();
                            mAdbDialog = null;
                        }
                    })
                    .build();
            mAdbDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
            mAdbDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    unbindScreenService();
                }
            });
        }
        mAdbDialog.show();
        defValue = 0;

        SwitchCompat swAdb = mAdbDialog.findViewById(R.id.sw_adb);
        SwitchCompat swSpeed = mAdbDialog.findViewById(R.id.sw_speed);
        SwitchCompat swPen = mAdbDialog.findViewById(R.id.sw_pen);
        SwitchCompat swR30WriteSpeed = mAdbDialog.findViewById(R.id.r30_write_speed);
        Button btnFactory = mAdbDialog.findViewById(R.id.btn_factory);
        Button btnDebug = mAdbDialog.findViewById(R.id.btn_debug);
        Button btnCalibration = mAdbDialog.findViewById(R.id.btn_touch);
        RelativeLayout resetFactoryRl = mAdbDialog.findViewById(R.id.rl_factory_reset);
        RelativeLayout colorTemperatureAdjustRl = mAdbDialog.findViewById(R.id.rl_color_temperature_adjust);
        mScreenActivationStatusTv = mAdbDialog.findViewById(R.id.tv_screen_activation_status);
        mPasswordEdt = mAdbDialog.findViewById(R.id.edt_password);

        //adb状态开关，返回值 0 打开  1 关闭
        int adbValue = Settings.Global.getInt(getContext().getContentResolver(), Settings.Global.ADB_ENABLED, -1);
        swAdb.setChecked(!(adbValue == 0));

        swAdb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    Settings.Global.putInt(getContext().getContentResolver(), Settings.Global.ADB_ENABLED, 1);
                } else {
                    Settings.Global.putInt(getContext().getContentResolver(), Settings.Global.ADB_ENABLED, 0);
                }
            }
        });

        //板书加速
        boolean isOpenAccelerate = SystemProperties.getBoolean("persist.eeo.whiteboard.accelerate", true);
        swSpeed.setChecked(isOpenAccelerate);
        swSpeed.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    setEeoSystemProperties("persist.eeo.whiteboard.accelerate", "true");
                } else {
                    setEeoSystemProperties("persist.eeo.whiteboard.accelerate", "false");
                }
            }
        });

        //R30书写预测加速
        String mWriteSpeed = SystemControlManager.getInstance().readSysFs("/sys/bus/usb/drivers/zydeviceR3/writing_speed");
        if (mWriteSpeed.equals("") || mWriteSpeed.equals("0")) {
            swR30WriteSpeed.setChecked(false);
        } else {
            swR30WriteSpeed.setChecked(true);
        }
        swR30WriteSpeed.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    SystemControlManager.getInstance().writeSysFs("/sys/bus/usb/drivers/zydeviceR3/writing_speed", "1");
                } else {
                    SystemControlManager.getInstance().writeSysFs("/sys/bus/usb/drivers/zydeviceR3/writing_speed", "0");
                }
            }
        });
        //书写加速
        boolean isOpenStroke = SystemProperties.getBoolean("persist.eeo.whiteboard.stroke", false);
        swPen.setChecked(isOpenStroke);
        swPen.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    setEeoSystemProperties("persist.eeo.whiteboard.stroke", "true");
                } else {
                    setEeoSystemProperties("persist.eeo.whiteboard.stroke", "false");
                }
            }
        });
        //工厂菜单、调试菜单、触摸校准密码
        mPasswordEdt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                Log.d(TAG, "afterTextChanged: " + s);
                showPasswordEditText();
                handlePassword(s.toString());
            }
        });

        mAdbDialog.getWindow().setLayout(CommonUtils.dp2px(getActivity(), Constant.DIALOG_RGB_WIDTH_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2),
                CommonUtils.dp2px(getActivity(), Constant.DIALOG_RGB_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2));
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.copyFrom(mAdbDialog.getWindow().getAttributes());
        layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
        layoutParams.x = CommonUtils.dp2px(getContext(), Constant.DIALOG_MARIN_END_IN_DP);
        layoutParams.y = CommonUtils.dp2px(getContext(), Constant.DIALOG_MARIN_BOTTOM_IN_DP);
        layoutParams.dimAmount = 0;
        mAdbDialog.getWindow().setAttributes(layoutParams);
        //无线投屏激活状态
        getScreenActivationStatus();

        resetFactoryRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showResetFactoryDialog();
            }
        });

        colorTemperatureAdjustRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RgbManager.getInstance(getContext()).showRgbDialog();
                RgbManager.getInstance(getContext()).setTouchLockClickListener(new RgbManager.OnClickListener() {
                    @Override
                    public void onClickDismiss() {
                        if (mAdbDialog != null) {
                            mAdbDialog.dismiss();
                            mAdbDialog = null;
                        }
                    }
                });
            }
        });

        btnFactory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPasswordEditText();
                mPasswordEdt.setText("");
                mPasswordEdt.requestFocus();
                InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                inputMethodManager.showSoftInput(mPasswordEdt, 0);
            }
        });
        btnDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPasswordEditText();
                mPasswordEdt.setText("");
                mPasswordEdt.requestFocus();
                InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                inputMethodManager.showSoftInput(mPasswordEdt, 0);
            }
        });
        btnCalibration.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPasswordEditText();
                mPasswordEdt.setText("");
                mPasswordEdt.requestFocus();
                InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                inputMethodManager.showSoftInput(mPasswordEdt, 0);
            }
        });
    }

    private void showPasswordEditText() {
        if (mPasswordEdt == null) {
            return;
        }
        if (mPasswordEdt.getVisibility() != View.VISIBLE) {
            mPasswordEdt.setVisibility(View.VISIBLE);
        }
        mHandler.removeMessages(MSG_DISMISS_PASSWORD_EDITTEXT);
        mHandler.sendEmptyMessageDelayed(MSG_DISMISS_PASSWORD_EDITTEXT, 3000);
    }

    private void dismissPasswordEditText() {
        if (mPasswordEdt == null) {
            return;
        }
        mHandler.removeMessages(MSG_DISMISS_PASSWORD_EDITTEXT);
        mPasswordEdt.setVisibility(View.INVISIBLE);
    }

    private void handlePassword(String password) {
        if (password == null) {
            return;
        }
        if (PWD_FACTORY_MENU.equalsIgnoreCase(password)) {
            startFactoryMenu();
        } else if (PWD_DEBUG_MENU.equalsIgnoreCase(password) || PWD_DEBUG_MENU_2.equalsIgnoreCase(password)) {
            startDebugMenu();
        } else if (PWD_CALIBRATION.equalsIgnoreCase(password)) {
            startCalibrationTool();
        }
    }

    private void startFactoryMenu() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            String packageName = "com.cvte.fac.menu";
            String className = "com.cvte.fac.menu.app.TvMenuWindowManagerService";
            intent.putExtra("com.cvte.fac.menu.commmand", "com.cvte.fac.menu.commmand.factory_menu");
            intent.setClassName(packageName, className);
            getContext().startService(intent);
            Log.d(TAG, "startFactoryMenu");
            if (mAdbDialog != null) {
                mAdbDialog.dismiss();
                mAdbDialog = null;
            }
            if (getActivity() != null) {
                getActivity().finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startDebugMenu() {
        try {
            Intent intent = new Intent();
            intent.setClassName("com.ifpdos.debugmenu", "com.ifpdos.debugmenu.MainActivity");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            getContext().startActivity(intent);
            Log.d(TAG, "startDebugMenu");
            if (mAdbDialog != null) {
                mAdbDialog.dismiss();
                mAdbDialog = null;
            }
            if (getActivity() != null) {
                getActivity().finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startCalibrationTool() {
        try {
            Intent intent = new Intent();
            intent.setClassName("com.calitoolsdk", "com.calitoolsdk.LoadJniLibrary");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            getContext().startActivity(intent);
            Log.d(TAG, "startCalibrationTool");
            if (mAdbDialog != null) {
                mAdbDialog.dismiss();
                mAdbDialog = null;
            }
            if (getActivity() != null) {
                getActivity().finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setEeoSystemProperties(String key, String value) {
        SystemProperties.set(key, value);
        Intent intent = new Intent("com.eeo.SystemProperties.changed");
        getContext().sendBroadcast(intent);
    }

    private void setWindowHostDialog() {
        if (mWindowHostDialog == null) {
            mWindowHostDialog = new CommonDialog.Builder(getContext())
                    .view(R.layout.dialog_systemsetting_window_host)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(false) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mWindowHostDialog.dismiss();
                            CommonUtils.setIsDialog(getContext(), false);
                        }
                    })
                    .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mWindowHostDialog.dismiss();
                            mWindowHostDialog = null;
                            CommonUtils.setIsDialog(getContext(), false);
                            if (getActivity() != null) {
                                getActivity().finish();
                            }
                            boolean isSuccess = EeoApplication.udi.resetOps();
                            if (isSuccess) {
                                showResetOpsDialog();
                                EeoApplication.isResettingOps = true; //禁止点击重启、关机
                                mHandler.sendEmptyMessageDelayed(MSG_RESET_OPS_TIMEOUT, 60000); //60s内不因为ops无信号而关机
                            }
                        }
                    })
                    .build();
        }
        mWindowHostDialog.show();
        CommonUtils.setIsDialog(getContext(), true);

        Window window = mWindowHostDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            layoutParams.x = CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_MARGIN_END);
            layoutParams.y = CommonUtils.dp2px(getActivity(), Constant.WINDOW_HOST_MARGIN_BOTTOM);
            window.setAttributes(layoutParams);
        }
    }

    private void showResetOpsDialog() {
        if (mResetOpsDialog == null) {
            mResetOpsDialog = new Dialog(getContext(), R.style.Dialog);
            mResetOpsDialog.getWindow().setContentView(R.layout.dialog_reset_ops);
            mResetOpsDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            mResetOpsDialog.setCanceledOnTouchOutside(false);
            mResetOpsDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                }
            });
            WindowManager.LayoutParams layoutParams = mResetOpsDialog.getWindow().getAttributes();
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.CENTER;
            mResetOpsDialog.getWindow().setAttributes(layoutParams);
            mResetOpsDialog.getWindow().setDimAmount(0.6f);
        }
        mResetOpsDialog.show();
        CommonUtils.setIsDialog(getContext(), true);
        EeoApplication.isShowResetOpsDialog = true;
        CommonUtils.enableOsd(getContext(), true);
        mHandler.removeMessages(MSG_DISMISS_RESET_OPS_DIALOG);
        mHandler.sendEmptyMessageDelayed(MSG_DISMISS_RESET_OPS_DIALOG, 15000);
    }

    private void dismissResetOpsDialog() {
        if (mResetOpsDialog != null && mResetOpsDialog.isShowing()) {
            mResetOpsDialog.dismiss();
        }
        CommonUtils.setIsDialog(getContext(), false);
        EeoApplication.isShowResetOpsDialog = false;
        CommonUtils.enableOsd(getContext(), false);
    }

    private void showResetFactoryDialog() {
        if (mResetFactoryDialog == null) {
            mResetFactoryDialog = new CommonDialog.Builder(getContext())
                    .view(R.layout.dialog_factory_reset)//设置弹窗的样式layout
                    .style(R.style.Dialog) //设置主题，这里可以将背景设为透明，这样只显示你需要显示的dialog部分
                    .cancelTouchout(true) //设置点击dialog之外是否弹窗消失，true为消失，false为不消失
                    .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mResetFactoryDialog.dismiss();
                            CommonUtils.setIsDialog(getContext(), false);
                        }
                    })
                    .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            mResetFactoryDialog.dismiss();
                            mResetFactoryDialog = null;
                            CommonUtils.setIsDialog(getContext(), false);
                            EeoApplication.udi.shutdownOps();
                            //恢复出厂前先存飞图激活码到/data/ft_activated_data.xml
                            CommonUtils.saveActivatedData(getContext());
                            //android8.0后恢复出厂设置
                            Intent resetIntent = new Intent("android.intent.action.FACTORY_RESET");
                            resetIntent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND); // run with high priority
                            resetIntent.setPackage("android");
                            getContext().sendBroadcast(resetIntent);
                        }
                    })
                    .build();
            mResetFactoryDialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }
        mResetFactoryDialog.show();
        CommonUtils.setIsDialog(getContext(), true);

        Window window = mResetFactoryDialog.getWindow();
        if (window != null) {
            window.setLayout(CommonUtils.dp2px(getContext(), Constant.WINDOW_HOST_WIDTH), CommonUtils.dp2px(getContext(), Constant.WINDOW_HOST_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            layoutParams.x = CommonUtils.dp2px(getContext(), Constant.WINDOW_HOST_MARGIN_END);
            layoutParams.y = CommonUtils.dp2px(getContext(), Constant.WINDOW_HOST_MARGIN_BOTTOM);
            window.setAttributes(layoutParams);
        }
    }

    /**
     * 飞图投屏激活状态相关
     */
    private TextView mScreenActivationStatusTv;
    private int mScreenActivationStatus = -2;
    private IRemoteInterface mIRemoteInterface;
    private boolean mIsScreenServiceConnected = false;
    private boolean mIsScreenEnabled = false; //未启动投屏应用时，需要绑定服务启动过一会后才能读到正确状态
    private int mGetScreenActivationStatusTimes = 0;
    private final ServiceConnection mScreenServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mIRemoteInterface = IRemoteInterface.Stub.asInterface(service);
            mIsScreenServiceConnected = true;
            mIsScreenEnabled = CommonUtils.isWirelessScreenEnabled(getContext());
            getScreenActivationStatus();
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected: ");
            mIsScreenServiceConnected = false;
        }
    };

    private void bindScreenService() {
        try {
            Intent intent = new Intent();
            intent.setClassName(Constant.MULTI_SCREEN_PKG_NAME, Constant.MULTI_SCREEN_AIDL_SERVICE__NAME);
            getContext().bindService(intent, mScreenServiceConnection, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "bindScreenService: " + e.toString());
        }
    }

    private void unbindScreenService() {
        try {
            getContext().unbindService(mScreenServiceConnection);
            mIsScreenServiceConnected = false;
        } catch (Exception e) {
            Log.e(TAG, "unbindScreenService: " + e.toString());
        }
    }

    private void getScreenActivationStatus() {
        if (mIsScreenServiceConnected) {
            try {
                mScreenActivationStatus = mIRemoteInterface.getActivationStatus();
                Log.d(TAG, "getScreenActivationStatus: " + mScreenActivationStatus);

                //未打开投屏功能时，读到的是未知或未激活时，读取多次
                if (!mIsScreenEnabled && mScreenActivationStatus <= 0 && mGetScreenActivationStatusTimes < 10) {
                    mGetScreenActivationStatusTimes++;
                    mHandler.sendEmptyMessageDelayed(MSG_GET_SCREEN_ACTIVATION_STATUS, 500);
                    return;
                }
                mGetScreenActivationStatusTimes = 0;

                if (mScreenActivationStatus == 2) {
                    if (mScreenActivationStatusTv != null) {
                        mScreenActivationStatusTv.setText(getContext().getString(R.string.screen_activation_status_activated));
                        mScreenActivationStatusTv.setTextColor(getContext().getResources().getColor(R.color.white));
                    }
                } else {
                    if (mScreenActivationStatusTv != null) {
                        mScreenActivationStatusTv.setText(getContext().getString(R.string.screen_activation_status_nonactivated));
                        mScreenActivationStatusTv.setTextColor(getContext().getResources().getColor(R.color.error_red));
                    }
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public Context getContext() {
        Context context = super.getContext();
        return context == null ? EeoApplication.getApplication() : context;
    }
}
