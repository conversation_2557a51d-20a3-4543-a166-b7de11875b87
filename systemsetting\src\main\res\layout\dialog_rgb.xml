<?xml version="1.0" encoding="utf-8"?>
<com.zyp.cardview.YcCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Main_YcCardView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:ycCardBackgroundColor="@color/adb_bg">

    <RelativeLayout
        android:layout_width="@dimen/adb_width"
        android:layout_height="@dimen/adb_height">

        <TextView
            android:id="@+id/txt_title"
            style="@style/Title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/setting_tv_title_margin_top"
            android:text="@string/color_temperature_adjust"
            android:textColor="@color/white" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/screen_dialog_iv_back_width"
            android:layout_height="@dimen/screen_dialog_iv_back_height"
            android:layout_marginStart="@dimen/iv_back_margin_start"
            android:layout_marginTop="@dimen/iv_back_margin_top"
            android:importantForAccessibility="no"
            android:src="@drawable/select_rgb_left_icon" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/adb_iv_close_margin_top"
            android:layout_marginEnd="@dimen/adb_iv_close_margin_end"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_close" />

        <View
            android:id="@+id/line1"
            style="@style/Line"
            android:layout_marginTop="@dimen/adb_line_margin_top" />

        <TextView
            android:id="@+id/tv_color_temperature"
            style="@style/Rgb_Text_Title"
            android:layout_below="@id/line1"
            android:layout_marginTop="@dimen/rgb_tv_color_temperature_margin_top"
            android:text="@string/color_temperature" />

        <com.eeo.systemsetting.view.CustomerSeekBar
            android:id="@+id/sb_color_temperature"
            style="@style/SeekBar_Rgb"
            android:layout_below="@id/tv_color_temperature"
            android:max="20"
            android:progress="10" />

        <TextView
            android:id="@+id/tv_color_temperature_cold"
            style="@style/Rgb_Text_Title"
            android:layout_below="@id/sb_color_temperature"
            android:layout_marginTop="@dimen/rgb_tv_color_temperature_cold_margin_top"
            android:text="@string/color_temperature_cold" />

        <TextView
            android:id="@+id/tv_color_temperature_warm"
            style="@style/Rgb_Text_Title"
            android:layout_alignTop="@id/tv_color_temperature_cold"
            android:layout_alignEnd="@id/sb_color_temperature"
            android:layout_marginEnd="@dimen/rgb_sb_padding_end"
            android:text="@string/color_temperature_warm" />

        <EditText
            android:id="@+id/et_color_temperature"
            android:layout_width="@dimen/rgb_et_width"
            android:layout_height="@dimen/rgb_et_height"
            android:layout_alignTop="@id/sb_color_temperature"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/adb_item_margin_end"
            android:background="@drawable/rgb_edit_bg"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="3"
            android:text="0"
            android:textColor="@color/white_70"
            android:textSize="@dimen/adb_tv_text_size" />

        <View
            android:id="@+id/line2"
            style="@style/Line"
            android:layout_below="@id/et_color_temperature"
            android:layout_marginHorizontal="@dimen/adb_item_margin_end"
            android:layout_marginTop="@dimen/rgb_line2_margin_top" />

        <TextView
            android:id="@+id/tv_red_gain"
            style="@style/Rgb_Text_Title"
            android:layout_below="@id/line2"
            android:layout_marginTop="@dimen/rgb_red_gain_margin_top"
            android:text="@string/rgb_red_gain" />

        <com.eeo.systemsetting.view.CustomerSeekBar
            android:id="@+id/sb_red_gain"
            style="@style/SeekBar_Rgb"
            android:layout_below="@id/tv_red_gain"
            android:progress="128" />

        <EditText
            android:id="@+id/et_red_gain"
            android:layout_width="@dimen/rgb_et_width"
            android:layout_height="@dimen/rgb_et_height"
            android:layout_alignTop="@id/sb_red_gain"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/adb_item_margin_end"
            android:background="@drawable/rgb_edit_bg"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="3"
            android:text="128"
            android:textColor="@color/white_70"
            android:textSize="@dimen/adb_tv_text_size" />

        <TextView
            android:id="@+id/tv_green_gain"
            style="@style/Rgb_Text_Title"
            android:layout_below="@id/sb_red_gain"
            android:layout_marginTop="@dimen/rgb_green_gain_margin_top"
            android:text="@string/rgb_green_gain" />

        <com.eeo.systemsetting.view.CustomerSeekBar
            android:id="@+id/sb_green_gain"
            style="@style/SeekBar_Rgb"
            android:layout_below="@id/tv_green_gain"
            android:progress="128" />

        <EditText
            android:id="@+id/et_green_gain"
            android:layout_width="@dimen/rgb_et_width"
            android:layout_height="@dimen/rgb_et_height"
            android:layout_alignTop="@id/sb_green_gain"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/adb_item_margin_end"
            android:background="@drawable/rgb_edit_bg"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="3"
            android:text="128"
            android:textColor="@color/white_70"
            android:textSize="@dimen/adb_tv_text_size" />

        <TextView
            android:id="@+id/tv_blue_gain"
            style="@style/Rgb_Text_Title"
            android:layout_below="@id/sb_green_gain"
            android:layout_marginTop="@dimen/rgb_green_gain_margin_top"
            android:text="@string/rgb_blue_gain" />

        <com.eeo.systemsetting.view.CustomerSeekBar
            android:id="@+id/sb_blue_gain"
            style="@style/SeekBar_Rgb"
            android:layout_below="@id/tv_blue_gain"
            android:progress="128" />

        <EditText
            android:id="@+id/et_blue_gain"
            android:layout_width="@dimen/rgb_et_width"
            android:layout_height="@dimen/rgb_et_height"
            android:layout_alignTop="@id/sb_blue_gain"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/adb_item_margin_end"
            android:background="@drawable/rgb_edit_bg"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="3"
            android:text="128"
            android:textColor="@color/white_70"
            android:textSize="@dimen/adb_tv_text_size" />

        <Button
            android:id="@+id/btn_reset"
            style="@style/Adb_Button"
            android:layout_width="@dimen/rgb_btn_reset_width"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/rgb_reset_margin_bottom"
            android:focusable="true"
            android:text="@string/rgb_reset" />

    </RelativeLayout>
</com.zyp.cardview.YcCardView>