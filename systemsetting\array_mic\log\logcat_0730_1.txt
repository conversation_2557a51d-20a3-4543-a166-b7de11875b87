07-30 15:04:33.180  1794  1806 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.341  2353  2373 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.358  2342  2358 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.368  1914  1929 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.369  2646  2658 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.379   667  1035 I RunningTasks: call getRunningTasks is1000
07-30 15:04:33.400   888   903 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.425     0     0 I [  539.494418@0]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:33.433  1846  1859 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.458  1594  1608 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.490   934   949 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.490  1868  1881 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.520   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:33.520   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:33.520   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:33.520   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:33.542     0     0 I [  539.609236@0]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:33.571   955   969 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.674  2009  2022 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.698  1969  1981 E oid.documentsu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.748  1943  1955 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.826  2674  2688 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.885   667   675 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.918  2041  2053 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:04:33.993     0     0 I [  540.060023@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:34.020   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:34.020   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:34.020   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:34.020   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:34.053  2288  2301 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.165   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:04:34.166   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:04:34.166   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:04:34.166   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:04:34.166   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:04:34.167   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:04:34.167   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:04:34.168   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:04:34.168   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:04:34.214  2552  2564 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.225  2093  2106 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.231  2697  2709 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.238  1249  1261 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.286  2114  2126 E d.process.acor: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.289  3360  3381 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:04:34.301  2401  2415 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.381   667   715 I RunningTasks: call getRunningTasks is1000
07-30 15:04:34.388  2166  2182 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.445     0     0 I [  540.514612@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:34.448  2846  2858 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.478   911   927 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.510  1281  1293 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.521   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:34.521   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:34.521   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:34.521   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:34.553  2441  2453 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.589  2140  2155 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.665  3340  3353 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.676  2463  2476 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.720  1307  1326 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.775  2584  2599 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.777     0     0 I [  540.845564@2]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:34.802  2197  2214 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.871  2236  2251 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:04:34.893     0     0 I [  540.961239@2]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:34.902  2504  2518 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.021   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:35.021   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:35.021   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:35.021   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:35.049  1174  1188 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.169   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:04:35.170   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:04:35.173   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:04:35.173   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:04:35.180  1794  1806 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.272  2401  2496 I main_taskProc: callServerApigetWzAuthStatus) enter, args=[-1]
07-30 15:04:35.272  2401  2496 I main_taskProc: callServerApigetWzAuthStatus) exit, rst=2
07-30 15:04:35.273  2401  2496 I AppServ_LicenseCheck: getWzAuthStatus) status=2
07-30 15:04:35.342  2353  2373 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.346     0     0 I [  541.411825@2]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:35.359  2342  2358 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.383   667   715 I RunningTasks: call getRunningTasks is1000
07-30 15:04:35.401   888   903 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.409  1914  1929 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.410  2646  2658 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.434  1846  1859 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.461  1594  1608 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.490  1868  1881 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.490   934   949 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.522   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:35.522   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:35.522   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:35.522   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:35.572   955   969 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.681     0     0 I [  541.749318@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:35.714  2009  2022 E id.printspoole: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.738  1969  1981 E oid.documentsu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.748  1943  1955 E seewo.osservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.866  2674  2688 E sservice:daemo: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.885   667   675 E system_server: failed to connect to jdwp control socket: Connection refused
07-30 15:04:35.958  2041  2053 E ndroid.keychai: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.022   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:36.022   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:36.022   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:36.023   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:36.054  2288  2301 E .screenrecorde: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.174   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:04:36.175   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:04:36.175   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:04:36.175   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:04:36.176   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:04:36.176   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:04:36.176   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:04:36.176   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:04:36.176   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:04:36.231  2697  2709 E wo.touchservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.238  1249  1261 E di.service.cor: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.249     0     0 I [  542.314770@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:36.254  2552  2564 E ndroid.setting: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.265  2093  2106 E externalstorag: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.290  3360  3381 E ocessService0:: failed to create Unix domain socket: Operation not permitted
07-30 15:04:36.301  2401  2415 E android.toofif: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.326  2114  2126 E d.process.acor: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.385   667   715 I RunningTasks: call getRunningTasks is1000
07-30 15:04:36.429  2166  2182 E d.process.medi: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.448  2846  2858 E awei.connectio: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.510  1281  1293 E com.android.se: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.518   911   927 E ssioncontrolle: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.523   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:36.523   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:36.523   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:36.523   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:36.555  2441  2453 E fpdos.debugmen: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.589  2140  2155 E o.classin.setu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.676  2463  2476 E boardAccelerat: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.697     0     0 I [  542.763342@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:36.702  3340  3353 E webview_servic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.720  1307  1326 E eewo.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.802  2197  2214 E FuseDaemon: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.814  2584  2599 E ackageinstalle: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.911  2236  2251 E com.seewo.ota: failed to connect to jdwp control socket: Connection refused
07-30 15:04:36.942  2504  2518 E dos.factorytes: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.023   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:37.023   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:37.024   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:37.024   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:37.029     0     0 I [  543.098111@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:37.049  1174  1188 E rkstack.proces: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.177   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI1, status:plug out
07-30 15:04:37.179   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI2, status:plug out
07-30 15:04:37.179   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI3, status:plug out
07-30 15:04:37.179   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :HDMI4, status:plug out
07-30 15:04:37.180   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DUMMY, status:plug in
07-30 15:04:37.180   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :SPDIF, status:plug out
07-30 15:04:37.180   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :YPBPR2, status:plug out
07-30 15:04:37.181  1794  1806 E id.providers.t: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.181   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :DTV, status:plug in
07-30 15:04:37.181   426   665 D tvserver: [CDevicesDetect]:GetSourceConnectStatus source :ADTV, status:plug out
07-30 15:04:37.342  2353  2373 E utmethod.pinyi: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.359  2342  2358 E o.systemsettin: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.387   667   715 I RunningTasks: call getRunningTasks is1000
07-30 15:04:37.401   888   903 E ndroid.systemu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.434  1846  1859 E pdos.mcuservic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.449  1914  1929 E o.electricshel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.450  2646  2658 E m.android.shel: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.462  1594  1608 E id.ext.service: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.481     0     0 I [  543.549495@3]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf
07-30 15:04:37.491  1868  1881 E com.android.nf: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.491   934   949 E idlogic.tvinpu: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.524   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 168, len = 1, offset = 0
07-30 15:04:37.524   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 1784, data = 62.
07-30 15:04:37.524   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: id = 287, len = 1, offset = 0
07-30 15:04:37.524   347   607 D SystemControl: [SSMAction]:SSMReadNTypes: actualAddr = 2103, data = 0.
07-30 15:04:37.572   955   969 E com.droidlogic: failed to connect to jdwp control socket: Connection refused
07-30 15:04:37.601     0     0 I [  543.667958@0]  [wlan][2580]p2pFuncValidateRxActionFrame: P2P WARN) [p2p1] unregistered p2p action packet filter 0xf