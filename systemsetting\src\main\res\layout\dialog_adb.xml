<?xml version="1.0" encoding="utf-8"?>
<com.zyp.cardview.YcCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Main_YcCardView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:ycCardBackgroundColor="@color/adb_bg">

    <RelativeLayout
        android:layout_width="@dimen/adb_width"
        android:layout_height="@dimen/adb_height">

        <TextView
            android:id="@+id/txt_title"
            style="@style/Title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/setting_tv_title_margin_top"
            android:text="@string/adb_title"
            android:textColor="@color/white" />

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="@dimen/screen_dialog_iv_back_width"
            android:layout_height="@dimen/screen_dialog_iv_back_height"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/adb_iv_close_margin_top"
            android:layout_marginEnd="@dimen/adb_iv_close_margin_end"
            android:importantForAccessibility="no"
            android:src="@drawable/select_close_icon" />

        <View
            android:id="@+id/line1"
            style="@style/Line"
            android:layout_marginTop="@dimen/adb_line_margin_top"
            />

        <ScrollView
            android:id="@+id/scrollview"
            android:layout_width="match_parent"
            android:layout_height="@dimen/adb_scrollview_height"
            android:layout_below="@id/line1"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_adb"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_adb_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_adb"
                        style="@style/Adb_Text_Title"
                        android:text="@string/adb" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/sw_adb"
                        style="@style/Adb_Switch" />
                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/rl_color_temperature_adjust"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_color_temperature_adjust"
                        style="@style/Adb_Text_Title"
                        android:text="@string/color_temperature_adjust" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_speed"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_speed"
                        style="@style/Adb_Text_Title"
                        android:text="@string/write_acceleration" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/sw_speed"
                        style="@style/Adb_Switch" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_pen"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_pen"
                        style="@style/Adb_Text_Title"
                        android:text="@string/stroke_algorithm" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/sw_pen"
                        style="@style/Adb_Switch" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_r30_write_speed"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_r30_write_speed"
                        style="@style/Adb_Text_Title"
                        android:text="@string/r30_write_speed" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/r30_write_speed"
                        style="@style/Adb_Switch" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_screen"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_screen"
                        style="@style/Adb_Text_Title"
                        android:text="@string/wireless_screen" />

                    <TextView
                        android:id="@+id/tv_screen_activation_status"
                        style="@style/Adb_Text_Title"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="@dimen/adb_sw_margin_end"
                        android:text="@string/screen_activation_status_getting" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_factory_reset"
                    android:layout_width="@dimen/adb_item_width"
                    android:layout_height="@dimen/adb_item_height"
                    android:layout_marginTop="@dimen/adb_item_margin_top"
                    android:background="@drawable/bg_adb_item">

                    <TextView
                        android:id="@+id/tv_factory_reset"
                        style="@style/Adb_Text_Title"
                        android:text="@string/factory_reset" />

                </RelativeLayout>
            </LinearLayout>

        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/adb_btn_margin_bottom"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_factory"
                style="@style/Adb_Button"
                android:layout_marginStart="@dimen/adb_item_margin_start"
                android:text="@string/factory_menu" />

            <Button
                android:id="@+id/btn_debug"
                style="@style/Adb_Button"
                android:layout_marginStart="@dimen/adb_btn_margin_start"
                android:text="@string/debug_menu" />

            <Button
                android:id="@+id/btn_touch"
                style="@style/Adb_Button"
                android:layout_width="wrap_content"
                android:layout_marginStart="@dimen/adb_btn_margin_start"
                android:text="@string/touch_calibration" />
        </LinearLayout>

        <EditText
            android:id="@+id/edt_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/line1"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_5"
            android:singleLine="true"
            android:textColor="@color/white"
            android:visibility="invisible" />
    </RelativeLayout>

</com.zyp.cardview.YcCardView>
