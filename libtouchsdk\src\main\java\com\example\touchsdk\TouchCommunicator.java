package com.example.touchsdk;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.util.Log;

import java.util.HashMap;

public class TouchCommunicator {
    private static final String TAG = "TouchCommunicator";
    private static final String libDeviceInterface = "DeviceInterface";

    static {
        System.loadLibrary(libDeviceInterface);
    }

    public native boolean InitSDK();

    public native boolean DeInitSDK();

    public native boolean GetSDKInitStatus();

    public native String GetSDKVersion();

    public native String GetDriverVersion();

    public native String GetProductModel();

    public native String GetFirmwareVersion();

    public native String GetFirmwareCheckSum();

    public native int GetDeviceCount();

    public native int GetMaxPointCount();

    // 设置USB1通道开关
    public native boolean SetUsb1Enable(boolean bFlag);

    // 设置USB2通道开关
    public native boolean SetUsb2Enable(boolean bFlag);

    // 获取USB1发点使能状态
    public native boolean GetUsb1SendCoordEn();

    // 获取USB2发点使能状态
    public native boolean GetUsb2SendCoordEn();

    /**
     * 设置穿透区域：
     * ID：0-9，最多只能10个区域。
     * （X1, Y1）为矩形左上角坐标，（X2, Y2）为矩形右下角坐标；
     * 满足X1<X2, Y1<Y2, 0<=X1/Y1/X2/Y2<=0x7FFF;
     * 坐标中低8位在前，高8位在后。
     * 返回值：
     * -1：命令发送失败
     * 0：设置成功
     * 1：区域ID错误
     * 2：删除或查询的区域ID不存在
     * 3：设置的区域不符合规则。
     */
    public native int SetNonThroughRegion(int nRegionID, int x1, int y1, int x2, int y2);

    /**
     * 删除穿透区域
     * ID：0-9
     */
    public native int DeleteNonThroughRegion(int nRegionID);

    public native boolean SendKeyBoardCmd(byte Mode, byte Key);

    public native boolean SendKeyRelease();
//    public native boolean SendPassThroughCmd(int nDirection, int nEndPoint, int nLen, byte[] SrcData);

    /**
     * 设置屏幕下拉程度。
     * mode：表示屏幕下拉程度，如图所示。
     * 00：表示全屏显示
     * 01：表示下拉1/2
     * 02：表示下拉1/3
     * 03：表示下拉1/4
     * 其他：表示下拉百分比。如下拉2/5，则为40
     */
    public native boolean SetTouchDown(byte mode);

    /**
     * 设置分辨率，用于屏幕显示变化后，触控也跟随变化
     * OrgWidth：原始分辨率宽
     * OrgHigh：原始分辨率高
     * ChgWidth：变化后的分辨率宽
     * ChgHigh：变化后的分辨率高
     * OrgWidth、OrgHigh、ChgWidth、ChgHigh取值范围为（0-32767），且低字节在前
     * <p>
     * 返回true表示设置成功，否则设置失败
     */
    public native boolean SetResolution(short OrgWidth, short OrgHigh, short ChgWidth, short ChgHigh);

    public final static byte CHANNEL_ANDROID = 0;
    public final static byte CHANNEL_OPS = 1;
    public final static byte CHANNEL_HDMI = 2;
    public final static byte CHANNEL_OTHERS = 3;

    /**
     * 设置屏幕小窗口
     * enable：0-关闭小窗口；1-开启小窗口
     * channel：0-安卓；1-OPS；2-HDMI；3-其他
     * x：小窗口的左上角X坐标
     * y：小窗口的左上角Y坐标
     * width：小窗口的宽度
     * high：小窗口的高度
     * x、y、width、high取值范围为（0-32767），且低字节在前
     * <p>
     * 返回值：false-设置失败；true-设置成功
     */
    public native boolean SetSmallWindow(byte enable, byte channel, short x, short y, short width, short high);

    /**
     * 获取小窗口状态
     * channel：0-安卓；1-OPS；2-HDMI；3-其他
     * <p>
     * 返回值：false：关闭；true-开启
     */
    public native boolean GetSmallWindow(byte channel);

    /**
     * 获取升级固件当前进度百分比
     */
    public native int GetUpgradeProcessbar();

    /**
     * 升级固件
     * firmwarePath:表示升级固件的文件路径
     */
    public native void UpgradeFirmware(String firmwarePath);

    /**
     * 精细校准
     * direction:表示精细调整的方向
     * 0x80:左边偏右X1+        0x40:左边偏左X1-
     * 0x20:右边偏右X2+        0x10:右边偏左X2-
     * 0x08:上边偏下Y1+        0x04:上边偏上Y1-
     * 0x02:下边偏下Y2+        0x01:下边偏上Y2-
     * pBuf:校准后的校准参数值
     */
    public native boolean SetFineCalibration(byte direction, byte[] pBuf);


    public final static int VID_TOUCH_ZHONGYUAN = 8183;   //0x1FF7

    /**
     * 通过usb枚举
     * 是否有连接该设备
     */
    public static boolean isPlugged(Context context) {
        UsbManager usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        HashMap<String, UsbDevice> deviceHashMap = usbManager.getDeviceList();
        for (UsbDevice usbDevice : deviceHashMap.values()) {
            if (usbDevice.getVendorId() == VID_TOUCH_ZHONGYUAN) {
                Log.d(TAG, "isPlugged: true");
                return true;
            }
        }
        Log.d(TAG, "isPlugged: false");
        return false;
    }
}
