package com.eeo.ota.arraymic;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.util.Log;

import java.util.HashMap;

/**
 * A helper class for executing ADB commands and detecting USB devices.
 * This class will encapsulate all interactions with the shell and UsbManager.
 */
public class AdbHelper {

    private static final String TAG = "ArrayMicOTA";

    private final Context mContext;

    public AdbHelper(Context context) {
        this.mContext = context.getApplicationContext();
    }

    /**
     * Finds a USB device by its Vendor ID and Product ID.
     *
     * @param vendorId  The Vendor ID of the device.
     * @param productId The Product ID of the device.
     * @return The UsbDevice object if found, otherwise null.
     */
    public UsbDevice findDeviceByPidVid(int vendorId, int productId) {
        UsbManager usbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        if (usbManager == null) {
            Log.e(TAG, "UsbManager not available.");
            return null;
        }
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        for (UsbDevice device : deviceList.values()) {
            if (device.getVendorId() == vendorId && device.getProductId() == productId) {
                Log.i(TAG, "Found device with VID: " + vendorId + ", PID: " + productId);
                return device;
            }
        }
        return null;
    }

    /**
     * Executes a shell command and returns its output.
     *
     * @param command The command to execute.
     * @return The output of the command, or null if an error occurred.
     */
    public String executeShellCommandWithOutput(String command) {
        Process process = null;
        try {
            // Use ProcessBuilder to better simulate a shell environment
            ProcessBuilder pb = new ProcessBuilder("sh", "-c", command);
            process = pb.start();
            java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            process.waitFor();
            return output.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error executing shell command with output: " + command, e);
            return null;
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
    }

    /**
     * Executes a command that does not produce significant output, and waits for it to complete.
     * This is more robust for commands like `push` or system commands.
     *
     * @param command The command to execute.
     * @return true if the command executes without errors (exit code 0), false otherwise.
     */
    public boolean executeCommandAndWait(String command) {
        Process process = null;
        try {
            Log.d(TAG, "Executing command: " + command);

            ProcessBuilder pb = new ProcessBuilder("sh", "-c", command);
            process = pb.start();

            consumeStream(process.getInputStream());
            consumeStream(process.getErrorStream());
            
            int exitCode = process.waitFor();
            Log.d(TAG, "Command [" + command + "] finished with exit code: " + exitCode);
            return exitCode == 0;
        } catch (Exception e) {
            Log.e(TAG, "Error executing command and waiting: " + command, e);
            return false;
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
    }

    private void consumeStream(java.io.InputStream stream) {
        new Thread(() -> {
            try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(stream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    Log.v(TAG, "Stream consumer: " + line);
                }
            } catch (java.io.IOException e) {
                // Ignore
            }
        }).start();
    }

    /**
     * Gets the size of a remote file via ADB.
     *
     * @param remotePath The full path to the file on the remote device.
     * @return The file size in bytes, or -1 if not found or an error occurs.
     */
    public long getRemoteFileSize(String remotePath) {
        // The `ls -l` command output is like:
        // -rw-rw-rw- 1 <USER> <GROUP> 12345 2025-07-25 14:00 /mnt/UDISK/firmware.swu
        // We need to parse the size (12345).
        String command = "adb -s " + "303_usb_device" + " shell ls -l " + remotePath;
        String result = executeShellCommandWithOutput(command);
        if (result == null || result.contains("No such file or directory")) {
            return -1;
        }
        try {
            String[] parts = result.trim().split("\\s+");
            if (parts.length > 4) {
                return Long.parseLong(parts[4]);
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "Failed to parse file size from ls output: " + result, e);
        }
        return -1;
    }
}
