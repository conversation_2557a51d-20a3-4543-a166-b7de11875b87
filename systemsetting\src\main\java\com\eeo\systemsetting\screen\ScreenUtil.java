package com.eeo.systemsetting.screen;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;

import com.eeo.systemsetting.utils.Constant;

import java.lang.reflect.Method;

public class ScreenUtil {

    /**
     * 启用无线投屏
     */
    public static void enableWirelessScreen(Context context) {
        //通过启动LicenseCheckService来启动飞图投屏
        Intent intent = new Intent();
        intent.setClassName(Constant.MULTI_SCREEN_PKG_NAME, Constant.MULTI_SCREEN_SERVICE_NAME);
        context.startService(intent);
        //启用飞图投屏用来开机启动的receiver
        PackageManager pm = context.getPackageManager();
        ComponentName componentName = new ComponentName(Constant.MULTI_SCREEN_PKG_NAME, Constant.MULTI_SCREEN_RECEIVER_NAME);
        pm.setComponentEnabledSetting(componentName, PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
    }

    /**
     * 禁用无线投屏
     */
    public static void disableWirelessScreen(Context context) {
        //杀掉飞图投屏
        forceStopPackage(context, Constant.MULTI_SCREEN_PKG_NAME);
        //禁用飞图投屏用来开机启动的receiver
        PackageManager pm = context.getPackageManager();
        ComponentName componentName = new ComponentName(Constant.MULTI_SCREEN_PKG_NAME, Constant.MULTI_SCREEN_RECEIVER_NAME);
        pm.setComponentEnabledSetting(componentName, PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
    }

    public static void forceStopPackage(Context context, String pkgName) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        try {
            Method forceStopPackage = am.getClass().getMethod("forceStopPackage", String.class);
            forceStopPackage.setAccessible(true);
            forceStopPackage.invoke(am, pkgName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 重新启动飞图投屏
     */
    public static void restartWirelessScreen(Context context) {
        //杀掉飞图投屏
        forceStopPackage(context, Constant.MULTI_SCREEN_PKG_NAME);
        //通过启动LicenseCheckService来启动飞图投屏
        Intent intent = new Intent();
        intent.setClassName(Constant.MULTI_SCREEN_PKG_NAME, Constant.MULTI_SCREEN_SERVICE_NAME);
        context.startService(intent);
    }
}
