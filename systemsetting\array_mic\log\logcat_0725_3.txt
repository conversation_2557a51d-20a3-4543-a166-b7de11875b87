2025-07-25 15:04:11.354  7013-7013  EeoApplication==        com.eeo.systemsetting                I  EeoApplication:onCreate - Sending broadcast to internal ArrayMicUpdateCheckReceiver.
2025-07-25 15:04:11.367  7013-7032  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 113 RECEIVER: 0 / ReceiverData{intent=Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver } packageName=com.eeo.systemsetting resultCode=-1 resultData=null resultExtras=null}
2025-07-25 15:04:11.756  7013-7013  ActivityThread          com.eeo.systemsetting                V  Performing receive of Intent { act=com.eeo.systemsetting.action.CHECK_ARRAY_MIC_UPDATE flg=0x10 pkg=com.eeo.systemsetting cmp=com.eeo.systemsetting/.receiver.ArrayMicUpdateCheckReceiver }: app=com.eeo.systemsetting.EeoApplication@d8c9f09, appName=com.eeo.systemsetting, pkg=com.eeo.systemsetting, comp={com.eeo.systemsetting/com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver}, dir=/data/app/~~qVgH2B_woJBpK7kbUYgmrg==/com.eeo.systemsetting-k9_mAaAEHepNRQ11EpWNUw==/base.apk
2025-07-25 15:04:11.756  7013-7013  ArrayMicUp...ckReceiver com.eeo.systemsetting                I  Received action to check Array Microphone update.
2025-07-25 15:04:11.757  7013-7013  ContextImpl             com.eeo.systemsetting                W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 android.content.ContextWrapper.startService:720 com.eeo.systemsetting.receiver.ArrayMicUpdateCheckReceiver.onReceive:20 android.app.ActivityThread.handleReceiver:4030 
2025-07-25 15:04:11.762  7013-7034  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 114 CREATE_SERVICE: 0 / CreateServiceData{token=android.os.BinderProxy@5817d0f className=com.eeo.ota.arraymic.ArrayMicUpdateService packageName=com.eeo.systemsetting intent=null}
2025-07-25 15:04:11.764  7013-7032  ActivityThread          com.eeo.systemsetting                V  SCHEDULE 115 SERVICE_ARGS: 0 / ServiceArgsData{token=android.os.BinderProxy@5817d0f startId=1 args=Intent { cmp=com.eeo.systemsetting/com.eeo.ota.arraymic.ArrayMicUpdateService }}
2025-07-25 15:04:11.931  7013-7013  ActivityThread          com.eeo.systemsetting                V  Creating service com.eeo.ota.arraymic.ArrayMicUpdateService
2025-07-25 15:04:11.933  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Service onCreate.
2025-07-25 15:04:11.943  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Service onStartCommand.
2025-07-25 15:04:11.943  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Checking for array mic update...
2025-07-25 15:04:11.947  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Config parsed: version=A013, file=QH303_V197_20240712.swu
2025-07-25 15:04:11.993  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Current version: A010, Target version: A013
2025-07-25 15:04:11.993  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Is version lower? true. Is specific error version? false
2025-07-25 15:04:11.993  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Starting array mic update...
2025-07-25 15:04:11.996  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Starting Array Mic update process...
2025-07-25 15:04:11.996  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: SWITCHING_USB
2025-07-25 15:04:13.652  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Found device with VID: 8711, PID: 25
2025-07-25 15:04:13.652  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  USB device detected.
2025-07-25 15:04:13.652  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: DETECTING_ADB
2025-07-25 15:04:13.814  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  ADB device detected.
2025-07-25 15:04:13.814  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: STOPPING_SERVICE
2025-07-25 15:04:15.852  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-07-25 15:04:17.927  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-07-25 15:04:34.192  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: VALIDATING_FIRMWARE
2025-07-25 15:04:34.221  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Validating firmware size. Local: 35676160, Remote: -1
2025-07-25 15:04:34.221  7013-7013  ArrayMicOTA             com.eeo.systemsetting                W  Firmware validation failed. Retrying push... (Attempt 2)
2025-07-25 15:04:34.221  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-07-25 15:04:36.295  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-07-25 15:04:50.441  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: VALIDATING_FIRMWARE
2025-07-25 15:04:50.468  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Validating firmware size. Local: 35676160, Remote: -1
2025-07-25 15:04:50.468  7013-7013  ArrayMicOTA             com.eeo.systemsetting                W  Firmware validation failed. Retrying push... (Attempt 3)
2025-07-25 15:04:50.468  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANING_REMOTE_DIR
2025-07-25 15:04:52.539  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: PUSHING_FIRMWARE
2025-07-25 15:05:06.704  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: VALIDATING_FIRMWARE
2025-07-25 15:05:06.730  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Validating firmware size. Local: 35676160, Remote: -1
2025-07-25 15:05:06.730  7013-7013  ArrayMicOTA             com.eeo.systemsetting                E  Update failed: Firmware validation failed after 3 retries.
2025-07-25 15:05:06.730  7013-7013  ArrayMicOTA             com.eeo.systemsetting                E  Internal callback: Update fail: Firmware validation failed after 3 retries.
2025-07-25 15:05:06.730  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Executing state: CLEANUP
2025-07-25 15:05:06.730  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Update process finished. Switching USB back to PC.
2025-07-25 15:05:07.758  7013-7013  ArrayMicOTA             com.eeo.systemsetting                I  Internal callback: All updates finished. Stopping service.
2025-07-25 15:05:07.763  7013-7013  ActivityThread          com.eeo.systemsetting                V  Destroying service com.eeo.ota.arraymic.ArrayMicUpdateService@c0e5997
2025-07-25 15:05:07.763  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Service onDestroy.
2025-07-25 15:05:07.763  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.
2025-07-25 15:05:07.763  7013-7013  ArrayMicOTA             com.eeo.systemsetting                D  Releasing resources.