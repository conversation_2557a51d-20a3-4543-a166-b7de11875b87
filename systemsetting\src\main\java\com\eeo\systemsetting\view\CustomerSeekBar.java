package com.eeo.systemsetting.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.SeekBar;

@SuppressLint("AppCompatCustomView")
public class CustomerSeekBar extends SeekBar {
    private int mCurrentPointerId = 0;
    private int mWidth;
    private int mHeight;

    public CustomerSeekBar(Context context) {
        super(context);
    }

    public CustomerSeekBar(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomerSeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec,
                                          int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = getWidth();
        mHeight = getHeight();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int pointerId = event.getPointerId(event.getActionIndex());
        if (mCurrentPointerId != pointerId) {
            //不同手指，且action的位置在控件外：丢掉该事件
            if (event.getX() < 0 || event.getX() > mWidth || event.getY() < 0 || event.getY() > mHeight) {
                return true;
            } else {
                mCurrentPointerId = pointerId;
            }
        }
        return super.onTouchEvent(event);
    }
}
