<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

        <TextView
            android:id="@+id/tv_screen_offset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/eeo_screen_move_txt"
            android:textSize="@dimen/screen_offset_tv_text_size"
            android:layout_alignParentBottom="true"
            android:textColor="#474747"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/screen_offset_tv_margin_horizontal"
            android:layout_marginBottom="@dimen/screen_offset_tv_margin_bottom"
            />
</RelativeLayout>