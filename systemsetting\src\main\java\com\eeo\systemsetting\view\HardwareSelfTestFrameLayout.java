package com.eeo.systemsetting.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;

@SuppressLint("ViewConstructor")
public class HardwareSelfTestFrameLayout extends FrameLayout {
    public static final String TAG = "HardwareSelfTestFrameLayout===";
    View rootView = null;
    Context context;

    private TextView cpuTv;
    private TextView memoryTv;
    private TextView hardDiskTv;
    private TextView touchTv;
    private TextView ethernetTv;
    private TextView wifiTv;
    private TextView micTv;

    private int index; //当前测试项id
    private static final int INDEX_CPU = 0;
    private static final int INDEX_MEMORY = 1;
    private static final int INDEX_HARD_DISK = 2;
    private static final int INDEX_TOUCH = 3;
    private static final int INDEX_ETHERNET = 4;
    private static final int INDEX_WIFI = 5;
    private static final int INDEX_MIC = 6;

    private static final int MSG_TEST_START = 0;
    private static final int MSG_TEST_RESULT = 1;

    private static final int RESULT_TESTING = 0;
    private static final int RESULT_SUCCESS = 1;
    private static final int RESULT_FAIL = 2;


    @SuppressLint("HandlerLeak")
    private final Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_TEST_START:
                    testItem();
                    break;
                case MSG_TEST_RESULT:
                    showTestResult(RESULT_SUCCESS);
                    index++;
                    if (index <= INDEX_MIC) {
                        sendEmptyMessage(MSG_TEST_START);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    public HardwareSelfTestFrameLayout(Context context) {
        super(context);
        this.context = context;
        if (rootView == null) {
            rootView = LayoutInflater.from(context).inflate(R.layout.activity_hardware_self_test, null);
        }

        findView();
        addView(rootView);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void findView() {
        cpuTv = rootView.findViewById(R.id.tv_cpu_test_result);
        memoryTv = rootView.findViewById(R.id.tv_memory_test_result);
        hardDiskTv = rootView.findViewById(R.id.tv_hard_disk_test_result);
        touchTv = rootView.findViewById(R.id.tv_touch_test_result);
        ethernetTv = rootView.findViewById(R.id.tv_ethernet_test_result);
        wifiTv = rootView.findViewById(R.id.tv_wifi_test_result);
        micTv = rootView.findViewById(R.id.tv_mic_test_result);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        //WebView初始化的时候会还原density的值，这里需要重新update
        CommonUtils.updateDensity(context);
        handler.sendEmptyMessage(MSG_TEST_START);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    private void testItem() {
        showTestResult(RESULT_TESTING);
        handler.removeMessages(MSG_TEST_RESULT);
        handler.sendEmptyMessageDelayed(MSG_TEST_RESULT, 1000);
    }

    private void showTestResult(int result) {
        TextView textView;
        switch (index) {
            case INDEX_CPU:
                textView = cpuTv;
                break;
            case INDEX_MEMORY:
                textView = memoryTv;
                break;
            case INDEX_HARD_DISK:
                textView = hardDiskTv;
                break;
            case INDEX_TOUCH:
                textView = touchTv;
                break;
            case INDEX_ETHERNET:
                textView = ethernetTv;
                break;
            case INDEX_WIFI:
                textView = wifiTv;
                break;
            case INDEX_MIC:
                textView = micTv;
                break;
            default:
                textView = cpuTv;
                break;
        }
        switch (result) {
            case RESULT_TESTING:
                textView.setText(context.getString(R.string.hardware_self_test_testing));
                textView.setTextColor(context.getColor(R.color.black_100));
                break;
            case RESULT_SUCCESS:
                textView.setText(context.getString(R.string.hardware_self_test_success));
                textView.setTextColor(context.getColor(R.color.green_100));
                break;
            case RESULT_FAIL:
                textView.setText(context.getString(R.string.hardware_self_test_fail));
                textView.setTextColor(context.getColor(R.color.error_red));
                break;
        }
    }
}
