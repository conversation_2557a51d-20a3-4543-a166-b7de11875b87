// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.fragment;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class AboutFragment_ViewBinding implements Unbinder {
  private AboutFragment target;

  private View view7f0800f6;

  private View view7f0800f3;

  private View view7f08011a;

  @UiThread
  public AboutFragment_ViewBinding(final AboutFragment target, View source) {
    this.target = target;

    View view;
    target.txtName = Utils.findRequiredViewAsType(source, R.id.txt_name, "field 'txtName'", TextView.class);
    target.txtSerial = Utils.findRequiredViewAsType(source, R.id.txt_serial, "field 'txtSerial'", TextView.class);
    target.txtResolution = Utils.findRequiredViewAsType(source, R.id.txt_resolution, "field 'txtResolution'", TextView.class);
    target.txtTotal = Utils.findRequiredViewAsType(source, R.id.txt_total, "field 'txtTotal'", TextView.class);
    target.txtStoreTotal = Utils.findRequiredViewAsType(source, R.id.txt_store_total, "field 'txtStoreTotal'", TextView.class);
    target.txtAndroidVersion = Utils.findRequiredViewAsType(source, R.id.txt_android_version, "field 'txtAndroidVersion'", TextView.class);
    target.txtSystemVersion = Utils.findRequiredViewAsType(source, R.id.txt_system_version, "field 'txtSystemVersion'", TextView.class);
    target.txtTouchVersion = Utils.findRequiredViewAsType(source, R.id.txt_touch_version, "field 'txtTouchVersion'", TextView.class);
    target.txtHotlineTitle = Utils.findRequiredViewAsType(source, R.id.txt_hotline_title, "field 'txtHotlineTitle'", TextView.class);
    target.txtHotline = Utils.findRequiredViewAsType(source, R.id.txt_hotline, "field 'txtHotline'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_windows_host, "field 'llWindowsHost' and method 'onClick'");
    target.llWindowsHost = Utils.castView(view, R.id.ll_windows_host, "field 'llWindowsHost'", LinearLayout.class);
    view7f0800f6 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.ll_system_version, "field 'llSystemVersion' and method 'onClick'");
    target.llSystemVersion = Utils.castView(view, R.id.ll_system_version, "field 'llSystemVersion'", LinearLayout.class);
    view7f0800f3 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_privacy_policy, "field 'rlPrivacy' and method 'onClick'");
    target.rlPrivacy = Utils.castView(view, R.id.rl_privacy_policy, "field 'rlPrivacy'", RelativeLayout.class);
    view7f08011a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    AboutFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.txtName = null;
    target.txtSerial = null;
    target.txtResolution = null;
    target.txtTotal = null;
    target.txtStoreTotal = null;
    target.txtAndroidVersion = null;
    target.txtSystemVersion = null;
    target.txtTouchVersion = null;
    target.txtHotlineTitle = null;
    target.txtHotline = null;
    target.llWindowsHost = null;
    target.llSystemVersion = null;
    target.rlPrivacy = null;

    view7f0800f6.setOnClickListener(null);
    view7f0800f6 = null;
    view7f0800f3.setOnClickListener(null);
    view7f0800f3 = null;
    view7f08011a.setOnClickListener(null);
    view7f08011a = null;
  }
}
