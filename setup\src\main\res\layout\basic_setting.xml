<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_bg"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="485dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="485dp"
        android:layout_marginTop="222dp"
        tools:ignore="MissingConstraints">
    <ImageView
        android:id="@+id/back_ic"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_arrow_left_w"
        tools:ignore="MissingConstraints" />

    <TextView
        app:layout_constraintLeft_toRightOf="@id/back_ic"
        android:layout_marginStart="103dp"
        android:layout_width="wrap_content"
        android:layout_height="21dp"
        android:text="@string/basic_setting"
        android:textSize="16sp"
        android:textColor="@color/white_100"
        tools:ignore="MissingConstraints"
        >
    </TextView>

        <TextView
            android:id="@+id/use_type"
            android:layout_width="43dp"
            android:layout_height="29dp"
            android:layout_marginTop="38dp"
            android:text="@string/use_type"
            android:textColor="@color/white_100"
            android:textSize="9sp"
            app:layout_constraintStart_toStartOf="@id/back_ic"
            app:layout_constraintTop_toBottomOf="@id/back_ic" />

        <RadioGroup
            android:id="@+id/education_radio"
            android:layout_width="wrap_content"
            android:layout_height="13dp"
            android:layout_marginLeft="35dp"
            android:orientation="horizontal"
            app:layout_constraintLeft_toRightOf="@+id/use_type"
            app:layout_constraintTop_toTopOf="@id/use_type">
            <RadioButton
                android:id="@+id/education_id"
                android:paddingLeft="2dp"
                android:layout_gravity="center|left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="7dp"
                android:enabled="true"
                android:checked="false"
                android:text="@string/education"
                android:textSize="9sp"
                android:button="@drawable/transparent_button"
                android:drawableLeft="@drawable/radio_btn_selctor"
                ></RadioButton>

            <RadioButton
                android:id="@+id/meetting_id"
                android:paddingLeft="2dp"
                android:layout_gravity="center|left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="27dp"
                android:text="@string/conference"
                android:enabled="true"
                android:textSize="9sp"
                android:checked="false"
                android:button="@drawable/transparent_button"
                android:drawableLeft="@drawable/radio_btn_selctor"
                android:drawablePadding="7dp"

                ></RadioButton>
        </RadioGroup>

        <TextView
            android:id="@+id/organization_id"
            app:layout_constraintStart_toStartOf="@id/back_ic"
            app:layout_constraintTop_toBottomOf="@id/education_radio"
            android:layout_marginTop="19dp"
            android:layout_width="56dp"
            android:layout_height="29dp"
            android:text="@string/organization_name"
            android:textSize="9sp"
            android:gravity="center_vertical"
            android:textColor="@color/white_100"
            ></TextView>
        <EditText
            android:id="@+id/organization_et_id"
            app:layout_constraintTop_toTopOf="@id/organization_id"
            app:layout_constraintLeft_toRightOf="@id/organization_id"
            android:layout_marginLeft="16dp"
            android:layout_width="237dp"
            android:layout_height="29dp"
            android:textSize="9sp"
            android:gravity="center|end"
            android:hint="@string/input_hint"
            android:background="@drawable/input_bg"
            android:paddingRight="9dp"
            android:textCursorDrawable="@drawable/shape_network_edt_cursor"
            ></EditText>

        <TextView
            android:id="@+id/device_addr_id"
            app:layout_constraintStart_toStartOf="@id/back_ic"
            app:layout_constraintTop_toBottomOf="@id/organization_id"
            android:layout_marginTop="19dp"
            android:layout_width="56dp"
            android:layout_height="29dp"
            android:text="@string/device_addr"
            android:textSize="9sp"
            android:gravity="center_vertical"
            android:textColor="@color/white_100"
            ></TextView>
        <EditText
            android:id="@+id/device_addr_et_id"
            app:layout_constraintTop_toTopOf="@id/device_addr_id"
            app:layout_constraintLeft_toRightOf="@id/device_addr_id"
            android:layout_marginLeft="16dp"
            android:layout_width="237dp"
            android:layout_height="29dp"
            android:textSize="9sp"
            android:hint="@string/input_hint"
            android:gravity="center_vertical|end"
            android:background="@drawable/input_bg"
            android:paddingRight="9dp"
            android:textCursorDrawable="@drawable/shape_network_edt_cursor"
            ></EditText>

        <TextView
            android:id="@+id/projection_tv_id"
            app:layout_constraintStart_toStartOf="@id/back_ic"
            app:layout_constraintTop_toBottomOf="@id/device_addr_id"
            android:layout_marginTop="19dp"
            android:layout_width="wrap_content"
            android:layout_height="13dp"
            android:text="@string/projection_text"
            android:textSize="9sp"
            android:gravity="center_vertical"
            android:textColor="@color/white_100"
            ></TextView>
        <TextView
            android:id="@+id/projection_tips_id"
            app:layout_constraintStart_toStartOf="@id/back_ic"
            app:layout_constraintTop_toBottomOf="@id/projection_tv_id"
            android:layout_width="wrap_content"
            android:layout_height="13dp"
            android:text="@string/projection_text"
            android:textSize="7sp"
            android:gravity="center_vertical"
            android:textColor="@color/white_70"
            ></TextView>

        <ToggleButton
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="270dp"
            android:layout_marginTop="181dp"
            android:id="@+id/projection_button"
            android:layout_width="40dp"
            android:layout_height="20dp"
            android:checked="true"
            android:gravity="center"
            android:textSize="0dp"
            android:background="@drawable/custom_toggle_button"
            ></ToggleButton>
        <Button
            android:id="@+id/confirm_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/projection_tips_id"
            android:layout_width="88dp"
            android:layout_height="28dp"
            android:layout_marginTop="44dp"
            android:layout_marginLeft="110dp"
            android:gravity="center"
            style="@style/MyButtonStyle"
            android:textSize="11sp"
            android:text="@string/confirm"></Button>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>