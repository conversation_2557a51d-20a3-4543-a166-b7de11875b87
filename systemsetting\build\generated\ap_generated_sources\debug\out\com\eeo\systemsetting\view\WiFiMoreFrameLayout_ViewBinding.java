// Generated code from Butter Knife. Do not modify!
package com.eeo.systemsetting.view;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.eeo.systemsetting.R;
import java.lang.CharSequence;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WiFiMoreFrameLayout_ViewBinding implements Unbinder {
  private WiFiMoreFrameLayout target;

  private View view7f0800bd;

  private View view7f0801d5;

  private View view7f080085;

  private View view7f080087;

  private View view7f0800a1;

  private View view7f080074;

  private TextWatcher view7f080074TextWatcher;

  private View view7f080077;

  private TextWatcher view7f080077TextWatcher;

  private View view7f080073;

  private TextWatcher view7f080073TextWatcher;

  private View view7f08006f;

  private TextWatcher view7f08006fTextWatcher;

  private View view7f0801c3;

  @UiThread
  public WiFiMoreFrameLayout_ViewBinding(WiFiMoreFrameLayout target) {
    this(target, target);
  }

  @UiThread
  public WiFiMoreFrameLayout_ViewBinding(final WiFiMoreFrameLayout target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_back, "field 'ivBack' and method 'onClick'");
    target.ivBack = Utils.castView(view, R.id.iv_back, "field 'ivBack'", ImageView.class);
    view7f0800bd = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.txt_save, "field 'txtSave' and method 'onClick'");
    target.txtSave = Utils.castView(view, R.id.txt_save, "field 'txtSave'", TextView.class);
    view7f0801d5 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.fl_disconnect, "field 'flDisconnect' and method 'onClick'");
    target.flDisconnect = Utils.castView(view, R.id.fl_disconnect, "field 'flDisconnect'", FrameLayout.class);
    view7f080085 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.fl_forget, "field 'flForget' and method 'onClick'");
    target.flForget = Utils.castView(view, R.id.fl_forget, "field 'flForget'", FrameLayout.class);
    view7f080087 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.img_arrow, "field 'imgArrow' and method 'onClick'");
    target.imgArrow = Utils.castView(view, R.id.img_arrow, "field 'imgArrow'", ImageView.class);
    view7f0800a1 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.rlManual = Utils.findRequiredViewAsType(source, R.id.rl_manual, "field 'rlManual'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.edt_ip_address, "field 'edtIpAddress', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtIpAddress = Utils.castView(view, R.id.edt_ip_address, "field 'edtIpAddress'", EditText.class);
    view7f080074 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080074TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080074TextWatcher);
    target.imgIp = Utils.findRequiredViewAsType(source, R.id.img_ip, "field 'imgIp'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_subnet_mask, "field 'edtSubnetMask', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtSubnetMask = Utils.castView(view, R.id.edt_subnet_mask, "field 'edtSubnetMask'", EditText.class);
    view7f080077 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080077TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080077TextWatcher);
    target.imgSubnetMask = Utils.findRequiredViewAsType(source, R.id.img_subnet_mask, "field 'imgSubnetMask'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_gateway, "field 'edtGateway', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtGateway = Utils.castView(view, R.id.edt_gateway, "field 'edtGateway'", EditText.class);
    view7f080073 = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f080073TextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f080073TextWatcher);
    target.imgGateway = Utils.findRequiredViewAsType(source, R.id.img_gateway, "field 'imgGateway'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.edt_DNS, "field 'edtDNS', method 'expandAppBarOnFocusChangeListener', and method 'onEditTextChange'");
    target.edtDNS = Utils.castView(view, R.id.edt_DNS, "field 'edtDNS'", EditText.class);
    view7f08006f = view;
    view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View p0, boolean p1) {
        target.expandAppBarOnFocusChangeListener(p0, p1);
      }
    });
    view7f08006fTextWatcher = new TextWatcher() {
      @Override
      public void onTextChanged(CharSequence p0, int p1, int p2, int p3) {
        target.onEditTextChange();
      }

      @Override
      public void beforeTextChanged(CharSequence p0, int p1, int p2, int p3) {
      }

      @Override
      public void afterTextChanged(Editable p0) {
      }
    };
    ((TextView) view).addTextChangedListener(view7f08006fTextWatcher);
    target.imgDNS = Utils.findRequiredViewAsType(source, R.id.img_DNS, "field 'imgDNS'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.txt_ip_setting, "field 'txtIpSetting' and method 'onClick'");
    target.txtIpSetting = Utils.castView(view, R.id.txt_ip_setting, "field 'txtIpSetting'", TextView.class);
    view7f0801c3 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onClick(p0);
      }
    });
    target.txtIpAddressTitle = Utils.findRequiredViewAsType(source, R.id.txt_ip_address_title, "field 'txtIpAddressTitle'", TextView.class);
    target.txtIpAddress = Utils.findRequiredViewAsType(source, R.id.txt_ip_address, "field 'txtIpAddress'", TextView.class);
    target.txtMacAddress = Utils.findRequiredViewAsType(source, R.id.txt_mac_address, "field 'txtMacAddress'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    WiFiMoreFrameLayout target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.ivBack = null;
    target.txtSave = null;
    target.flDisconnect = null;
    target.flForget = null;
    target.imgArrow = null;
    target.rlManual = null;
    target.edtIpAddress = null;
    target.imgIp = null;
    target.edtSubnetMask = null;
    target.imgSubnetMask = null;
    target.edtGateway = null;
    target.imgGateway = null;
    target.edtDNS = null;
    target.imgDNS = null;
    target.txtIpSetting = null;
    target.txtIpAddressTitle = null;
    target.txtIpAddress = null;
    target.txtMacAddress = null;

    view7f0800bd.setOnClickListener(null);
    view7f0800bd = null;
    view7f0801d5.setOnClickListener(null);
    view7f0801d5 = null;
    view7f080085.setOnClickListener(null);
    view7f080085 = null;
    view7f080087.setOnClickListener(null);
    view7f080087 = null;
    view7f0800a1.setOnClickListener(null);
    view7f0800a1 = null;
    view7f080074.setOnFocusChangeListener(null);
    ((TextView) view7f080074).removeTextChangedListener(view7f080074TextWatcher);
    view7f080074TextWatcher = null;
    view7f080074 = null;
    view7f080077.setOnFocusChangeListener(null);
    ((TextView) view7f080077).removeTextChangedListener(view7f080077TextWatcher);
    view7f080077TextWatcher = null;
    view7f080077 = null;
    view7f080073.setOnFocusChangeListener(null);
    ((TextView) view7f080073).removeTextChangedListener(view7f080073TextWatcher);
    view7f080073TextWatcher = null;
    view7f080073 = null;
    view7f08006f.setOnFocusChangeListener(null);
    ((TextView) view7f08006f).removeTextChangedListener(view7f08006fTextWatcher);
    view7f08006fTextWatcher = null;
    view7f08006f = null;
    view7f0801c3.setOnClickListener(null);
    view7f0801c3 = null;
  }
}
