package com.eeo.systemsetting.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.SeekBar;

@SuppressLint("AppCompatCustomView")
public class VerticalSeekBar extends SeekBar {
    /**
     * 是否支持点击修改progress
     */
    private boolean mEnableClick = true;

    private float mDownY;
    private int mDownProgress;
    private int mLastProgress;
    private int mProgress;
    private int mMax;
    private int mHeight;

    public VerticalSeekBar(Context context) {
        super(context);
        mMax = getMax();
    }

    public VerticalSeekBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        mMax = getMax();
    }

    public VerticalSeekBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mMax = getMax();
    }

    /**
     * 水平旋转成竖直旋转-90°
     */
    @Override
    protected void onDraw(Canvas c) {
        c.rotate(-90);
        c.translate(-mHeight, 0);
        super.onDraw(c);
    }

    /**
     * 水平旋转成竖直width和height互换
     */
    @Override
    protected synchronized void onMeasure(int widthMeasureSpec,
                                          int heightMeasureSpec) {
        super.onMeasure(heightMeasureSpec, widthMeasureSpec);
        setMeasuredDimension(getMeasuredHeight(), getMeasuredWidth());
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isEnabled()) {
            return false;
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownY = event.getY();
                mDownProgress = getProgress();
                mLastProgress = getProgress();
                break;
            case MotionEvent.ACTION_MOVE:
                if (mEnableClick) {
                    mProgress = mMax - Math.round(mMax * event.getY() / mHeight);
                } else {
                    mProgress = mDownProgress + Math.round(mMax * (mDownY - event.getY()) / mHeight);
                }
                if (mProgress < 0) {
                    mProgress = 0;
                }
                if (mProgress > mMax) {
                    mProgress = mMax;
                }
                if (mProgress != mLastProgress) {
                    setProgress(mProgress);
                    mLastProgress = mProgress;
//                    onSizeChanged(getWidth(), mHeight, 0, 0);
                }
                break;
            case MotionEvent.ACTION_UP:
                break;
        }
        return true;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(h, w, oldh, oldw);
        mHeight = h;
    }

    /**
     * 设置是否支持点击修改progress
     */
    public void enableClick(boolean enable) {
        mEnableClick = enable;
    }
}
