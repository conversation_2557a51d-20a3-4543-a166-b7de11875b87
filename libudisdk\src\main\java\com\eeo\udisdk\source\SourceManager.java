package com.eeo.udisdk.source;

import android.content.Context;
import android.util.Log;

import com.eeo.udisdk.UdiConstant;
import com.ifpdos.udi.sdk.IEventHandler;
import com.ifpdos.udi.sdk.UdiSdk;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 信号源通道相关
 */
public class SourceManager {
    private static final String TAG = "Udi-SourceManager";
    private Context mContext;
    private MediaType mMediaType;
    private OkHttpClient mClient;
    private IEventHandler mSourceChangeHandler = null;

    private final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    public SourceManager(Context context, MediaType mediaType, OkHttpClient client) {
        mContext = context;
        mMediaType = mediaType;
        mClient = client;
    }

    /**
     * 获取当前信号源
     *
     * @return
     * @see UdiConstant#SOURCE_PC
     */
    public String getCurrentSource() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/source/current"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getCurrentSource body:" + body);
                /*返回的例子:
                {
                    "source":"HDMI1"
                }*/
                JSONObject jsonObject = new JSONObject(body);
                return jsonObject.optString("source");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取支持的信号源
     *
     * @return
     */
    public String getAvailableSourceArray() {
        String body = null;
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/source/list/available"))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getAvailableSourceArray: response=" + response);
            if (response.code() == 200) {
                body = response.body().string();
                Log.e(TAG, "getSourceArray: " + body);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        return body;
    }

    /**
     * 切换信号源
     *
     * @param source
     * @see UdiConstant#SOURCE_PC
     */
    public void changeSource(String source) {
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("source", source);
                    jsonObject.put("reason", UdiConstant.SOURCE_CHANGE_REASON_NORMAL);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Request request = new Request.Builder()
                        .url(UdiSdk.getFullUrl("/v1/source/change"))
                        .post(RequestBody.create(jsonObject.toString(), mMediaType))
                        .build();
                try {
                    Response response = mClient.newCall(request).execute();
                    Log.d(TAG, "changeSource:response=" + response);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 监听信号源通道变化
     */
    public void registerSourceChangeListener(SourceChangeListener sourceChangeListener) {
        if (sourceChangeListener == null) {
            return;
        }
        if (mSourceChangeHandler != null) {
            unregisterSourceChangeListener();
        }
        mSourceChangeHandler = new IEventHandler() {
            @Override
            public void onEvent(String event, String data) {
                Log.d(TAG, "onEvent: " + event + ",  data=" + data);
               /* event 是 /v1 / source / change,
                        data 是数据的 json:
                {
                    "source":"HDMI1",
                        "previousSource":"HDMI1",
                        "isFinished":true
                }*/
                if (data != null) {
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        String previousSource = jsonObject.optString("previousSource");
                        String newSource = jsonObject.optString("source");
                        boolean isFinished = jsonObject.optBoolean("isFinished");
                        sourceChangeListener.onSourceChange(previousSource, newSource, isFinished);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
        //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
        UdiSdk.registerEvents(Arrays.asList("v1/source/change"), mSourceChangeHandler);
    }

    /**
     * 取消监听信号源通道变化
     */
    public void unregisterSourceChangeListener() {
        if (mSourceChangeHandler != null) {
            //历史原因，旧的 UDI 版本不支持 / 开头的消息监听。
            UdiSdk.unregisterEvents(Arrays.asList("v1/source/change"), mSourceChangeHandler);
            mSourceChangeHandler = null;
        }
    }

    public String getHdmirxEdidType() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/picture/resolution"))
                .build();
        /*返回的例子:
        {
            "videoStandard":"VIDEO_STANDARD_AUTO",
                "audioStandard":"AUDIO_STANDARD_AUTO",
                "horizontal":1,
                "vertical":1,
                "interlace":true,
                "frequency":1,
                "isVideoType":true
        }*/
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getHdmirxEdidType:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getHdmirxEdidType body:" + body);
                //TODO 解析具体要的内容
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return body;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getHdmiOutTimmingFormat() {
        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/source/hdmi/out/format"))
                .build();
        /*返回的例子:
        {
            "format":"FMT_AUTO"
        }*/
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "getHdmiOutTimmingFormat:response=" + response);
            if (response.code() == 200) {
                String body = response.body().string();
                Log.d(TAG, "getHdmiOutTimmingFormat body:" + body);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(body);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return body;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 切换当前输出EDID分辨率
     *
     * @param format 见 {@link UdiConstant}
     * @see UdiConstant#HDMI_OUT_FORMAT_3840X2160_60HZ_REAL_COLOR
     */
    public boolean setHdmiOutTimmingFormat(String format) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("format", format);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Request request = new Request.Builder()
                .url(UdiSdk.getFullUrl("/v1/source/hdmi/out/format"))
                .post(RequestBody.create(jsonObject.toString(), mMediaType))
                .build();
        try {
            Response response = mClient.newCall(request).execute();
            Log.d(TAG, "setHdmiOutTimmingFormat:response=" + response);
            if (response.code() == 200) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
