package com.eeo.systemsetting.view;

import android.view.ViewGroup;

public class ViewGroupWrapper {

    private ViewGroup mViewGroupTarget;

    public ViewGroupWrapper(ViewGroup viewGroup){
        this.mViewGroupTarget = viewGroup;
    }

    public void setTrueWidth(int width){
        mViewGroupTarget.getLayoutParams().width = width;
        mViewGroupTarget.requestLayout();//必须调用，否则宽度改变但UI没有刷新
    }

    public int getTrueWidth() {
        return mViewGroupTarget.getLayoutParams().width;
    }

    public void setTrueHeight(int height){
        mViewGroupTarget.getLayoutParams().height = height;
        mViewGroupTarget.requestLayout();
    }

    public int getTrueHeight(){
        return mViewGroupTarget.getLayoutParams().height;
    }

}
