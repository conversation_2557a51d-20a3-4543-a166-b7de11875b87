package com.eeo.systemsetting.view;

import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentManager;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.fragment.AboutFragment;
import com.eeo.systemsetting.fragment.ExtraFragment;
import com.eeo.systemsetting.fragment.LocaleFragment;
import com.eeo.systemsetting.fragment.NetWorkFragment;
import com.eeo.systemsetting.fragment.UpdateFragment;
import com.eeo.systemsetting.fragment.WiFiFragment;
import com.eeo.systemsetting.utils.CommonUtils;
import com.lihang.ShadowLayout;

import java.util.ArrayList;
import java.util.List;

public class SettingFrameLayout extends FrameLayout implements View.OnClickListener {
    public static final String TAG = "SettingFrameLayout===";
    Context context = null;
    Activity activity = null;
    View rootView = null;
    List<ShadowLayout> layoutList = null;
    List<Fragment> fragmentList = null;
    ShadowLayout slNetwork;
    ShadowLayout slWifi;
    ShadowLayout slLocale;
    ShadowLayout slExtra;
    ShadowLayout slAbout;
    ShadowLayout slCheckUpdate;
    FrameLayout flSetting;
    ImageView ivBack;

    TextView titleTv;
    TextView networkTv;
    TextView wifiTv;
    TextView localeTv;
    TextView extraTv;
    TextView aboutTv;
    TextView updateTv;

    private final int NETWORK_INDEX = 0;
    private final int WIFI_INDEX = 1;
    private final int LOCALE_INDEX = 2;
    private final int EXTRA_INDEX = 3;
    private final int ABOUT_INDEX = 4;
    private final int UPDATE_INDEX = 5;

    private FragmentManager fragmentManager;
    private LocaleFragment localeFragment;
    private WiFiFragment wiFiFragment;

    public SettingFrameLayout(Activity activity, Context context) {
        super(context);

        this.context = context;
        this.activity = activity;
        if (rootView == null) {
            rootView = LayoutInflater.from(context).inflate(R.layout.activity_setting, null);
        }
        addView(rootView);
        findView();
    }

    private void findView() {
        ivBack = rootView.findViewById(R.id.iv_back);
        ivBack.setOnClickListener(this);
        flSetting = rootView.findViewById(R.id.fl_fragment);
        slNetwork = rootView.findViewById(R.id.sl_network);
        slNetwork.setOnClickListener(this);
        slWifi = rootView.findViewById(R.id.sl_wifi);
        slWifi.setOnClickListener(this);
        slLocale = rootView.findViewById(R.id.sl_locale);
        slLocale.setOnClickListener(this);
        slExtra = rootView.findViewById(R.id.sl_extra);
        slExtra.setOnClickListener(this);
        slAbout = rootView.findViewById(R.id.sl_about);
        slAbout.setOnClickListener(this);
        slCheckUpdate = rootView.findViewById(R.id.sl_check_update);
        slCheckUpdate.setOnClickListener(this);

        titleTv = rootView.findViewById(R.id.txt_title);
        networkTv = rootView.findViewById(R.id.txt_network);
        wifiTv = rootView.findViewById(R.id.txt_wifi);
        localeTv = rootView.findViewById(R.id.txt_locale);
        extraTv = rootView.findViewById(R.id.txt_extra);
        aboutTv = rootView.findViewById(R.id.txt_about);
        updateTv = rootView.findViewById(R.id.txt_update);

        if (layoutList == null) {
            layoutList = new ArrayList<>();
        }

        layoutList.add(slNetwork);
        layoutList.add(slWifi);
        layoutList.add(slLocale);
        layoutList.add(slExtra);
        layoutList.add(slAbout);
        layoutList.add(slCheckUpdate);

        if (fragmentList == null) {
            fragmentList = new ArrayList<>();
        }

        fragmentList.add(new NetWorkFragment());
        wiFiFragment = new WiFiFragment();
        fragmentList.add(wiFiFragment);
        localeFragment = new LocaleFragment();
        fragmentList.add(localeFragment);
        fragmentList.add(new ExtraFragment());
        fragmentList.add(new AboutFragment());
        fragmentList.add(new UpdateFragment());

        slNetwork.setSelected(true);
        fragmentManager = activity.getFragmentManager();
        fragmentManager.beginTransaction().add(R.id.fl_fragment, fragmentList.get(0)).commit();
    }

    /**
     * 所有的点击事件全部取消select
     */
    private void toAllUnSelect() {
        for (int i = 0; i < layoutList.size(); i++) {
            layoutList.get(i).setSelected(false);
        }
    }

    @Override
    public void onClick(View view) {
        toAllUnSelect();
        switch (view.getId()) {
            //返回
            case R.id.iv_back:
                if (wiFiFragment != null) {
                    wiFiFragment.release();
                }
                CommonUtils.back(activity);
                break;
            //有线网络
            case R.id.sl_network:
                slNetwork.setSelected(true);
                selectFragment(NETWORK_INDEX);
                break;

            case R.id.sl_wifi:
                slWifi.setSelected(true);
                selectFragment(WIFI_INDEX);
                break;

            case R.id.sl_locale:
                slLocale.setSelected(true);
                selectFragment(LOCALE_INDEX);
                break;

            case R.id.sl_extra:
                slExtra.setSelected(true);
                selectFragment(EXTRA_INDEX);
                break;

            case R.id.sl_about:
                slAbout.setSelected(true);
                selectFragment(ABOUT_INDEX);
                break;

            case R.id.sl_check_update:
                slCheckUpdate.setSelected(true);
                selectFragment(UPDATE_INDEX);
                break;

            default:
                break;
        }
    }

    private void selectFragment(int index) {
        if (fragmentList != null) {
            fragmentManager.beginTransaction().replace(R.id.fl_fragment, fragmentList.get(index)).commit();
        }
    }

    /**
     * 切换语言刷新view后，重新选中LocaleFragment
     */
    public void selectLocaleFragment() {
        toAllUnSelect();
        slLocale.setSelected(true);
        selectFragment(LOCALE_INDEX);
    }
}
