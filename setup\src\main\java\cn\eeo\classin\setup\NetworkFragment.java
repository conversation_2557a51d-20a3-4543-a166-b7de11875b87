package cn.eeo.classin.setup;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.text.method.TransformationMethod;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.eeo.udisdk.UdiConstant;
import com.eeo.udisdk.network.EthernetConfig;
import com.elvishew.xlog.XLog;

import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import butterknife.OnFocusChange;
import butterknife.OnTextChanged;
import cn.eeo.classin.setup.adapter.WiFiConnectedAdapter;
import cn.eeo.classin.setup.adapter.WiFiScanListAdapter;
import cn.eeo.classin.setup.base.BaseFragment;
import cn.eeo.classin.setup.dialog.CommonDialog;
import cn.eeo.classin.setup.popupwindow.AutoManualPopupWindow;
import cn.eeo.classin.setup.utils.CommonUtils;
import cn.eeo.classin.setup.wifi.AccessPoint;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class NetworkFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener, View.OnScrollChangeListener {
    public static final String TAG = "NetworkFragment===";
    @BindView(R.id.scrollview)
    NestedScrollView scrollview;
    /**
     * 无线网络
     */
    @BindView(R.id.sw_wifi_settings)
    Switch swWifi;
    @BindView(R.id.txt_wifi_settings)
    TextView txtWifi;
    @BindView(R.id.rl_sub_wifi)
    RelativeLayout rlWifi;
    @BindView(R.id.rv_connected)
    RecyclerView rvWiFiConnected;
    @BindView(R.id.rv_wifi_list)
    RecyclerView rvWifiList;

    /**
     * 有线网络
     */
    @BindView(R.id.sw_ethernet_settings)
    Switch swNetwork;
    @BindView(R.id.rl_sub_ethernet)
    RelativeLayout rlEthernet;
    @BindView(R.id.txt_mac_address)
    TextView txtMacAddress;
    @BindView(R.id.txt_ip_setting)
    TextView txtIpSetting;
    @BindView(R.id.img_arrow)
    ImageView imgArrow;
    @BindView(R.id.ll_mac_address)
    LinearLayout llMacAddress;
    @BindView(R.id.ll_ip_mode)
    LinearLayout llIpMode;
    @BindView(R.id.ll_ip_setting)
    LinearLayout llIpSetting;
    @BindView(R.id.txt_ip_address)
    TextView txtIpAddress;
    @BindView(R.id.ll_ip)
    LinearLayout llIp;
    @BindView(R.id.img_ip)
    ImageView imgIp;
    @BindView(R.id.edt_ip_address)
    EditText edtIpAddress;
    @BindView(R.id.ll_mask)
    LinearLayout llMask;
    @BindView(R.id.img_mask)
    ImageView imgMask;
    @BindView(R.id.edt_mask_address)
    EditText edtMaskAddress;
    @BindView(R.id.ll_gateway)
    LinearLayout llGateway;
    @BindView(R.id.img_gateway)
    ImageView imgGateway;
    @BindView(R.id.edt_gateway)
    EditText edtGateway;
    @BindView(R.id.ll_dns)
    LinearLayout llDns;
    @BindView(R.id.img_dns)
    ImageView imgDns;
    @BindView(R.id.edt_dns)
    EditText edtDns;

    @BindView(R.id.btn_skip)
    Button btnSkip;
    @BindView(R.id.btn_confirm)
    Button btnConfirm;

    CommonDialog inputPwdDialog;
    EditText edtPassword;
    TextView txtPwdError;
    ImageView back_iv;
    private String lastPassword;
    private Network currentNetwork;
    private NetworkInfo lastNetworkInfo;
    private WifiInfo lastWifiInfo;
    private Disposable scanWifi;
    private WifiConfiguration lastWifiConfiguration;

    private List<AccessPoint> lastAccessPoints = new CopyOnWriteArrayList<>();
    private List<WifiInfo> wifiConnectedList = new CopyOnWriteArrayList<>();
    private WiFiScanListAdapter scanListAdapter;
    private WiFiConnectedAdapter connectedAdapter;
    private int lastPortalNetworkId = AccessPoint.INVALID_NETWORK_ID;

    private boolean isWifiRegistered = false;
    private boolean isEthernetRegistered = false;
    private final static int REQUEST_CODE = 1101;

    //自动还是手动标记
    private int selectIndex = 0;
    private final int AUTO = 0;
    private final int MANUAL = 1;
    private final int CLOSE_NETWORK = 0;
    private final int OPEN_NETWORK_AUTO = 1;
    private final int OPEN_NETWORK_MANUAL = 2;
    private final String ETHERNET_MANUAL = "MANUAL";
    private final String ETHERNET_DHCP = "DHCP";
    //当手动时候点击确定之后，给个标记，让按钮置灰不让点击
    private boolean isSetting = true;
    private WifiManager wifiManager;
    private NetWorkChangeBroadcast netWorkChangeBroadcast;

    private final int IP_ADDRESS = 0;
    private final int SUBNET_MASK = 1;
    private final int GATEWAY = 2;
    private final int DNS = 3;

    private boolean isScrollDown = false;
    private IntentFilter filter;

    private NavController navController;
    /**
     * 是否读取过网络模式
     */
    private boolean mGetEthernetMode = false;

    /**
     * 有线网络、无线网络开关状态
     * 0：都关闭
     * 1：无线网络
     * 2：有线网络
     */
    private final int SWITCH_STATE_CLOSE = 0;
    private final int SWITCH_STATE_WIFI = 1;
    private final int SWITCH_STATE_ETHERNET = 2;
    private int mSwitchState = SWITCH_STATE_CLOSE;
    private boolean mIsWifiSwitchOpen = false;
    private boolean mIsEthernetSwitchOpen = false;
    private boolean mShouldShowAutoItem = false; //刚切换自动时短暂的无网络不隐藏，避免闪烁

    /**
     * 搜索wifi...动画
     */
    private String mDotAnimStr = "";
    private Runnable mDotAnimRunnable = new Runnable() {
        @Override
        public void run() {
            txtWifi.setText(getContext().getString(R.string.wifi_settings) + " " + mDotAnimStr);
            mHandler.postDelayed(this, 300);
            mDotAnimStr = mDotAnimStr.length() == 0 ? "." : mDotAnimStr.length() == 1 ? ".." : mDotAnimStr.length() == 2 ? "..." : "";
        }
    };

    private static final int MSG_HIDE_AUTO_ITEM = 0x001;
    private static final int MSG_HANDLE_ETHERNET_OPEN = 0x002;
    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_HIDE_AUTO_ITEM:
                    hideAutoItem();
                    break;
                case MSG_HANDLE_ETHERNET_OPEN:
                    if (mIsEthernetSwitchOpen) {
                        handleEthernetSwitchOpen();
                    }
                    break;
            }
        }
    };

    @Override
    public int getLayout() {
        return R.layout.fragment_network;
    }

    @Override
    public void initDate() {
        wifiManager = (WifiManager) getActivity().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        scrollview.setOnScrollChangeListener(this);
        //设置监听
        swNetwork.setOnCheckedChangeListener(this);
        swWifi.setOnCheckedChangeListener(this);
        initWifi();
        swWifi.setChecked(true); //默认打开无线
    }

    private void initWifi() {
        scanListAdapter = new WiFiScanListAdapter(this);
        scanListAdapter.setItemClickListener(this::showDialog);
        rvWifiList.setLayoutManager(new LinearLayoutManager(getActivity()));
        rvWifiList.setAdapter(scanListAdapter);
        connectedAdapter = new WiFiConnectedAdapter(getActivity());
        rvWiFiConnected.setLayoutManager(new LinearLayoutManager(getActivity()));
        rvWiFiConnected.setAdapter(connectedAdapter);
    }

    private void setNetWorkState() {
        txtMacAddress.setText(EeoApplication.udi.getMacAddress());
        //设置连接状态
        boolean isEthernetEnabled = EeoApplication.udi.isEthernetEnabled();

        if (isEthernetEnabled) {
            //已连接
            txtIpAddress.setText(EeoApplication.udi.getIpAddress());
        } else {
            //未连接
            txtIpAddress.setText(getResources().getText(R.string.disconnect));
            //未连接状态下，隐藏ip设置与ip地址还有剩下的edittext
            if (!mShouldShowAutoItem) {
                llIpMode.setVisibility(View.GONE);
            }
            llMask.setVisibility(View.GONE);
            llGateway.setVisibility(View.GONE);
            llDns.setVisibility(View.GONE);
        }
        //获取整机有线网络连接模式
        String ethernetMode = EeoApplication.udi.getEthernetMode();
        XLog.i("initDate: ethernetMode : " + ethernetMode);
        mGetEthernetMode = true;
        if (ethernetMode.equals(ETHERNET_DHCP)) {
            selectIndex = AUTO;
            //自动模式下将edittext全部隐藏，由于没网络情况下，连接状态使得不显示，所以不考虑没有网络这种情况

            //此情况下，隐藏edit输入状态，显示text
            edtIpAddress.setVisibility(View.GONE);
            txtIpAddress.setVisibility(View.VISIBLE);
            //显示ip
            txtIpAddress.setText(EeoApplication.udi.getIpAddress());
            txtIpSetting.setText(getContext().getString(R.string.auto));
            //隐藏其他输入框
            llMask.setVisibility(View.GONE);
            llGateway.setVisibility(View.GONE);
            llDns.setVisibility(View.GONE);
        } else if (ethernetMode.equals(ETHERNET_MANUAL)) {
            selectIndex = MANUAL;
            //手动
            XLog.i("initDate: OPEN_NETWORK_MANUAL");
            llIp.setVisibility(View.VISIBLE);
            llMask.setVisibility(View.VISIBLE);
            llGateway.setVisibility(View.VISIBLE);
            llDns.setVisibility(View.VISIBLE);

            //隐藏txtIp
            llIpSetting.setVisibility(View.GONE);
            txtIpAddress.setVisibility(View.GONE);
            txtIpSetting.setText(getContext().getString(R.string.manual));

            EthernetConfig ethernetConfig = EeoApplication.udi.getEthernetConfig();
            edtIpAddress.setText(ethernetConfig.getIp());
            edtMaskAddress.setText(ethernetConfig.getMask());
            edtGateway.setText(ethernetConfig.getGateway());
            edtDns.setText(ethernetConfig.getDns1());
        }
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mIsWifiSwitchOpen) {
            handleWifiSwitchOpen();
        } else if (mIsEthernetSwitchOpen) {
            handleEthernetSwitchOpen();
        }
        isSetting = true;
    }


    private void handleWifiSwitchOpen() {
        registerWifiReceiver();
        if (EeoApplication.udi.isEthernetEnabled()) {
            EeoApplication.udi.enableEthernet(false);
        }
        if (!wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(true);
        }
        lastNetworkInfo = getActiveNetworkInfo();
        lastWifiInfo = wifiManager.getConnectionInfo();
        setWifiState();
        scanWifi();
    }

    private void scanWifi() {
        if (scanWifi == null) {
            scanWifi = Observable.interval(0, 10, TimeUnit.SECONDS)
                    .observeOn(Schedulers.io())
                    .doOnNext(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            wifiManager.startScan();
                        }
                    })
                    .subscribe();
        } else {
            wifiManager.startScan();
        }
    }

    private void handleWifiSwitchClose() {
        unregisterWifiReceiver();
        if (scanWifi != null && !scanWifi.isDisposed()) {
            scanWifi.dispose();
            scanWifi = null;
        }
        if (wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(false);
        }
        rlWifi.setVisibility(View.GONE);
    }

    private void handleEthernetSwitchOpen() {
        registerEthernetReceiver();
        if (wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(false);
        }
        if (!EeoApplication.udi.isEthernetEnabled()) {
            llIpSetting.setVisibility(View.GONE);
            llIpMode.setVisibility(View.GONE);
            EeoApplication.udi.enableEthernet(true);
        }
        rlEthernet.setVisibility(View.VISIBLE);
        setNetWorkState();
    }

    private void handleEthernetSwitchClose() {
        unregisterEthernetReceiver();
        if (EeoApplication.udi.isEthernetEnabled()) {
            EeoApplication.udi.enableEthernet(false);
        }
        rlEthernet.setVisibility(View.GONE);
    }

    private void enableConfirmButton(boolean enable) {
        btnConfirm.setEnabled(enable);
        btnSkip.setBackground(getContext().getDrawable(enable ? R.drawable.skip_button_background : R.drawable.custom_button_background));
    }

    @Override
    public void onStop() {
        super.onStop();
        unregisterWifiReceiver();
        unregisterEthernetReceiver();
        if (scanWifi != null && !scanWifi.isDisposed()) {
            scanWifi.dispose();
            scanWifi = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        navController = Navigation.findNavController(view);
    }

    @OnClick({R.id.txt_ip_setting, R.id.img_arrow, R.id.btn_confirm, R.id.btn_skip, R.id.back_ic})
    public void onClick(View view) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.txt_ip_setting:
            case R.id.img_arrow:
                llIpSetting.requestFocus();

                //隐藏键盘
                InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);

                AutoManualPopupWindow autoManualPopupWindow = new AutoManualPopupWindow(getActivity(), imgArrow, selectIndex);
                autoManualPopupWindow.setAutoManualClickCallback(new AutoManualPopupWindow.OnClickCallback() {
                    @Override
                    public void onClickCallback(int index) {
                        if (index == AUTO) {
                            if (selectIndex != AUTO) {
                                isSetting = true;
                                selectIndex = AUTO;
                                mShouldShowAutoItem = true;
                                txtIpSetting.setText(getContext().getString(R.string.auto));
                                EeoApplication.udi.setEthernetMode(UdiConstant.ETHERNET_MODE_DHCP);
                                setItemVisibleState(OPEN_NETWORK_AUTO);
                            }
                        } else {
                            if (selectIndex != MANUAL) {
                                selectIndex = MANUAL;
                                txtIpSetting.setText(getContext().getString(R.string.manual));
                                llIpSetting.setVisibility(View.GONE);
                                setItemVisibleState(OPEN_NETWORK_MANUAL);
                            }
                        }
                    }
                });
                break;

            case R.id.btn_confirm:
                if (mIsEthernetSwitchOpen && selectIndex == MANUAL) {
                    EthernetConfig ethernetConfig = new EthernetConfig(edtIpAddress.getText().toString(), edtGateway.getText().toString(), edtMaskAddress.getText().toString(), edtDns.getText().toString(), "0.0.0.0");
                    boolean isSuccess = EeoApplication.udi.setEthernetConfig(ethernetConfig);
                    if (isSuccess) {
                        //隐藏键盘
                        InputMethodManager imm1 = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                        imm1.hideSoftInputFromWindow(view.getWindowToken(), 0);
                        isSetting = false;
                        enableConfirmButton(false);
                        CommonUtils.showSuccessToast(getActivity());
                        NavHostFragment.findNavController(NetworkFragment.this)
                                .navigate(R.id.action_NetworkFragment_to_BasicSettingFragment);
                    } else {
                        CommonUtils.showFailToast(getActivity());
                    }
                } else {
                    NavHostFragment.findNavController(NetworkFragment.this)
                            .navigate(R.id.action_NetworkFragment_to_BasicSettingFragment);
                }
                break;
            case R.id.btn_skip:
                NavHostFragment.findNavController(NetworkFragment.this)
                        .navigate(R.id.action_NetworkFragment_to_BasicSettingFragment);
                break;
            case R.id.back_ic:
                /*if (navController!=null){action_NetworkFragment_to_BWiFiFragment
                    navController.popBackStack();
                }else {
                    XLog.d("navController is null");
                }*/
                NavHostFragment.findNavController(NetworkFragment.this)
                        .navigate(R.id.action_NetworkFragment_to_SecondFragment);
                break;
            default:
                break;
        }
    }

    /**
     * 设置各个item显示模块
     *
     * @param state
     */
    private void setItemVisibleState(int state) {
        switch (state) {
            //关闭有线网络开关，全部隐藏
            case CLOSE_NETWORK:
                XLog.i("setVisibleState: close1");
                setInVisibleAllItem("close network");
                break;

            //打开有线网络,并且选择自动
            case OPEN_NETWORK_AUTO:
                selectIndex = 0;
                mHandler.removeMessages(MSG_HIDE_AUTO_ITEM);
                setInVisibleAllItem("open network auto");

                llIpMode.setVisibility(View.VISIBLE);
                llMacAddress.setVisibility(View.VISIBLE);

                llIpSetting.setVisibility(View.VISIBLE);
                txtIpAddress.setVisibility(View.VISIBLE);
                String ipAddress = EeoApplication.udi.getIpAddress();
                txtIpAddress.setText(ipAddress == null ? getResources().getText(R.string.not_network) : ipAddress);
                txtIpSetting.setText(getContext().getString(R.string.auto));
                break;

            //打开有线网络，并且选择手动
            case OPEN_NETWORK_MANUAL:
                selectIndex = 1;
                llIp.setVisibility(View.VISIBLE);
                llIpMode.setVisibility(View.VISIBLE);
                llMacAddress.setVisibility(View.VISIBLE);
                llIpSetting.setVisibility(View.GONE);
                edtIpAddress.setVisibility(View.VISIBLE);
                txtIpAddress.setVisibility(View.GONE);
                llMask.setVisibility(View.VISIBLE);
                llGateway.setVisibility(View.VISIBLE);
                llDns.setVisibility(View.VISIBLE);

                EthernetConfig ethernetConfig = EeoApplication.udi.getEthernetConfig();
                edtIpAddress.setText(ethernetConfig.getIp());
                edtMaskAddress.setText(ethernetConfig.getMask());
                edtGateway.setText(ethernetConfig.getGateway());
                edtDns.setText(ethernetConfig.getDns1());
                txtIpSetting.setText(getContext().getString(R.string.manual));
                break;

            default:
                break;
        }
    }

    /**
     * 隐藏所有item
     */
    private void setInVisibleAllItem(String msg) {
        XLog.i(msg + " , setInVisibleAllItem: initDate");
        llMacAddress.setVisibility(View.GONE);
        llIpSetting.setVisibility(View.GONE);

        llIpMode.setVisibility(View.GONE);
        txtIpAddress.setVisibility(View.GONE);
        edtIpAddress.setVisibility(View.GONE);

        llMask.setVisibility(View.GONE);
        llGateway.setVisibility(View.GONE);
        llIp.setVisibility(View.GONE);
        llMask.setVisibility(View.GONE);
        llGateway.setVisibility(View.GONE);
        llDns.setVisibility(View.GONE);
    }

    /**
     * 隐藏所有Manual相关item
     */
    private void hideAutoItem() {
        llIpSetting.setVisibility(View.GONE);
        llIpMode.setVisibility(View.GONE);
        txtIpAddress.setVisibility(View.GONE);
    }

    /**
     * 隐藏所有Auto相关item
     */
    private void hideManualItem() {
        edtIpAddress.setVisibility(View.GONE);
        llMask.setVisibility(View.GONE);
        llGateway.setVisibility(View.GONE);
        llIp.setVisibility(View.GONE);
        llMask.setVisibility(View.GONE);
        llGateway.setVisibility(View.GONE);
        llDns.setVisibility(View.GONE);
    }


    /**
     * 判断是否滚动到底部
     *
     * @return
     */
    private boolean isScrollDown() {

        int height = scrollview.getHeight() + scrollview.getScrollY();
        int childHeight = scrollview.getChildAt(0).getHeight();
        if (height == childHeight) {
            return true;
        } else {
            return false;
        }
    }

    @OnFocusChange(value = {R.id.edt_ip_address, R.id.edt_mask_address, R.id.edt_gateway, R.id.edt_dns})
    public void expandAppBarOnFocusChangeListener(View view, boolean hasFocus) {
        switch (view.getId()) {
            case R.id.edt_ip_address:

                if (!hasFocus) {
                    boolean isIpAddress = checkoutEditIpByIndex(IP_ADDRESS);
                    XLog.i("expandAppBarOnFocusChangeListener: edt_ip_address : " + isIpAddress);
                    if (isIpAddress) {
                        imgIp.setVisibility(View.INVISIBLE);
                    } else {
                        imgIp.setVisibility(View.VISIBLE);
                    }
                } else {
                    XLog.i("expandAppBarOnFocusChangeListener: edt_ip_address get focus : " + isScrollDown());
                    isSetting = true;
                    if (!isScrollDown) {
                        scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                        isScrollDown = true;
                        edtIpAddress.setFocusable(true);
                        edtIpAddress.setFocusableInTouchMode(true);
                        edtIpAddress.requestFocus();
                    }


                }
                break;

            case R.id.edt_mask_address:
                if (!hasFocus) {

                    boolean isSubNetMask = checkoutEditIpByIndex(SUBNET_MASK);
                    XLog.i("expandAppBarOnFocusChangeListener: edt_mask_address : " + isSubNetMask);
                    if (isSubNetMask) {
                        imgMask.setVisibility(View.INVISIBLE);
                    } else {
                        imgMask.setVisibility(View.VISIBLE);
                    }

                } else {
                    XLog.i("expandAppBarOnFocusChangeListener: edt_mask_address get focus : " + isScrollDown());
                    isSetting = true;
                    if (!isScrollDown) {
                        scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                        isScrollDown = true;
                        edtMaskAddress.setFocusable(true);
                        edtMaskAddress.setFocusableInTouchMode(true);
                        edtMaskAddress.requestFocus();
                    }
                }

                break;

            case R.id.edt_gateway:
                if (!hasFocus) {
                    boolean isGateWay = checkoutEditIpByIndex(GATEWAY);
                    XLog.i("expandAppBarOnFocusChangeListener: edt_gateway : " + isGateWay);
                    if (isGateWay) {
                        imgGateway.setVisibility(View.INVISIBLE);
                    } else {
                        imgGateway.setVisibility(View.VISIBLE);
                    }
                } else {
                    XLog.i("expandAppBarOnFocusChangeListener: edt_gateway get focus : " + isScrollDown());
                    isSetting = true;
                    if (!isScrollDown) {
                        scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                        isScrollDown = true;
                        edtGateway.setFocusable(true);
                        edtGateway.setFocusableInTouchMode(true);
                        edtGateway.requestFocus();
                    }
                }

                break;

            case R.id.edt_dns:
                if (!hasFocus) {

                    boolean isDns1 = checkoutEditIpByIndex(DNS);
                    XLog.i("expandAppBarOnFocusChangeListener: edt_dns1 : " + isDns1);
                    if (isDns1) {
                        imgDns.setVisibility(View.INVISIBLE);
                    } else {
                        imgDns.setVisibility(View.VISIBLE);
                    }
                } else {
                    XLog.i("expandAppBarOnFocusChangeListener: edt_dns1 get focus : " + isScrollDown());
                    isSetting = true;
                    if (!isScrollDown) {
                        scrollview.fullScroll(ScrollView.FOCUS_DOWN);
                        isScrollDown = true;
                        edtDns.setFocusable(true);
                        edtDns.setFocusableInTouchMode(true);
                        edtDns.requestFocus();
                    }
                }

                break;


            default:
                break;
        }
    }

    @OnTextChanged(value = {R.id.edt_ip_address, R.id.edt_mask_address, R.id.edt_gateway, R.id.edt_dns})
    public void onEditTextChange() {
        if (!TextUtils.isEmpty(edtIpAddress.getText().toString()) && !TextUtils.isEmpty(edtMaskAddress.getText().toString()) && !TextUtils.isEmpty(edtGateway.getText().toString()) && !TextUtils.isEmpty(edtDns.getText().toString())) {
            if (checkoutEditIpByIndex(IP_ADDRESS) && checkoutEditIpByIndex(SUBNET_MASK) && checkoutEditIpByIndex(GATEWAY) && checkoutEditIpByIndex(DNS) && isSetting) {
                imgIp.setVisibility(View.INVISIBLE);
                imgMask.setVisibility(View.INVISIBLE);
                imgGateway.setVisibility(View.INVISIBLE);
                imgDns.setVisibility(View.INVISIBLE);
                enableConfirmButton(true);
            } else {
                if (!checkoutEditIpByIndex(IP_ADDRESS)) {
                    imgIp.setVisibility(View.VISIBLE);
                }

                if (!checkoutEditIpByIndex(SUBNET_MASK)) {
                    imgMask.setVisibility(View.VISIBLE);
                }

                if (!checkoutEditIpByIndex(GATEWAY)) {
                    imgGateway.setVisibility(View.VISIBLE);
                }

                if (!checkoutEditIpByIndex(DNS)) {
                    imgDns.setVisibility(View.VISIBLE);
                }
                enableConfirmButton(false);
            }

        } else {
            enableConfirmButton(false);
        }
    }

    private boolean checkoutEditIpByIndex(int index) {
        if (index == IP_ADDRESS) {
            if (TextUtils.isEmpty(edtIpAddress.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtIpAddress.getText().toString());
            }
        }

        if (index == SUBNET_MASK) {

            if (TextUtils.isEmpty(edtMaskAddress.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtMaskAddress.getText().toString());
            }

        }

        if (index == GATEWAY) {

            if (TextUtils.isEmpty(edtGateway.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtGateway.getText().toString());
            }

        }

        if (index == DNS) {

            if (TextUtils.isEmpty(edtDns.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtDns.getText().toString());
            }

        }

        return true;
    }

    private long mWifiSettingsLastOpenTime;

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean isOpen) {
        switch (compoundButton.getId()) {
            case R.id.sw_wifi_settings:
                if (isOpen) {
                    mWifiSettingsLastOpenTime = System.currentTimeMillis();
                    swNetwork.setChecked(false);
                    if (!mHandler.hasCallbacks(mDotAnimRunnable)) {
                        mHandler.post(mDotAnimRunnable);
                    }
                    handleWifiSwitchOpen();
                    mIsWifiSwitchOpen = true;
                } else {
                    handleWifiSwitchClose();
                    mIsWifiSwitchOpen = false;
                    if (!mIsEthernetSwitchOpen) {
                        enableConfirmButton(false);
                    }
                    if (mHandler.hasCallbacks(mDotAnimRunnable)) {
                        mHandler.removeCallbacks(mDotAnimRunnable);
                        txtWifi.setText(getContext().getString(R.string.wifi_settings));
                        mDotAnimStr = "";
                    }
                }
                break;
            case R.id.sw_ethernet_settings:
                if (isOpen) {
                    long current = System.currentTimeMillis();
                    if (selectIndex == AUTO) {
                        mShouldShowAutoItem = true;
                    }
                    swWifi.setChecked(false);
                    if (current - mWifiSettingsLastOpenTime < 2000) {
                        //避免刚打开wifi后立刻切到有线，打开有线网络异常问题
                        mHandler.removeMessages(MSG_HANDLE_ETHERNET_OPEN);
                        mHandler.sendEmptyMessageDelayed(MSG_HANDLE_ETHERNET_OPEN, 1000);
                    } else {
                        handleEthernetSwitchOpen();
                    }
                    mIsEthernetSwitchOpen = true;
                } else {
                    mHandler.removeMessages(MSG_HANDLE_ETHERNET_OPEN);
                    handleEthernetSwitchClose();
                    mIsEthernetSwitchOpen = false;
                    if (!mIsWifiSwitchOpen) {
                        enableConfirmButton(false);
                    }
                }
                break;
        }
    }

    @Override
    public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
        View contentView = scrollview.getChildAt(0);
        if (contentView != null && contentView.getMeasuredHeight() == (scrollview.getScrollY() + scrollview.getHeight())) {
            //底部
            isScrollDown = true;
        } else {
            isScrollDown = false;
        }
        XLog.i("onScrollChange: isScrollDown : " + isScrollDown);
    }

    class NetWorkChangeBroadcast extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            XLog.i("onReceive: " + CommonUtils.isNetSystemUsable(getContext()));
            if (CommonUtils.isNetSystemUsable(getContext()) && EeoApplication.udi.isEthernetEnabled()) {
                XLog.i("swNetwork.isChecked(): " + swNetwork.isChecked());
                if (mIsEthernetSwitchOpen) {
                    enableConfirmButton(true);
                    XLog.i("!mGetEthernetMode: " + !mGetEthernetMode);
                    if (!mGetEthernetMode) {
                        //获取整机有线网络连接模式
                        String ethernetMode = EeoApplication.udi.getEthernetMode();
                        XLog.i("NetWorkChange: ethernetMode : " + ethernetMode);
                        mGetEthernetMode = true;
                        if (ethernetMode.equals(ETHERNET_DHCP)) {
                            selectIndex = AUTO;
                        } else if (ethernetMode.equals(ETHERNET_MANUAL)) {
                            selectIndex = MANUAL;
                        }
                    }
                    if (selectIndex == AUTO) {
                        XLog.i("initDate: ----auto");
                        setItemVisibleState(OPEN_NETWORK_AUTO);
                    } else {
                        XLog.i("onReceive: manual");
                        setItemVisibleState(OPEN_NETWORK_MANUAL);
                    }
                }
            } else {
                XLog.i("onReceive: notNetwork");
                //无网络
                if (mIsEthernetSwitchOpen) {
                    enableConfirmButton(false);
                    hideManualItem();
//                    llMacAddress.setVisibility(View.VISIBLE);
                    if (selectIndex == AUTO) {
                        if (mShouldShowAutoItem) {
                            //切换AUTO后的短暂无网络、避免闪烁
                            mShouldShowAutoItem = false;
                            mHandler.removeMessages(MSG_HIDE_AUTO_ITEM);
                            mHandler.sendEmptyMessageDelayed(MSG_HIDE_AUTO_ITEM, 3000);
                        } else {
                            hideAutoItem();
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置已经连接上的wifi显示
     */
    private void setConnectedWifi() {
        if (lastWifiInfo != null) {
            XLog.i("initDate: wifiInfo : " + lastWifiInfo.getSSID());
            rvWiFiConnected.setVisibility(View.VISIBLE);
            if (!lastWifiInfo.getSSID().equals(WifiManager.UNKNOWN_SSID)) {
                boolean contains = false;
                for (WifiInfo wifiInfo : wifiConnectedList) {
                    if (wifiInfo.getSSID().equals(lastWifiInfo.getSSID())
                            && wifiInfo.getBSSID().equals(lastWifiInfo.getBSSID())) {
                        contains = true;
                        break;
                    }
                }
                if (!contains) {
                    wifiConnectedList.add(lastWifiInfo);
                }
            }

            connectedAdapter.setConnectedWifi(wifiConnectedList, lastNetworkInfo);
            //txtOtherNetwork = binding.txtOtherNetwork

        } else {
            rvWiFiConnected.setVisibility(View.GONE);
        }
    }

    private void setWifiState() {
        XLog.d("wifiManager.isWifiEnabled():" + wifiManager.isWifiEnabled());
        if (wifiManager.isWifiEnabled()) {
//            EeoApplication.udi.enableEthernet(false);
        } else {
            wifiConnectedList.clear();
            lastAccessPoints.clear();
            connectedAdapter.notifyDataSetChanged();
            scanListAdapter.notifyDataSetChanged();
            return;
        }

        setConnectedWifi();
    }

    private void registerEthernetReceiver() {
        if (!isEthernetRegistered) {
            if (netWorkChangeBroadcast == null) {
                netWorkChangeBroadcast = new NetWorkChangeBroadcast();
            }
            if (filter == null) {
                filter = new IntentFilter();
                filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            }
            getContext().registerReceiver(netWorkChangeBroadcast, filter);
            isEthernetRegistered = true;
        }
    }

    private void unregisterEthernetReceiver() {
        if (isEthernetRegistered) {
            getContext().unregisterReceiver(netWorkChangeBroadcast);
            isEthernetRegistered = false;
        }
    }


    private void registerWifiReceiver() {
        if (!isWifiRegistered) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
            filter.addAction(Constant.CONFIGURED_NETWORKS_CHANGE);
            filter.addAction(Constant.LINK_CONFIGURATION_CHANGED);
            filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
            filter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);
            getActivity().registerReceiver(mWifiBroadcastReceiver, filter);
            ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null) {
                NetworkRequest.Builder request = new NetworkRequest.Builder()
                        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI);
                cm.registerNetworkCallback(request.build(), callback);
            }
            isWifiRegistered = true;
        }
    }

    private void unregisterWifiReceiver() {
        if (isWifiRegistered) {
            getActivity().unregisterReceiver(mWifiBroadcastReceiver);
            ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null) {
                cm.unregisterNetworkCallback(callback);
            }
            isWifiRegistered = false;
        }
    }

    private BroadcastReceiver mWifiBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                    case WifiManager.SCAN_RESULTS_AVAILABLE_ACTION:
                    case Constant.CONFIGURED_NETWORKS_CHANGE:
                    case Constant.LINK_CONFIGURATION_CHANGED:
                        XLog.i("onReceive: CONFIGURED_NETWORKS_CHANGE");
                        //定时扫描变化时，更新wifi列表
                        updateAccessPoints();

                        break;
                    case WifiManager.NETWORK_STATE_CHANGED_ACTION:
                        //WIFI 的连接状态发生变化时的广播
                        XLog.i("onReceive: NETWORK_STATE_CHANGED_ACTION");
                        //定时扫描变化时，更新wifi列表
                        updateAccessPoints();
                        NetworkInfo info = intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
                        updateNetworkInfo(info);
                        break;
                    case WifiManager.SUPPLICANT_STATE_CHANGED_ACTION:
                        //WIFI 密码错误
                        XLog.i("onReceive: SUPPLICANT_STATE_CHANGED_ACTION");
                        int error = intent.getIntExtra(WifiManager.EXTRA_SUPPLICANT_ERROR, -1);
                        if (error == WifiManager.ERROR_AUTHENTICATING) {
                            handlePasswordError();
                        }
                        break;
                }
            }
        }
    };

    //监听网络
    private ConnectivityManager.NetworkCallback callback = new ConnectivityManager.NetworkCallback() {

        @Override
        public void onAvailable(Network network) {
            super.onAvailable(network);
            //网络已链接
            setCurrentNetwork(network);
            portalCurrentWifi();
        }

        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
            if (network.equals(getCurrentNetwork())) {
                updateNetworkInfo(null);
            }
        }
    };

    public Network getCurrentNetwork() {
        return currentNetwork;
    }

    public void setCurrentNetwork(Network currentNetwork) {
        this.currentNetwork = currentNetwork;
    }


    /**
     * 扫描时更新list列表
     * 更新AccessPoints
     */
    private void updateAccessPoints() {
        Single.create((SingleOnSubscribe<List<AccessPoint>>) emitter -> {
                    List<AccessPoint> accessPoints = new ArrayList<>();
                    //获取扫描到的wifi
                    List<ScanResult> scanResults = wifiManager.getScanResults();
                    if (lastWifiInfo != null && lastWifiInfo.getNetworkId() != AccessPoint.INVALID_NETWORK_ID) {
                        lastWifiConfiguration = getWifiConfigurationForNetworkId(lastWifiInfo.getNetworkId());
                    }
                    if (scanResults != null) {
                        //扫描wifi列表
                        for (ScanResult scanResult : scanResults) {
                            //过滤掉wifi中ssid为空，AccessPoint相同的wifi
                            if (TextUtils.isEmpty(scanResult.SSID)) {
                                continue;
                            }

                            AccessPoint accessPoint = new AccessPoint(getActivity().getApplicationContext(), scanResult);
                            //过滤掉重复wifi和已经连接上的wifi
                            if (accessPoints.contains(accessPoint) || accessPoint.getQuotedSSID().equals(lastWifiInfo.getSSID())) {
                                continue;
                            }
                            //获取已经配置保存好的wifi
                            List<WifiConfiguration> wifiConfigurations = wifiManager.getConfiguredNetworks();
                            if (wifiConfigurations != null) {
                                for (WifiConfiguration config : wifiConfigurations) {
                                    //如果accessPoint的ssid的名字与config的名字相同，则将config的wifi添加进accessPoint中
                                    if (accessPoint.getQuotedSSID().equals(config.SSID)) {
                                        accessPoint.setWifiConfiguration(config);
                                    }
                                }
                            }
                            if (lastWifiInfo != null && lastNetworkInfo != null) {
                                accessPoint.update(lastWifiConfiguration, lastWifiInfo, lastNetworkInfo);
                            }
                            //将过滤完的accessPoint添加到list中
                            accessPoints.add(accessPoint);
                        }
                    }

                    //升序排列
                    Collections.sort(accessPoints);
                    emitter.onSuccess(accessPoints);
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<AccessPoint>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(List<AccessPoint> accessPoints) {
                        lastAccessPoints = accessPoints;
                        //更新扫描列表
                        scanListAdapter.setAccessPoints(lastAccessPoints);
                        lastWifiInfo = wifiManager.getConnectionInfo();
                        if (lastWifiInfo.getSSID().equals("<unknown ssid>")) {
                            if (!CommonUtils.isWiFiConnected(getActivity().getApplicationContext())) {
                                enableConfirmButton(false);
                            }
                            wifiConnectedList.clear();
                            rvWiFiConnected.setVisibility(View.GONE);
                        } else {
                            if (CommonUtils.isWiFiConnected(getActivity().getApplicationContext())) {
                                enableConfirmButton(true);
                            }
                            rvWiFiConnected.setVisibility(View.VISIBLE);
                            wifiConnectedList.clear();
                            wifiConnectedList.add(lastWifiInfo);

                        }
                        connectedAdapter.setConnectedWifi(wifiConnectedList, lastNetworkInfo);
                        if (mIsWifiSwitchOpen && accessPoints.size() > 0) {
                            rlWifi.setVisibility(View.VISIBLE);
                            if (mHandler.hasCallbacks(mDotAnimRunnable)) {
                                mHandler.removeCallbacks(mDotAnimRunnable);
                                txtWifi.setText(getContext().getString(R.string.wifi_settings));
                                mDotAnimStr = "";
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {

                    }
                });
    }

    public void updateNetworkInfo(NetworkInfo networkInfo) {
        Single.create((SingleOnSubscribe<List<AccessPoint>>) emitter -> {
                    if (networkInfo != null) {
                        lastNetworkInfo = networkInfo;
                    }
                    lastWifiInfo = wifiManager.getConnectionInfo();

                    if (lastWifiInfo.getNetworkId() == AccessPoint.INVALID_NETWORK_ID) {
                        // 表示没有 wifi 连接，lastPortalNetworkId 置为无效
                        lastPortalNetworkId = AccessPoint.INVALID_NETWORK_ID;
                    }
                    if (lastWifiInfo != null && lastWifiInfo.getNetworkId() != AccessPoint.INVALID_NETWORK_ID) {
                        lastWifiConfiguration = getWifiConfigurationForNetworkId(lastWifiInfo.getNetworkId());
                    }
                    boolean reorder = false;
                    for (AccessPoint accessPoint : lastAccessPoints) {
                        if (accessPoint.update(lastWifiConfiguration, lastWifiInfo, lastNetworkInfo)) {
                            reorder = true;
                        }
                    }
                    if (reorder) {
                        Collections.sort(lastAccessPoints);
                    }
                    emitter.onSuccess(lastAccessPoints);
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<AccessPoint>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(List<AccessPoint> accessPoints) {
                        // 更新列表
                        XLog.d("onSuccess");
                        scanListAdapter.setAccessPoints(accessPoints);
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                    }
                });

    }

    public void showDialog(AccessPoint accessPoint) {

        if (accessPoint.isSaved()) {

            //如果不需要输入密码，则直接调用连接接口
            connect(accessPoint);

        } else {
            //如果并未保存并且还有密码，则弹出密码输入框输入密码
            if (!accessPoint.isSaved() && accessPoint.isSecured) {
                showInputPasswordDialog(accessPoint, false);

            } else {
                //如果不需要输入密码，则直接调用连接接口
                connect(accessPoint);
            }
        }
    }


    /**
     * 输入密码弹窗
     *
     * @param accessPoint
     */
    private void showInputPasswordDialog(AccessPoint accessPoint, Boolean isPasswordError) {
        final boolean[] isPwd = {true};
        if (inputPwdDialog != null && inputPwdDialog.isShowing()) {
            inputPwdDialog.dismiss();
            inputPwdDialog = null;
        }
        inputPwdDialog = new CommonDialog.Builder(getActivity())
                .view(R.layout.wifi_dialog_intput_password)
                .cancelTouchout(false)
                .style(R.style.Dialog)
                .setTitle(R.id.txt_wifi_name, accessPoint.ssid)
                .addViewOnclick(R.id.btn_cancel, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        dismissInputPasswordDialog();
                    }
                })
                .addTextWatchListener(R.id.edt_password, new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        txtPwdError.setVisibility(View.GONE);
                        Button btnConfirm = inputPwdDialog.getView().findViewById(R.id.btn_confirm);
                        if (charSequence.length() < 8) {
                            enableConfirmButton(false);
                            btnConfirm.setClickable(false);
                            btnConfirm.setEnabled(false);
                            btnConfirm.setBackgroundResource(R.drawable.btn_un_click_green);
                        } else {
                            enableConfirmButton(true);
                            btnConfirm.setClickable(true);
                            btnConfirm.setEnabled(true);
                            btnConfirm.setBackgroundResource(R.drawable.shape_shutdown_btn_green);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {

                    }
                })
                .addViewOnclick(R.id.btn_confirm, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        lastPassword = edtPassword.getText().toString();
                        accessPoint.setPassword(lastPassword);
                        connect(accessPoint);
                        dismissInputPasswordDialog();
                    }
                })

                .addViewOnclick(R.id.img_eye, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (isPwd[0]) {
                            //设置明文显示
                            HideReturnsTransformationMethod method = HideReturnsTransformationMethod.getInstance();
                            edtPassword.setTransformationMethod(method);
                            edtPassword.setSelection(edtPassword.getText().length());
                            view.setBackgroundResource(R.drawable.ic_eye_on);
                            isPwd[0] = false;
                        } else {
                            //设置密文显示
                            TransformationMethod method = PasswordTransformationMethod.getInstance();
                            edtPassword.setTransformationMethod(method);
                            //将光标移动到密码末尾
                            edtPassword.setSelection(edtPassword.getText().length());
                            view.setBackgroundResource(R.drawable.ic_eye_off);
                            isPwd[0] = true;
                        }
                    }
                })
                .build();
        inputPwdDialog.show();
        edtPassword = inputPwdDialog.findViewById(R.id.edt_password);
        txtPwdError = inputPwdDialog.findViewById(R.id.txt_password_error);
        //CommonUtils.setIsDialog(getContext(), true);

        Window inputPwdWindow = inputPwdDialog.getWindow();
        if (inputPwdWindow != null) {
            inputPwdWindow.setLayout(CommonUtils.dp2px(getActivity(), Constant.DIALOG_WIDTH), CommonUtils.dp2px(getActivity(), Constant.DIALOG_HEIGHT));
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(inputPwdWindow.getAttributes());
            //layoutParams.gravity = Gravity.BOTTOM;
           /* layoutParams.x = CommonUtils.dp2px(getActivity(),520);//Constant.DIALOG_X;
            layoutParams.y = CommonUtils.dp2px(getActivity(),286);//Constant.DIALOG_Y;*/
            inputPwdWindow.setAttributes(layoutParams);
        }

        if (isPasswordError) {
            edtPassword.setText(lastPassword);
            txtPwdError.setVisibility(View.VISIBLE);
        }
        edtPassword.requestFocus();
        edtPassword.setSelection(edtPassword.getText().length());
        // 显示软键盘
        inputPwdWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    }

    private void dismissInputPasswordDialog() {
        if (inputPwdDialog != null && inputPwdDialog.isShowing()) {
            inputPwdDialog.dismiss();
            inputPwdDialog = null;
        }
        //CommonUtils.setIsDialog(getContext(), false);
    }

    private void connect(AccessPoint accessPoint) {
        accessPoint.generateNetworkConfig();
        int networkId = wifiManager.addNetwork(accessPoint.wifiConfiguration);
        wifiManager.enableNetwork(networkId, true);
    }

    /**
     * 断开链接
     *
     * @param netId
     * @return
     */
    public boolean disconnectWifi(int netId) {

        if (null != wifiManager) {

            boolean isDisable = wifiManager.disableNetwork(netId);

            boolean isDisconnect = wifiManager.disconnect();

            return isDisable && isDisconnect;

        }

        return false;

    }

    /**
     * 忘记密码
     *
     * @param networkId
     */
    public void forgetWifi(int networkId) {
        boolean result = wifiManager.removeNetwork(networkId);
        //需要添加判斷
    }

    public void handlePasswordError() {
        if (lastWifiConfiguration != null) {
            AccessPoint accessPoint = new AccessPoint(lastWifiConfiguration);
            accessPoint.setPasswordError(true);
            showInputPasswordDialog(accessPoint, true);
            forgetWifi(lastWifiConfiguration.networkId);
            lastWifiConfiguration = null;
            lastPassword = null;
        }

    }

    public void portalCurrentWifi() {
        if (lastWifiInfo.getNetworkId() != lastPortalNetworkId) {
            lastPortalNetworkId = lastWifiInfo.getNetworkId();
            Single.create((SingleOnSubscribe<Boolean>) emitter -> {
                        Network currentNetwork = getCurrentNetwork();
                        HttpURLConnection urlConnection = null;
                        try {
                            // 使用当前的网络打开链接
                            urlConnection = (HttpURLConnection) currentNetwork.openConnection(new URL("http://connect.rom.miui.com/generate_204"));
                            urlConnection.setInstanceFollowRedirects(false);
                            urlConnection.setConnectTimeout(10000);
                            urlConnection.setReadTimeout(10000);
                            urlConnection.setUseCaches(false);
                            urlConnection.getInputStream();
                            int responseCode = urlConnection.getResponseCode();
                            if (responseCode == 200 && urlConnection.getContentLength() == 0) {
                                responseCode = 204;
                            }
                            emitter.onSuccess(responseCode != 204 && responseCode >= 200 && responseCode <= 399);
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            if (urlConnection != null) {
                                urlConnection.disconnect();
                            }
                        }
                    })
                    .retry(throwable -> throwable instanceof UnknownHostException)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new SingleObserver<Boolean>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                        }

                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            if (aBoolean) {
                                // 调用网络登录界面
                                /*new AlertDialog.Builder(getActivity())
                                        .setTitle("需要登录")
                                        .setPositiveButton("登录", null)
                                        .setNegativeButton("取消", null)
                                        .show();*/
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            e.printStackTrace();
                        }
                    });
        }
    }

    /**
     * 根据 NetworkId 获取 WifiConfiguration 信息
     *
     * @param networkId 需要获取 WifiConfiguration 信息的 networkId
     * @return 指定 networkId 的 WifiConfiguration 信息
     */
    private WifiConfiguration getWifiConfigurationForNetworkId(int networkId) {
        final List<WifiConfiguration> configs = wifiManager.getConfiguredNetworks();
        if (configs != null) {
            for (WifiConfiguration config : configs) {
                if (lastWifiInfo != null && networkId == config.networkId) {
                    return config;
                }
            }
        }
        return null;
    }

    /**
     * 获取当前网络信息
     */
    public NetworkInfo getActiveNetworkInfo() {
        ConnectivityManager cm = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            return cm.getActiveNetworkInfo();
        }
        return null;
    }
}
