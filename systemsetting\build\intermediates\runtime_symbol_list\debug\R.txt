int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int array languages 0x7f020000
int array startup_channels 0x7f020001
int array startup_channels_windows_disabled 0x7f020002
int attr actionBarDivider 0x7f030000
int attr actionBarItemBackground 0x7f030001
int attr actionBarPopupTheme 0x7f030002
int attr actionBarSize 0x7f030003
int attr actionBarSplitStyle 0x7f030004
int attr actionBarStyle 0x7f030005
int attr actionBarTabBarStyle 0x7f030006
int attr actionBarTabStyle 0x7f030007
int attr actionBarTabTextStyle 0x7f030008
int attr actionBarTheme 0x7f030009
int attr actionBarWidgetTheme 0x7f03000a
int attr actionButtonStyle 0x7f03000b
int attr actionDropDownStyle 0x7f03000c
int attr actionLayout 0x7f03000d
int attr actionMenuTextAppearance 0x7f03000e
int attr actionMenuTextColor 0x7f03000f
int attr actionModeBackground 0x7f030010
int attr actionModeCloseButtonStyle 0x7f030011
int attr actionModeCloseDrawable 0x7f030012
int attr actionModeCopyDrawable 0x7f030013
int attr actionModeCutDrawable 0x7f030014
int attr actionModeFindDrawable 0x7f030015
int attr actionModePasteDrawable 0x7f030016
int attr actionModePopupWindowStyle 0x7f030017
int attr actionModeSelectAllDrawable 0x7f030018
int attr actionModeShareDrawable 0x7f030019
int attr actionModeSplitBackground 0x7f03001a
int attr actionModeStyle 0x7f03001b
int attr actionModeWebSearchDrawable 0x7f03001c
int attr actionOverflowButtonStyle 0x7f03001d
int attr actionOverflowMenuStyle 0x7f03001e
int attr actionProviderClass 0x7f03001f
int attr actionViewClass 0x7f030020
int attr activityChooserViewStyle 0x7f030021
int attr alertDialogButtonGroupStyle 0x7f030022
int attr alertDialogCenterButtons 0x7f030023
int attr alertDialogStyle 0x7f030024
int attr alertDialogTheme 0x7f030025
int attr allowStacking 0x7f030026
int attr alpha 0x7f030027
int attr alphabeticModifiers 0x7f030028
int attr arrowHeadLength 0x7f030029
int attr arrowShaftLength 0x7f03002a
int attr autoCompleteTextViewStyle 0x7f03002b
int attr autoSizeMaxTextSize 0x7f03002c
int attr autoSizeMinTextSize 0x7f03002d
int attr autoSizePresetSizes 0x7f03002e
int attr autoSizeStepGranularity 0x7f03002f
int attr autoSizeTextType 0x7f030030
int attr background 0x7f030031
int attr backgroundSplit 0x7f030032
int attr backgroundStacked 0x7f030033
int attr backgroundTint 0x7f030034
int attr backgroundTintMode 0x7f030035
int attr barLength 0x7f030036
int attr barrierAllowsGoneWidgets 0x7f030037
int attr barrierDirection 0x7f030038
int attr borderlessButtonStyle 0x7f030039
int attr buttonBarButtonStyle 0x7f03003a
int attr buttonBarNegativeButtonStyle 0x7f03003b
int attr buttonBarNeutralButtonStyle 0x7f03003c
int attr buttonBarPositiveButtonStyle 0x7f03003d
int attr buttonBarStyle 0x7f03003e
int attr buttonCompat 0x7f03003f
int attr buttonGravity 0x7f030040
int attr buttonIconDimen 0x7f030041
int attr buttonPanelSideLayout 0x7f030042
int attr buttonStyle 0x7f030043
int attr buttonStyleSmall 0x7f030044
int attr buttonTint 0x7f030045
int attr buttonTintMode 0x7f030046
int attr cardBackgroundColor 0x7f030047
int attr cardCornerRadius 0x7f030048
int attr cardElevation 0x7f030049
int attr cardMaxElevation 0x7f03004a
int attr cardPreventCornerOverlap 0x7f03004b
int attr cardUseCompatPadding 0x7f03004c
int attr cardViewStyle 0x7f03004d
int attr chainUseRtl 0x7f03004e
int attr checkboxStyle 0x7f03004f
int attr checkedTextViewStyle 0x7f030050
int attr clickable 0x7f030051
int attr closeIcon 0x7f030052
int attr closeItemLayout 0x7f030053
int attr collapseContentDescription 0x7f030054
int attr collapseIcon 0x7f030055
int attr color 0x7f030056
int attr colorAccent 0x7f030057
int attr colorBackgroundFloating 0x7f030058
int attr colorButtonNormal 0x7f030059
int attr colorControlActivated 0x7f03005a
int attr colorControlHighlight 0x7f03005b
int attr colorControlNormal 0x7f03005c
int attr colorError 0x7f03005d
int attr colorPrimary 0x7f03005e
int attr colorPrimaryDark 0x7f03005f
int attr colorSwitchThumbNormal 0x7f030060
int attr commitIcon 0x7f030061
int attr constraintSet 0x7f030062
int attr constraint_referenced_ids 0x7f030063
int attr content 0x7f030064
int attr contentDescription 0x7f030065
int attr contentInsetEnd 0x7f030066
int attr contentInsetEndWithActions 0x7f030067
int attr contentInsetLeft 0x7f030068
int attr contentInsetRight 0x7f030069
int attr contentInsetStart 0x7f03006a
int attr contentInsetStartWithNavigation 0x7f03006b
int attr contentPadding 0x7f03006c
int attr contentPaddingBottom 0x7f03006d
int attr contentPaddingLeft 0x7f03006e
int attr contentPaddingRight 0x7f03006f
int attr contentPaddingTop 0x7f030070
int attr controlBackground 0x7f030071
int attr coordinatorLayoutStyle 0x7f030072
int attr customNavigationLayout 0x7f030073
int attr defaultQueryHint 0x7f030074
int attr dialogCornerRadius 0x7f030075
int attr dialogPreferredPadding 0x7f030076
int attr dialogTheme 0x7f030077
int attr displayOptions 0x7f030078
int attr divider 0x7f030079
int attr dividerHorizontal 0x7f03007a
int attr dividerPadding 0x7f03007b
int attr dividerVertical 0x7f03007c
int attr drawableBottomCompat 0x7f03007d
int attr drawableEndCompat 0x7f03007e
int attr drawableLeftCompat 0x7f03007f
int attr drawableRightCompat 0x7f030080
int attr drawableSize 0x7f030081
int attr drawableStartCompat 0x7f030082
int attr drawableTint 0x7f030083
int attr drawableTintMode 0x7f030084
int attr drawableTopCompat 0x7f030085
int attr drawerArrowStyle 0x7f030086
int attr dropDownListViewStyle 0x7f030087
int attr dropdownListPreferredItemHeight 0x7f030088
int attr editTextBackground 0x7f030089
int attr editTextColor 0x7f03008a
int attr editTextStyle 0x7f03008b
int attr elevation 0x7f03008c
int attr emptyVisibility 0x7f03008d
int attr expandActivityOverflowButtonDrawable 0x7f03008e
int attr fastScrollEnabled 0x7f03008f
int attr fastScrollHorizontalThumbDrawable 0x7f030090
int attr fastScrollHorizontalTrackDrawable 0x7f030091
int attr fastScrollVerticalThumbDrawable 0x7f030092
int attr fastScrollVerticalTrackDrawable 0x7f030093
int attr firstBaselineToTopHeight 0x7f030094
int attr font 0x7f030095
int attr fontFamily 0x7f030096
int attr fontProviderAuthority 0x7f030097
int attr fontProviderCerts 0x7f030098
int attr fontProviderFetchStrategy 0x7f030099
int attr fontProviderFetchTimeout 0x7f03009a
int attr fontProviderPackage 0x7f03009b
int attr fontProviderQuery 0x7f03009c
int attr fontStyle 0x7f03009d
int attr fontVariationSettings 0x7f03009e
int attr fontWeight 0x7f03009f
int attr gapBetweenBars 0x7f0300a0
int attr goIcon 0x7f0300a1
int attr height 0x7f0300a2
int attr hideOnContentScroll 0x7f0300a3
int attr hl_angle 0x7f0300a4
int attr hl_bindTextView 0x7f0300a5
int attr hl_centerColor 0x7f0300a6
int attr hl_cornerRadius 0x7f0300a7
int attr hl_cornerRadius_leftBottom 0x7f0300a8
int attr hl_cornerRadius_leftTop 0x7f0300a9
int attr hl_cornerRadius_rightBottom 0x7f0300aa
int attr hl_cornerRadius_rightTop 0x7f0300ab
int attr hl_endColor 0x7f0300ac
int attr hl_layoutBackground 0x7f0300ad
int attr hl_layoutBackground_clickFalse 0x7f0300ae
int attr hl_layoutBackground_true 0x7f0300af
int attr hl_shadowColor 0x7f0300b0
int attr hl_shadowHidden 0x7f0300b1
int attr hl_shadowHiddenBottom 0x7f0300b2
int attr hl_shadowHiddenLeft 0x7f0300b3
int attr hl_shadowHiddenRight 0x7f0300b4
int attr hl_shadowHiddenTop 0x7f0300b5
int attr hl_shadowLimit 0x7f0300b6
int attr hl_shadowOffsetX 0x7f0300b7
int attr hl_shadowOffsetY 0x7f0300b8
int attr hl_shadowSymmetry 0x7f0300b9
int attr hl_shapeMode 0x7f0300ba
int attr hl_startColor 0x7f0300bb
int attr hl_strokeColor 0x7f0300bc
int attr hl_strokeColor_true 0x7f0300bd
int attr hl_strokeWith 0x7f0300be
int attr hl_stroke_dashGap 0x7f0300bf
int attr hl_stroke_dashWidth 0x7f0300c0
int attr hl_text 0x7f0300c1
int attr hl_textColor 0x7f0300c2
int attr hl_textColor_true 0x7f0300c3
int attr hl_text_true 0x7f0300c4
int attr homeAsUpIndicator 0x7f0300c5
int attr homeLayout 0x7f0300c6
int attr icon 0x7f0300c7
int attr iconTint 0x7f0300c8
int attr iconTintMode 0x7f0300c9
int attr iconifiedByDefault 0x7f0300ca
int attr imageButtonStyle 0x7f0300cb
int attr indeterminateProgressStyle 0x7f0300cc
int attr initialActivityCount 0x7f0300cd
int attr isLightTheme 0x7f0300ce
int attr itemPadding 0x7f0300cf
int attr keylines 0x7f0300d0
int attr lastBaselineToBottomHeight 0x7f0300d1
int attr layout 0x7f0300d2
int attr layoutManager 0x7f0300d3
int attr layout_anchor 0x7f0300d4
int attr layout_anchorGravity 0x7f0300d5
int attr layout_behavior 0x7f0300d6
int attr layout_constrainedHeight 0x7f0300d7
int attr layout_constrainedWidth 0x7f0300d8
int attr layout_constraintBaseline_creator 0x7f0300d9
int attr layout_constraintBaseline_toBaselineOf 0x7f0300da
int attr layout_constraintBottom_creator 0x7f0300db
int attr layout_constraintBottom_toBottomOf 0x7f0300dc
int attr layout_constraintBottom_toTopOf 0x7f0300dd
int attr layout_constraintCircle 0x7f0300de
int attr layout_constraintCircleAngle 0x7f0300df
int attr layout_constraintCircleRadius 0x7f0300e0
int attr layout_constraintDimensionRatio 0x7f0300e1
int attr layout_constraintEnd_toEndOf 0x7f0300e2
int attr layout_constraintEnd_toStartOf 0x7f0300e3
int attr layout_constraintGuide_begin 0x7f0300e4
int attr layout_constraintGuide_end 0x7f0300e5
int attr layout_constraintGuide_percent 0x7f0300e6
int attr layout_constraintHeight_default 0x7f0300e7
int attr layout_constraintHeight_max 0x7f0300e8
int attr layout_constraintHeight_min 0x7f0300e9
int attr layout_constraintHeight_percent 0x7f0300ea
int attr layout_constraintHorizontal_bias 0x7f0300eb
int attr layout_constraintHorizontal_chainStyle 0x7f0300ec
int attr layout_constraintHorizontal_weight 0x7f0300ed
int attr layout_constraintLeft_creator 0x7f0300ee
int attr layout_constraintLeft_toLeftOf 0x7f0300ef
int attr layout_constraintLeft_toRightOf 0x7f0300f0
int attr layout_constraintRight_creator 0x7f0300f1
int attr layout_constraintRight_toLeftOf 0x7f0300f2
int attr layout_constraintRight_toRightOf 0x7f0300f3
int attr layout_constraintStart_toEndOf 0x7f0300f4
int attr layout_constraintStart_toStartOf 0x7f0300f5
int attr layout_constraintTop_creator 0x7f0300f6
int attr layout_constraintTop_toBottomOf 0x7f0300f7
int attr layout_constraintTop_toTopOf 0x7f0300f8
int attr layout_constraintVertical_bias 0x7f0300f9
int attr layout_constraintVertical_chainStyle 0x7f0300fa
int attr layout_constraintVertical_weight 0x7f0300fb
int attr layout_constraintWidth_default 0x7f0300fc
int attr layout_constraintWidth_max 0x7f0300fd
int attr layout_constraintWidth_min 0x7f0300fe
int attr layout_constraintWidth_percent 0x7f0300ff
int attr layout_dodgeInsetEdges 0x7f030100
int attr layout_editor_absoluteX 0x7f030101
int attr layout_editor_absoluteY 0x7f030102
int attr layout_goneMarginBottom 0x7f030103
int attr layout_goneMarginEnd 0x7f030104
int attr layout_goneMarginLeft 0x7f030105
int attr layout_goneMarginRight 0x7f030106
int attr layout_goneMarginStart 0x7f030107
int attr layout_goneMarginTop 0x7f030108
int attr layout_insetEdge 0x7f030109
int attr layout_keyline 0x7f03010a
int attr layout_optimizationLevel 0x7f03010b
int attr lineHeight 0x7f03010c
int attr listChoiceBackgroundIndicator 0x7f03010d
int attr listChoiceIndicatorMultipleAnimated 0x7f03010e
int attr listChoiceIndicatorSingleAnimated 0x7f03010f
int attr listDividerAlertDialog 0x7f030110
int attr listItemLayout 0x7f030111
int attr listLayout 0x7f030112
int attr listMenuViewStyle 0x7f030113
int attr listPopupWindowStyle 0x7f030114
int attr listPreferredItemHeight 0x7f030115
int attr listPreferredItemHeightLarge 0x7f030116
int attr listPreferredItemHeightSmall 0x7f030117
int attr listPreferredItemPaddingEnd 0x7f030118
int attr listPreferredItemPaddingLeft 0x7f030119
int attr listPreferredItemPaddingRight 0x7f03011a
int attr listPreferredItemPaddingStart 0x7f03011b
int attr logo 0x7f03011c
int attr logoDescription 0x7f03011d
int attr maxButtonHeight 0x7f03011e
int attr measureWithLargestChild 0x7f03011f
int attr menu 0x7f030120
int attr multiChoiceItemLayout 0x7f030121
int attr navigationContentDescription 0x7f030122
int attr navigationIcon 0x7f030123
int attr navigationMode 0x7f030124
int attr numericModifiers 0x7f030125
int attr overlapAnchor 0x7f030126
int attr paddingBottomNoButtons 0x7f030127
int attr paddingEnd 0x7f030128
int attr paddingStart 0x7f030129
int attr paddingTopNoTitle 0x7f03012a
int attr panelBackground 0x7f03012b
int attr panelMenuListTheme 0x7f03012c
int attr panelMenuListWidth 0x7f03012d
int attr popupMenuStyle 0x7f03012e
int attr popupTheme 0x7f03012f
int attr popupWindowStyle 0x7f030130
int attr preserveIconSpacing 0x7f030131
int attr progressBarPadding 0x7f030132
int attr progressBarStyle 0x7f030133
int attr queryBackground 0x7f030134
int attr queryHint 0x7f030135
int attr radioButtonStyle 0x7f030136
int attr ratingBarStyle 0x7f030137
int attr ratingBarStyleIndicator 0x7f030138
int attr ratingBarStyleSmall 0x7f030139
int attr reverseLayout 0x7f03013a
int attr searchHintIcon 0x7f03013b
int attr searchIcon 0x7f03013c
int attr searchViewStyle 0x7f03013d
int attr seekBarStyle 0x7f03013e
int attr selectableItemBackground 0x7f03013f
int attr selectableItemBackgroundBorderless 0x7f030140
int attr showAsAction 0x7f030141
int attr showDividers 0x7f030142
int attr showText 0x7f030143
int attr showTitle 0x7f030144
int attr singleChoiceItemLayout 0x7f030145
int attr spanCount 0x7f030146
int attr spinBars 0x7f030147
int attr spinnerDropDownItemStyle 0x7f030148
int attr spinnerStyle 0x7f030149
int attr splitTrack 0x7f03014a
int attr srcCompat 0x7f03014b
int attr stackFromEnd 0x7f03014c
int attr state_above_anchor 0x7f03014d
int attr statusBarBackground 0x7f03014e
int attr subMenuArrow 0x7f03014f
int attr submitBackground 0x7f030150
int attr subtitle 0x7f030151
int attr subtitleTextAppearance 0x7f030152
int attr subtitleTextColor 0x7f030153
int attr subtitleTextStyle 0x7f030154
int attr suggestionRowLayout 0x7f030155
int attr switchMinWidth 0x7f030156
int attr switchPadding 0x7f030157
int attr switchStyle 0x7f030158
int attr switchTextAppearance 0x7f030159
int attr textAllCaps 0x7f03015a
int attr textAppearanceLargePopupMenu 0x7f03015b
int attr textAppearanceListItem 0x7f03015c
int attr textAppearanceListItemSecondary 0x7f03015d
int attr textAppearanceListItemSmall 0x7f03015e
int attr textAppearancePopupMenuHeader 0x7f03015f
int attr textAppearanceSearchResultSubtitle 0x7f030160
int attr textAppearanceSearchResultTitle 0x7f030161
int attr textAppearanceSmallPopupMenu 0x7f030162
int attr textColorAlertDialogListItem 0x7f030163
int attr textColorSearchUrl 0x7f030164
int attr textLocale 0x7f030165
int attr theme 0x7f030166
int attr thickness 0x7f030167
int attr thumbTextPadding 0x7f030168
int attr thumbTint 0x7f030169
int attr thumbTintMode 0x7f03016a
int attr tickMark 0x7f03016b
int attr tickMarkTint 0x7f03016c
int attr tickMarkTintMode 0x7f03016d
int attr tint 0x7f03016e
int attr tintMode 0x7f03016f
int attr title 0x7f030170
int attr titleMargin 0x7f030171
int attr titleMarginBottom 0x7f030172
int attr titleMarginEnd 0x7f030173
int attr titleMarginStart 0x7f030174
int attr titleMarginTop 0x7f030175
int attr titleMargins 0x7f030176
int attr titleTextAppearance 0x7f030177
int attr titleTextColor 0x7f030178
int attr titleTextStyle 0x7f030179
int attr toolbarNavigationButtonStyle 0x7f03017a
int attr toolbarStyle 0x7f03017b
int attr tooltipForegroundColor 0x7f03017c
int attr tooltipFrameBackground 0x7f03017d
int attr tooltipText 0x7f03017e
int attr track 0x7f03017f
int attr trackTint 0x7f030180
int attr trackTintMode 0x7f030181
int attr ttcIndex 0x7f030182
int attr viewInflaterClass 0x7f030183
int attr voiceIcon 0x7f030184
int attr windowActionBar 0x7f030185
int attr windowActionBarOverlay 0x7f030186
int attr windowActionModeOverlay 0x7f030187
int attr windowFixedHeightMajor 0x7f030188
int attr windowFixedHeightMinor 0x7f030189
int attr windowFixedWidthMajor 0x7f03018a
int attr windowFixedWidthMinor 0x7f03018b
int attr windowMinWidthMajor 0x7f03018c
int attr windowMinWidthMinor 0x7f03018d
int attr windowNoTitle 0x7f03018e
int attr ycCardBackgroundColor 0x7f03018f
int attr ycCardCornerRadius 0x7f030190
int attr ycCardElevation 0x7f030191
int attr ycCardMaxElevation 0x7f030192
int attr ycCardPreventCornerOverlap 0x7f030193
int attr ycCardUseCompatPadding 0x7f030194
int attr ycContentPadding 0x7f030195
int attr ycContentPaddingLeft 0x7f030196
int attr ycContentPaddingRight 0x7f030197
int attr ycContentPaddingTop 0x7f030198
int attr ycEndShadowColor 0x7f030199
int attr ycStartShadowColor 0x7f03019a
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_allow_stacked_button_bar 0x7f040001
int bool abc_config_actionMenuItemAllCaps 0x7f040002
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color adb_bg 0x7f05001b
int color adb_btn_default 0x7f05001c
int color adb_btn_press 0x7f05001d
int color adb_btn_press_bg 0x7f05001e
int color adb_item_bg 0x7f05001f
int color androidx_core_ripple_material_light 0x7f050020
int color androidx_core_secondary_text_default_material_light 0x7f050021
int color background_floating_material_dark 0x7f050022
int color background_floating_material_light 0x7f050023
int color background_material_dark 0x7f050024
int color background_material_light 0x7f050025
int color black 0x7f050026
int color black_10 0x7f050027
int color black_100 0x7f050028
int color black_60 0x7f050029
int color black_70 0x7f05002a
int color black_80 0x7f05002b
int color black_90 0x7f05002c
int color black_check 0x7f05002d
int color bright_foreground_disabled_material_dark 0x7f05002e
int color bright_foreground_disabled_material_light 0x7f05002f
int color bright_foreground_inverse_material_dark 0x7f050030
int color bright_foreground_inverse_material_light 0x7f050031
int color bright_foreground_material_dark 0x7f050032
int color bright_foreground_material_light 0x7f050033
int color btn_default_bg_green 0x7f050034
int color btn_default_bg_white 0x7f050035
int color btn_dis_click_bg_green 0x7f050036
int color btn_dis_click_bg_white 0x7f050037
int color btn_press_bg_green 0x7f050038
int color btn_press_bg_white 0x7f050039
int color btn_press_color_green 0x7f05003a
int color btn_press_color_white 0x7f05003b
int color button_material_dark 0x7f05003c
int color button_material_light 0x7f05003d
int color cardview_dark_background 0x7f05003e
int color cardview_light_background 0x7f05003f
int color cardview_shadow_end_color 0x7f050040
int color cardview_shadow_start_color 0x7f050041
int color colorAccent 0x7f050042
int color colorPrimary 0x7f050043
int color colorPrimaryDark 0x7f050044
int color colorUpgradeBarBg 0x7f050045
int color colorUpgradeBarContent 0x7f050046
int color color_network_edit 0x7f050047
int color default_shadow_color 0x7f050048
int color default_shadowback_color 0x7f050049
int color default_textColor 0x7f05004a
int color dim_foreground_disabled_material_dark 0x7f05004b
int color dim_foreground_disabled_material_light 0x7f05004c
int color dim_foreground_material_dark 0x7f05004d
int color dim_foreground_material_light 0x7f05004e
int color error_color_material_dark 0x7f05004f
int color error_color_material_light 0x7f050050
int color error_red 0x7f050051
int color foreground_material_dark 0x7f050052
int color foreground_material_light 0x7f050053
int color gray 0x7f050054
int color gray_10 0x7f050055
int color gray_100 0x7f050056
int color gray_50 0x7f050057
int color gray_60 0x7f050058
int color gray_80 0x7f050059
int color gray_85 0x7f05005a
int color gray_90 0x7f05005b
int color green 0x7f05005c
int color green_0 0x7f05005d
int color green_10 0x7f05005e
int color green_100 0x7f05005f
int color green_press 0x7f050060
int color grey_10 0x7f050061
int color highlighted_text_material_dark 0x7f050062
int color highlighted_text_material_light 0x7f050063
int color line1 0x7f050064
int color line2 0x7f050065
int color line_50 0x7f050066
int color main_line 0x7f050067
int color main_screen_text_color 0x7f050068
int color main_shadow_color 0x7f050069
int color main_shutdown_text_color 0x7f05006a
int color main_text_color 0x7f05006b
int color material_blue_grey_800 0x7f05006c
int color material_blue_grey_900 0x7f05006d
int color material_blue_grey_950 0x7f05006e
int color material_deep_teal_200 0x7f05006f
int color material_deep_teal_500 0x7f050070
int color material_grey_100 0x7f050071
int color material_grey_300 0x7f050072
int color material_grey_50 0x7f050073
int color material_grey_600 0x7f050074
int color material_grey_800 0x7f050075
int color material_grey_850 0x7f050076
int color material_grey_900 0x7f050077
int color notification_action_color_filter 0x7f050078
int color notification_icon_bg_color 0x7f050079
int color press_color 0x7f05007a
int color primary_dark_material_dark 0x7f05007b
int color primary_dark_material_light 0x7f05007c
int color primary_material_dark 0x7f05007d
int color primary_material_light 0x7f05007e
int color primary_text_default_material_dark 0x7f05007f
int color primary_text_default_material_light 0x7f050080
int color primary_text_disabled_material_dark 0x7f050081
int color primary_text_disabled_material_light 0x7f050082
int color progress_bar_background 0x7f050083
int color progress_bar_projection_background 0x7f050084
int color progress_bar_rgb_background 0x7f050085
int color purple_200 0x7f050086
int color purple_500 0x7f050087
int color purple_700 0x7f050088
int color red_invalid 0x7f050089
int color ripple_material_dark 0x7f05008a
int color ripple_material_light 0x7f05008b
int color scrollbar_track_background 0x7f05008c
int color secondary_text_default_material_dark 0x7f05008d
int color secondary_text_default_material_light 0x7f05008e
int color secondary_text_disabled_material_dark 0x7f05008f
int color secondary_text_disabled_material_light 0x7f050090
int color shadow_color 0x7f050091
int color shadow_color_middle 0x7f050092
int color switch_thumb_disabled_material_dark 0x7f050093
int color switch_thumb_disabled_material_light 0x7f050094
int color switch_thumb_material_dark 0x7f050095
int color switch_thumb_material_light 0x7f050096
int color switch_thumb_normal_material_dark 0x7f050097
int color switch_thumb_normal_material_light 0x7f050098
int color teal_200 0x7f050099
int color teal_700 0x7f05009a
int color text_70 0x7f05009b
int color text_black_100 0x7f05009c
int color text_enable_green 0x7f05009d
int color text_enable_white 0x7f05009e
int color text_lock 0x7f05009f
int color text_press_green_black 0x7f0500a0
int color tooltip_background_dark 0x7f0500a1
int color tooltip_background_light 0x7f0500a2
int color tv_text_normal 0x7f0500a3
int color tv_text_pressed 0x7f0500a4
int color white 0x7f0500a5
int color white_0 0x7f0500a6
int color white_10 0x7f0500a7
int color white_100 0x7f0500a8
int color white_15 0x7f0500a9
int color white_20 0x7f0500aa
int color white_25 0x7f0500ab
int color white_30 0x7f0500ac
int color white_35 0x7f0500ad
int color white_40 0x7f0500ae
int color white_45 0x7f0500af
int color white_5 0x7f0500b0
int color white_50 0x7f0500b1
int color white_55 0x7f0500b2
int color white_60 0x7f0500b3
int color white_65 0x7f0500b4
int color white_70 0x7f0500b5
int color white_75 0x7f0500b6
int color white_80 0x7f0500b7
int color white_85 0x7f0500b8
int color white_90 0x7f0500b9
int color white_95 0x7f0500ba
int color yc_cardview_dark_background 0x7f0500bb
int color yc_cardview_light_background 0x7f0500bc
int color yc_cardview_shadow_end_color 0x7f0500bd
int color yc_cardview_shadow_start_color 0x7f0500be
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_switch_padding 0x7f06003b
int dimen abc_text_size_body_1_material 0x7f06003c
int dimen abc_text_size_body_2_material 0x7f06003d
int dimen abc_text_size_button_material 0x7f06003e
int dimen abc_text_size_caption_material 0x7f06003f
int dimen abc_text_size_display_1_material 0x7f060040
int dimen abc_text_size_display_2_material 0x7f060041
int dimen abc_text_size_display_3_material 0x7f060042
int dimen abc_text_size_display_4_material 0x7f060043
int dimen abc_text_size_headline_material 0x7f060044
int dimen abc_text_size_large_material 0x7f060045
int dimen abc_text_size_medium_material 0x7f060046
int dimen abc_text_size_menu_header_material 0x7f060047
int dimen abc_text_size_menu_material 0x7f060048
int dimen abc_text_size_small_material 0x7f060049
int dimen abc_text_size_subhead_material 0x7f06004a
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004b
int dimen abc_text_size_title_material 0x7f06004c
int dimen abc_text_size_title_material_toolbar 0x7f06004d
int dimen adb_btn_height 0x7f06004e
int dimen adb_btn_margin_bottom 0x7f06004f
int dimen adb_btn_margin_start 0x7f060050
int dimen adb_btn_text_size 0x7f060051
int dimen adb_btn_width 0x7f060052
int dimen adb_height 0x7f060053
int dimen adb_item_adb_margin_top 0x7f060054
int dimen adb_item_height 0x7f060055
int dimen adb_item_margin_end 0x7f060056
int dimen adb_item_margin_start 0x7f060057
int dimen adb_item_margin_top 0x7f060058
int dimen adb_item_radius 0x7f060059
int dimen adb_item_width 0x7f06005a
int dimen adb_iv_back_height 0x7f06005b
int dimen adb_iv_back_margin_start 0x7f06005c
int dimen adb_iv_back_margin_top 0x7f06005d
int dimen adb_iv_back_width 0x7f06005e
int dimen adb_iv_close_margin_end 0x7f06005f
int dimen adb_iv_close_margin_top 0x7f060060
int dimen adb_iv_reset_height 0x7f060061
int dimen adb_iv_reset_margin_start 0x7f060062
int dimen adb_iv_reset_width 0x7f060063
int dimen adb_line_margin_top 0x7f060064
int dimen adb_scrollview_height 0x7f060065
int dimen adb_sw_height 0x7f060066
int dimen adb_sw_margin_end 0x7f060067
int dimen adb_sw_width 0x7f060068
int dimen adb_tv_margin_start 0x7f060069
int dimen adb_tv_text_size 0x7f06006a
int dimen adb_width 0x7f06006b
int dimen bg_radius 0x7f06006c
int dimen btn_height 0x7f06006d
int dimen btn_text_size 0x7f06006e
int dimen btn_width 0x7f06006f
int dimen cardview_compat_inset_shadow 0x7f060070
int dimen cardview_default_elevation 0x7f060071
int dimen cardview_default_radius 0x7f060072
int dimen compat_button_inset_horizontal_material 0x7f060073
int dimen compat_button_inset_vertical_material 0x7f060074
int dimen compat_button_padding_horizontal_material 0x7f060075
int dimen compat_button_padding_vertical_material 0x7f060076
int dimen compat_control_corner_material 0x7f060077
int dimen compat_notification_large_icon_max_height 0x7f060078
int dimen compat_notification_large_icon_max_width 0x7f060079
int dimen dialog_factory_reset_btn_cancel_margin_top 0x7f06007a
int dimen dialog_factory_reset_content_margin_top 0x7f06007b
int dimen dialog_factory_reset_iv_warn_height 0x7f06007c
int dimen dialog_factory_reset_iv_warn_width 0x7f06007d
int dimen dialog_factory_reset_title_margin_start 0x7f06007e
int dimen dialog_factory_reset_title_margin_top 0x7f06007f
int dimen dialog_have_update_btn_cancel_margin_bottom 0x7f060080
int dimen dialog_have_update_btn_cancel_margin_start 0x7f060081
int dimen dialog_have_update_content_margin_top 0x7f060082
int dimen dialog_have_update_content_text_size 0x7f060083
int dimen dialog_have_update_height 0x7f060084
int dimen dialog_have_update_title_margin_top 0x7f060085
int dimen dialog_have_update_title_text_size 0x7f060086
int dimen dialog_have_update_width 0x7f060087
int dimen dialog_install_fail_tv_title_drawable_padding 0x7f060088
int dimen dialog_install_fail_tv_title_margin_top 0x7f060089
int dimen dialog_installing_pb_height 0x7f06008a
int dimen dialog_installing_pb_width 0x7f06008b
int dimen dialog_installing_text_size 0x7f06008c
int dimen dialog_installing_tv_margin_start 0x7f06008d
int dimen dialog_network_auto_manual_height 0x7f06008e
int dimen dialog_network_auto_manual_item_height 0x7f06008f
int dimen dialog_network_auto_manual_iv_height 0x7f060090
int dimen dialog_network_auto_manual_iv_margin_start 0x7f060091
int dimen dialog_network_auto_manual_iv_width 0x7f060092
int dimen dialog_network_auto_manual_line_margin_left 0x7f060093
int dimen dialog_network_auto_manual_line_margin_right 0x7f060094
int dimen dialog_network_auto_manual_tv_margin_start 0x7f060095
int dimen dialog_network_auto_manual_tv_text_size 0x7f060096
int dimen dialog_network_auto_manual_width 0x7f060097
int dimen dialog_shutdown_content_margin_top 0x7f060098
int dimen dialog_shutdown_countdown_content_margin_top 0x7f060099
int dimen dialog_shutdown_title_margin_top 0x7f06009a
int dimen dialog_update_fail_btn_confirm_margin_start 0x7f06009b
int dimen dialog_update_fail_btn_confirm_text_size 0x7f06009c
int dimen dialog_update_fail_btn_height 0x7f06009d
int dimen dialog_update_fail_btn_width 0x7f06009e
int dimen dialog_update_fail_ll_btn_margin_bottom 0x7f06009f
int dimen dialog_update_fail_ll_btn_margin_start 0x7f0600a0
int dimen dialog_update_fail_tv_content_line_spacing 0x7f0600a1
int dimen dialog_update_fail_tv_content_margin_top 0x7f0600a2
int dimen dialog_update_fail_tv_content_text_size 0x7f0600a3
int dimen dialog_update_fail_tv_title_drawable_padding 0x7f0600a4
int dimen dialog_update_fail_tv_title_margin_top 0x7f0600a5
int dimen dialog_update_fail_tv_title_text_size 0x7f0600a6
int dimen dialog_update_height 0x7f0600a7
int dimen dialog_update_success_btn_confirm_margin_top 0x7f0600a8
int dimen dialog_update_success_tv_title_margin_top 0x7f0600a9
int dimen dialog_update_width 0x7f0600aa
int dimen dialog_updating_pb_height 0x7f0600ab
int dimen dialog_updating_pb_width 0x7f0600ac
int dimen dialog_updating_tv_margin_start 0x7f0600ad
int dimen dialog_updating_tv_text_size 0x7f0600ae
int dimen dialog_volume_height 0x7f0600af
int dimen dialog_volume_iv_margin_start 0x7f0600b0
int dimen dialog_volume_margin_bottom 0x7f0600b1
int dimen dialog_volume_margin_end 0x7f0600b2
int dimen dialog_volume_sb_margin_start 0x7f0600b3
int dimen dialog_volume_tv_text_size 0x7f0600b4
int dimen dialog_volume_width 0x7f0600b5
int dimen disabled_alpha_material_dark 0x7f0600b6
int dimen disabled_alpha_material_light 0x7f0600b7
int dimen dp_0 0x7f0600b8
int dimen dp_15 0x7f0600b9
int dimen dp_5 0x7f0600ba
int dimen fastscroll_default_thickness 0x7f0600bb
int dimen fastscroll_margin 0x7f0600bc
int dimen fastscroll_minimum_range 0x7f0600bd
int dimen fl_desktop_padding_vertical 0x7f06026a
int dimen fragment_about_content_height 0x7f0600bf
int dimen fragment_about_content_width 0x7f0600c0
int dimen fragment_about_device_name_margin_top 0x7f0600c1
int dimen fragment_about_iv_windows_host_margin_start 0x7f0600c2
int dimen fragment_about_line_height 0x7f0600c3
int dimen fragment_about_line_margin_start 0x7f0600c4
int dimen fragment_about_line_margin_top 0x7f0600c5
int dimen fragment_about_padding_bottom 0x7f0600c6
int dimen fragment_about_padding_view_height 0x7f0600c7
int dimen fragment_about_scrollbar_size 0x7f0600c8
int dimen fragment_about_text_size 0x7f0600c9
int dimen fragment_about_title_height 0x7f0600ca
int dimen fragment_about_title_margin_start 0x7f0600cb
int dimen fragment_about_title_margin_top 0x7f0600cc
int dimen fragment_about_title_width 0x7f0600cd
int dimen fragment_about_tv_system_version_margin_start 0x7f0600ce
int dimen fragment_about_windows_host_width 0x7f0600cf
int dimen fragment_extra_item_margin_top 0x7f0600d0
int dimen fragment_extra_sw_height 0x7f0600d1
int dimen fragment_extra_sw_margin_end 0x7f0600d2
int dimen fragment_extra_sw_width 0x7f0600d3
int dimen fragment_extra_tv_margin_start 0x7f0600d4
int dimen fragment_extra_tv_text_size 0x7f0600d5
int dimen fragment_extra_wireless_screen_margin_top 0x7f0600d6
int dimen fragment_locale_iv_language_margin_end 0x7f0600d7
int dimen fragment_locale_iv_language_margin_top 0x7f0600d8
int dimen fragment_locale_spinner_width 0x7f0600d9
int dimen fragment_locale_tv_language_height 0x7f0600da
int dimen fragment_locale_tv_language_margin_top 0x7f0600db
int dimen fragment_network_btn_confirm_margin_start 0x7f0600dc
int dimen fragment_network_btn_confirm_margin_top 0x7f0600dd
int dimen fragment_network_content_margin_start 0x7f0600de
int dimen fragment_network_content_width 0x7f0600df
int dimen fragment_network_dns_content_margin_start 0x7f0600e0
int dimen fragment_network_dns_title_width 0x7f0600e1
int dimen fragment_network_et_height 0x7f0600e2
int dimen fragment_network_et_margin_start 0x7f0600e3
int dimen fragment_network_et_padding_end 0x7f0600e4
int dimen fragment_network_et_text_size 0x7f0600e5
int dimen fragment_network_et_width 0x7f0600e6
int dimen fragment_network_item_margin_top 0x7f0600e7
int dimen fragment_network_iv_ip_height 0x7f0600e8
int dimen fragment_network_iv_ip_margin_start 0x7f0600e9
int dimen fragment_network_iv_ip_width 0x7f0600ea
int dimen fragment_network_ll_ip_margin_top 0x7f0600eb
int dimen fragment_network_ll_mask_margin_top 0x7f0600ec
int dimen fragment_network_network_margin_top 0x7f0600ed
int dimen fragment_network_sw_height 0x7f0600ee
int dimen fragment_network_sw_margin_start 0x7f0600ef
int dimen fragment_network_sw_width 0x7f0600f0
int dimen fragment_network_text_size 0x7f0600f1
int dimen fragment_network_title_margin_start 0x7f0600f2
int dimen fragment_network_title_width 0x7f0600f3
int dimen fragment_network_tv_ip_setting_width 0x7f0600f4
int dimen fragment_update_btn_cancel_margin_top 0x7f0600f5
int dimen fragment_update_btn_install_margin_top 0x7f0600f6
int dimen fragment_update_btn_retry_margin_top 0x7f0600f7
int dimen fragment_update_btn_right_update_gone_margin_top 0x7f0600f8
int dimen fragment_update_btn_right_update_margin_top 0x7f0600f9
int dimen fragment_update_line_margin_top 0x7f0600fa
int dimen fragment_update_pb_check_update_margin_top 0x7f0600fb
int dimen fragment_update_pb_download_margin_end 0x7f0600fc
int dimen fragment_update_pb_download_margin_top 0x7f0600fd
int dimen fragment_update_pb_horizontal_max_height 0x7f0600fe
int dimen fragment_update_pb_horizontal_min_height 0x7f0600ff
int dimen fragment_update_tv_check_network_margin_top 0x7f060100
int dimen fragment_update_tv_checking_margin_top 0x7f060101
int dimen fragment_update_tv_no_network_margin_start 0x7f060102
int dimen fragment_update_tv_no_network_margin_top 0x7f060103
int dimen fragment_update_tv_update_description_height 0x7f060104
int dimen fragment_update_tv_update_description_margin_top 0x7f060105
int dimen fragment_update_tv_update_description_max_height 0x7f060106
int dimen fragment_update_tv_update_description_width 0x7f060107
int dimen fragment_update_tv_version_margin_top 0x7f060108
int dimen fragment_update_tv_version_text_size 0x7f060109
int dimen fragment_wifi_line1_margin_end 0x7f06010a
int dimen fragment_wifi_line1_margin_start 0x7f06010b
int dimen fragment_wifi_line1_margin_top 0x7f06010c
int dimen fragment_wifi_ll_network_gone_margin_top 0x7f06010d
int dimen fragment_wifi_ll_network_margin_start 0x7f06010e
int dimen fragment_wifi_ll_network_margin_top 0x7f06010f
int dimen fragment_wifi_pb_check_update_height 0x7f060110
int dimen fragment_wifi_pb_check_update_margin_start 0x7f060111
int dimen fragment_wifi_pb_check_update_width 0x7f060112
int dimen fragment_wifi_rv_connected_margin_top 0x7f060113
int dimen fragment_wifi_rv_wifi_list_margin_top 0x7f060114
int dimen fragment_wifi_sw_margin_start 0x7f060115
int dimen fragment_wifi_title_wifi_width 0x7f060116
int dimen fragment_wifi_tv_other_network_text_size 0x7f060117
int dimen highlight_alpha_material_colored 0x7f060118
int dimen highlight_alpha_material_dark 0x7f060119
int dimen highlight_alpha_material_light 0x7f06011a
int dimen hint_alpha_material_dark 0x7f06011b
int dimen hint_alpha_material_light 0x7f06011c
int dimen hint_pressed_alpha_material_dark 0x7f06011d
int dimen hint_pressed_alpha_material_light 0x7f06011e
int dimen inter_touch_lock_height 0x7f06011f
int dimen inter_touch_lock_margin_start 0x7f060120
int dimen inter_touch_lock_margin_top 0x7f060121
int dimen inter_touch_lock_width 0x7f060122
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f060123
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f060124
int dimen item_touch_helper_swipe_escape_velocity 0x7f060125
int dimen item_wifi_connect_height 0x7f060126
int dimen item_wifi_connect_iv_check_height 0x7f060127
int dimen item_wifi_connect_iv_check_width 0x7f060128
int dimen item_wifi_connect_iv_password_margin_start 0x7f060129
int dimen item_wifi_connect_margin_top 0x7f06012a
int dimen item_wifi_height 0x7f06012b
int dimen item_wifi_iv_password_margin_end 0x7f06012c
int dimen item_wifi_iv_rank_height 0x7f06012d
int dimen item_wifi_iv_rank_margin_end 0x7f06012e
int dimen item_wifi_iv_rank_width 0x7f06012f
int dimen item_wifi_line_margin_top 0x7f060130
int dimen item_wifi_more_drawable_padding 0x7f060131
int dimen item_wifi_more_et_address_margin_top 0x7f060132
int dimen item_wifi_more_et_subnet_mask_margin_top 0x7f060133
int dimen item_wifi_more_fl_disconnect_margin_top 0x7f060134
int dimen item_wifi_more_ip_address_margin_top 0x7f060135
int dimen item_wifi_more_item_margin_end 0x7f060136
int dimen item_wifi_more_item_margin_top 0x7f060137
int dimen item_wifi_more_iv_arrow_margin_top 0x7f060138
int dimen item_wifi_more_iv_disconnect_margin_start 0x7f060139
int dimen item_wifi_more_iv_ip_marin_end 0x7f06013a
int dimen item_wifi_more_iv_manual_margin_end 0x7f06013b
int dimen item_wifi_more_line1_margin_top 0x7f06013c
int dimen item_wifi_more_line_margin_top 0x7f06013d
int dimen item_wifi_more_rl_manual_margin_top 0x7f06013e
int dimen item_wifi_more_title_margin_start 0x7f06013f
int dimen item_wifi_more_tv_disconnect_height 0x7f060140
int dimen item_wifi_more_tv_disconnect_margin_start 0x7f060141
int dimen item_wifi_more_tv_disconnect_padding_left 0x7f060142
int dimen item_wifi_more_tv_dns_width 0x7f060143
int dimen item_wifi_more_tv_ip_setting_width 0x7f060144
int dimen item_wifi_more_tv_save_margin_bottom 0x7f060145
int dimen item_wifi_more_tv_save_margin_end 0x7f060146
int dimen item_wifi_more_tv_save_margin_start 0x7f060147
int dimen item_wifi_more_tv_save_margin_top 0x7f060148
int dimen item_wifi_more_tv_save_text_size 0x7f060149
int dimen item_wifi_tv_state_margin_top 0x7f06014a
int dimen item_wifi_tv_state_text_size 0x7f06014b
int dimen item_wifi_tv_wifi_height 0x7f06014c
int dimen item_wifi_tv_wifi_margin_top 0x7f06014d
int dimen item_wifi_tv_wifi_name_height 0x7f06014e
int dimen item_wifi_tv_wifi_name_max_width 0x7f06014f
int dimen iv_back_height 0x7f060150
int dimen iv_back_margin_start 0x7f060151
int dimen iv_back_margin_top 0x7f060152
int dimen iv_back_width 0x7f060153
int dimen iv_bright_height 0x7f060154
int dimen iv_bright_margin_start 0x7f060155
int dimen iv_bright_margin_top 0x7f060156
int dimen iv_bright_width 0x7f060157
int dimen iv_lock_padding 0x7f060158
int dimen iv_screen_height 0x7f060159
int dimen iv_screen_margin_top 0x7f06015a
int dimen iv_screen_width 0x7f06015b
int dimen iv_shutdown_height 0x7f06015c
int dimen iv_shutdown_margin_left 0x7f06015d
int dimen iv_shutdown_margin_top 0x7f06015e
int dimen iv_shutdown_width 0x7f06015f
int dimen iv_voice_margin_top 0x7f060160
int dimen launcher_main_btn_start_height 0x7f060161
int dimen launcher_main_btn_start_margin_top 0x7f060162
int dimen launcher_main_btn_start_width 0x7f060163
int dimen launcher_main_iv_no_signal_height 0x7f060164
int dimen launcher_main_iv_no_signal_margin_top 0x7f060165
int dimen launcher_main_iv_no_signal_width 0x7f060166
int dimen launcher_main_iv_privacy_height 0x7f060167
int dimen launcher_main_iv_privacy_width 0x7f060168
int dimen launcher_main_tv_current_signal_margin_top 0x7f060169
int dimen launcher_main_tv_current_signal_text_size 0x7f06016a
int dimen launcher_main_tv_hint_height 0x7f06016b
int dimen launcher_main_tv_hint_text_size 0x7f06016c
int dimen launcher_main_tv_hint_width 0x7f06016d
int dimen launcher_main_tv_hotline_height 0x7f06016e
int dimen launcher_main_tv_hotline_margin_top 0x7f06016f
int dimen launcher_main_tv_hotline_width 0x7f060170
int dimen launcher_main_tv_no_signal_margin_top 0x7f060171
int dimen launcher_main_tv_no_signal_text_size 0x7f060172
int dimen launcher_main_tv_privacy_margin_top 0x7f060173
int dimen launcher_main_tv_privacy_text_size 0x7f060174
int dimen ll_select_width 0x7f060175
int dimen ll_touch_lock_margin_bottom 0x7f060176
int dimen ll_touch_lock_width 0x7f060177
int dimen main_bg_radius 0x7f060178
int dimen main_bg_stroke 0x7f060179
int dimen main_cv_height 0x7f06017a
int dimen main_cv_width 0x7f06017b
int dimen main_height 0x7f06017c
int dimen main_line1_height 0x7f06017d
int dimen main_line1_margin_top 0x7f06017e
int dimen main_tv_title_margin_top 0x7f06017f
int dimen main_width 0x7f060180
int dimen notification_action_icon_size 0x7f060181
int dimen notification_action_text_size 0x7f060182
int dimen notification_big_circle_margin 0x7f060183
int dimen notification_content_margin_start 0x7f060184
int dimen notification_large_icon_height 0x7f060185
int dimen notification_large_icon_width 0x7f060186
int dimen notification_main_column_padding_top 0x7f060187
int dimen notification_media_narrow_margin 0x7f060188
int dimen notification_right_icon_size 0x7f060189
int dimen notification_right_side_padding_top 0x7f06018a
int dimen notification_small_icon_background_padding 0x7f06018b
int dimen notification_small_icon_size_as_large 0x7f06018c
int dimen notification_subtext_size 0x7f06018d
int dimen notification_top_pad 0x7f06018e
int dimen notification_top_pad_large_text 0x7f06018f
int dimen privacy_height 0x7f060190
int dimen privacy_margin_top 0x7f060191
int dimen privacy_width 0x7f060192
int dimen rgb_btn_reset_width 0x7f060193
int dimen rgb_et_height 0x7f060194
int dimen rgb_et_width 0x7f060195
int dimen rgb_green_gain_margin_top 0x7f060196
int dimen rgb_line2_margin_top 0x7f060197
int dimen rgb_red_gain_margin_top 0x7f060198
int dimen rgb_reset_margin_bottom 0x7f060199
int dimen rgb_sb_height 0x7f06019a
int dimen rgb_sb_margin_start 0x7f06019b
int dimen rgb_sb_margin_top 0x7f06019c
int dimen rgb_sb_padding_end 0x7f06019d
int dimen rgb_sb_padding_start 0x7f06019e
int dimen rgb_sb_thumb_height 0x7f06019f
int dimen rgb_sb_width 0x7f0601a0
int dimen rgb_tv_color_temperature_cold_margin_top 0x7f0601a1
int dimen rgb_tv_color_temperature_margin_top 0x7f0601a2
int dimen rgb_tv_margin_start 0x7f0601a3
int dimen sb_bright_height 0x7f0601a4
int dimen sb_bright_margin_start 0x7f0601a5
int dimen sb_bright_margin_top 0x7f0601a6
int dimen sb_bright_max_height 0x7f0601a7
int dimen sb_bright_padding_end 0x7f0601a8
int dimen sb_bright_padding_start 0x7f0601a9
int dimen sb_bright_width 0x7f0601aa
int dimen sb_voice_margin_top 0x7f0601ab
int dimen screen_dialog_content_text_size 0x7f0601ac
int dimen screen_dialog_cv_wired_screen_height 0x7f0601ad
int dimen screen_dialog_cv_wired_screen_margin_top 0x7f0601ae
int dimen screen_dialog_cv_wireless_screen_height 0x7f0601af
int dimen screen_dialog_iv_back_height 0x7f0601b0
int dimen screen_dialog_iv_back_margin_start 0x7f0601b1
int dimen screen_dialog_iv_back_margin_top 0x7f0601b2
int dimen screen_dialog_iv_back_width 0x7f0601b3
int dimen screen_dialog_iv_code_height 0x7f0601b4
int dimen screen_dialog_iv_code_margin_end 0x7f0601b5
int dimen screen_dialog_iv_code_margin_top 0x7f0601b6
int dimen screen_dialog_iv_code_width 0x7f0601b7
int dimen screen_dialog_iv_step_1_height 0x7f0601b8
int dimen screen_dialog_iv_step_1_margin_top 0x7f0601b9
int dimen screen_dialog_iv_step_1_width 0x7f0601ba
int dimen screen_dialog_iv_step_2_margin_top 0x7f0601bb
int dimen screen_dialog_iv_step_3_margin_top 0x7f0601bc
int dimen screen_dialog_line2_margin_top 0x7f0601bd
int dimen screen_dialog_rl_sub_wired_screen_margin_top 0x7f0601be
int dimen screen_dialog_rl_sub_wireless_screen_margin_top 0x7f0601bf
int dimen screen_dialog_rl_sub_wireless_screen_width 0x7f0601c0
int dimen screen_dialog_rl_wired_screen_height 0x7f0601c1
int dimen screen_dialog_rl_wired_screen_margin_top 0x7f0601c2
int dimen screen_dialog_rl_wireless_screen_height 0x7f0601c3
int dimen screen_dialog_switch_enable_pin_code_margin_end 0x7f0601c4
int dimen screen_dialog_switch_enable_wireless_screen_height 0x7f0601c5
int dimen screen_dialog_switch_enable_wireless_screen_margin_end 0x7f0601c6
int dimen screen_dialog_switch_enable_wireless_screen_margin_top 0x7f0601c7
int dimen screen_dialog_switch_enable_wireless_screen_width 0x7f0601c8
int dimen screen_dialog_title_text_size 0x7f0601c9
int dimen screen_dialog_tv_code_line_height 0x7f0601ca
int dimen screen_dialog_tv_code_margin_end 0x7f0601cb
int dimen screen_dialog_tv_code_margin_top 0x7f0601cc
int dimen screen_dialog_tv_code_text_size 0x7f0601cd
int dimen screen_dialog_tv_enable_pin_code_margin_top 0x7f0601ce
int dimen screen_dialog_tv_margin_start 0x7f0601cf
int dimen screen_dialog_tv_one_margin_start 0x7f0601d0
int dimen screen_dialog_tv_order_height 0x7f0601d1
int dimen screen_dialog_tv_order_width 0x7f0601d2
int dimen screen_dialog_tv_pin_code_margin_top 0x7f0601d3
int dimen screen_dialog_tv_signal_margin_top 0x7f0601d4
int dimen screen_dialog_tv_step_1_height 0x7f0601d5
int dimen screen_dialog_tv_step_1_line_height 0x7f0601d6
int dimen screen_dialog_tv_step_1_margin_start 0x7f0601d7
int dimen screen_dialog_tv_step_1_margin_top 0x7f0601d8
int dimen screen_dialog_tv_title_margin_top 0x7f0601d9
int dimen screen_offset_tv_margin_bottom 0x7f0601da
int dimen screen_offset_tv_margin_horizontal 0x7f0601db
int dimen screen_offset_tv_margin_top 0x7f0601dc
int dimen screen_offset_tv_text_size 0x7f0601dd
int dimen setting_line1_margin_top 0x7f0601de
int dimen setting_tv_title_margin_top 0x7f0601df
int dimen shutdown_btn_cancel_margin_left 0x7f0601e0
int dimen shutdown_btn_confirm_height 0x7f0601e1
int dimen shutdown_btn_confirm_margin_left 0x7f0601e2
int dimen shutdown_btn_confirm_margin_top 0x7f0601e3
int dimen shutdown_btn_confirm_text_size 0x7f0601e4
int dimen shutdown_btn_confirm_width 0x7f0601e5
int dimen shutdown_content_margin_top 0x7f0601e6
int dimen shutdown_content_text_size 0x7f0601e7
int dimen shutdown_tittle_margin_left 0x7f0601e8
int dimen shutdown_tittle_margin_top 0x7f0601e9
int dimen shutdown_tittle_text_size 0x7f0601ea
int dimen signal_item_margin_top 0x7f0601eb
int dimen signal_iv_margin_start 0x7f0601ec
int dimen signal_tv_drawable_padding 0x7f0601ed
int dimen signal_tv_height 0x7f0601ee
int dimen signal_tv_margin_start 0x7f0601ef
int dimen signal_tv_text_size 0x7f0601f0
int dimen signal_tv_width 0x7f0601f1
int dimen signal_tv_window_margin_top 0x7f0601f2
int dimen sl_lock_height 0x7f0601f3
int dimen sl_lock_width 0x7f0601f4
int dimen sl_network_height 0x7f0601f5
int dimen sl_network_margin_start 0x7f0601f6
int dimen sl_network_margin_top 0x7f0601f7
int dimen sl_network_shadow_offset_x 0x7f0601f8
int dimen sl_network_shadow_offset_y 0x7f0601f9
int dimen sl_screen_corner_radius 0x7f0601fa
int dimen sl_screen_elevation 0x7f0601fb
int dimen sl_screen_height 0x7f0601fc
int dimen sl_screen_margin_start 0x7f0601fd
int dimen sl_screen_margin_top 0x7f0601fe
int dimen sl_screen_shadow_limit 0x7f0601ff
int dimen sl_screen_shadow_offset_x 0x7f060200
int dimen sl_screen_shadow_offset_y 0x7f060201
int dimen sl_screen_stroke_width 0x7f060202
int dimen sl_screen_width 0x7f060203
int dimen sl_setting_margin_start 0x7f060204
int dimen sl_setting_width 0x7f060205
int dimen sl_wifi_margin_top 0x7f060206
int dimen sl_write_margin_top 0x7f060207
int dimen small_window_iv_home_height 0x7f060208
int dimen small_window_iv_home_width 0x7f060209
int dimen small_window_iv_volume_height 0x7f06020a
int dimen small_window_iv_volume_margin_end 0x7f06020b
int dimen small_window_iv_volume_width 0x7f06020c
int dimen small_window_shortcut_drawable_padding 0x7f06020d
int dimen small_window_shortcut_height 0x7f06020e
int dimen small_window_shortcut_iv_screen_gone_margin_start 0x7f06020f
int dimen small_window_shortcut_iv_screen_margin_end 0x7f060210
int dimen small_window_shortcut_text_size 0x7f060211
int dimen small_window_shortcut_tv_desktop_margin_start 0x7f06026b
int dimen small_window_shortcut_width 0x7f060213
int dimen small_window_sv_volume_height 0x7f060214
int dimen small_window_sv_volume_margin_end 0x7f060215
int dimen small_window_sv_volume_padding_end 0x7f060216
int dimen small_window_sv_volume_padding_start 0x7f060217
int dimen small_window_sv_volume_width 0x7f060218
int dimen small_window_tv_home_margin_start 0x7f060219
int dimen spinner_dropdown_item_iv_margin_vertical 0x7f06021a
int dimen spinner_dropdown_item_tv_margin_start 0x7f06021b
int dimen toast_height 0x7f06021c
int dimen toast_msg_margin_start 0x7f06021d
int dimen toast_msg_text_size 0x7f06021e
int dimen toast_padding_horizontal 0x7f06021f
int dimen toast_padding_vertical 0x7f060220
int dimen toast_resolution_corner_radius 0x7f060221
int dimen toast_resolution_height 0x7f060222
int dimen toast_resolution_margin_start 0x7f060223
int dimen toast_resolution_tv_text_size 0x7f060224
int dimen toast_resolution_width 0x7f060225
int dimen toast_shutting_down_ops_height 0x7f060226
int dimen toast_shutting_down_ops_iv_margin_top 0x7f060227
int dimen toast_shutting_down_ops_tv_margin_top 0x7f060228
int dimen toast_shutting_down_ops_width 0x7f060229
int dimen toast_uhd_height 0x7f06022a
int dimen toast_uhd_iv_height 0x7f06022b
int dimen toast_uhd_iv_margin_start 0x7f06022c
int dimen toast_uhd_iv_width 0x7f06022d
int dimen toast_uhd_margin_bottom 0x7f06022e
int dimen toast_uhd_tv_martin_start 0x7f06022f
int dimen toast_uhd_tv_text_size 0x7f060230
int dimen toast_uhd_width 0x7f060231
int dimen toast_width 0x7f060232
int dimen tooltip_corner_radius 0x7f060233
int dimen tooltip_horizontal_padding 0x7f060234
int dimen tooltip_margin 0x7f060235
int dimen tooltip_precise_anchor_extra_offset 0x7f060236
int dimen tooltip_precise_anchor_threshold 0x7f060237
int dimen tooltip_vertical_padding 0x7f060238
int dimen tooltip_y_offset_non_touch 0x7f060239
int dimen tooltip_y_offset_touch 0x7f06023a
int dimen tv_desktop_margin_bottom 0x7f06026c
int dimen tv_desktop_margin_start 0x7f06026d
int dimen tv_desktop_margin_top 0x7f06026e
int dimen tv_desktop_text_size 0x7f06026f
int dimen tv_eye_margin_start 0x7f06023b
int dimen tv_lock_margin_top 0x7f060240
int dimen tv_network_margin_start 0x7f060241
int dimen tv_network_text_size 0x7f060242
int dimen tv_rest_margin_desktop 0x7f060270
int dimen tv_rest_margin_start 0x7f060243
int dimen tv_restart_margin_start 0x7f060244
int dimen tv_screen_drawable_padding 0x7f060245
int dimen tv_screen_height 0x7f060246
int dimen tv_screen_margin_top 0x7f060247
int dimen tv_screen_text_size 0x7f060248
int dimen tv_screen_width 0x7f060249
int dimen tv_setting_margin_start 0x7f06024a
int dimen tv_setting_margin_top 0x7f06024b
int dimen tv_shutdown_width 0x7f06024c
int dimen tv_signal_margin_start 0x7f06024d
int dimen tv_touch_lock_margin_start 0x7f06024e
int dimen tv_touch_lock_width 0x7f06024f
int dimen wifi_dialog_input_password_btn_margin_top 0x7f060250
int dimen wifi_dialog_input_password_et_password_height 0x7f060251
int dimen wifi_dialog_input_password_et_password_margin_top 0x7f060252
int dimen wifi_dialog_input_password_et_password_padding_end 0x7f060253
int dimen wifi_dialog_input_password_et_password_padding_start 0x7f060254
int dimen wifi_dialog_input_password_et_password_text_size 0x7f060255
int dimen wifi_dialog_input_password_et_password_width 0x7f060256
int dimen wifi_dialog_input_password_iv_eye_height 0x7f060257
int dimen wifi_dialog_input_password_iv_eye_margin_start 0x7f060258
int dimen wifi_dialog_input_password_iv_eye_margin_top 0x7f060259
int dimen wifi_dialog_input_password_iv_eye_width 0x7f06025a
int dimen wifi_dialog_input_password_iv_wifi_margin_start 0x7f06025b
int dimen wifi_dialog_input_password_iv_wifi_margin_top 0x7f06025c
int dimen wifi_dialog_input_password_title_margin_start 0x7f06025d
int dimen wifi_dialog_input_password_title_margin_top 0x7f06025e
int dimen wifi_dialog_input_password_title_text_size 0x7f06025f
int dimen wifi_dialog_input_password_title_wifi_name_max_height 0x7f060260
int dimen wifi_dialog_input_password_title_wifi_name_max_width 0x7f060261
int dimen wifi_dialog_input_password_tv_password_error_margin_start 0x7f060262
int dimen wifi_dialog_input_password_tv_password_error_margin_top 0x7f060263
int dimen wifi_dialog_input_password_tv_password_error_text_size 0x7f060264
int dimen window_ruler_iv_ruler_height 0x7f060265
int dimen window_ruler_iv_ruler_width 0x7f060266
int dimen yc_cardview_compat_inset_shadow 0x7f060267
int dimen yc_cardview_default_elevation 0x7f060268
int dimen yc_cardview_default_radius 0x7f060269
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070001
int drawable abc_action_bar_item_background_material 0x7f070002
int drawable abc_btn_borderless_material 0x7f070003
int drawable abc_btn_check_material 0x7f070004
int drawable abc_btn_check_material_anim 0x7f070005
int drawable abc_btn_check_to_on_mtrl_000 0x7f070006
int drawable abc_btn_check_to_on_mtrl_015 0x7f070007
int drawable abc_btn_colored_material 0x7f070008
int drawable abc_btn_default_mtrl_shape 0x7f070009
int drawable abc_btn_radio_material 0x7f07000a
int drawable abc_btn_radio_material_anim 0x7f07000b
int drawable abc_btn_radio_to_on_mtrl_000 0x7f07000c
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000d
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000e
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000f
int drawable abc_cab_background_internal_bg 0x7f070010
int drawable abc_cab_background_top_material 0x7f070011
int drawable abc_cab_background_top_mtrl_alpha 0x7f070012
int drawable abc_control_background_material 0x7f070013
int drawable abc_dialog_material_background 0x7f070014
int drawable abc_edit_text_material 0x7f070015
int drawable abc_ic_ab_back_material 0x7f070016
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070017
int drawable abc_ic_clear_material 0x7f070018
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070019
int drawable abc_ic_go_search_api_material 0x7f07001a
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001b
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f07001c
int drawable abc_ic_menu_overflow_material 0x7f07001d
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001e
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001f
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070020
int drawable abc_ic_search_api_material 0x7f070021
int drawable abc_ic_star_black_16dp 0x7f070022
int drawable abc_ic_star_black_36dp 0x7f070023
int drawable abc_ic_star_black_48dp 0x7f070024
int drawable abc_ic_star_half_black_16dp 0x7f070025
int drawable abc_ic_star_half_black_36dp 0x7f070026
int drawable abc_ic_star_half_black_48dp 0x7f070027
int drawable abc_ic_voice_search_api_material 0x7f070028
int drawable abc_item_background_holo_dark 0x7f070029
int drawable abc_item_background_holo_light 0x7f07002a
int drawable abc_list_divider_material 0x7f07002b
int drawable abc_list_divider_mtrl_alpha 0x7f07002c
int drawable abc_list_focused_holo 0x7f07002d
int drawable abc_list_longpressed_holo 0x7f07002e
int drawable abc_list_pressed_holo_dark 0x7f07002f
int drawable abc_list_pressed_holo_light 0x7f070030
int drawable abc_list_selector_background_transition_holo_dark 0x7f070031
int drawable abc_list_selector_background_transition_holo_light 0x7f070032
int drawable abc_list_selector_disabled_holo_dark 0x7f070033
int drawable abc_list_selector_disabled_holo_light 0x7f070034
int drawable abc_list_selector_holo_dark 0x7f070035
int drawable abc_list_selector_holo_light 0x7f070036
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070037
int drawable abc_popup_background_mtrl_mult 0x7f070038
int drawable abc_ratingbar_indicator_material 0x7f070039
int drawable abc_ratingbar_material 0x7f07003a
int drawable abc_ratingbar_small_material 0x7f07003b
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f07003c
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07003d
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f07003e
int drawable abc_scrubber_primary_mtrl_alpha 0x7f07003f
int drawable abc_scrubber_track_mtrl_alpha 0x7f070040
int drawable abc_seekbar_thumb_material 0x7f070041
int drawable abc_seekbar_tick_mark_material 0x7f070042
int drawable abc_seekbar_track_material 0x7f070043
int drawable abc_spinner_mtrl_am_alpha 0x7f070044
int drawable abc_spinner_textfield_background_material 0x7f070045
int drawable abc_switch_thumb_material 0x7f070046
int drawable abc_switch_track_mtrl_alpha 0x7f070047
int drawable abc_tab_indicator_material 0x7f070048
int drawable abc_tab_indicator_mtrl_alpha 0x7f070049
int drawable abc_text_cursor_material 0x7f07004a
int drawable abc_text_select_handle_left_mtrl_dark 0x7f07004b
int drawable abc_text_select_handle_left_mtrl_light 0x7f07004c
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f07004d
int drawable abc_text_select_handle_middle_mtrl_light 0x7f07004e
int drawable abc_text_select_handle_right_mtrl_dark 0x7f07004f
int drawable abc_text_select_handle_right_mtrl_light 0x7f070050
int drawable abc_textfield_activated_mtrl_alpha 0x7f070051
int drawable abc_textfield_default_mtrl_alpha 0x7f070052
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070053
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070054
int drawable abc_textfield_search_material 0x7f070055
int drawable abc_vector_test 0x7f070056
int drawable annotate 0x7f070057
int drawable bg_adb_item 0x7f070058
int drawable bg_btn_cancel_normal 0x7f070059
int drawable bg_btn_confirm 0x7f07005a
int drawable bg_btn_confirm_normal 0x7f07005b
int drawable bg_btn_press 0x7f07005c
int drawable bg_button_cancel 0x7f07005d
int drawable bg_no_signal 0x7f07005e
int drawable bg_projection 0x7f07005f
int drawable bg_projection_shortcut 0x7f070060
int drawable bg_seek_bar 0x7f070061
int drawable bg_shortcut 0x7f070062
int drawable bg_update_dialog 0x7f070063
int drawable bg_window 0x7f070064
int drawable brightness 0x7f070065
int drawable bt_switch_off 0x7f070066
int drawable bt_switch_open 0x7f070067
int drawable btn_checkbox_checked_mtrl 0x7f070068
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070069
int drawable btn_checkbox_unchecked_mtrl 0x7f07006a
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f07006b
int drawable btn_radio_off_mtrl 0x7f07006c
int drawable btn_radio_off_to_on_mtrl_animation 0x7f07006d
int drawable btn_radio_on_mtrl 0x7f07006e
int drawable btn_radio_on_to_off_mtrl_animation 0x7f07006f
int drawable btn_un_click_green 0x7f070070
int drawable close 0x7f070071
int drawable desktop 0x7f070159
int drawable guide_network_error 0x7f070072
int drawable hint_no_control_bg 0x7f070073
int drawable home 0x7f070074
int drawable ic_arrow_down 0x7f070075
int drawable ic_arrow_down_h 0x7f070076
int drawable ic_arrow_left 0x7f070077
int drawable ic_arrow_left_h 0x7f070078
int drawable ic_arrow_left_rgb 0x7f070079
int drawable ic_arrow_right 0x7f07007a
int drawable ic_arrow_right_h 0x7f07007b
int drawable ic_arrow_right_n 0x7f07007c
int drawable ic_arrow_up 0x7f07007d
int drawable ic_arrow_up_h 0x7f07007e
int drawable ic_bright 0x7f07007f
int drawable ic_bright_01 0x7f070080
int drawable ic_bright_02 0x7f070081
int drawable ic_bright_03 0x7f070082
int drawable ic_close 0x7f070083
int drawable ic_close_h 0x7f070084
int drawable ic_desktop_h 0x7f07015a
int drawable ic_desktop_n 0x7f07015b
int drawable ic_desktop_s_h 0x7f07015c
int drawable ic_desktop_s_n 0x7f07015d
int drawable ic_eye_off 0x7f070085
int drawable ic_eye_on 0x7f070086
int drawable ic_failure 0x7f070087
int drawable ic_help_d 0x7f070088
int drawable ic_help_h 0x7f070089
int drawable ic_help_n 0x7f07008a
int drawable ic_home_s 0x7f07008b
int drawable ic_home_s_h 0x7f07008c
int drawable ic_launcher_background 0x7f07008d
int drawable ic_launcher_foreground 0x7f07008e
int drawable ic_maximize_s_h 0x7f07015e
int drawable ic_maximize_s_n 0x7f07015f
int drawable ic_more_h 0x7f07008f
int drawable ic_more_n 0x7f070090
int drawable ic_no_signal 0x7f070091
int drawable ic_password 0x7f070092
int drawable ic_restart_bg 0x7f070093
int drawable ic_restart_disable 0x7f070094
int drawable ic_restart_h 0x7f070095
int drawable ic_restart_n 0x7f070096
int drawable ic_resting_screen_d 0x7f070097
int drawable ic_resting_screen_h 0x7f070098
int drawable ic_resting_screen_n 0x7f070099
int drawable ic_screen_image_d 0x7f07009a
int drawable ic_screen_image_h 0x7f07009b
int drawable ic_screen_image_h1 0x7f07009c
int drawable ic_screen_image_n 0x7f07009d
int drawable ic_screen_image_s 0x7f07009e
int drawable ic_screen_image_s_h 0x7f07009f
int drawable ic_shutdown_bg 0x7f0700a0
int drawable ic_shutdown_disable 0x7f0700a1
int drawable ic_shutdown_h 0x7f0700a2
int drawable ic_shutdown_n 0x7f0700a3
int drawable ic_signal_hdmi_h 0x7f0700a4
int drawable ic_signal_hdmi_n 0x7f0700a5
int drawable ic_signal_type_c_h 0x7f0700a6
int drawable ic_signal_type_c_n 0x7f0700a7
int drawable ic_signal_win_h 0x7f0700a8
int drawable ic_signal_win_n 0x7f0700a9
int drawable ic_status_wrong 0x7f0700aa
int drawable ic_succeed 0x7f0700ab
int drawable ic_voice 0x7f0700ac
int drawable ic_voice_00 0x7f0700ad
int drawable ic_voice_00s 0x7f0700ae
int drawable ic_voice_01 0x7f0700af
int drawable ic_voice_01s 0x7f0700b0
int drawable ic_voice_02 0x7f0700b1
int drawable ic_voice_02s 0x7f0700b2
int drawable ic_voice_03 0x7f0700b3
int drawable ic_voice_03s 0x7f0700b4
int drawable ic_warn 0x7f0700b5
int drawable ic_wifi_1 0x7f0700b6
int drawable ic_wifi_2 0x7f0700b7
int drawable ic_wifi_3 0x7f0700b8
int drawable ic_write_d 0x7f0700b9
int drawable ic_write_h 0x7f0700ba
int drawable ic_write_n 0x7f0700bb
int drawable ic_write_s 0x7f0700bc
int drawable ic_write_s_h 0x7f0700bd
int drawable icon_wifi_signal_lock_level 0x7f0700be
int drawable loading_update 0x7f0700bf
int drawable maximize 0x7f070160
int drawable network_btn_bg 0x7f0700c0
int drawable network_edit_bg 0x7f0700c1
int drawable network_switch_bg 0x7f0700c2
int drawable notification_action_background 0x7f0700c3
int drawable notification_bg 0x7f0700c4
int drawable notification_bg_low 0x7f0700c5
int drawable notification_bg_low_normal 0x7f0700c6
int drawable notification_bg_low_pressed 0x7f0700c7
int drawable notification_bg_normal 0x7f0700c8
int drawable notification_bg_normal_pressed 0x7f0700c9
int drawable notification_icon_background 0x7f0700ca
int drawable notification_template_icon_bg 0x7f0700cb
int drawable notification_template_icon_low_bg 0x7f0700cc
int drawable notification_tile_bg 0x7f0700cd
int drawable notify_panel_notification_icon_bg 0x7f0700ce
int drawable ota_ic_failure 0x7f0700cf
int drawable ota_ic_success 0x7f0700d0
int drawable privacy 0x7f0700d1
int drawable progress_indeterminate_horizontal 0x7f0700d2
int drawable progress_projection_shape 0x7f0700d3
int drawable progress_vertical_gradient_simple_shape 0x7f0700d4
int drawable progress_vertical_gradient_simple_shape1 0x7f0700d5
int drawable progress_vertical_gradient_simple_shape10 0x7f0700d6
int drawable progress_vertical_gradient_simple_shape2 0x7f0700d7
int drawable progress_vertical_gradient_simple_shape3 0x7f0700d8
int drawable progress_vertical_gradient_simple_shape4 0x7f0700d9
int drawable progress_vertical_gradient_simple_shape5 0x7f0700da
int drawable progress_vertical_gradient_simple_shape6 0x7f0700db
int drawable progress_vertical_gradient_simple_shape7 0x7f0700dc
int drawable progress_vertical_gradient_simple_shape8 0x7f0700dd
int drawable progress_vertical_gradient_simple_shape9 0x7f0700de
int drawable progress_vertical_gradient_simple_shape_rgb 0x7f0700df
int drawable rgb_edit_bg 0x7f0700e0
int drawable ruler 0x7f0700e1
int drawable screen 0x7f0700e2
int drawable searching_1 0x7f0700e3
int drawable searching_2 0x7f0700e4
int drawable searching_3 0x7f0700e5
int drawable searching_4 0x7f0700e6
int drawable searching_5 0x7f0700e7
int drawable searching_6 0x7f0700e8
int drawable searching_7 0x7f0700e9
int drawable searching_8 0x7f0700ea
int drawable searching_animation 0x7f0700eb
int drawable sel_shortcut 0x7f0700ec
int drawable select_about_icon 0x7f0700ed
int drawable select_check_update_icon 0x7f0700ee
int drawable select_close_icon 0x7f0700ef
int drawable select_disconnect_network_icon 0x7f0700f0
int drawable select_down_icon 0x7f0700f1
int drawable select_extra_icon 0x7f0700f2
int drawable select_forget_network_icon 0x7f0700f3
int drawable select_left_icon 0x7f0700f4
int drawable select_locale_icon 0x7f0700f5
int drawable select_main_desktop_icon 0x7f070161
int drawable select_main_eye_icon 0x7f0700f6
int drawable select_main_help_icon 0x7f0700f7
int drawable select_main_lock_icon 0x7f0700f8
int drawable select_main_reset_icon 0x7f0700f9
int drawable select_main_restart_icon 0x7f0700fa
int drawable select_main_screen_icon 0x7f0700fb
int drawable select_main_setting_icon 0x7f0700fc
int drawable select_main_shutdown_icon 0x7f0700fd
int drawable select_main_sign_icon 0x7f0700fe
int drawable select_main_write_icon 0x7f0700ff
int drawable select_network_icon 0x7f070100
int drawable select_rgb_left_icon 0x7f070101
int drawable select_right_icon 0x7f070102
int drawable select_signal_hdmi_icon 0x7f070103
int drawable select_signal_typec_icon 0x7f070104
int drawable select_signal_windows_icon 0x7f070105
int drawable select_up_icon 0x7f070106
int drawable select_wifi_icon 0x7f070107
int drawable select_wifi_img_press_icon 0x7f070108
int drawable select_wifi_item_press_color 0x7f070109
int drawable set_ic_about_h 0x7f07010a
int drawable set_ic_about_n 0x7f07010b
int drawable set_ic_checkbox 0x7f07010c
int drawable set_ic_extra_set_h 0x7f07010d
int drawable set_ic_extra_set_n 0x7f07010e
int drawable set_ic_eye_d 0x7f07010f
int drawable set_ic_eye_h 0x7f070110
int drawable set_ic_eye_n 0x7f070111
int drawable set_ic_locale_h 0x7f070112
int drawable set_ic_locale_n 0x7f070113
int drawable set_ic_lock_h 0x7f070114
int drawable set_ic_lock_s 0x7f070115
int drawable set_ic_network_h 0x7f070116
int drawable set_ic_network_n 0x7f070117
int drawable set_ic_setting_d 0x7f070118
int drawable set_ic_setting_h 0x7f070119
int drawable set_ic_setting_n 0x7f07011a
int drawable set_ic_signal_d 0x7f07011b
int drawable set_ic_signal_h 0x7f07011c
int drawable set_ic_signal_n 0x7f07011d
int drawable set_ic_update_h 0x7f07011e
int drawable set_ic_update_n 0x7f07011f
int drawable set_ic_wifi_add 0x7f070120
int drawable set_ic_wifi_add_h 0x7f070121
int drawable set_ic_wifi_delet 0x7f070122
int drawable set_ic_wifi_delet_h 0x7f070123
int drawable set_ic_wifi_disconnect 0x7f070124
int drawable set_ic_wifi_disconnect_h 0x7f070125
int drawable set_ic_wifi_h 0x7f070126
int drawable set_ic_wifi_n 0x7f070127
int drawable shape_adb_btn 0x7f070128
int drawable shape_adb_dialog 0x7f070129
int drawable shape_bg_lock 0x7f07012a
int drawable shape_btn_bg 0x7f07012b
int drawable shape_main_bg 0x7f07012c
int drawable shape_main_progress_bg 0x7f07012d
int drawable shape_main_unclick_bg 0x7f07012e
int drawable shape_network_auto_manual 0x7f07012f
int drawable shape_network_btn_click 0x7f070130
int drawable shape_network_btn_unclick 0x7f070131
int drawable shape_network_edit_focused 0x7f070132
int drawable shape_network_edit_normal 0x7f070133
int drawable shape_network_edt_cursor 0x7f070134
int drawable shape_network_scrollview 0x7f070135
int drawable shape_network_toast_bg 0x7f070136
int drawable shape_no_sign_btn_green 0x7f070137
int drawable shape_no_sign_btn_white 0x7f070138
int drawable shape_rgb_edit_focused 0x7f070139
int drawable shape_rgb_edit_normal 0x7f07013a
int drawable shape_screen_dialog 0x7f07013b
int drawable shape_screen_dialog_wireless_disabled 0x7f07013c
int drawable shape_shadow_progress 0x7f07013d
int drawable shape_shutdown_btn_green 0x7f07013e
int drawable shape_shutdown_btn_white 0x7f07013f
int drawable shape_signal_bg 0x7f070140
int drawable shape_tvview_bg 0x7f070141
int drawable shape_windows_host_bg 0x7f070142
int drawable shutdown 0x7f070143
int drawable spinner_background 0x7f070144
int drawable step_1 0x7f070145
int drawable step_2 0x7f070146
int drawable step_3 0x7f070147
int drawable sw_adb_bg 0x7f070148
int drawable sw_adb_off 0x7f070149
int drawable sw_adb_on 0x7f07014a
int drawable thumb_rgb 0x7f07014b
int drawable toast_resolution_bg 0x7f07014c
int drawable tooltip_frame_dark 0x7f07014d
int drawable tooltip_frame_light 0x7f07014e
int drawable transcreen_app 0x7f07014f
int drawable tv_text_color 0x7f070150
int drawable uhd_hdmi 0x7f070151
int drawable uhd_type_c 0x7f070152
int drawable volume_0 0x7f070153
int drawable volume_1 0x7f070154
int drawable volume_2 0x7f070155
int drawable volume_3 0x7f070156
int drawable wifi_edt_password 0x7f070157
int drawable window_arror 0x7f070158
int id ALT 0x7f080000
int id CTRL 0x7f080001
int id FUNCTION 0x7f080002
int id META 0x7f080003
int id SHIFT 0x7f080004
int id SYM 0x7f080005
int id accessibility_action_clickable_span 0x7f080006
int id accessibility_custom_action_0 0x7f080007
int id accessibility_custom_action_1 0x7f080008
int id accessibility_custom_action_10 0x7f080009
int id accessibility_custom_action_11 0x7f08000a
int id accessibility_custom_action_12 0x7f08000b
int id accessibility_custom_action_13 0x7f08000c
int id accessibility_custom_action_14 0x7f08000d
int id accessibility_custom_action_15 0x7f08000e
int id accessibility_custom_action_16 0x7f08000f
int id accessibility_custom_action_17 0x7f080010
int id accessibility_custom_action_18 0x7f080011
int id accessibility_custom_action_19 0x7f080012
int id accessibility_custom_action_2 0x7f080013
int id accessibility_custom_action_20 0x7f080014
int id accessibility_custom_action_21 0x7f080015
int id accessibility_custom_action_22 0x7f080016
int id accessibility_custom_action_23 0x7f080017
int id accessibility_custom_action_24 0x7f080018
int id accessibility_custom_action_25 0x7f080019
int id accessibility_custom_action_26 0x7f08001a
int id accessibility_custom_action_27 0x7f08001b
int id accessibility_custom_action_28 0x7f08001c
int id accessibility_custom_action_29 0x7f08001d
int id accessibility_custom_action_3 0x7f08001e
int id accessibility_custom_action_30 0x7f08001f
int id accessibility_custom_action_31 0x7f080020
int id accessibility_custom_action_4 0x7f080021
int id accessibility_custom_action_5 0x7f080022
int id accessibility_custom_action_6 0x7f080023
int id accessibility_custom_action_7 0x7f080024
int id accessibility_custom_action_8 0x7f080025
int id accessibility_custom_action_9 0x7f080026
int id action_bar 0x7f080027
int id action_bar_activity_content 0x7f080028
int id action_bar_container 0x7f080029
int id action_bar_root 0x7f08002a
int id action_bar_spinner 0x7f08002b
int id action_bar_subtitle 0x7f08002c
int id action_bar_title 0x7f08002d
int id action_container 0x7f08002e
int id action_context_bar 0x7f08002f
int id action_divider 0x7f080030
int id action_image 0x7f080031
int id action_menu_divider 0x7f080032
int id action_menu_presenter 0x7f080033
int id action_mode_bar 0x7f080034
int id action_mode_bar_stub 0x7f080035
int id action_mode_close_button 0x7f080036
int id action_text 0x7f080037
int id actions 0x7f080038
int id activity_chooser_view_content 0x7f080039
int id add 0x7f08003a
int id alertTitle 0x7f08003b
int id all 0x7f08003c
int id always 0x7f08003d
int id async 0x7f08003e
int id barrier 0x7f08003f
int id beginning 0x7f080040
int id bg_projection 0x7f080041
int id blocking 0x7f080042
int id bottom 0x7f080043
int id btn_cancel 0x7f080044
int id btn_confirm 0x7f080045
int id btn_debug 0x7f080046
int id btn_factory 0x7f080047
int id btn_install 0x7f080048
int id btn_reboot 0x7f080049
int id btn_reinstall 0x7f08004a
int id btn_reset 0x7f08004b
int id btn_restart 0x7f08004c
int id btn_right_update 0x7f08004d
int id btn_shutdown 0x7f08004e
int id btn_start 0x7f08004f
int id btn_touch 0x7f080050
int id buttonPanel 0x7f080051
int id center 0x7f080052
int id center_horizontal 0x7f080053
int id center_vertical 0x7f080054
int id chains 0x7f080055
int id checkbox 0x7f080056
int id checked 0x7f080057
int id chronometer 0x7f080058
int id cl_right_update 0x7f080059
int id clip_horizontal 0x7f08005a
int id clip_vertical 0x7f08005b
int id collapseActionView 0x7f08005c
int id content 0x7f08005d
int id contentPanel 0x7f08005e
int id custom 0x7f08005f
int id customPanel 0x7f080060
int id cv_bright 0x7f080061
int id cv_screen 0x7f080062
int id cv_vg_setting 0x7f080063
int id cv_wired_screen 0x7f080064
int id cv_wireless_screen 0x7f080065
int id cv_write 0x7f080066
int id dashLine 0x7f080067
int id decor_content_parent 0x7f080068
int id default_activity_button 0x7f080069
int id dialog_button 0x7f08006a
int id dimensions 0x7f08006b
int id direct 0x7f08006c
int id disableHome 0x7f08006d
int id edit_query 0x7f08006e
int id edt_DNS 0x7f08006f
int id edt_dns1 0x7f080070
int id edt_dns2 0x7f080071
int id edt_focus 0x7f080072
int id edt_gateway 0x7f080073
int id edt_ip_address 0x7f080074
int id edt_mask_address 0x7f080075
int id edt_password 0x7f080076
int id edt_subnet_mask 0x7f080077
int id end 0x7f080078
int id et_blue_gain 0x7f080079
int id et_color_temperature 0x7f08007a
int id et_green_gain 0x7f08007b
int id et_red_gain 0x7f08007c
int id expand_activities_button 0x7f08007d
int id expanded_menu 0x7f08007e
int id fill 0x7f08007f
int id fill_horizontal 0x7f080080
int id fill_vertical 0x7f080081
int id fl_annotate 0x7f080082
int id fl_behind_hdmi1 0x7f080083
int id fl_behind_hdmi2 0x7f080084
int id fl_desktop 0x7f0801f8
int id fl_disconnect 0x7f080085
int id fl_eye 0x7f080086
int id fl_forget 0x7f080087
int id fl_fragment 0x7f080088
int id fl_maximize 0x7f0801f9
int id fl_rest 0x7f08008b
int id fl_restart 0x7f08008c
int id fl_screen 0x7f08008d
int id fl_setting 0x7f08008e
int id fl_shutdown 0x7f08008f
int id fl_signal 0x7f080090
int id fl_touch_lock 0x7f080091
int id fl_typec 0x7f080092
int id fl_windows 0x7f080093
int id forever 0x7f080094
int id frame 0x7f080095
int id glide_custom_view_target_tag 0x7f080096
int id gone 0x7f080097
int id group_divider 0x7f080098
int id groups 0x7f080099
int id home 0x7f08009a
int id homeAsUp 0x7f08009b
int id icon 0x7f08009c
int id icon_group 0x7f08009d
int id ifRoom 0x7f08009e
int id image 0x7f08009f
int id img_DNS 0x7f0800a0
int id img_arrow 0x7f0800a1
int id img_auto 0x7f0800a2
int id img_back 0x7f0800a3
int id img_blur 0x7f0800a4
int id img_bright 0x7f0800a5
int id img_check 0x7f0800a6
int id img_dns1 0x7f0800a7
int id img_dns2 0x7f0800a8
int id img_eye 0x7f0800a9
int id img_gateway 0x7f0800aa
int id img_ip 0x7f0800ab
int id img_manual 0x7f0800ac
int id img_mask 0x7f0800ad
int id img_more 0x7f0800ae
int id img_no_sign 0x7f0800af
int id img_password 0x7f0800b0
int id img_rank 0x7f0800b1
int id img_restart 0x7f0800b2
int id img_shutdown 0x7f0800b3
int id img_subnet_mask 0x7f0800b4
int id img_voice 0x7f0800b5
int id img_wifi 0x7f0800b6
int id img_window 0x7f0800b7
int id info 0x7f0800b8
int id invisible 0x7f0800b9
int id italic 0x7f0800ba
int id item_touch_helper_previous_elevation 0x7f0800bb
int id iv_annotate 0x7f0800bc
int id iv_back 0x7f0800bd
int id iv_behind_hdmi1 0x7f0800be
int id iv_behind_hdmi2 0x7f0800bf
int id iv_close 0x7f0800c0
int id iv_code 0x7f0800c1
int id iv_desktop 0x7f0801fa
int id iv_disconnect 0x7f0800c2
int id iv_eye 0x7f0800c3
int id iv_forget 0x7f0800c4
int id iv_lock 0x7f0800c7
int id iv_maximize 0x7f0801fb
int id iv_rest 0x7f0800c8
int id iv_restart 0x7f0800c9
int id iv_ruler 0x7f0800ca
int id iv_screen 0x7f0800cb
int id iv_select 0x7f0800cc
int id iv_setting 0x7f0800cd
int id iv_shutdown 0x7f0800ce
int id iv_shutting_down_ops 0x7f0800cf
int id iv_signal 0x7f0800d0
int id iv_step_1 0x7f0800d1
int id iv_step_2 0x7f0800d2
int id iv_step_3 0x7f0800d3
int id iv_touch_lock 0x7f0800d4
int id iv_typec 0x7f0800d5
int id iv_uhd 0x7f0800d6
int id iv_volume 0x7f0800d7
int id iv_windows 0x7f0800d8
int id iv_write 0x7f0800d9
int id left 0x7f0800da
int id line 0x7f0800db
int id line1 0x7f0800dc
int id line2 0x7f0800dd
int id line3 0x7f0800de
int id line4 0x7f0800df
int id listMode 0x7f0800e0
int id list_item 0x7f0800e1
int id ll_auto 0x7f0800e2
int id ll_btn 0x7f0800e3
int id ll_check_update 0x7f0800e4
int id ll_dns1 0x7f0800e5
int id ll_dns2 0x7f0800e6
int id ll_download 0x7f0800e7
int id ll_gateway 0x7f0800e8
int id ll_install 0x7f0800e9
int id ll_ip 0x7f0800ea
int id ll_ip_mode 0x7f0800eb
int id ll_mac_address 0x7f0800ec
int id ll_manual 0x7f0800ed
int id ll_mask 0x7f0800ee
int id ll_network 0x7f0800ef
int id ll_not_network 0x7f0800f0
int id ll_select 0x7f0800f1
int id ll_switch 0x7f0800f2
int id ll_system_version 0x7f0800f3
int id ll_title 0x7f0800f4
int id ll_touch_lock 0x7f0800f5
int id ll_windows_host 0x7f0800f6
int id message 0x7f0800f7
int id middle 0x7f0800f8
int id multiply 0x7f0800f9
int id never 0x7f0800fa
int id none 0x7f0800fb
int id normal 0x7f0800fc
int id notification_background 0x7f0800fd
int id notification_main_column 0x7f0800fe
int id notification_main_column_container 0x7f0800ff
int id off 0x7f080100
int id on 0x7f080101
int id packed 0x7f080102
int id parent 0x7f080103
int id parentPanel 0x7f080104
int id percent 0x7f080105
int id pressed 0x7f080106
int id progress_circular 0x7f080107
int id progress_download 0x7f080108
int id progress_horizontal 0x7f080109
int id progressbar_check_update 0x7f08010a
int id r30_write_speed 0x7f08010b
int id radio 0x7f08010c
int id right 0x7f08010d
int id right_icon 0x7f08010e
int id right_side 0x7f08010f
int id ripple 0x7f080110
int id rlBg 0x7f080111
int id rl_adb 0x7f080112
int id rl_color_temperature_adjust 0x7f080113
int id rl_factory_reset 0x7f080114
int id rl_hardware_self_test 0x7f080115
int id rl_main 0x7f080116
int id rl_manual 0x7f080117
int id rl_no_sign 0x7f080118
int id rl_pen 0x7f080119
int id rl_privacy_policy 0x7f08011a
int id rl_r30_write_speed 0x7f08011b
int id rl_screen 0x7f08011c
int id rl_speed 0x7f08011d
int id rl_sub_wired_screen 0x7f08011e
int id rl_sub_wireless_screen 0x7f08011f
int id rl_touch_slider 0x7f080120
int id rl_tvview 0x7f080121
int id rl_windows_disable 0x7f080122
int id rl_windows_task_manager 0x7f080123
int id rl_wired_screen 0x7f080124
int id rl_wireless_screen 0x7f080125
int id rv_connected 0x7f080126
int id rv_wifi_list 0x7f080127
int id sb_blue_gain 0x7f080128
int id sb_bright 0x7f080129
int id sb_color_temperature 0x7f08012a
int id sb_green_gain 0x7f08012b
int id sb_red_gain 0x7f08012c
int id sb_volume 0x7f08012d
int id screen 0x7f08012e
int id scrollIndicatorDown 0x7f08012f
int id scrollIndicatorUp 0x7f080130
int id scrollView 0x7f080131
int id scrollview 0x7f080132
int id search_badge 0x7f080133
int id search_bar 0x7f080134
int id search_button 0x7f080135
int id search_close_btn 0x7f080136
int id search_edit_frame 0x7f080137
int id search_go_btn 0x7f080138
int id search_mag_icon 0x7f080139
int id search_plate 0x7f08013a
int id search_src_text 0x7f08013b
int id search_voice_btn 0x7f08013c
int id select_dialog_listview 0x7f08013d
int id selected 0x7f08013e
int id shortcut 0x7f08013f
int id showCustom 0x7f080140
int id showHome 0x7f080141
int id showTitle 0x7f080142
int id sl_about 0x7f080143
int id sl_check_update 0x7f080144
int id sl_extra 0x7f080145
int id sl_locale 0x7f080146
int id sl_network 0x7f080147
int id sl_wifi 0x7f080148
int id spacer 0x7f080149
int id spinner_language 0x7f08014a
int id spinner_startup_channel 0x7f08014b
int id split_action_bar 0x7f08014c
int id spread 0x7f08014d
int id spread_inside 0x7f08014e
int id src_atop 0x7f08014f
int id src_in 0x7f080150
int id src_over 0x7f080151
int id standard 0x7f080152
int id start 0x7f080153
int id submenuarrow 0x7f080154
int id submit_area 0x7f080155
int id sv_privacy 0x7f080156
int id sv_update_description 0x7f080157
int id sw_adb 0x7f080158
int id sw_breath_led_on 0x7f080159
int id sw_enable_pin_code 0x7f08015a
int id sw_enable_wireless_screen 0x7f08015b
int id sw_network 0x7f08015c
int id sw_pen 0x7f08015d
int id sw_speed 0x7f08015e
int id sw_touch_slider 0x7f08015f
int id sw_windows_disable 0x7f080160
int id sw_write_without_screen_on 0x7f080161
int id tabMode 0x7f080162
int id tag_accessibility_actions 0x7f080163
int id tag_accessibility_clickable_spans 0x7f080164
int id tag_accessibility_heading 0x7f080165
int id tag_accessibility_pane_title 0x7f080166
int id tag_screen_reader_focusable 0x7f080167
int id tag_transition_group 0x7f080168
int id tag_unhandled_key_event_manager 0x7f080169
int id tag_unhandled_key_listeners 0x7f08016a
int id text 0x7f08016b
int id text2 0x7f08016c
int id textSpacerNoButtons 0x7f08016d
int id textSpacerNoTitle 0x7f08016e
int id time 0x7f08016f
int id title 0x7f080170
int id titleDividerNoCustom 0x7f080171
int id title_template 0x7f080172
int id top 0x7f080173
int id topPanel 0x7f080174
int id tv_adb 0x7f080175
int id tv_annotate 0x7f080176
int id tv_blue_gain 0x7f080177
int id tv_code 0x7f080178
int id tv_color_temperature 0x7f080179
int id tv_color_temperature_adjust 0x7f08017a
int id tv_color_temperature_cold 0x7f08017b
int id tv_color_temperature_warm 0x7f08017c
int id tv_content 0x7f08017d
int id tv_cpu 0x7f08017e
int id tv_cpu_test_result 0x7f08017f
int id tv_desktop 0x7f0801fc
int id tv_enable_pin_code 0x7f080180
int id tv_enable_wireless_screen 0x7f080181
int id tv_ethernet 0x7f080182
int id tv_ethernet_test_result 0x7f080183
int id tv_factory_reset 0x7f080184
int id tv_green_gain 0x7f080185
int id tv_hard_disk 0x7f080186
int id tv_hard_disk_test_result 0x7f080187
int id tv_hardware_self_test 0x7f080188
int id tv_hint 0x7f08018a
int id tv_installing 0x7f08018c
int id tv_item 0x7f08018d
int id tv_language 0x7f08018e
int id tv_maximize 0x7f0801fd
int id tv_memory 0x7f08018f
int id tv_memory_test_result 0x7f080190
int id tv_mic 0x7f080191
int id tv_mic_test_result 0x7f080192
int id tv_pen 0x7f080193
int id tv_pin_code 0x7f080194
int id tv_player 0x7f080195
int id tv_r30_write_speed 0x7f080196
int id tv_red_gain 0x7f080197
int id tv_reset_ops 0x7f080198
int id tv_resolution 0x7f080199
int id tv_rest 0x7f08019a
int id tv_restart 0x7f08019b
int id tv_screen 0x7f08019c
int id tv_screen_activation_status 0x7f08019d
int id tv_screen_offset 0x7f08019e
int id tv_shutdown 0x7f08019f
int id tv_shutting_down_ops 0x7f0801a0
int id tv_speed 0x7f0801a1
int id tv_startup_channel 0x7f0801a2
int id tv_step_1 0x7f0801a3
int id tv_step_2 0x7f0801a4
int id tv_step_3 0x7f0801a5
int id tv_title 0x7f0801a6
int id tv_title_wired_screen 0x7f0801a7
int id tv_title_wireless_screen 0x7f0801a8
int id tv_touch 0x7f0801a9
int id tv_touch_test_result 0x7f0801aa
int id tv_uhd 0x7f0801ab
int id tv_volume 0x7f0801ac
int id tv_wifi 0x7f0801ad
int id tv_wifi_test_result 0x7f0801ae
int id tv_windows_task_manager 0x7f0801af
int id txt_about 0x7f0801b0
int id txt_android_version 0x7f0801b1
int id txt_android_version_title 0x7f0801b2
int id txt_auto 0x7f0801b3
int id txt_behind_hdmi1 0x7f0801b4
int id txt_behind_hdmi2 0x7f0801b5
int id txt_company 0x7f0801b6
int id txt_company_title 0x7f0801b7
int id txt_content 0x7f0801b8
int id txt_disconnect 0x7f0801b9
int id txt_email 0x7f0801ba
int id txt_email_title 0x7f0801bb
int id txt_extra 0x7f0801bc
int id txt_eye 0x7f0801bd
int id txt_forget 0x7f0801be
int id txt_hotline 0x7f0801bf
int id txt_hotline_title 0x7f0801c0
int id txt_ip_address 0x7f0801c1
int id txt_ip_address_title 0x7f0801c2
int id txt_ip_setting 0x7f0801c3
int id txt_locale 0x7f0801c4
int id txt_lock 0x7f0801c5
int id txt_mac_address 0x7f0801c6
int id txt_manual 0x7f0801c7
int id txt_msg 0x7f0801c8
int id txt_msg_phone 0x7f0801c9
int id txt_name 0x7f0801ca
int id txt_name_title 0x7f0801cb
int id txt_network 0x7f0801cc
int id txt_no_network 0x7f0801cd
int id txt_no_pc 0x7f0801ce
int id txt_no_sign 0x7f0801cf
int id txt_other_network 0x7f0801d0
int id txt_password_error 0x7f0801d1
int id txt_resolution 0x7f0801d2
int id txt_resolution_title 0x7f0801d3
int id txt_right_update 0x7f0801d4
int id txt_save 0x7f0801d5
int id txt_screen 0x7f0801d6
int id txt_serial 0x7f0801d7
int id txt_serial_title 0x7f0801d8
int id txt_setting 0x7f0801d9
int id txt_signal 0x7f0801da
int id txt_state 0x7f0801db
int id txt_store_total 0x7f0801dc
int id txt_store_total_title 0x7f0801dd
int id txt_system_version 0x7f0801de
int id txt_title 0x7f0801df
int id txt_title_update_description 0x7f0801e0
int id txt_total 0x7f0801e1
int id txt_total_title 0x7f0801e2
int id txt_touch_lock 0x7f0801e3
int id txt_touch_version 0x7f0801e4
int id txt_touch_version_title 0x7f0801e5
int id txt_typec 0x7f0801e6
int id txt_update 0x7f0801e7
int id txt_update_description 0x7f0801e8
int id txt_version_msg 0x7f0801e9
int id txt_wifi 0x7f0801ea
int id txt_wifi_behind_str 0x7f0801eb
int id txt_wifi_front_str 0x7f0801ec
int id txt_wifi_name 0x7f0801ed
int id txt_windows 0x7f0801ee
int id txt_write 0x7f0801ef
int id unchecked 0x7f0801f0
int id uniform 0x7f0801f1
int id up 0x7f0801f2
int id useLogo 0x7f0801f3
int id webView 0x7f0801f4
int id withText 0x7f0801f5
int id wrap 0x7f0801f6
int id wrap_content 0x7f0801f7
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer cancel_button_image_alpha 0x7f090002
int integer config_tooltipAnimTime 0x7f090003
int integer status_bar_notification_info_maxnum 0x7f090004
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout activity_blur 0x7f0b001c
int layout activity_hardware_self_test 0x7f0b001d
int layout activity_main 0x7f0b001e
int layout activity_main_desktop 0x7f0b0056
int layout activity_privacy 0x7f0b001f
int layout activity_restart 0x7f0b0020
int layout activity_setting 0x7f0b0021
int layout activity_shutdown 0x7f0b0022
int layout activity_signal 0x7f0b0023
int layout custom_dialog 0x7f0b0024
int layout dialog_adb 0x7f0b0025
int layout dialog_factory_reset 0x7f0b0026
int layout dialog_have_update 0x7f0b0027
int layout dialog_install_fail 0x7f0b0028
int layout dialog_installing 0x7f0b0029
int layout dialog_network_auto_manual 0x7f0b002a
int layout dialog_reset_ops 0x7f0b002b
int layout dialog_restart 0x7f0b002c
int layout dialog_rgb 0x7f0b002d
int layout dialog_shutdown 0x7f0b002e
int layout dialog_shutdown_countdown 0x7f0b002f
int layout dialog_systemsetting_window_host 0x7f0b0030
int layout dialog_touch_lock 0x7f0b0031
int layout dialog_update_fail 0x7f0b0032
int layout dialog_update_success 0x7f0b0033
int layout dialog_updating 0x7f0b0034
int layout dialog_volume 0x7f0b0035
int layout fragment_about 0x7f0b0036
int layout fragment_extra 0x7f0b0037
int layout fragment_locale 0x7f0b0038
int layout fragment_network 0x7f0b0039
int layout fragment_update 0x7f0b003a
int layout fragment_wifi 0x7f0b003b
int layout item_wifi 0x7f0b003c
int layout item_wifi_connect 0x7f0b003d
int layout item_wifi_more 0x7f0b003e
int layout launcher_main 0x7f0b003f
int layout notification_action 0x7f0b0040
int layout notification_action_tombstone 0x7f0b0041
int layout notification_template_custom_big 0x7f0b0042
int layout notification_template_icon_group 0x7f0b0043
int layout notification_template_part_chronometer 0x7f0b0044
int layout notification_template_part_time 0x7f0b0045
int layout screen_dialog 0x7f0b0046
int layout screen_offset_view 0x7f0b0047
int layout select_dialog_item_material 0x7f0b0048
int layout select_dialog_multichoice_material 0x7f0b0049
int layout select_dialog_singlechoice_material 0x7f0b004a
int layout small_window_shortcut 0x7f0b004b
int layout spinner_dropdown_item 0x7f0b004c
int layout spinner_item 0x7f0b004d
int layout support_simple_spinner_dropdown_item 0x7f0b004e
int layout toast_fail_bg 0x7f0b004f
int layout toast_resolution 0x7f0b0050
int layout toast_shutting_down_ops 0x7f0b0051
int layout toast_success_bg 0x7f0b0052
int layout toast_uhd 0x7f0b0053
int layout wifi_dialog_input_password 0x7f0b0054
int layout window_ruler 0x7f0b0055
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_round 0x7f0c0001
int string DNS 0x7f0d0000
int string DNS1 0x7f0d0001
int string DNS2 0x7f0d0002
int string abc_action_bar_home_description 0x7f0d0003
int string abc_action_bar_up_description 0x7f0d0004
int string abc_action_menu_overflow_description 0x7f0d0005
int string abc_action_mode_done 0x7f0d0006
int string abc_activity_chooser_view_see_all 0x7f0d0007
int string abc_activitychooserview_choose_application 0x7f0d0008
int string abc_capital_off 0x7f0d0009
int string abc_capital_on 0x7f0d000a
int string abc_menu_alt_shortcut_label 0x7f0d000b
int string abc_menu_ctrl_shortcut_label 0x7f0d000c
int string abc_menu_delete_shortcut_label 0x7f0d000d
int string abc_menu_enter_shortcut_label 0x7f0d000e
int string abc_menu_function_shortcut_label 0x7f0d000f
int string abc_menu_meta_shortcut_label 0x7f0d0010
int string abc_menu_shift_shortcut_label 0x7f0d0011
int string abc_menu_space_shortcut_label 0x7f0d0012
int string abc_menu_sym_shortcut_label 0x7f0d0013
int string abc_prepend_shortcut_label 0x7f0d0014
int string abc_search_hint 0x7f0d0015
int string abc_searchview_description_clear 0x7f0d0016
int string abc_searchview_description_query 0x7f0d0017
int string abc_searchview_description_search 0x7f0d0018
int string abc_searchview_description_submit 0x7f0d0019
int string abc_searchview_description_voice 0x7f0d001a
int string abc_shareactionprovider_share_with 0x7f0d001b
int string abc_shareactionprovider_share_with_application 0x7f0d001c
int string abc_toolbar_collapse_description 0x7f0d001d
int string about 0x7f0d001e
int string adb 0x7f0d001f
int string adb_title 0x7f0d0020
int string android_version 0x7f0d0021
int string annotate 0x7f0d0022
int string app_name 0x7f0d0023
int string app_name_ota 0x7f0d0024
int string auto 0x7f0d0025
int string auto_update 0x7f0d0026
int string behind_hdmi1 0x7f0d0027
int string behind_hdmi2 0x7f0d0028
int string breath_led_on 0x7f0d0029
int string cancel 0x7f0d002a
int string cancel_download 0x7f0d002b
int string cancel_update_fail 0x7f0d002c
int string change_to_pc 0x7f0d002d
int string check_network 0x7f0d002e
int string check_update 0x7f0d002f
int string checking_update 0x7f0d0030
int string click_lock 0x7f0d0031
int string color_temperature 0x7f0d0032
int string color_temperature_adjust 0x7f0d0033
int string color_temperature_cold 0x7f0d0034
int string color_temperature_eye_care 0x7f0d0035
int string color_temperature_value_wrong 0x7f0d0036
int string color_temperature_warm 0x7f0d0037
int string company 0x7f0d0038
int string company_name 0x7f0d0039
int string confirm 0x7f0d003a
int string confirm_update_fail 0x7f0d003b
int string confirm_update_success 0x7f0d003c
int string connect_fail 0x7f0d003d
int string current_sign 0x7f0d003e
int string current_sign_hdmi 0x7f0d003f
int string current_sign_hdmi_1 0x7f0d0040
int string current_sign_hdmi_2 0x7f0d0041
int string current_sign_typec 0x7f0d0042
int string current_sign_windows_disabled 0x7f0d0043
int string debug_menu 0x7f0d0044
int string desktop 0x7f0d0111
int string device_name 0x7f0d0045
int string disconnect 0x7f0d0046
int string disconnect_network 0x7f0d0047
int string download 0x7f0d0048
int string dynrec_fail 0x7f0d0049
int string eeo_screen_move_txt 0x7f0d004a
int string email 0x7f0d004b
int string english 0x7f0d004c
int string error_md5 0x7f0d004d
int string extra 0x7f0d004e
int string eye 0x7f0d004f
int string factory_menu 0x7f0d0050
int string factory_reset 0x7f0d0051
int string file_not_exist 0x7f0d0052
int string finish_lock 0x7f0d0053
int string forget_network 0x7f0d0054
int string front_typec 0x7f0d0055
int string gateway 0x7f0d0056
int string hardware_self_test 0x7f0d0057
int string hardware_self_test_CPU 0x7f0d0058
int string hardware_self_test_ethernet 0x7f0d0059
int string hardware_self_test_fail 0x7f0d005a
int string hardware_self_test_hard_disk 0x7f0d005b
int string hardware_self_test_memory 0x7f0d005c
int string hardware_self_test_mic 0x7f0d005d
int string hardware_self_test_no_test 0x7f0d005e
int string hardware_self_test_success 0x7f0d005f
int string hardware_self_test_testing 0x7f0d0060
int string hardware_self_test_touch 0x7f0d0061
int string hardware_self_test_wifi 0x7f0d0062
int string have_update 0x7f0d0063
int string have_update_hint 0x7f0d0064
int string help 0x7f0d0065
int string hint_no_control 0x7f0d0066
int string home 0x7f0d0067
int string hotline 0x7f0d0068
int string install_fail 0x7f0d0069
int string install_fail_reinstall 0x7f0d006a
int string install_ing 0x7f0d006b
int string install_msg 0x7f0d006c
int string install_right 0x7f0d006d
int string install_title 0x7f0d006e
int string ip_address 0x7f0d006f
int string ip_setting 0x7f0d0070
int string join 0x7f0d0071
int string language_and_locale 0x7f0d0072
int string language_change 0x7f0d0073
int string lastest_version 0x7f0d0074
int string lock 0x7f0d0075
int string login 0x7f0d0076
int string login_cancel 0x7f0d0077
int string login_title 0x7f0d0078
int string mac_address 0x7f0d0079
int string manual 0x7f0d007a
int string maximize 0x7f0d0112
int string network 0x7f0d007b
int string network_error 0x7f0d007c
int string network_wifi_status_authenticating 0x7f0d007d
int string network_wifi_status_blocked 0x7f0d007e
int string network_wifi_status_connected 0x7f0d007f
int string network_wifi_status_connected_no_internet 0x7f0d0080
int string network_wifi_status_connecting 0x7f0d0081
int string network_wifi_status_disabled 0x7f0d0082
int string network_wifi_status_disconnected 0x7f0d0083
int string network_wifi_status_disconnecting 0x7f0d0084
int string network_wifi_status_failed 0x7f0d0085
int string network_wifi_status_idle 0x7f0d0086
int string network_wifi_status_network_failure 0x7f0d0087
int string network_wifi_status_obtaining_ip_address 0x7f0d0088
int string network_wifi_status_password_failure 0x7f0d0089
int string network_wifi_status_saved 0x7f0d008a
int string network_wifi_status_scanning 0x7f0d008b
int string network_wifi_status_suspended 0x7f0d008c
int string network_wifi_status_verifying_poor_link 0x7f0d008d
int string network_wifi_status_wifi_failure 0x7f0d008e
int string no_network 0x7f0d008f
int string no_pc_1 0x7f0d0090
int string no_pc_2 0x7f0d0091
int string no_sign 0x7f0d0092
int string no_sign_hdmi 0x7f0d0093
int string no_sign_msg 0x7f0d0094
int string no_sign_windows_disabled 0x7f0d0095
int string not_found_new_version 0x7f0d0096
int string not_network 0x7f0d0097
int string password 0x7f0d0098
int string password_error 0x7f0d0099
int string privacy 0x7f0d009a
int string privacy_policy 0x7f0d009b
int string projection 0x7f0d009c
int string r30_write_speed 0x7f0d009d
int string ready_install 0x7f0d009e
int string reinstall 0x7f0d009f
int string remaining 0x7f0d00a0
int string report_version_timeout 0x7f0d00a1
int string reset_ops 0x7f0d00a2
int string resolution 0x7f0d00a3
int string restart 0x7f0d00a4
int string restart_content 0x7f0d00a5
int string restart_countdown_confirm 0x7f0d00a6
int string restart_title 0x7f0d00a7
int string resting 0x7f0d00a8
int string retry 0x7f0d00a9
int string rgb_blue_gain 0x7f0d00aa
int string rgb_blue_gain_value_wrong 0x7f0d00ab
int string rgb_green_gain 0x7f0d00ac
int string rgb_green_gain_value_wrong 0x7f0d00ad
int string rgb_red_gain 0x7f0d00ae
int string rgb_red_gain_value_wrong 0x7f0d00af
int string rgb_reset 0x7f0d00b0
int string right_update 0x7f0d00b1
int string running_memory 0x7f0d00b2
int string save 0x7f0d00b3
int string saved_network 0x7f0d00b4
int string screen_activation_status_activated 0x7f0d00b5
int string screen_activation_status_getting 0x7f0d00b6
int string screen_activation_status_nonactivated 0x7f0d00b7
int string screen_air_play 0x7f0d00b8
int string screen_msg1 0x7f0d00b9
int string screen_name 0x7f0d00ba
int string screen_permission_refuse 0x7f0d00bb
int string screen_pin_code 0x7f0d00bc
int string screen_pin_code2 0x7f0d00bd
int string screen_step_1 0x7f0d00be
int string screen_step_2 0x7f0d00bf
int string screen_step_3 0x7f0d00c0
int string screen_text 0x7f0d00c1
int string screen_title 0x7f0d00c2
int string screen_wifi_connect 0x7f0d00c3
int string screen_wifi_eeo_guest 0x7f0d00c4
int string screen_wifi_eeo_password 0x7f0d00c5
int string screen_wifi_name 0x7f0d00c6
int string screen_wifi_password 0x7f0d00c7
int string search_menu_title 0x7f0d00c8
int string select_network 0x7f0d00c9
int string serial_number 0x7f0d00ca
int string service_email 0x7f0d00cb
int string service_hotline 0x7f0d00cc
int string setting 0x7f0d00cd
int string shutdown 0x7f0d00ce
int string shutdown_content 0x7f0d00cf
int string shutdown_countdown_cancel 0x7f0d00d0
int string shutdown_countdown_confirm 0x7f0d00d1
int string shutdown_countdown_content 0x7f0d00d2
int string shutdown_countdown_title 0x7f0d00d3
int string shutdown_title 0x7f0d00d4
int string sign 0x7f0d00d5
int string simplified_chinese 0x7f0d00d6
int string start_computer 0x7f0d00d7
int string startup_channel 0x7f0d00d8
int string status_bar_notification_info_overflow 0x7f0d00d9
int string store_memory 0x7f0d00da
int string stroke_algorithm 0x7f0d00db
int string sub_device_update_fail 0x7f0d00dc
int string sub_device_update_success 0x7f0d00dd
int string subnet_mask 0x7f0d00de
int string switch_wifi 0x7f0d00df
int string system_lastest_version 0x7f0d00e0
int string system_version 0x7f0d00e1
int string toast_fail 0x7f0d00e2
int string toast_resolution 0x7f0d00e3
int string toast_set_ip_fail 0x7f0d00e4
int string toast_shutting_down_ops 0x7f0d00e5
int string toast_success 0x7f0d00e6
int string total 0x7f0d00e7
int string touch_calibration 0x7f0d00e8
int string touch_slider 0x7f0d00e9
int string touch_unplugged 0x7f0d00ea
int string touch_version 0x7f0d00eb
int string uhd_hdmi 0x7f0d00ec
int string uhd_type_c 0x7f0d00ed
int string unlock 0x7f0d00ee
int string updatable 0x7f0d00ef
int string update_description 0x7f0d00f0
int string update_fail 0x7f0d00f1
int string update_fail_content 0x7f0d00f2
int string update_msg 0x7f0d00f3
int string update_success 0x7f0d00f4
int string update_success_content 0x7f0d00f5
int string updating 0x7f0d00f6
int string updating_touch 0x7f0d00f7
int string wifi 0x7f0d00f8
int string wifi_behind_str 0x7f0d00f9
int string wifi_device_error 0x7f0d00fa
int string wifi_front_str 0x7f0d00fb
int string wifi_more 0x7f0d00fc
int string wifi_network 0x7f0d00fd
int string window_factory_reset_content 0x7f0d00fe
int string window_factory_reset_title 0x7f0d00ff
int string window_host 0x7f0d0100
int string window_host_content 0x7f0d0101
int string window_host_title 0x7f0d0102
int string windows 0x7f0d0103
int string windows_disable 0x7f0d0104
int string windows_disabled 0x7f0d0105
int string windows_task_manager 0x7f0d0106
int string wired_screen_name 0x7f0d0107
int string wireless_screen 0x7f0d0108
int string wireless_screen_disabled 0x7f0d0109
int string wireless_screen_disabled_toast 0x7f0d010a
int string wireless_screen_enable 0x7f0d010b
int string wireless_screen_enable_pin_code 0x7f0d010c
int string wireless_screen_name 0x7f0d010d
int string write_acceleration 0x7f0d010e
int string write_text 0x7f0d010f
int string write_without_screen_on 0x7f0d0110
int style About_Line 0x7f0e0000
int style About_Text_Content 0x7f0e0001
int style About_Text_Title 0x7f0e0002
int style Adb_Button 0x7f0e0003
int style Adb_Switch 0x7f0e0004
int style Adb_Text_Title 0x7f0e0005
int style AlertDialog_AppCompat 0x7f0e0006
int style AlertDialog_AppCompat_Light 0x7f0e0007
int style Animation_AppCompat_Dialog 0x7f0e0008
int style Animation_AppCompat_DropDownUp 0x7f0e0009
int style Animation_AppCompat_Tooltip 0x7f0e000a
int style Base_AlertDialog_AppCompat 0x7f0e000b
int style Base_AlertDialog_AppCompat_Light 0x7f0e000c
int style Base_Animation_AppCompat_Dialog 0x7f0e000d
int style Base_Animation_AppCompat_DropDownUp 0x7f0e000e
int style Base_Animation_AppCompat_Tooltip 0x7f0e000f
int style Base_CardView 0x7f0e0010
int style Base_DialogWindowTitle_AppCompat 0x7f0e0011
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0e0012
int style Base_TextAppearance_AppCompat 0x7f0e0013
int style Base_TextAppearance_AppCompat_Body1 0x7f0e0014
int style Base_TextAppearance_AppCompat_Body2 0x7f0e0015
int style Base_TextAppearance_AppCompat_Button 0x7f0e0016
int style Base_TextAppearance_AppCompat_Caption 0x7f0e0017
int style Base_TextAppearance_AppCompat_Display1 0x7f0e0018
int style Base_TextAppearance_AppCompat_Display2 0x7f0e0019
int style Base_TextAppearance_AppCompat_Display3 0x7f0e001a
int style Base_TextAppearance_AppCompat_Display4 0x7f0e001b
int style Base_TextAppearance_AppCompat_Headline 0x7f0e001c
int style Base_TextAppearance_AppCompat_Inverse 0x7f0e001d
int style Base_TextAppearance_AppCompat_Large 0x7f0e001e
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0e001f
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e0020
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e0021
int style Base_TextAppearance_AppCompat_Medium 0x7f0e0022
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0e0023
int style Base_TextAppearance_AppCompat_Menu 0x7f0e0024
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0e0025
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e0026
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0e0027
int style Base_TextAppearance_AppCompat_Small 0x7f0e0028
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0e0029
int style Base_TextAppearance_AppCompat_Subhead 0x7f0e002a
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0e002b
int style Base_TextAppearance_AppCompat_Title 0x7f0e002c
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0e002d
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0e002e
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e0030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e0031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e0032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e0033
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e0034
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e0035
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0e0036
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e0037
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e0038
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e0039
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e003a
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e003d
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0e003e
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e003f
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e0040
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e0041
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e0042
int style Base_Theme_AppCompat 0x7f0e0043
int style Base_Theme_AppCompat_CompactMenu 0x7f0e0044
int style Base_Theme_AppCompat_Dialog 0x7f0e0045
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0e0046
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0e0047
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0e0048
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0e0049
int style Base_Theme_AppCompat_Light 0x7f0e004a
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0e004b
int style Base_Theme_AppCompat_Light_Dialog 0x7f0e004c
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0e004d
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0e004e
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e004f
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0e0050
int style Base_ThemeOverlay_AppCompat 0x7f0e0051
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0e0052
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0e0053
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e0054
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0e0055
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e0056
int style Base_ThemeOverlay_AppCompat_Light 0x7f0e0057
int style Base_V21_Theme_AppCompat 0x7f0e0058
int style Base_V21_Theme_AppCompat_Dialog 0x7f0e0059
int style Base_V21_Theme_AppCompat_Light 0x7f0e005a
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0e005b
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0e005c
int style Base_V22_Theme_AppCompat 0x7f0e005d
int style Base_V22_Theme_AppCompat_Light 0x7f0e005e
int style Base_V23_Theme_AppCompat 0x7f0e005f
int style Base_V23_Theme_AppCompat_Light 0x7f0e0060
int style Base_V26_Theme_AppCompat 0x7f0e0061
int style Base_V26_Theme_AppCompat_Light 0x7f0e0062
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0e0063
int style Base_V28_Theme_AppCompat 0x7f0e0064
int style Base_V28_Theme_AppCompat_Light 0x7f0e0065
int style Base_V7_Theme_AppCompat 0x7f0e0066
int style Base_V7_Theme_AppCompat_Dialog 0x7f0e0067
int style Base_V7_Theme_AppCompat_Light 0x7f0e0068
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0e0069
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0e006a
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0e006b
int style Base_V7_Widget_AppCompat_EditText 0x7f0e006c
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0e006d
int style Base_Widget_AppCompat_ActionBar 0x7f0e006e
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0e006f
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0e0070
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0e0071
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0e0072
int style Base_Widget_AppCompat_ActionButton 0x7f0e0073
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0e0074
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0e0075
int style Base_Widget_AppCompat_ActionMode 0x7f0e0076
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0e0077
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0e0078
int style Base_Widget_AppCompat_Button 0x7f0e0079
int style Base_Widget_AppCompat_Button_Borderless 0x7f0e007a
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0e007b
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e007c
int style Base_Widget_AppCompat_Button_Colored 0x7f0e007d
int style Base_Widget_AppCompat_Button_Small 0x7f0e007e
int style Base_Widget_AppCompat_ButtonBar 0x7f0e007f
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e0080
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0e0081
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0e0082
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0e0083
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0e0084
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0e0085
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0e0086
int style Base_Widget_AppCompat_EditText 0x7f0e0087
int style Base_Widget_AppCompat_ImageButton 0x7f0e0088
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0e0089
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0e008a
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e008b
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0e008c
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e008d
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0e008e
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0e008f
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e0090
int style Base_Widget_AppCompat_ListMenuView 0x7f0e0091
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0e0092
int style Base_Widget_AppCompat_ListView 0x7f0e0093
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0e0094
int style Base_Widget_AppCompat_ListView_Menu 0x7f0e0095
int style Base_Widget_AppCompat_PopupMenu 0x7f0e0096
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0e0097
int style Base_Widget_AppCompat_PopupWindow 0x7f0e0098
int style Base_Widget_AppCompat_ProgressBar 0x7f0e0099
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0e009a
int style Base_Widget_AppCompat_RatingBar 0x7f0e009b
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0e009c
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0e009d
int style Base_Widget_AppCompat_SearchView 0x7f0e009e
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0e009f
int style Base_Widget_AppCompat_SeekBar 0x7f0e00a0
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0e00a1
int style Base_Widget_AppCompat_Spinner 0x7f0e00a2
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0e00a3
int style Base_Widget_AppCompat_TextView 0x7f0e00a4
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0e00a5
int style Base_Widget_AppCompat_Toolbar 0x7f0e00a6
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e00a7
int style Base_YcCardView 0x7f0e00a8
int style CardView 0x7f0e00a9
int style CardView_Dark 0x7f0e00aa
int style CardView_Light 0x7f0e00ab
int style Dialog 0x7f0e00ac
int style Dialog_NO_DARK 0x7f0e00ad
int style EeoDialogStyle 0x7f0e00ae
int style Extra_Switch 0x7f0e00af
int style Extra_TextView 0x7f0e00b0
int style IP_Dialog_Select_Image 0x7f0e00b1
int style IP_Dialog_Select_Linear 0x7f0e00b2
int style IP_Dialog_Select_TextView 0x7f0e00b3
int style Line 0x7f0e00b4
int style Main_ImageView 0x7f0e00b5
int style Main_ShadowLayout_Circle_Img 0x7f0e00b6
int style Main_ShadowLayout_Circle_Text 0x7f0e00b7
int style Main_Shutdown_TextView 0x7f0e00b8
int style Main_YcCardView 0x7f0e00b9
int style NetWork_Linear 0x7f0e00ba
int style NetWork_Switch 0x7f0e00bb
int style NetWork_TextView 0x7f0e00bc
int style Network_EditText 0x7f0e00bd
int style Ota_Update_Button 0x7f0e00be
int style Platform_AppCompat 0x7f0e00bf
int style Platform_AppCompat_Light 0x7f0e00c0
int style Platform_ThemeOverlay_AppCompat 0x7f0e00c1
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0e00c2
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0e00c3
int style Platform_V21_AppCompat 0x7f0e00c4
int style Platform_V21_AppCompat_Light 0x7f0e00c5
int style Platform_V25_AppCompat 0x7f0e00c6
int style Platform_V25_AppCompat_Light 0x7f0e00c7
int style Platform_Widget_AppCompat_Spinner 0x7f0e00c8
int style Projection_ImageView 0x7f0e00c9
int style Projection_Text_Content 0x7f0e00ca
int style Rgb_Text_Title 0x7f0e00cb
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0e00cc
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0e00cd
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0e00ce
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0e00cf
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0e00d0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0e00d1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0e00d2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0e00d3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0e00d4
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0e00d5
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0e00d6
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0e00d7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0e00d8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0e00d9
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0e00da
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0e00db
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0e00dc
int style SeekBar_Light 0x7f0e00dd
int style SeekBar_Rgb 0x7f0e00de
int style Setting_ImageView 0x7f0e00df
int style Setting_ShadowLayout 0x7f0e00e0
int style Setting_TextView 0x7f0e00e1
int style Shutdown_Btn_Cancel 0x7f0e00e2
int style Shutdown_Btn_Confirm 0x7f0e00e3
int style Shutdown_Icon 0x7f0e00e4
int style Shutdown_Text_Content 0x7f0e00e5
int style Shutdown_Text_Title 0x7f0e00e6
int style Signal_FrameLayout 0x7f0e00e7
int style Signal_ImageView 0x7f0e00e8
int style Signal_Text 0x7f0e00e9
int style SystemSetting_PopWindow_Host_Btn 0x7f0e00ea
int style TextAppearance_AppCompat 0x7f0e00eb
int style TextAppearance_AppCompat_Body1 0x7f0e00ec
int style TextAppearance_AppCompat_Body2 0x7f0e00ed
int style TextAppearance_AppCompat_Button 0x7f0e00ee
int style TextAppearance_AppCompat_Caption 0x7f0e00ef
int style TextAppearance_AppCompat_Display1 0x7f0e00f0
int style TextAppearance_AppCompat_Display2 0x7f0e00f1
int style TextAppearance_AppCompat_Display3 0x7f0e00f2
int style TextAppearance_AppCompat_Display4 0x7f0e00f3
int style TextAppearance_AppCompat_Headline 0x7f0e00f4
int style TextAppearance_AppCompat_Inverse 0x7f0e00f5
int style TextAppearance_AppCompat_Large 0x7f0e00f6
int style TextAppearance_AppCompat_Large_Inverse 0x7f0e00f7
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0e00f8
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0e00f9
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e00fa
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e00fb
int style TextAppearance_AppCompat_Medium 0x7f0e00fc
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0e00fd
int style TextAppearance_AppCompat_Menu 0x7f0e00fe
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e00ff
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0e0100
int style TextAppearance_AppCompat_Small 0x7f0e0101
int style TextAppearance_AppCompat_Small_Inverse 0x7f0e0102
int style TextAppearance_AppCompat_Subhead 0x7f0e0103
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0e0104
int style TextAppearance_AppCompat_Title 0x7f0e0105
int style TextAppearance_AppCompat_Title_Inverse 0x7f0e0106
int style TextAppearance_AppCompat_Tooltip 0x7f0e0107
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e0108
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e0109
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e010a
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e010b
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e010c
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e010d
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0e010e
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e010f
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0e0110
int style TextAppearance_AppCompat_Widget_Button 0x7f0e0111
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e0112
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e0113
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e0114
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e0115
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e0116
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e0117
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e0118
int style TextAppearance_AppCompat_Widget_Switch 0x7f0e0119
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e011a
int style TextAppearance_Compat_Notification 0x7f0e011b
int style TextAppearance_Compat_Notification_Info 0x7f0e011c
int style TextAppearance_Compat_Notification_Line2 0x7f0e011d
int style TextAppearance_Compat_Notification_Time 0x7f0e011e
int style TextAppearance_Compat_Notification_Title 0x7f0e011f
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e0120
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e0121
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e0122
int style Theme_AppCompat 0x7f0e0123
int style Theme_AppCompat_CompactMenu 0x7f0e0124
int style Theme_AppCompat_DayNight 0x7f0e0125
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0e0126
int style Theme_AppCompat_DayNight_Dialog 0x7f0e0127
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0e0128
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0e0129
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0e012a
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0e012b
int style Theme_AppCompat_Dialog 0x7f0e012c
int style Theme_AppCompat_Dialog_Alert 0x7f0e012d
int style Theme_AppCompat_Dialog_MinWidth 0x7f0e012e
int style Theme_AppCompat_DialogWhenLarge 0x7f0e012f
int style Theme_AppCompat_Empty 0x7f0e0130
int style Theme_AppCompat_Light 0x7f0e0131
int style Theme_AppCompat_Light_DarkActionBar 0x7f0e0132
int style Theme_AppCompat_Light_Dialog 0x7f0e0133
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0e0134
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e0135
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0e0136
int style Theme_AppCompat_Light_NoActionBar 0x7f0e0137
int style Theme_AppCompat_NoActionBar 0x7f0e0138
int style Theme_Ota 0x7f0e0139
int style Theme_SystemSetting 0x7f0e013a
int style ThemeOverlay_AppCompat 0x7f0e013b
int style ThemeOverlay_AppCompat_ActionBar 0x7f0e013c
int style ThemeOverlay_AppCompat_Dark 0x7f0e013d
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e013e
int style ThemeOverlay_AppCompat_DayNight 0x7f0e013f
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0e0140
int style ThemeOverlay_AppCompat_Dialog 0x7f0e0141
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e0142
int style ThemeOverlay_AppCompat_Light 0x7f0e0143
int style Title 0x7f0e0144
int style Update_Button 0x7f0e0145
int style Update_TextView 0x7f0e0146
int style WiFi_Text 0x7f0e0147
int style Widget_AppCompat_ActionBar 0x7f0e0148
int style Widget_AppCompat_ActionBar_Solid 0x7f0e0149
int style Widget_AppCompat_ActionBar_TabBar 0x7f0e014a
int style Widget_AppCompat_ActionBar_TabText 0x7f0e014b
int style Widget_AppCompat_ActionBar_TabView 0x7f0e014c
int style Widget_AppCompat_ActionButton 0x7f0e014d
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0e014e
int style Widget_AppCompat_ActionButton_Overflow 0x7f0e014f
int style Widget_AppCompat_ActionMode 0x7f0e0150
int style Widget_AppCompat_ActivityChooserView 0x7f0e0151
int style Widget_AppCompat_AutoCompleteTextView 0x7f0e0152
int style Widget_AppCompat_Button 0x7f0e0153
int style Widget_AppCompat_Button_Borderless 0x7f0e0154
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0e0155
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e0156
int style Widget_AppCompat_Button_Colored 0x7f0e0157
int style Widget_AppCompat_Button_Small 0x7f0e0158
int style Widget_AppCompat_ButtonBar 0x7f0e0159
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e015a
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0e015b
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0e015c
int style Widget_AppCompat_CompoundButton_Switch 0x7f0e015d
int style Widget_AppCompat_DrawerArrowToggle 0x7f0e015e
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0e015f
int style Widget_AppCompat_EditText 0x7f0e0160
int style Widget_AppCompat_ImageButton 0x7f0e0161
int style Widget_AppCompat_Light_ActionBar 0x7f0e0162
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0e0163
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0e0164
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e0165
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0e0166
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0e0167
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e0168
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0e0169
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0e016a
int style Widget_AppCompat_Light_ActionButton 0x7f0e016b
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0e016c
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0e016d
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0e016e
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0e016f
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0e0170
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0e0171
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0e0172
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0e0173
int style Widget_AppCompat_Light_PopupMenu 0x7f0e0174
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e0175
int style Widget_AppCompat_Light_SearchView 0x7f0e0176
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0e0177
int style Widget_AppCompat_ListMenuView 0x7f0e0178
int style Widget_AppCompat_ListPopupWindow 0x7f0e0179
int style Widget_AppCompat_ListView 0x7f0e017a
int style Widget_AppCompat_ListView_DropDown 0x7f0e017b
int style Widget_AppCompat_ListView_Menu 0x7f0e017c
int style Widget_AppCompat_PopupMenu 0x7f0e017d
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0e017e
int style Widget_AppCompat_PopupWindow 0x7f0e017f
int style Widget_AppCompat_ProgressBar 0x7f0e0180
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0e0181
int style Widget_AppCompat_RatingBar 0x7f0e0182
int style Widget_AppCompat_RatingBar_Indicator 0x7f0e0183
int style Widget_AppCompat_RatingBar_Small 0x7f0e0184
int style Widget_AppCompat_SearchView 0x7f0e0185
int style Widget_AppCompat_SearchView_ActionBar 0x7f0e0186
int style Widget_AppCompat_SeekBar 0x7f0e0187
int style Widget_AppCompat_SeekBar_Discrete 0x7f0e0188
int style Widget_AppCompat_Spinner 0x7f0e0189
int style Widget_AppCompat_Spinner_DropDown 0x7f0e018a
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0e018b
int style Widget_AppCompat_Spinner_Underlined 0x7f0e018c
int style Widget_AppCompat_TextView 0x7f0e018d
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0e018e
int style Widget_AppCompat_Toolbar 0x7f0e018f
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e0190
int style Widget_Compat_NotificationActionContainer 0x7f0e0191
int style Widget_Compat_NotificationActionText 0x7f0e0192
int style Widget_Support_CoordinatorLayout 0x7f0e0193
int style Wifi_Text_NAME 0x7f0e0194
int style YcCardView 0x7f0e0195
int style YcCardView_Dark 0x7f0e0196
int style YcCardView_Light 0x7f0e0197
int style update_progress_horizontal 0x7f0e0198
int[] styleable ActionBar { 0x7f030031, 0x7f030032, 0x7f030033, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f030073, 0x7f030078, 0x7f030079, 0x7f03008c, 0x7f0300a2, 0x7f0300a3, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300cc, 0x7f0300cf, 0x7f03011c, 0x7f030124, 0x7f03012f, 0x7f030132, 0x7f030133, 0x7f030151, 0x7f030154, 0x7f030170, 0x7f030179 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030031, 0x7f030032, 0x7f030053, 0x7f0300a2, 0x7f030154, 0x7f030179 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f03008e, 0x7f0300cd }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f030041, 0x7f030042, 0x7f030111, 0x7f030112, 0x7f030121, 0x7f030144, 0x7f030145 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatImageView { 0x01010119, 0x7f03014b, 0x7f03016e, 0x7f03016f }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f03016b, 0x7f03016c, 0x7f03016d }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f03002c, 0x7f03002d, 0x7f03002e, 0x7f03002f, 0x7f030030, 0x7f03007d, 0x7f03007e, 0x7f03007f, 0x7f030080, 0x7f030082, 0x7f030083, 0x7f030084, 0x7f030085, 0x7f030094, 0x7f030096, 0x7f03009e, 0x7f0300d1, 0x7f03010c, 0x7f03015a, 0x7f030165 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030000, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f03002b, 0x7f030039, 0x7f03003a, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f03003e, 0x7f030043, 0x7f030044, 0x7f03004f, 0x7f030050, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030071, 0x7f030075, 0x7f030076, 0x7f030077, 0x7f03007a, 0x7f03007c, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f0300c5, 0x7f0300cb, 0x7f03010d, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f030130, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013d, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030148, 0x7f030149, 0x7f030158, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f03017a, 0x7f03017b, 0x7f03017c, 0x7f03017d, 0x7f030183, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f030188, 0x7f030189, 0x7f03018a, 0x7f03018b, 0x7f03018c, 0x7f03018d, 0x7f03018e }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 72
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 73
int styleable AppCompatTheme_listDividerAlertDialog 74
int styleable AppCompatTheme_listMenuViewStyle 75
int styleable AppCompatTheme_listPopupWindowStyle 76
int styleable AppCompatTheme_listPreferredItemHeight 77
int styleable AppCompatTheme_listPreferredItemHeightLarge 78
int styleable AppCompatTheme_listPreferredItemHeightSmall 79
int styleable AppCompatTheme_listPreferredItemPaddingEnd 80
int styleable AppCompatTheme_listPreferredItemPaddingLeft 81
int styleable AppCompatTheme_listPreferredItemPaddingRight 82
int styleable AppCompatTheme_listPreferredItemPaddingStart 83
int styleable AppCompatTheme_panelBackground 84
int styleable AppCompatTheme_panelMenuListTheme 85
int styleable AppCompatTheme_panelMenuListWidth 86
int styleable AppCompatTheme_popupMenuStyle 87
int styleable AppCompatTheme_popupWindowStyle 88
int styleable AppCompatTheme_radioButtonStyle 89
int styleable AppCompatTheme_ratingBarStyle 90
int styleable AppCompatTheme_ratingBarStyleIndicator 91
int styleable AppCompatTheme_ratingBarStyleSmall 92
int styleable AppCompatTheme_searchViewStyle 93
int styleable AppCompatTheme_seekBarStyle 94
int styleable AppCompatTheme_selectableItemBackground 95
int styleable AppCompatTheme_selectableItemBackgroundBorderless 96
int styleable AppCompatTheme_spinnerDropDownItemStyle 97
int styleable AppCompatTheme_spinnerStyle 98
int styleable AppCompatTheme_switchStyle 99
int styleable AppCompatTheme_textAppearanceLargePopupMenu 100
int styleable AppCompatTheme_textAppearanceListItem 101
int styleable AppCompatTheme_textAppearanceListItemSecondary 102
int styleable AppCompatTheme_textAppearanceListItemSmall 103
int styleable AppCompatTheme_textAppearancePopupMenuHeader 104
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 105
int styleable AppCompatTheme_textAppearanceSearchResultTitle 106
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 107
int styleable AppCompatTheme_textColorAlertDialogListItem 108
int styleable AppCompatTheme_textColorSearchUrl 109
int styleable AppCompatTheme_toolbarNavigationButtonStyle 110
int styleable AppCompatTheme_toolbarStyle 111
int styleable AppCompatTheme_tooltipForegroundColor 112
int styleable AppCompatTheme_tooltipFrameBackground 113
int styleable AppCompatTheme_viewInflaterClass 114
int styleable AppCompatTheme_windowActionBar 115
int styleable AppCompatTheme_windowActionBarOverlay 116
int styleable AppCompatTheme_windowActionModeOverlay 117
int styleable AppCompatTheme_windowFixedHeightMajor 118
int styleable AppCompatTheme_windowFixedHeightMinor 119
int styleable AppCompatTheme_windowFixedWidthMajor 120
int styleable AppCompatTheme_windowFixedWidthMinor 121
int styleable AppCompatTheme_windowMinWidthMajor 122
int styleable AppCompatTheme_windowMinWidthMinor 123
int styleable AppCompatTheme_windowNoTitle 124
int[] styleable ButtonBarLayout { 0x7f030026 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f030047, 0x7f030048, 0x7f030049, 0x7f03004a, 0x7f03004b, 0x7f03004c, 0x7f03006c, 0x7f03006d, 0x7f03006e, 0x7f03006f, 0x7f030070 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x7f030027 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_alpha 2
int[] styleable CompoundButton { 0x01010107, 0x7f03003f, 0x7f030045, 0x7f030046 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x7f030037, 0x7f030038, 0x7f03004e, 0x7f030062, 0x7f030063, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f0300ec, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f0300f0, 0x7f0300f1, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f0300f9, 0x7f0300fa, 0x7f0300fb, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f0300ff, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f03010b }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_maxWidth 1
int styleable ConstraintLayout_Layout_android_maxHeight 2
int styleable ConstraintLayout_Layout_android_minWidth 3
int styleable ConstraintLayout_Layout_android_minHeight 4
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 5
int styleable ConstraintLayout_Layout_barrierDirection 6
int styleable ConstraintLayout_Layout_chainUseRtl 7
int styleable ConstraintLayout_Layout_constraintSet 8
int styleable ConstraintLayout_Layout_constraint_referenced_ids 9
int styleable ConstraintLayout_Layout_layout_constrainedHeight 10
int styleable ConstraintLayout_Layout_layout_constrainedWidth 11
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 12
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 13
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 14
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 15
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 16
int styleable ConstraintLayout_Layout_layout_constraintCircle 17
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 18
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 19
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 20
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 21
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 22
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 23
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 24
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 25
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 26
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 27
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 28
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 29
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 30
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 31
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 32
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 33
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 34
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 35
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 36
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 37
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 38
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 39
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 40
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 41
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 42
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 43
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 44
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 45
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 46
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 47
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 48
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 49
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 50
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 51
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 52
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 53
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 54
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 55
int styleable ConstraintLayout_Layout_layout_goneMarginRight 56
int styleable ConstraintLayout_Layout_layout_goneMarginStart 57
int styleable ConstraintLayout_Layout_layout_goneMarginTop 58
int styleable ConstraintLayout_Layout_layout_optimizationLevel 59
int[] styleable ConstraintLayout_placeholder { 0x7f030064, 0x7f03008d }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030037, 0x7f030038, 0x7f03004e, 0x7f030063, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f0300ec, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f0300f0, 0x7f0300f1, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f0300f9, 0x7f0300fa, 0x7f0300fb, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f0300ff, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f030107, 0x7f030108 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_alpha 13
int styleable ConstraintSet_android_transformPivotX 14
int styleable ConstraintSet_android_transformPivotY 15
int styleable ConstraintSet_android_translationX 16
int styleable ConstraintSet_android_translationY 17
int styleable ConstraintSet_android_scaleX 18
int styleable ConstraintSet_android_scaleY 19
int styleable ConstraintSet_android_rotation 20
int styleable ConstraintSet_android_rotationX 21
int styleable ConstraintSet_android_rotationY 22
int styleable ConstraintSet_android_layout_marginStart 23
int styleable ConstraintSet_android_layout_marginEnd 24
int styleable ConstraintSet_android_translationZ 25
int styleable ConstraintSet_android_elevation 26
int styleable ConstraintSet_barrierAllowsGoneWidgets 27
int styleable ConstraintSet_barrierDirection 28
int styleable ConstraintSet_chainUseRtl 29
int styleable ConstraintSet_constraint_referenced_ids 30
int styleable ConstraintSet_layout_constrainedHeight 31
int styleable ConstraintSet_layout_constrainedWidth 32
int styleable ConstraintSet_layout_constraintBaseline_creator 33
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 34
int styleable ConstraintSet_layout_constraintBottom_creator 35
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 36
int styleable ConstraintSet_layout_constraintBottom_toTopOf 37
int styleable ConstraintSet_layout_constraintCircle 38
int styleable ConstraintSet_layout_constraintCircleAngle 39
int styleable ConstraintSet_layout_constraintCircleRadius 40
int styleable ConstraintSet_layout_constraintDimensionRatio 41
int styleable ConstraintSet_layout_constraintEnd_toEndOf 42
int styleable ConstraintSet_layout_constraintEnd_toStartOf 43
int styleable ConstraintSet_layout_constraintGuide_begin 44
int styleable ConstraintSet_layout_constraintGuide_end 45
int styleable ConstraintSet_layout_constraintGuide_percent 46
int styleable ConstraintSet_layout_constraintHeight_default 47
int styleable ConstraintSet_layout_constraintHeight_max 48
int styleable ConstraintSet_layout_constraintHeight_min 49
int styleable ConstraintSet_layout_constraintHeight_percent 50
int styleable ConstraintSet_layout_constraintHorizontal_bias 51
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 52
int styleable ConstraintSet_layout_constraintHorizontal_weight 53
int styleable ConstraintSet_layout_constraintLeft_creator 54
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 55
int styleable ConstraintSet_layout_constraintLeft_toRightOf 56
int styleable ConstraintSet_layout_constraintRight_creator 57
int styleable ConstraintSet_layout_constraintRight_toLeftOf 58
int styleable ConstraintSet_layout_constraintRight_toRightOf 59
int styleable ConstraintSet_layout_constraintStart_toEndOf 60
int styleable ConstraintSet_layout_constraintStart_toStartOf 61
int styleable ConstraintSet_layout_constraintTop_creator 62
int styleable ConstraintSet_layout_constraintTop_toBottomOf 63
int styleable ConstraintSet_layout_constraintTop_toTopOf 64
int styleable ConstraintSet_layout_constraintVertical_bias 65
int styleable ConstraintSet_layout_constraintVertical_chainStyle 66
int styleable ConstraintSet_layout_constraintVertical_weight 67
int styleable ConstraintSet_layout_constraintWidth_default 68
int styleable ConstraintSet_layout_constraintWidth_max 69
int styleable ConstraintSet_layout_constraintWidth_min 70
int styleable ConstraintSet_layout_constraintWidth_percent 71
int styleable ConstraintSet_layout_editor_absoluteX 72
int styleable ConstraintSet_layout_editor_absoluteY 73
int styleable ConstraintSet_layout_goneMarginBottom 74
int styleable ConstraintSet_layout_goneMarginEnd 75
int styleable ConstraintSet_layout_goneMarginLeft 76
int styleable ConstraintSet_layout_goneMarginRight 77
int styleable ConstraintSet_layout_goneMarginStart 78
int styleable ConstraintSet_layout_goneMarginTop 79
int[] styleable CoordinatorLayout { 0x7f0300d0, 0x7f03014e }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d6, 0x7f030100, 0x7f030109, 0x7f03010a }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerArrowToggle { 0x7f030029, 0x7f03002a, 0x7f030036, 0x7f030056, 0x7f030081, 0x7f0300a0, 0x7f030147, 0x7f030167 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f030097, 0x7f030098, 0x7f030099, 0x7f03009a, 0x7f03009b, 0x7f03009c }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030095, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f030182 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearConstraintLayout { 0x010100c4 }
int styleable LinearConstraintLayout_android_orientation 0
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f030079, 0x7f03007b, 0x7f03011f, 0x7f030142 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000d, 0x7f03001f, 0x7f030020, 0x7f030028, 0x7f030065, 0x7f0300c8, 0x7f0300c9, 0x7f030125, 0x7f030141, 0x7f03017e }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030131, 0x7f03014f }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f030126 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f03014d }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f030127, 0x7f03012a }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100f1, 0x7f03008f, 0x7f030090, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f0300d3, 0x7f03013a, 0x7f030146, 0x7f03014c }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_fastScrollEnabled 2
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 3
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 4
int styleable RecyclerView_fastScrollVerticalThumbDrawable 5
int styleable RecyclerView_fastScrollVerticalTrackDrawable 6
int styleable RecyclerView_layoutManager 7
int styleable RecyclerView_reverseLayout 8
int styleable RecyclerView_spanCount 9
int styleable RecyclerView_stackFromEnd 10
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f030052, 0x7f030061, 0x7f030074, 0x7f0300a1, 0x7f0300ca, 0x7f0300d2, 0x7f030134, 0x7f030135, 0x7f03013b, 0x7f03013c, 0x7f030150, 0x7f030155, 0x7f030184 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable ShadowLayout { 0x7f030051, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300a7, 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0300b4, 0x7f0300b5, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8, 0x7f0300b9, 0x7f0300ba, 0x7f0300bb, 0x7f0300bc, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300c0, 0x7f0300c1, 0x7f0300c2, 0x7f0300c3, 0x7f0300c4 }
int styleable ShadowLayout_clickable 0
int styleable ShadowLayout_hl_angle 1
int styleable ShadowLayout_hl_bindTextView 2
int styleable ShadowLayout_hl_centerColor 3
int styleable ShadowLayout_hl_cornerRadius 4
int styleable ShadowLayout_hl_cornerRadius_leftBottom 5
int styleable ShadowLayout_hl_cornerRadius_leftTop 6
int styleable ShadowLayout_hl_cornerRadius_rightBottom 7
int styleable ShadowLayout_hl_cornerRadius_rightTop 8
int styleable ShadowLayout_hl_endColor 9
int styleable ShadowLayout_hl_layoutBackground 10
int styleable ShadowLayout_hl_layoutBackground_clickFalse 11
int styleable ShadowLayout_hl_layoutBackground_true 12
int styleable ShadowLayout_hl_shadowColor 13
int styleable ShadowLayout_hl_shadowHidden 14
int styleable ShadowLayout_hl_shadowHiddenBottom 15
int styleable ShadowLayout_hl_shadowHiddenLeft 16
int styleable ShadowLayout_hl_shadowHiddenRight 17
int styleable ShadowLayout_hl_shadowHiddenTop 18
int styleable ShadowLayout_hl_shadowLimit 19
int styleable ShadowLayout_hl_shadowOffsetX 20
int styleable ShadowLayout_hl_shadowOffsetY 21
int styleable ShadowLayout_hl_shadowSymmetry 22
int styleable ShadowLayout_hl_shapeMode 23
int styleable ShadowLayout_hl_startColor 24
int styleable ShadowLayout_hl_strokeColor 25
int styleable ShadowLayout_hl_strokeColor_true 26
int styleable ShadowLayout_hl_strokeWith 27
int styleable ShadowLayout_hl_stroke_dashGap 28
int styleable ShadowLayout_hl_stroke_dashWidth 29
int styleable ShadowLayout_hl_text 30
int styleable ShadowLayout_hl_textColor 31
int styleable ShadowLayout_hl_textColor_true 32
int styleable ShadowLayout_hl_text_true 33
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f03012f }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f030143, 0x7f03014a, 0x7f030156, 0x7f030157, 0x7f030159, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03017f, 0x7f030180, 0x7f030181 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f030096, 0x7f03009e, 0x7f03015a, 0x7f030165 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030040, 0x7f030054, 0x7f030055, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f030120, 0x7f030122, 0x7f030123, 0x7f03012f, 0x7f030151, 0x7f030152, 0x7f030153, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f030174, 0x7f030175, 0x7f030176, 0x7f030177, 0x7f030178 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable View { 0x01010000, 0x010100da, 0x7f030128, 0x7f030129, 0x7f030166 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030034, 0x7f030035 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable YcCardView { 0x0101013f, 0x01010140, 0x7f03006d, 0x7f03018f, 0x7f030190, 0x7f030191, 0x7f030192, 0x7f030193, 0x7f030194, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f030199, 0x7f03019a }
int styleable YcCardView_android_minWidth 0
int styleable YcCardView_android_minHeight 1
int styleable YcCardView_contentPaddingBottom 2
int styleable YcCardView_ycCardBackgroundColor 3
int styleable YcCardView_ycCardCornerRadius 4
int styleable YcCardView_ycCardElevation 5
int styleable YcCardView_ycCardMaxElevation 6
int styleable YcCardView_ycCardPreventCornerOverlap 7
int styleable YcCardView_ycCardUseCompatPadding 8
int styleable YcCardView_ycContentPadding 9
int styleable YcCardView_ycContentPaddingLeft 10
int styleable YcCardView_ycContentPaddingRight 11
int styleable YcCardView_ycContentPaddingTop 12
int styleable YcCardView_ycEndShadowColor 13
int styleable YcCardView_ycStartShadowColor 14
int xml accessibility_service_config 0x7f100000
