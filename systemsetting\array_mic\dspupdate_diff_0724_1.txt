commit 097efef35553d90f04d98f23fdefa97ec8be9feb
Author: chensibo <<EMAIL>>
Date:   Mon Jun 23 15:21:58 2025 +0800

    [ota]新增DSP固件升级功能

diff --git a/ota/src/main/AndroidManifest.xml b/ota/src/main/AndroidManifest.xml
index dafc942..0b3a83e 100644
--- a/ota/src/main/AndroidManifest.xml
+++ b/ota/src/main/AndroidManifest.xml
@@ -39,6 +39,10 @@
                 <action android:name="com.eeo.ota.action.AutoUpdateService" />
             </intent-filter>
         </service>
+
+        <service
+            android:name=".service.DspUpdateService"
+            android:exported="false" />
     </application>
 
 </manifest>
\ No newline at end of file
diff --git a/ota/src/main/java/com/eeo/ota/dsp/DspFirmwareUpdater.java b/ota/src/main/java/com/eeo/ota/dsp/DspFirmwareUpdater.java
new file mode 100644
index 0000000..dacdec9
--- /dev/null
+++ b/ota/src/main/java/com/eeo/ota/dsp/DspFirmwareUpdater.java
@@ -0,0 +1,1134 @@
+package com.eeo.ota.dsp;
+
+import android.content.Context;
+import android.os.Handler;
+import android.os.Looper;
+import android.util.Log;
+
+// Import the new interface
+import com.eeo.ota.dsp.IDspCommunicationManager;
+
+import java.io.File;
+import java.io.FileInputStream;
+import java.io.IOException;
+import java.io.InputStream;
+import java.util.Arrays;
+import java.util.Locale;
+
+/**
+ * DspFirmwareUpdater 类负责处理 DSP 固件升级的全部逻辑。
+ * 它通过 UsbSerialManager 与 DSP 设备进行通信，执行固件升级协议中定义的步骤。
+ * 日志TAG统一使用 "DSPUpdate"。
+ */
+public class DspFirmwareUpdater {
+    public static final boolean DEBUG = true;  // DSP升级详细日志开关
+
+    // Inner class to hold the result of a BANK packet send and acknowledgment
+    private static class BankResponse {
+        boolean success;
+        byte receivedBank; // Raw byte from DSP
+        byte errorCode;    // Raw byte from DSP (0x00 for OK)
+        String errorMessage;
+
+        BankResponse(boolean success, byte receivedBank, byte errorCode, String errorMessage) {
+            this.success = success;
+            this.receivedBank = receivedBank;
+            this.errorCode = errorCode;
+            this.errorMessage = errorMessage;
+        }
+
+        // Getter methods can be added if needed, for example:
+        // public boolean isSuccess() { return success; }
+        // public byte getReceivedBank() { return receivedBank; }
+        // public byte getErrorCode() { return errorCode; }
+        // public String getErrorMessage() { return errorMessage; }
+    }
+
+    /**
+     * DSP升级统计信息收集类
+     * 用于统计各命令码的发送和接收成功失败情况
+     */
+    private static class DspCommandStatistics {
+        private int sendSuccessCount = 0;
+        private int sendFailureCount = 0;
+        private int receiveSuccessCount = 0;
+        private int receiveFailureCount = 0;
+
+        public void recordSendSuccess() {
+            sendSuccessCount++;
+        }
+
+        public void recordSendFailure() {
+            sendFailureCount++;
+        }
+
+        public void recordReceiveSuccess() {
+            receiveSuccessCount++;
+        }
+
+        public void recordReceiveFailure() {
+            receiveFailureCount++;
+        }
+
+        public String getStatisticsString(String commandName) {
+            return String.format("[%s]: Sent[fail/Successfully]: %d/%d, Receive[fail/Successfully]: %d/%d",
+                    commandName, sendFailureCount, sendSuccessCount, receiveFailureCount, receiveSuccessCount);
+        }
+    }
+
+    /**
+     * DSP升级全局统计管理器
+     */
+    private static class DspUpgradeStatistics {
+        private final DspCommandStatistics c0Stats = new DspCommandStatistics(); // 版本读取
+        private final DspCommandStatistics c1Stats = new DspCommandStatistics(); // 固件升级请求
+        private final DspCommandStatistics c2Stats = new DspCommandStatistics(); // 固件bank传输
+        private final DspCommandStatistics c3Stats = new DspCommandStatistics(); // 固件保存
+
+        // 版本信息记录
+        private String beforeUpgradeVersion = "Unknown";
+        private String afterUpgradeVersion = "Unknown";
+
+        public DspCommandStatistics getC0Stats() { return c0Stats; }
+        public DspCommandStatistics getC1Stats() { return c1Stats; }
+        public DspCommandStatistics getC2Stats() { return c2Stats; }
+        public DspCommandStatistics getC3Stats() { return c3Stats; }
+
+        public void setBeforeUpgradeVersion(String version) {
+            this.beforeUpgradeVersion = version != null ? version : "Unknown";
+        }
+        
+        public void setAfterUpgradeVersion(String version) {
+            this.afterUpgradeVersion = version != null ? version : "Unknown";
+        }
+
+        public void printSummary() {
+            Log.i(TAG, "=== DSP Upgrade Statistics Summary ===");
+            // Log.i(TAG, c0Stats.getStatisticsString("0xC0"));
+            Log.i(TAG, c1Stats.getStatisticsString("0xC1"));
+            Log.i(TAG, c2Stats.getStatisticsString("0xC2"));
+            Log.i(TAG, c3Stats.getStatisticsString("0xC3"));
+            Log.i(TAG, "=========================================");
+            Log.i(TAG, "DSP Version Change: " + beforeUpgradeVersion + " -> " + afterUpgradeVersion);
+        }
+
+        public void clearAll() {
+            // 重置所有统计数据
+            c0Stats.sendSuccessCount = 0;
+            c0Stats.sendFailureCount = 0;
+            c0Stats.receiveSuccessCount = 0;
+            c0Stats.receiveFailureCount = 0;
+            
+            c1Stats.sendSuccessCount = 0;
+            c1Stats.sendFailureCount = 0;
+            c1Stats.receiveSuccessCount = 0;
+            c1Stats.receiveFailureCount = 0;
+            
+            c2Stats.sendSuccessCount = 0;
+            c2Stats.sendFailureCount = 0;
+            c2Stats.receiveSuccessCount = 0;
+            c2Stats.receiveFailureCount = 0;
+            
+            c3Stats.sendSuccessCount = 0;
+            c3Stats.sendFailureCount = 0;
+            c3Stats.receiveSuccessCount = 0;
+            c3Stats.receiveFailureCount = 0;
+            
+            // 重置版本信息
+            beforeUpgradeVersion = "Unknown";
+            afterUpgradeVersion = "Unknown";
+        }
+    }
+
+    private static final String TAG = "DSPUpdate";
+
+    // DSP 通信协议相关常量
+    private static final byte FRAME_HEADER_START = 0x7B;
+    private static final byte FRAME_HEADER_END = 0x7D;
+    private static final byte FRAME_TRAILER_START = 0x7D;
+    private static final byte FRAME_TRAILER_END = 0x7B;
+    private static final byte DEFAULT_DEVICE_ADDRESS = 0x01;
+
+    // DSP 固件升级指令
+    private static final byte CMD_GET_VERSION = (byte) 0xC0;
+    private static final byte CMD_UPDATE_REQUEST = (byte) 0xC1;
+    private static final byte CMD_BANK_TRANSFER = (byte) 0xC2;
+    private static final byte CMD_SAVE_FIRMWARE = (byte) 0xC3;
+    private static final byte CMD_CPU_RESET = (byte) 0xB3;
+
+    // 0xC0 (固件版本读取) 指令的 DATA 字段 (无特定参数，根据协议补00)
+    private static final byte DATA_C0_PARAM1 = 0x00;
+    private static final byte DATA_C0_PARAM2 = 0x00;
+
+    // 0xC1 (固件升级请求) 指令的 DATA 字段
+    private static final byte DATA_C1_PARAM1 = 0x00;
+    private static final byte DATA_C1_PARAM2 = 0x00;
+
+    // 0xC3 (固件保存) 指令的 DATA 字段
+    private static final byte DATA_C3_PARAM1 = 0x00;
+    private static final byte DATA_C3_PARAM2 = 0x00;
+
+    // 0xB3 (CPU复位) 指令的 DATA 字段 (无特定参数，根据协议补00)
+    private static final byte DATA_B3_PARAM1 = 0x00;
+    private static final byte DATA_B3_PARAM2 = 0x00;
+
+    // 0xC2 (固件 Bank 传输) 相关常量
+    private static final int MAX_FIRMWARE_DATA_PER_PACKET = 255; // LENGTH 字段最大值 (0-255 for data)
+    private static final int BANK_NUMBER_MAX = 255; // BANK 序号最大值，之后从1开始循环
+
+    // 应答相关常量
+    // Expected core data lengths for different responses
+    private static final int CORE_DATA_LEN_C0_RESPONSE = 4; // CC (Command Byte), Major, Minor, Revision
+    private static final int CORE_DATA_LEN_C1_RESPONSE = 1; // BYTE
+    private static final int CORE_DATA_LEN_C2_RESPONSE = 2; // BANK, ERROR
+    private static final int CORE_DATA_LEN_C3_RESPONSE = 1; // BYTE
+    // No response expected for B3 (CPU Reset)
+
+    private static final byte DSP_RESPONSE_C0_EXPECTED_CC_BYTE = (byte) 0xCC;
+    private static final byte DSP_RESPONSE_C1_READY = 0x00;
+    private static final byte DSP_RESPONSE_C1_BUSY = (byte) 0x01; // DSP忙碌
+    private static final byte DSP_RESPONSE_C2_ERROR_OK = (byte) 0x00; // C2命令响应中，ERROR字节为0表示成功
+    private static final byte DSP_RESPONSE_C3_SAVE_OK = 0x00;
+
+    // 操作常量
+    private static final int MAX_RETRIES_PER_PACKET = 2; // 每包最大重试次数 (不含首次发送)
+    private static final int RESPONSE_TIMEOUT_C0_MS = 3000; // 0xC0版本读取响应超时时间 (ms)
+    private static final int RESPONSE_TIMEOUT_C1_MS = 3000; // 0xC1升级请求响应超时时间 (ms)
+    private static final int RESPONSE_TIMEOUT_C2_MS = 3000; // 0xC2固件传输响应超时时间 (ms) - 调整为3秒
+    private static final int RESPONSE_TIMEOUT_C3_MS = 10000; // 0xC3固件保存响应超时时间 (ms) - 调整为10秒
+    private static final int WRITE_TIMEOUT_MS = 200;     // DSP写入超时时间 (ms)
+    private static final int PROGRESS_UPDATE_INTERVAL_PACKETS = 10;
+    private static final int MAX_C2_RETRIES = 3; // Max retries for C2 packet (excluding initial send)
+
+    private final IDspCommunicationManager communicationManager;
+    private final Handler mainHandler;
+
+    private FirmwareUpdateListener listener;
+    private String firmwarePath;
+    private volatile boolean isUpdating = false;
+    private int currentBankNumber = 1;
+    private int totalPacketsCalculated = 0;
+    private int packetsSuccessfullySent = 0;
+    
+    // DSP升级统计实例
+    private final DspUpgradeStatistics upgradeStatistics = new DspUpgradeStatistics();
+
+    public interface FirmwareUpdateListener {
+        void onProgress(int percentage);
+        void onSuccess();
+        void onFailure(String errorMessage, int dspErrorCode);
+    }
+
+    /**
+     * 构造函数
+     *
+     * @param context Context 对象 (用于 Handler)
+     * @param manager IDspCommunicationManager 实例
+     */
+    public DspFirmwareUpdater(Context context, IDspCommunicationManager manager) {
+        this.communicationManager = manager;
+        this.mainHandler = new Handler(Looper.getMainLooper());
+    }
+
+    public synchronized boolean startUpdate(String firmwarePathInAndroid, FirmwareUpdateListener listener) {
+        return startUpdate(firmwarePathInAndroid, listener, null);
+    }
+
+    public synchronized boolean startUpdate(String firmwarePathInAndroid, FirmwareUpdateListener listener, String beforeUpgradeVersion) {
+        if (isUpdating) {
+            Log.w(TAG, "Update process is already running.");
+            if (listener != null) {
+                mainHandler.post(() -> listener.onFailure("Update process is already running.", -1));
+            }
+            return false;
+        }
+        if (firmwarePathInAndroid == null || firmwarePathInAndroid.isEmpty()) {
+            Log.e(TAG, "Firmware path is null or empty.");
+            if (listener != null) {
+                mainHandler.post(() -> listener.onFailure("Firmware path is null or empty.", -1));
+            }
+            return false;
+        }
+        if (listener == null) {
+            Log.e(TAG, "FirmwareUpdateListener is null.");
+        }
+
+        Log.i(TAG, "Starting DSP firmware update. Firmware path: " + firmwarePathInAndroid);
+        this.isUpdating = true;
+        this.firmwarePath = firmwarePathInAndroid;
+        this.listener = listener;
+        this.currentBankNumber = 1;
+        this.packetsSuccessfullySent = 0;
+        this.totalPacketsCalculated = 0;
+        
+        // 清空并重新开始统计
+        this.upgradeStatistics.clearAll();
+
+        if (beforeUpgradeVersion != null) {
+            upgradeStatistics.setBeforeUpgradeVersion(beforeUpgradeVersion);
+        }
+
+        new Thread(this::runUpdateProcess).start();
+        return true;
+    }
+
+    private void runUpdateProcess() {
+        boolean success = false;
+        String failureMessage = "Unknown error during update";
+        int dspErrorCode = -1;
+
+        try {
+            if (communicationManager == null) {
+                Log.e(TAG, "IDspCommunicationManager (communicationManager) not available.");
+                failureMessage = "IDspCommunicationManager not provided.";
+                throw new UpdateStepException(failureMessage, DspErrorCodes.USB_MANAGER_NOT_READY);
+            }
+
+            Log.d(TAG, "Step 1: Requesting update mode (0xC1)...");
+            if (!requestUpdateMode()) {
+                failureMessage = "Failed to enter update mode (0xC1).";
+                throw new UpdateStepException(failureMessage, DspErrorCodes.UPDATE_MODE_REQUEST_FAILED);
+            }
+            Log.i(TAG, "Step 1: Successfully entered update mode.");
+            notifyProgress(5);
+
+            Log.d(TAG, "Step 2: Transferring firmware data (0xC2)...");
+            if (!transferFirmwareData()) {
+                failureMessage = "Failed to transfer firmware data (0xC2).";
+                throw new UpdateStepException(failureMessage, DspErrorCodes.FIRMWARE_TRANSFER_FAILED);
+            }
+            Log.i(TAG, "Step 2: Firmware data transferred successfully.");
+
+            Log.d(TAG, "Step 3: Requesting firmware save (0xC3)...then CPU reset (0xB3)");
+            if (!saveFirmwareAndResetCpu()) {
+                failureMessage = "Firmware save (0xC3) was successful, but CPU Reset command (0xB3) failed to send.";
+                throw new UpdateStepException("Firmware save (0xC3) OK, but failed to send CPU Reset (0xB3)", DspErrorCodes.CPU_RESET_SEND_FAILED);
+            }
+            Log.i(TAG, "Step 3: Firmware saved and CPU reset sequence initiated successfully.");
+            notifyProgress(100);
+
+            // 获取升级后的DSP版本
+            Log.d(TAG, "Getting DSP version after upgrade...");
+            try {
+                // DSP重启后需要等待一段时间再读取版本
+                Thread.sleep(3000); // 等待3秒让DSP完全重启
+                String afterVersion = getCurrentDspVersion();
+                upgradeStatistics.setAfterUpgradeVersion(afterVersion);
+                Log.i(TAG, "DSP version after upgrade: " + afterVersion);
+            } catch (Exception e) {
+                Log.w(TAG, "Failed to get DSP version after upgrade: " + e.getMessage());
+                upgradeStatistics.setAfterUpgradeVersion("Failed to read");
+            }
+
+            success = true;
+            Log.i(TAG, "DSP Firmware Update process completed successfully!");
+
+        } catch (UpdateStepException e) {
+            failureMessage = e.getMessage();
+            dspErrorCode = e.getDspErrorCode();
+            Log.e(TAG, "DSP Firmware Update failed: " + failureMessage +
+                       (dspErrorCode != -1 ? " (DSP Error: 0x" + Integer.toHexString(dspErrorCode & 0xFF) + ")" : ""), e);
+        } catch (IOException e) {
+            failureMessage = "File I/O or communication error: " + e.getMessage();
+            dspErrorCode = DspErrorCodes.IO_EXCEPTION;
+            Log.e(TAG, "DSP Firmware Update failed due to IOException: " + failureMessage, e);
+        } catch (Exception e) {
+            failureMessage = "Unexpected error during update: " + e.getMessage();
+            dspErrorCode = DspErrorCodes.UNEXPECTED_ERROR;
+            Log.e(TAG, "DSP Firmware Update failed due to unexpected exception: " + failureMessage, e);
+        } finally {
+            // 在升级结束时打印统计汇总信息
+            upgradeStatistics.printSummary();
+            
+            if (success) {
+                notifySuccess();
+            } else {
+                notifyFailure(failureMessage, dspErrorCode);
+            }
+            isUpdating = false;
+            Log.i(TAG, "DSP Firmware Update process finished. isUpdating set to false.");
+        }
+    }
+
+    private boolean requestUpdateMode() throws IOException, UpdateStepException {
+        final int MAX_UPDATE_REQUEST_RETRIES = 3;
+        final int RETRY_DELAY_MS = 3000;
+        
+        UpdateStepException lastException = null;
+        
+        for (int attempt = 1; attempt <= MAX_UPDATE_REQUEST_RETRIES; attempt++) {
+            try {
+        byte[] command = buildCommand(CMD_UPDATE_REQUEST, DATA_C1_PARAM1, DATA_C1_PARAM2);
+                Log.d(TAG, "Sending 0xC1 - Attempt " + attempt + "/" + MAX_UPDATE_REQUEST_RETRIES + ": " + bytesToHexString(command));
+
+        byte[] coreResponse = sendAndReceiveCommand(command, CORE_DATA_LEN_C1_RESPONSE);
+        if (coreResponse == null || coreResponse.length != CORE_DATA_LEN_C1_RESPONSE) {
+            throw new UpdateStepException("0xC1: No valid response from DSP", DspErrorCodes.NO_RESPONSE_C1);
+        }
+
+        byte statusByte = coreResponse[0];
+        Log.d(TAG, "0xC1 Response status byte: " + String.format("0x%02X", statusByte));
+
+        if (statusByte == DSP_RESPONSE_C1_READY) {
+                    Log.i(TAG, "0xC1: DSP ready for update on attempt " + attempt);
+            return true;
+        } else if (statusByte == DSP_RESPONSE_C1_BUSY) {
+                    Log.w(TAG, "0xC1: DSP busy (attempt " + attempt + "/" + MAX_UPDATE_REQUEST_RETRIES + ")");
+            throw new UpdateStepException("0xC1: DSP busy", DspErrorCodes.DSP_BUSY_C1);
+        } else {
+            Log.e(TAG, "0xC1: DSP returned unknown status: " + String.format("0x%02X", statusByte));
+            throw new UpdateStepException("0xC1: Unknown DSP status " + String.format("0x%02X", statusByte), statusByte);
+        }
+                
+            } catch (UpdateStepException e) {
+                lastException = e;
+                Log.w(TAG, "0xC1: Attempt " + attempt + "/" + MAX_UPDATE_REQUEST_RETRIES + " failed: " + e.getMessage());
+                
+                if (attempt < MAX_UPDATE_REQUEST_RETRIES) {
+                    Log.i(TAG, "0xC1: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC1 retry delay");
+                    }
+                }
+            } catch (IOException e) {
+                Log.w(TAG, "0xC1: Attempt " + attempt + "/" + MAX_UPDATE_REQUEST_RETRIES + " failed due to IOException: " + e.getMessage());
+                
+                if (attempt < MAX_UPDATE_REQUEST_RETRIES) {
+                    Log.i(TAG, "0xC1: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC1 retry delay");
+                    }
+                } else {
+                    throw e; // Re-throw IOException on final attempt
+                }
+            }
+        }
+        
+        // All retries failed
+        Log.e(TAG, "0xC1: All " + MAX_UPDATE_REQUEST_RETRIES + " attempts failed to enter update mode.");
+        throw lastException != null ? lastException : 
+            new UpdateStepException("0xC1: Failed to enter update mode after " + MAX_UPDATE_REQUEST_RETRIES + " attempts", DspErrorCodes.NO_RESPONSE_C1);
+    }
+
+    private boolean transferFirmwareData() throws IOException, UpdateStepException {
+        File firmwareFile = new File(firmwarePath);
+        if (!firmwareFile.exists() || !firmwareFile.isFile() || !firmwareFile.canRead()) {
+            Log.e(TAG, "Firmware file not found, is not a file, or cannot be read: " + firmwarePath);
+            throw new UpdateStepException("Firmware file not found or not accessible: " + firmwarePath, DspErrorCodes.FILE_NOT_FOUND);
+        }
+
+        long fileSize = firmwareFile.length();
+        if (fileSize == 0) {
+            Log.e(TAG, "Firmware file is empty: " + firmwarePath);
+            throw new UpdateStepException("Firmware file is empty", DspErrorCodes.FILE_EMPTY);
+        }
+        Log.i(TAG, "Firmware file size: " + fileSize + " bytes.");
+
+        totalPacketsCalculated = (int) Math.ceil((double) fileSize / MAX_FIRMWARE_DATA_PER_PACKET);
+        Log.i(TAG, "Total data packets to send (excluding final empty packet): " + totalPacketsCalculated);
+        notifyProgress(10); // Initial progress after file checks
+
+        try (InputStream inputStream = new FileInputStream(firmwareFile)) {
+            byte[] buffer = new byte[MAX_FIRMWARE_DATA_PER_PACKET];
+            int bytesRead;
+            packetsSuccessfullySent = 0;
+
+            while ((bytesRead = inputStream.read(buffer)) != -1) {
+                byte[] actualData = Arrays.copyOf(buffer, bytesRead);
+                byte length = (byte) (bytesRead & 0xFF);
+
+                byte sum = calculateChecksum(length, actualData);
+
+                boolean packetSentAndAcked = false;
+                for (int retry = 0; retry <= MAX_C2_RETRIES; retry++) { // 0 is first attempt, 1-MAX_C2_RETRIES are retries
+                    BankResponse response = sendBankPacketDirectly((byte)currentBankNumber, sum, length, actualData);
+                    if (response.success) {
+                        packetSentAndAcked = true;
+                        break; // Success, exit retry loop
+                    } else {
+                        Log.w(TAG, String.format(Locale.US, "BANK %d send/ack failed (Attempt %d/%d). Error: %s, DSP_ErrorCode: 0x%02X",
+                                                 (currentBankNumber & 0xFF), retry + 1, MAX_C2_RETRIES + 1, response.errorMessage, (response.errorCode & 0xFF)));
+                        if (retry < MAX_C2_RETRIES) {
+                            Log.i(TAG, "Retrying BANK " + (currentBankNumber & 0xFF) + " after 2 seconds...");
+                            try { Thread.sleep(2000); } catch (InterruptedException ie) { Thread.currentThread().interrupt(); throw new IOException("Thread interrupted during retry delay"); }
+                        }
+                    }
+                }
+
+                if (!packetSentAndAcked) {
+                    String errorMsg = String.format(Locale.US, "Critical error: Failed to send/ack BANK %d after %d retries. Aborting update.",
+                                                    (currentBankNumber & 0xFF), MAX_C2_RETRIES + 1);
+                    Log.e(TAG, errorMsg);
+                    throw new UpdateStepException(errorMsg, DspErrorCodes.BANK_SEND_ACK_FAILED_AFTER_RETRIES);
+                }
+                
+                packetsSuccessfullySent++;
+
+                if (totalPacketsCalculated > 0) {
+                    // Progress: 10% (initial) + 80% (data transfer) + 5% (empty packet) + 5% (save/reset)
+                    int currentProgress = 10 + (int) (((double) packetsSuccessfullySent / totalPacketsCalculated) * 80);
+                    notifyProgress(Math.min(currentProgress, 90)); // Cap at 90 before empty packet
+                }
+
+                currentBankNumber++;
+                if (currentBankNumber > BANK_NUMBER_MAX || currentBankNumber == 0) { // currentBankNumber is int, so ==0 check is for safety, though > BANK_NUMBER_MAX should catch it.
+                    currentBankNumber = 1;
+                }
+            }
+
+            // Send the final empty BANK packet to signify end of data transfer for C2 stage
+            Log.i(TAG, "All data packets sent. Sending empty BANK packet to finalize C2 transfer stage...");
+            byte emptyLength = 0x00;
+            byte[] emptyData = new byte[0];
+            byte emptySum = calculateChecksum(emptyLength, emptyData);
+            
+            boolean emptyPacketSent = sendEmptyBankPacketDirectly((byte)currentBankNumber, emptySum, emptyLength, emptyData).success;
+
+            if (!emptyPacketSent) {
+                String errorMsg = String.format(Locale.US, "Failed to send empty BANK %d after retries. Aborting update.",
+                                                (currentBankNumber & 0xFF));
+                Log.e(TAG, errorMsg);
+                throw new UpdateStepException(errorMsg, DspErrorCodes.EMPTY_BANK_SEND_ACK_FAILED_AFTER_RETRIES);
+            }
+
+            Log.i(TAG, "Empty BANK packet sent and processed.");
+            notifyProgress(95); // Progress after successful C2 stage including empty packet
+            
+            
+            return true;
+        }
+    }
+
+    private BankResponse sendBankPacketDirectly(byte bank, byte sum, byte length, byte[] data) throws IOException, UpdateStepException {
+        byte[] command = buildCommandForC2(bank, sum, length, data);
+
+        if (DEBUG) Log.d(TAG, String.format(Locale.US, "Sending 0xC2 - BANK: %d, SUM: 0x%02X, LENGTH: %d, Data: %s",
+                                 (bank & 0xFF), sum, (length & 0xFF), (data != null && data.length > 0 ? bytesToHexString(data).substring(0, Math.min(bytesToHexString(data).length(), 30)) : "empty") +"..."));
+
+        if (communicationManager == null) {
+            Log.e(TAG, "IDspCommunicationManager (communicationManager) is null in sendBankPacketDirectly");
+            throw new UpdateStepException("IDspCommunicationManager not initialized", DspErrorCodes.USB_MANAGER_NOT_READY);
+        }
+        
+        int bytesSent = communicationManager.writeForUpgrade(command, WRITE_TIMEOUT_MS);
+        if (bytesSent != command.length) {
+            // 统计C2发送失败
+            upgradeStatistics.getC2Stats().recordSendFailure();
+            String errorMsg = String.format(Locale.US, "Failed to send full C2 command for BANK %d. Sent %d/%d", (bank & 0xFF), bytesSent, command.length);
+            Log.e(TAG, errorMsg);
+            // Still attempt to read a response, as DSP might have processed a partial command or be in an error state.
+            // However, it's more likely a send failure means no response or an unpredictable one.
+            // For robustness, we could try to read, but it's safer to assume failure here.
+            return new BankResponse(false, bank, (byte)0xFF, errorMsg); // Indicate send failure
+        }
+        
+        // 统计C2发送成功
+        upgradeStatistics.getC2Stats().recordSendSuccess();
+        
+        if (DEBUG) Log.d(TAG, "BANK " + (bank & 0xFF) + " data sent. Waiting for response...");
+        return readAndVerifyBankResponse(bank);
+    }
+
+
+
+    /**
+     * Sends empty BANK packet with special handling: success if sent, regardless of DSP response
+     */
+    private BankResponse sendEmptyBankPacketDirectly(byte bank, byte sum, byte length, byte[] data) throws IOException, UpdateStepException {
+        byte[] command = buildCommandForC2(bank, sum, length, data);
+
+        Log.d(TAG, String.format(Locale.US, "Sending 0xC2 EMPTY BANK: %d, SUM: 0x%02X, LENGTH: %d",
+                                 (bank & 0xFF), sum, (length & 0xFF)));
+
+        if (communicationManager == null) {
+            Log.e(TAG, "IDspCommunicationManager (communicationManager) is null in sendEmptyBankPacketDirectly");
+            throw new UpdateStepException("IDspCommunicationManager not initialized", DspErrorCodes.USB_MANAGER_NOT_READY);
+        }
+        
+        int bytesSent = communicationManager.writeForUpgrade(command, WRITE_TIMEOUT_MS);
+        if (bytesSent != command.length) {
+            // 统计C2空包发送失败
+            upgradeStatistics.getC2Stats().recordSendFailure();
+            String errorMsg = String.format(Locale.US, "Failed to send full empty C2 command for BANK %d. Sent %d/%d", (bank & 0xFF), bytesSent, command.length);
+            Log.e(TAG, errorMsg);
+            return new BankResponse(false, bank, (byte)0xFF, errorMsg);
+        }
+        
+        // 统计C2空包发送成功
+        upgradeStatistics.getC2Stats().recordSendSuccess();
+        
+        Log.d(TAG, "Empty BANK " + (bank & 0xFF) + " data sent. Attempting to read response...");
+        
+        // For empty packet: Try to read response but don't fail if no response or error response
+        try {
+            BankResponse response = readAndVerifyBankResponse(bank);
+            if (response.success) {
+                Log.i(TAG, "Empty BANK " + (bank & 0xFF) + " sent and received valid response.");
+                // 空包接收成功：收到有效应答（readAndVerifyBankResponse已经统计了接收成功）
+                return new BankResponse(true, bank, (byte)0x00, "Empty packet sent and acknowledged");
+            } else {
+                Log.w(TAG, "Empty BANK " + (bank & 0xFF) + " sent but received error response: " + response.errorMessage + 
+                           ". This is acceptable for empty packets.");
+                // 空包特殊处理：应答错误不算接收成功（readAndVerifyBankResponse已经统计了接收失败）
+                return new BankResponse(true, bank, response.errorCode, "Empty packet sent (DSP response error ignored)");
+            }
+        } catch (Exception e) {
+            Log.w(TAG, "Empty BANK " + (bank & 0xFF) + " sent but failed to read response: " + e.getMessage() + 
+                       ". This is acceptable for empty packets.");
+            // 空包特殊处理：无应答不算接收成功，需要统计接收失败
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            return new BankResponse(true, bank, (byte)0xFF, "Empty packet sent (no DSP response)");
+        }
+    }
+
+    // This method seems to be the one that should orchestrate C3 and B3 calls.
+    // Let's ensure it calls the correctly named methods.
+    private boolean saveFirmwareAndResetCpu() throws IOException, UpdateStepException {
+        // 问题2修复：注释掉0xC3重试机制，改为单次尝试后直接发0xB3
+        // final int MAX_C3_RETRIES = 3;
+        // final int RETRY_DELAY_MS = 2000;
+        
+        // UpdateStepException lastException = null;
+        
+        // 改为单次尝试，不重试
+        try {
+            byte[] commandC3 = buildCommand(CMD_SAVE_FIRMWARE, DATA_C3_PARAM1, DATA_C3_PARAM2);
+            Log.d(TAG, "Sending 0xC3 (Save Firmware) - Single attempt (no retries): " + bytesToHexString(commandC3));
+
+            byte[] coreResponseC3 = sendAndReceiveCommand(commandC3, CORE_DATA_LEN_C3_RESPONSE);
+            if (coreResponseC3 == null || coreResponseC3.length != CORE_DATA_LEN_C3_RESPONSE) {
+                Log.w(TAG, "0xC3: No valid response from DSP - proceeding to CPU reset anyway");
+                // 不抛异常，直接继续执行CPU重置
+            } else {
+                byte statusByteC3 = coreResponseC3[0];
+                Log.d(TAG, "0xC3 Response status byte: " + String.format("0x%02X", statusByteC3));
+
+                if (statusByteC3 == DSP_RESPONSE_C3_SAVE_OK) {
+                    Log.i(TAG, "0xC3: Firmware save successful. Proceeding to CPU reset.");
+                } else {
+                    Log.w(TAG, "0xC3: Firmware save response indicates error. DSP Status: " + String.format("0x%02X", statusByteC3) + " - proceeding to CPU reset anyway");
+                }
+            }
+            
+        } catch (UpdateStepException e) {
+            Log.w(TAG, "0xC3: Command failed: " + e.getMessage() + " - proceeding to CPU reset anyway");
+        } catch (IOException e) {
+            Log.w(TAG, "0xC3: IOException during command: " + e.getMessage() + " - proceeding to CPU reset anyway");
+        }
+        
+        // 等待3秒后发送0xB3 CPU重置命令
+        Log.i(TAG, "Waiting 3 seconds before sending CPU reset command...");
+        try {
+            Thread.sleep(3000); // 3 seconds delay as per task 7 requirements
+        } catch (InterruptedException ie) {
+            Thread.currentThread().interrupt();
+            throw new IOException("Thread interrupted during pre-CPU-reset delay");
+        }
+        
+        if (!sendB3CpuResetCommand()) {
+            Log.e(TAG, "0xB3: Failed to send CPU reset command. Update considered complete but reset might be manual.");
+            return false;
+        }
+        Log.i(TAG, "0xB3: CPU Reset command sent successfully.");
+        return true;
+        
+        /* 原重试逻辑已注释，保留供参考：
+        final int MAX_C3_RETRIES = 3;
+        final int RETRY_DELAY_MS = 2000;
+        UpdateStepException lastException = null;
+        for (int attempt = 1; attempt <= MAX_C3_RETRIES; attempt++) {
+            try {
+                byte[] commandC3 = buildCommand(CMD_SAVE_FIRMWARE, DATA_C3_PARAM1, DATA_C3_PARAM2);
+                Log.d(TAG, "Sending 0xC3 (Save Firmware) - Attempt " + attempt + "/" + MAX_C3_RETRIES + ": " + bytesToHexString(commandC3));
+
+                byte[] coreResponseC3 = sendAndReceiveCommand(commandC3, CORE_DATA_LEN_C3_RESPONSE);
+                if (coreResponseC3 == null || coreResponseC3.length != CORE_DATA_LEN_C3_RESPONSE) {
+                    throw new UpdateStepException("0xC3: No valid response from DSP", DspErrorCodes.NO_RESPONSE_C3);
+                }
+
+                byte statusByteC3 = coreResponseC3[0];
+                Log.d(TAG, "0xC3 Response status byte: " + String.format("0x%02X", statusByteC3));
+
+                if (statusByteC3 == DSP_RESPONSE_C3_SAVE_OK) {
+                    Log.i(TAG, "0xC3: Firmware save successful on attempt " + attempt + ". Proceeding to CPU reset.");
+                    
+                    // Wait 3 seconds after firmware save before CPU reset
+                    Log.i(TAG, "Waiting 3 seconds before sending CPU reset command...");
+                    try {
+                        Thread.sleep(3000); // 3 seconds delay as per task 7 requirements
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during pre-CPU-reset delay");
+                    }
+                    
+                    if (!sendB3CpuResetCommand()) {
+                        Log.e(TAG, "0xB3: Failed to send CPU reset command after successful save. Update considered complete but reset might be manual.");
+                        return false;
+                    }
+                    Log.i(TAG, "0xB3: CPU Reset command sent successfully.");
+                    return true;
+                } else {
+                    Log.e(TAG, "0xC3: Firmware save failed. DSP Status: " + String.format("0x%02X", statusByteC3));
+                    throw new UpdateStepException("0xC3: Firmware save failed. DSP Status: " + String.format("0x%02X", statusByteC3), statusByteC3);
+                }
+                
+            } catch (UpdateStepException e) {
+                lastException = e;
+                Log.w(TAG, "0xC3: Attempt " + attempt + "/" + MAX_C3_RETRIES + " failed: " + e.getMessage());
+                
+                if (attempt < MAX_C3_RETRIES) {
+                    Log.i(TAG, "0xC3: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC3 retry delay");
+                    }
+                }
+            } catch (IOException e) {
+                Log.w(TAG, "0xC3: Attempt " + attempt + "/" + MAX_C3_RETRIES + " failed due to IOException: " + e.getMessage());
+                
+                if (attempt < MAX_C3_RETRIES) {
+                    Log.i(TAG, "0xC3: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC3 retry delay");
+                    }
+                } else {
+                    throw e; // Re-throw IOException on final attempt
+                }
+            }
+        }
+        
+        // All retries failed
+        Log.e(TAG, "0xC3: All " + MAX_C3_RETRIES + " attempts failed to save firmware.");
+        throw lastException != null ? lastException : 
+            new UpdateStepException("0xC3: Failed to save firmware after " + MAX_C3_RETRIES + " attempts", DspErrorCodes.NO_RESPONSE_C3);
+        */
+    }
+
+    private BankResponse readAndVerifyBankResponse(byte expectedBank) throws UpdateStepException {
+        if (DEBUG) Log.d(TAG, "readAndVerifyBankResponse called for expected BANK: " + (expectedBank & 0xFF));
+        byte[] rawResponse = new byte[6]; // Frame: 7B 7D BANK ERROR 7D 7B
+        int bytesRead; 
+
+        if (communicationManager == null) {
+            Log.e(TAG, "IDspCommunicationManager (communicationManager) is null in readAndVerifyBankResponse");
+            throw new UpdateStepException("IDspCommunicationManager not initialized", DspErrorCodes.USB_MANAGER_NOT_READY);
+        }
+
+        bytesRead = communicationManager.readForUpgrade(rawResponse, RESPONSE_TIMEOUT_C2_MS);
+
+        if (bytesRead < 0) { 
+            // C2接收失败：读取错误
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            Log.e(TAG, "Error during readForUpgrade for BANK response, read returned: " + bytesRead);
+            return new BankResponse(false, expectedBank, (byte)0xFF, "Error reading BANK response: " + bytesRead);
+        }
+
+        if (bytesRead == 0) { 
+            // C2接收失败：超时
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            Log.w(TAG, "Timeout waiting for BANK response for expected BANK: " + (expectedBank & 0xFF));
+            return new BankResponse(false, expectedBank, (byte)0xFF, "Timeout waiting for BANK response");
+        }
+
+        if (bytesRead < rawResponse.length) { 
+            // C2接收失败：不完整应答
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            Log.w(TAG, String.format(Locale.US, "Incomplete BANK response for expected BANK %d. Expected %d, got %d. Data: %s",
+                                     (expectedBank & 0xFF), rawResponse.length, bytesRead, bytesToHexString(Arrays.copyOf(rawResponse, bytesRead))));
+            return new BankResponse(false, expectedBank, (byte)0xFF, "Incomplete BANK response. Expected " + rawResponse.length + " got " + bytesRead);
+        }
+
+        if (DEBUG) Log.d(TAG, "Received BANK response: " + bytesToHexString(rawResponse) + " for expected BANK: " + (expectedBank & 0xFF));
+
+        if (rawResponse[0] != FRAME_HEADER_START || rawResponse[1] != FRAME_HEADER_END ||
+            rawResponse[4] != FRAME_TRAILER_START || rawResponse[5] != FRAME_TRAILER_END) {
+            // C2接收失败：帧格式错误
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            Log.e(TAG, "Invalid BANK response frame. Data: " + bytesToHexString(rawResponse));
+            return new BankResponse(false, rawResponse[2], rawResponse[3], "Invalid BANK response frame");
+        }
+
+        byte actualReceivedBank = rawResponse[2];
+        byte errorCode = rawResponse[3];
+
+        if ((actualReceivedBank & 0xFF) != (expectedBank & 0xFF)) {
+            // C2接收失败：BANK号不匹配
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            String msg = String.format(Locale.US, "BANK mismatch. Expected: %d, Got: %d. Full response: %s",
+                                       (expectedBank & 0xFF), (actualReceivedBank & 0xFF), bytesToHexString(rawResponse));
+            Log.w(TAG, msg);
+            return new BankResponse(false, actualReceivedBank, errorCode, msg);
+        }
+
+        if (errorCode != DSP_RESPONSE_C2_ERROR_OK) {
+            // C2接收失败：ERROR码非0
+            upgradeStatistics.getC2Stats().recordReceiveFailure();
+            String msg = String.format(Locale.US, "BANK %d response contains error code: 0x%02X", (actualReceivedBank & 0xFF), errorCode);
+            Log.w(TAG, msg);
+            return new BankResponse(false, actualReceivedBank, errorCode, msg);
+        }
+
+        // C2接收成功：所有检查都通过
+        upgradeStatistics.getC2Stats().recordReceiveSuccess();
+        return new BankResponse(true, actualReceivedBank, errorCode, "Success");
+    }
+
+private boolean sendB3CpuResetCommand() throws UpdateStepException {
+    byte[] command = buildCommand(CMD_CPU_RESET, DATA_B3_PARAM1, DATA_B3_PARAM2);
+    Log.d(TAG, "Sending 0xB3 (CPU Reset): " + bytesToHexString(command));
+
+    if (communicationManager == null) {
+        Log.e(TAG, "IDspCommunicationManager is null in sendB3CpuResetCommand");
+        throw new UpdateStepException("IDspCommunicationManager not initialized", DspErrorCodes.USB_MANAGER_NOT_READY);
+    }
+
+    int bytesSent = communicationManager.writeForUpgrade(command, WRITE_TIMEOUT_MS); // Changed to writeForUpgrade
+    if (bytesSent != command.length) {
+        Log.e(TAG, "Failed to send full 0xB3 CPU Reset command. Sent " + bytesSent + "/" + command.length);
+        return false;
+    }
+    return true;
+}
+
+
+    private byte[] buildCommand(byte commandCode, byte... dataPayload) {
+        byte[] frame = new byte[2 + 1 + 1 + dataPayload.length + 2];
+        int i = 0;
+        frame[i++] = FRAME_HEADER_START;
+        frame[i++] = FRAME_HEADER_END;
+        frame[i++] = DEFAULT_DEVICE_ADDRESS;
+        frame[i++] = commandCode;
+        if (dataPayload.length > 0) {
+            System.arraycopy(dataPayload, 0, frame, i, dataPayload.length);
+            i += dataPayload.length;
+        }
+        frame[i++] = FRAME_TRAILER_START;
+        frame[i++] = FRAME_TRAILER_END;
+        return frame;
+    }
+
+    private byte[] buildCommandForC2(byte bank, byte sum, byte length, byte[] data) {
+        int dataLength = (data != null) ? data.length : 0;
+        if ((length & 0xFF) != dataLength) {
+            Log.w(TAG, "buildCommandForC2: Mismatch between protocol 'length' byte (" + (length & 0xFF) + ") and actual data array length (" + dataLength + "). Protocol length field will be used in the command.");
+        }
+
+        byte[] frame = new byte[2 + 1 + 1 + 1 + 1 + 1 + dataLength + 2];
+        int idx = 0;
+        frame[idx++] = FRAME_HEADER_START;
+        frame[idx++] = FRAME_HEADER_END;
+        frame[idx++] = DEFAULT_DEVICE_ADDRESS;
+        frame[idx++] = CMD_BANK_TRANSFER;
+        frame[idx++] = bank;
+        frame[idx++] = sum;
+        frame[idx++] = length;
+        if (data != null && dataLength > 0) {
+            System.arraycopy(data, 0, frame, idx, dataLength);
+            idx += dataLength;
+        }
+        frame[idx++] = FRAME_TRAILER_START;
+        frame[idx++] = FRAME_TRAILER_END;
+        return frame;
+    }
+
+/**
+     * Calculates the checksum for a C2 BANK_TRANSFER packet based on the formula:
+     * SUM = (LENGTH + DATA1 + ... + DATAn) % 255
+     *
+     * @param length The value of the LENGTH field (number of bytes in the data payload).
+     * @param data The data payload (DATA1...DATAn).
+     * @return The calculated checksum byte.
+     */
+    private byte calculateChecksum(byte length, byte[] data) {
+        int sum = 0;
+        sum += (length & 0xFF); // Add LENGTH, treating byte as unsigned
+        if (data != null) {
+            for (byte b : data) {
+                sum += (b & 0xFF); // Add each data byte, treating as unsigned
+            }
+        }
+        return (byte) (sum % 255);
+    }
+
+    private byte[] sendAndReceiveCommand(byte[] commandBytes, int expectedCoreDataLength) throws IOException, UpdateStepException {
+        if (communicationManager == null) {
+            Log.e(TAG, "IDspCommunicationManager (communicationManager) is null in sendAndReceiveCommand");
+            throw new UpdateStepException("IDspCommunicationManager not initialized", DspErrorCodes.USB_MANAGER_NOT_READY);
+        }
+
+        // 提取命令码用于统计
+        byte commandCode = commandBytes.length >= 4 ? commandBytes[3] : 0;
+        DspCommandStatistics cmdStats = getStatisticsForCommand(commandCode);
+
+        communicationManager.enterReadOperation();
+        byte[] rawResponse = null;
+        try {
+            int bytesSent = communicationManager.writeForUpgrade(commandBytes, WRITE_TIMEOUT_MS);
+            if (bytesSent != commandBytes.length) {
+                // 统计发送失败
+                if (cmdStats != null) cmdStats.recordSendFailure();
+                Log.e(TAG, "Failed to send full command. Sent " + bytesSent + "/" + commandBytes.length + ". Command: " + bytesToHexString(commandBytes));
+                throw new IOException("Failed to send full command (sent " + bytesSent + "/" + commandBytes.length + ")");
+            }
+            
+            // 统计发送成功
+            if (cmdStats != null) cmdStats.recordSendSuccess();
+
+            byte[] responseBuffer = new byte[128];
+            
+            // 根据命令码选择适当的超时时间
+            int timeoutMs;
+            switch (commandCode) {
+                case CMD_GET_VERSION:      // 0xC0
+                    timeoutMs = RESPONSE_TIMEOUT_C0_MS;
+                    break;
+                case CMD_UPDATE_REQUEST:   // 0xC1
+                    timeoutMs = RESPONSE_TIMEOUT_C1_MS;
+                    break;
+                case CMD_SAVE_FIRMWARE:    // 0xC3
+                    timeoutMs = RESPONSE_TIMEOUT_C3_MS;
+                    break;
+                default:
+                    timeoutMs = RESPONSE_TIMEOUT_C1_MS; // 默认使用C1的超时时间
+                    break;
+            }
+            
+
+            Log.d(TAG, String.format("sendAndReceiveCommand: Command 0x%02X using timeout %d ms (expected: 0xC3->%d, 0xC1->%d, 0xC0->%d)", 
+                    commandCode, timeoutMs, RESPONSE_TIMEOUT_C3_MS, RESPONSE_TIMEOUT_C1_MS, RESPONSE_TIMEOUT_C0_MS));
+            
+            int bytesRead = communicationManager.readForUpgrade(responseBuffer, timeoutMs);
+
+            if (bytesRead > 0) {
+                rawResponse = Arrays.copyOf(responseBuffer, bytesRead);
+                Log.d(TAG, "Raw response from DSP (sync read): " + bytesToHexString(rawResponse) + " for command: " + bytesToHexString(commandBytes).substring(0, Math.min(bytesToHexString(commandBytes).length(), 30))+"...");
+            } else {
+                // 统计接收失败（超时或错误）
+                if (cmdStats != null) cmdStats.recordReceiveFailure();
+                Log.w(TAG, "No data read from DSP (timeout or error). Bytes read: " + bytesRead + " for command: " + bytesToHexString(commandBytes).substring(0, Math.min(bytesToHexString(commandBytes).length(), 30))+"...");
+                return null;
+            }
+        } finally {
+            communicationManager.exitReadOperation();
+        }
+
+        int expectedFullFrameLength = 2 + expectedCoreDataLength + 2;
+
+        // Special handling for 0xC3 command: check if received delayed 0xC2 empty packet response
+        if (commandCode == CMD_SAVE_FIRMWARE && rawResponse.length == 6) {
+            // 0xC3 command received 6-byte response, might be delayed 0xC2 response
+            if (rawResponse[0] == FRAME_HEADER_START && rawResponse[1] == FRAME_HEADER_END &&
+                rawResponse[4] == FRAME_TRAILER_START && rawResponse[5] == FRAME_TRAILER_END) {
+                byte possibleBank = rawResponse[2];
+                byte possibleError = rawResponse[3];
+                Log.w(TAG, "0xC3 received 6-byte response that looks like delayed 0xC2 format: BANK=" + 
+                          String.format("0x%02X", possibleBank) + " (" + (possibleBank & 0xFF) + "), ERROR=" + 
+                          String.format("0x%02X", possibleError));
+                
+                // Check if this could be delayed 0xC2 empty packet response
+                if ((possibleBank & 0xFF) >= 40) { // Reasonable range for empty packet BANK number
+                    Log.i(TAG, "DETECTED: 0xC3 command received delayed 0xC2 EMPTY BANK response. Attempting to read actual 0xC3 response...");
+                    
+                    // Try to read the actual 0xC3 response
+                    byte[] secondResponse = new byte[128];
+                    int secondBytesRead = communicationManager.readForUpgrade(secondResponse, RESPONSE_TIMEOUT_C3_MS);
+                    
+                    if (secondBytesRead > 0) {
+                        rawResponse = Arrays.copyOf(secondResponse, secondBytesRead);
+                        Log.i(TAG, "Successfully read actual 0xC3 response after delayed 0xC2: " + bytesToHexString(rawResponse));
+                    } else {
+                        Log.w(TAG, "No second response received for 0xC3 after delayed 0xC2. Using delayed response as failure indicator.");
+                        // 统计接收失败（收到错误的应答类型）
+                        if (cmdStats != null) cmdStats.recordReceiveFailure();
+                        return null;
+                    }
+                }
+            }
+        }
+
+        if (rawResponse.length == expectedFullFrameLength &&
+            rawResponse[0] == FRAME_HEADER_START && rawResponse[1] == FRAME_HEADER_END &&
+            rawResponse[rawResponse.length - 2] == FRAME_TRAILER_START && rawResponse[rawResponse.length - 1] == FRAME_TRAILER_END) {
+
+            // 统计接收成功（帧格式正确，具体内容验证在调用方进行）
+            if (cmdStats != null) cmdStats.recordReceiveSuccess();
+
+            byte[] coreData = new byte[expectedCoreDataLength];
+            System.arraycopy(rawResponse, 2, coreData, 0, expectedCoreDataLength);
+            return coreData;
+        } else {
+            // 统计接收失败（帧格式错误）
+            if (cmdStats != null) cmdStats.recordReceiveFailure();
+            Log.e(TAG, "Invalid frame received. Expected full length " + expectedFullFrameLength + ", Got: " + rawResponse.length +
+                       ". Content: " + bytesToHexString(rawResponse) + ". Command sent: " + bytesToHexString(commandBytes).substring(0, Math.min(bytesToHexString(commandBytes).length(), 30))+"...");
+            return null;
+        }
+    }
+
+    private void notifyProgress(final int percentage) {
+        final int p = Math.max(0, Math.min(100, percentage));
+        Log.d(TAG, "Update progress: " + p + "%");
+        if (listener != null) {
+            mainHandler.post(() -> {
+                if (listener != null) listener.onProgress(p);
+            });
+        }
+    }
+
+    private void notifySuccess() {
+        if (listener != null) {
+            mainHandler.post(listener::onSuccess);
+        }
+        Log.i(TAG, "Firmware update SUCCESSFUL.");
+    }
+
+    private void notifyFailure(final String errorMessage, final int dspErrorCode) {
+        if (listener != null) {
+            mainHandler.post(() -> listener.onFailure(errorMessage, dspErrorCode));
+        }
+    }
+
+    /**
+     * 根据命令码获取对应的统计对象
+     */
+    private DspCommandStatistics getStatisticsForCommand(byte commandCode) {
+        switch (commandCode) {
+            case CMD_GET_VERSION:      // 0xC0
+                return upgradeStatistics.getC0Stats();
+            case CMD_UPDATE_REQUEST:   // 0xC1
+                return upgradeStatistics.getC1Stats();
+            case CMD_BANK_TRANSFER:    // 0xC2
+                return upgradeStatistics.getC2Stats();
+            case CMD_SAVE_FIRMWARE:    // 0xC3
+                return upgradeStatistics.getC3Stats();
+            default:
+                // 对于不统计的命令（如0xB3），返回null表示不进行统计
+                return null;
+        }
+    }
+
+    private static String bytesToHexString(byte[] bytes) {
+        if (bytes == null) return "null";
+        if (bytes.length == 0) return "empty";
+        StringBuilder sb = new StringBuilder();
+        for (byte b : bytes) {
+            sb.append(String.format("%02X ", b));
+        }
+        return sb.toString().trim();
+    }
+
+    public static class UpdateStepException extends Exception {
+        private final int dspErrorCodeForListener;
+
+        public UpdateStepException(String message, int appOrDspErrorCode) {
+            super(message);
+            this.dspErrorCodeForListener = appOrDspErrorCode;
+        }
+
+        public int getDspErrorCode() {
+            return dspErrorCodeForListener;
+        }
+    }
+
+    private static class DspErrorCodes {
+        public static final int USB_MANAGER_NOT_READY = 0x1001;
+        public static final int FILE_NOT_FOUND = 0x1002;
+        public static final int FILE_EMPTY = 0x1003;
+        public static final int UPDATE_MODE_REQUEST_FAILED = 0x1004;
+        public static final int NO_RESPONSE_C1 = 0x1005;
+        public static final int DSP_BUSY_C1 = 0x1006;
+        public static final int FIRMWARE_TRANSFER_FAILED = 0x1008;
+        public static final int BANK_SEND_FAILED_RETRIES = 0x1009;
+        public static final int NO_RESPONSE_C2 = 0x100A;
+        public static final int BANK_MISMATCH_C2 = 0x100B;
+        public static final int EMPTY_BANK_SEND_FAILED = 0x100C;
+        public static final int FIRMWARE_SAVE_FAILED = 0x100D;
+        public static final int NO_RESPONSE_C3 = 0x100E;
+        public static final int IO_EXCEPTION = 0x1010;
+        public static final int UNEXPECTED_ERROR = 0x1011;
+        public static final int BANK_SEND_FAILED_DIRECTLY = 0x1012;
+        public static final int BANK_SEND_ACK_FAILED_AFTER_RETRIES = 0x1016;
+        public static final int EMPTY_BANK_SEND_ACK_FAILED_AFTER_RETRIES = 0x1017;
+        public static final int CPU_RESET_SEND_FAILED = 0x1013;
+        public static final int GET_VERSION_FAILED_RESPONSE = 0x1014;
+        public static final int GET_VERSION_FAILED_INVALID_FORMAT = 0x1015;
+    }
+
+    public String getCurrentDspVersion() throws IOException, UpdateStepException {
+        final int MAX_VERSION_RETRIES = 8;
+        final int RETRY_DELAY_MS = 2000;
+        
+        UpdateStepException lastException = null;
+        
+        for (int attempt = 1; attempt <= MAX_VERSION_RETRIES; attempt++) {
+            try {
+        byte[] command = buildCommand(CMD_GET_VERSION, DATA_C0_PARAM1, DATA_C0_PARAM2);
+                Log.d(TAG, "Sending 0xC0 (Get Version) - Attempt " + attempt + "/" + MAX_VERSION_RETRIES + ": " + bytesToHexString(command));
+
+        byte[] coreResponse = sendAndReceiveCommand(command, CORE_DATA_LEN_C0_RESPONSE);
+
+        if (coreResponse == null || coreResponse.length != CORE_DATA_LEN_C0_RESPONSE) {
+            throw new UpdateStepException("0xC0: No valid response from DSP for version check", DspErrorCodes.GET_VERSION_FAILED_RESPONSE);
+        }
+
+        byte responseCC = coreResponse[0];
+        if (responseCC != DSP_RESPONSE_C0_EXPECTED_CC_BYTE) {
+            Log.e(TAG, String.format("0xC0: Response command byte mismatch. Expected 0x%02X, Got 0x%02X", DSP_RESPONSE_C0_EXPECTED_CC_BYTE, responseCC));
+            throw new UpdateStepException(String.format("0xC0: Version response format error (CC byte mismatch: 0x%02X)", responseCC), DspErrorCodes.GET_VERSION_FAILED_INVALID_FORMAT);
+        }
+
+        int major = coreResponse[1] & 0xFF;
+        int minor = coreResponse[2] & 0xFF;
+        int revision = coreResponse[3] & 0xFF;
+
+        String version = String.format(Locale.US, "%d.%d.%d", major, minor, revision);
+        Log.i(TAG, "0xC0: DSP Version successfully read: " + version);
+        return version;
+                
+            } catch (UpdateStepException e) {
+                lastException = e;
+                Log.w(TAG, "0xC0: Attempt " + attempt + "/" + MAX_VERSION_RETRIES + " failed: " + e.getMessage());
+                
+                if (attempt < MAX_VERSION_RETRIES) {
+                    Log.i(TAG, "0xC0: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC0 retry delay");
+                    }
+                }
+            } catch (IOException e) {
+                Log.w(TAG, "0xC0: Attempt " + attempt + "/" + MAX_VERSION_RETRIES + " failed due to IOException: " + e.getMessage());
+                
+                if (attempt < MAX_VERSION_RETRIES) {
+                    Log.i(TAG, "0xC0: Retrying after " + RETRY_DELAY_MS + "ms...");
+                    try {
+                        Thread.sleep(RETRY_DELAY_MS);
+                    } catch (InterruptedException ie) {
+                        Thread.currentThread().interrupt();
+                        throw new IOException("Thread interrupted during 0xC0 retry delay");
+                    }
+                } else {
+                    throw e; // Re-throw IOException on final attempt
+                }
+            }
+        }
+        
+        // All retries failed
+        Log.e(TAG, "0xC0: All " + MAX_VERSION_RETRIES + " attempts failed to get DSP version.");
+        throw lastException != null ? lastException : 
+            new UpdateStepException("0xC0: Failed to get DSP version after " + MAX_VERSION_RETRIES + " attempts", DspErrorCodes.GET_VERSION_FAILED_RESPONSE);
+    }
+} 
\ No newline at end of file
diff --git a/ota/src/main/java/com/eeo/ota/dsp/DspUpdate.java b/ota/src/main/java/com/eeo/ota/dsp/DspUpdate.java
new file mode 100644
index 0000000..c1b6893
--- /dev/null
+++ b/ota/src/main/java/com/eeo/ota/dsp/DspUpdate.java
@@ -0,0 +1,343 @@
+package com.eeo.ota.dsp;
+
+import android.content.Context;
+import android.util.Log;
+
+// Assuming SubDeviceUpdateCallback can be reused, or a new DspUpdateCallback would be created similarly.
+import com.eeo.ota.callback.SubDeviceUpdateCallback;
+// Import the interface instead
+import com.eeo.ota.dsp.IDspCommunicationManager;
+
+// Imports for version checking logic
+import org.json.JSONArray;
+import org.json.JSONObject;
+import org.json.JSONException;
+import java.io.File;
+import java.io.FileInputStream;
+import java.io.BufferedReader;
+import java.io.InputStreamReader;
+import java.io.IOException;
+import java.nio.charset.StandardCharsets;
+import java.util.Locale;
+import java.util.Arrays;
+
+public class DspUpdate {
+    public static final String TAG = "DspUpdate";
+
+    // Path to the DSP configuration file on the Android system
+    private static final String DSP_CONFIG_FILE_PATH = "/system/ota/dsp_config.json";
+    // Base path for firmware files listed in the config
+    private static final String DSP_FIRMWARE_BASE_PATH = "/system/ota/";
+
+    private Context mContext;
+    private DspFirmwareUpdater mDspFirmwareUpdater;
+    private SubDeviceUpdateCallback mExternalCallback; // Can be SubDeviceUpdateCallback or a new DspUpdateCallback
+    private boolean mIsUpdating = false;
+    private IDspCommunicationManager mCommunicationManager; // Field to hold the injected manager
+    private String mResolvedFirmwarePath; // To store the path of the firmware file if an update is needed
+    private String mCurrentDspVersion; // To store the current DSP version obtained during checkUpdate
+
+    private final DspFirmwareUpdater.FirmwareUpdateListener mInternalDspListener = new DspFirmwareUpdater.FirmwareUpdateListener() {
+        @Override
+        public void onProgress(int percentage) {
+            if (mExternalCallback != null) {
+                mExternalCallback.onUpdateProgressChanged(percentage);
+            }
+        }
+
+        @Override
+        public void onSuccess() {
+            Log.i(TAG, "DspUpdate: Firmware update internal listener onSuccess.");
+            mIsUpdating = false;
+            if (mExternalCallback != null) {
+                mExternalCallback.onUpdateSuccess();
+                mExternalCallback.onAllUpdateFinish(); // Assuming one DSP update means all relevant sub-device updates are done
+            }
+            if (mCommunicationManager != null) {
+                Log.d(TAG, "mInternalDspListener.onSuccess: Exiting upgrade mode.");
+                mCommunicationManager.exitUpgradeMode();
+            }
+        }
+
+        @Override
+        public void onFailure(String errorMessage, int dspErrorCode) {
+            Log.e(TAG, "DspUpdate: Firmware update internal listener onFailure. Error: " + errorMessage + ", Code: " + dspErrorCode);
+            mIsUpdating = false;
+            if (mExternalCallback != null) {
+                mExternalCallback.onUpdateFail(errorMessage + " (DSP Code: " + dspErrorCode + ")");
+                mExternalCallback.onAllUpdateFinish();
+            }
+            if (mCommunicationManager != null) {
+                Log.d(TAG, "mInternalDspListener.onFailure: Exiting upgrade mode.");
+                mCommunicationManager.exitUpgradeMode();
+            }
+        }
+    };
+
+    /**
+     * Constructor for DspUpdate.
+     * @param context Application context.
+     * @param commManager An instance of IDspCommunicationManager for DSP communication.
+     * @param callback Callback for update status.
+     */
+    public DspUpdate(Context context, IDspCommunicationManager commManager, SubDeviceUpdateCallback callback) {
+        mContext = context.getApplicationContext();
+        mCommunicationManager = commManager; // Store the injected communication manager
+        mExternalCallback = callback;
+    }
+
+    /**
+     * Checks if a DSP update is required by comparing the current DSP firmware version
+     * with the target version specified in dsp_config.json.
+     *
+     * @return true if an update is required and the firmware file is valid, false otherwise.
+     */
+    public boolean checkUpdate() {
+        Log.i(TAG, "DspUpdate.checkUpdate() called. Starting version check process.");
+
+        if (mContext == null) {
+            Log.e(TAG, "Context is null in DspUpdate.checkUpdate(). Cannot proceed.");
+            return false;
+        }
+        if (mCommunicationManager == null) {
+            Log.e(TAG, "IDspCommunicationManager is null in DspUpdate.checkUpdate(). Cannot proceed.");
+            return false;
+        }
+
+        mCommunicationManager.enterUpgradeMode();
+        boolean updateNeeded = false;
+        try {
+            String currentDspVersion;
+            try {
+                // Create a temporary DspFirmwareUpdater to get the current version
+                // DspFirmwareUpdater itself uses the IDspCommunicationManager for actual communication
+                DspFirmwareUpdater versionChecker = new DspFirmwareUpdater(mContext, mCommunicationManager);
+                currentDspVersion = versionChecker.getCurrentDspVersion();
+                Log.i(TAG, "Current DSP firmware version: " + currentDspVersion);
+            } catch (IOException | DspFirmwareUpdater.UpdateStepException e) {
+                Log.e(TAG, "Failed to get current DSP version.", e);
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+
+            if (currentDspVersion == null || currentDspVersion.isEmpty()) {
+                Log.e(TAG, "Retrieved current DSP version is null or empty.");
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+
+            File configFile = new File(DSP_CONFIG_FILE_PATH);
+            if (!configFile.exists() || !configFile.canRead()) {
+                Log.e(TAG, "DSP config file not found or not readable: " + DSP_CONFIG_FILE_PATH);
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+
+            StringBuilder stringBuilder = new StringBuilder();
+            try (FileInputStream fis = new FileInputStream(configFile);
+                 InputStreamReader inputStreamReader = new InputStreamReader(fis, StandardCharsets.UTF_8);
+                 BufferedReader reader = new BufferedReader(inputStreamReader)) {
+                String line;
+                while ((line = reader.readLine()) != null) {
+                    stringBuilder.append(line);
+                }
+            } catch (IOException e) {
+                Log.e(TAG, "Error reading DSP config file: " + DSP_CONFIG_FILE_PATH, e);
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+
+            String targetVersion;
+            try {
+                // Parse the string as a JSONArray
+                JSONArray configArray = new JSONArray(stringBuilder.toString());
+
+                if (configArray.length() == 0) {
+                    Log.e(TAG, "DSP config JSON array is empty.");
+                    return false;
+                }
+
+                // Get the last JSONObject from the array
+                JSONObject lastFirmwareInfo = configArray.getJSONObject(configArray.length() - 1);
+
+                targetVersion = lastFirmwareInfo.optString("version");
+                String targetFirmwarePath = lastFirmwareInfo.optString("path"); // Assuming 'path' contains the relative path like 'system/ota/file.bin'
+
+                if (targetVersion.isEmpty() || targetFirmwarePath.isEmpty()) {
+                    Log.e(TAG, "DSP config JSON (last entry) is missing 'version' or 'path'.");
+                    return false;
+                }
+                Log.i(TAG, "Target DSP firmware version from config (last entry): " + targetVersion + ", Firmware path: " + targetFirmwarePath);
+
+                // Construct the mResolvedFirmwarePath. 
+                // Assuming targetFirmwarePath is like "system/ota/firmware.bin"
+                // And we need an absolute path like "/system/ota/firmware.bin"
+                if (!targetFirmwarePath.startsWith("/")) {
+                    mResolvedFirmwarePath = "/" + targetFirmwarePath;
+                } else {
+                    mResolvedFirmwarePath = targetFirmwarePath;
+                }
+                // firmwareFilename is no longer directly used from a top-level JSON object field for path construction here
+
+            } catch (JSONException e) {
+                Log.e(TAG, "Error parsing DSP config JSON.", e);
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+
+            if (!isVersionEqual(currentDspVersion, targetVersion)) {
+                Log.i(TAG, "Update required. Current version: " + currentDspVersion + " differs from target version: " + targetVersion);
+                // mResolvedFirmwarePath is now set in the JSON parsing block above
+
+                File firmwareFile = new File(mResolvedFirmwarePath);
+                if (!firmwareFile.exists() || !firmwareFile.canRead()) {
+                    Log.e(TAG, "Firmware file specified in config not found or not readable: " + mResolvedFirmwarePath);
+                    mResolvedFirmwarePath = null; // Invalidate path
+                    // updateNeeded remains false, finally block will exit upgrade mode
+                    return false;
+                }
+                Log.i(TAG, "Firmware file for update: " + mResolvedFirmwarePath + " exists and is readable.");
+                updateNeeded = true; // Set to true, so finally block WON'T exit upgrade mode
+                mCurrentDspVersion = currentDspVersion; // Save the current DSP version
+                return true;
+            } else {
+                Log.i(TAG, "No update required. Current version: " + currentDspVersion + " is same as target: " + targetVersion);
+                // updateNeeded remains false, finally block will exit upgrade mode
+                return false;
+            }
+        } finally {
+            if (!updateNeeded && mCommunicationManager != null) {
+                Log.d(TAG, "checkUpdate determined no update needed or error occurred. Exiting upgrade mode.");
+                mCommunicationManager.exitUpgradeMode();
+            }
+        }
+    }
+
+    /**
+     * Helper method to compare two version strings (e.g., "1.2.3" vs "1.2.10").
+     * @param currentVersion The current version string.
+     * @param targetVersion The target version string.
+     * @return true if currentVersion is lower than targetVersion, false otherwise.
+     */
+    private boolean isVersionLower(String currentVersion, String targetVersion) {
+        if (currentVersion == null || currentVersion.isEmpty() || targetVersion == null || targetVersion.isEmpty()) {
+            Log.w(TAG, "Cannot compare versions, one or both are null/empty. current: " + currentVersion + ", target: " + targetVersion);
+            return false; // Or throw an IllegalArgumentException
+        }
+        String[] currentParts = currentVersion.split("\\.");
+        String[] targetParts = targetVersion.split("\\.");
+
+        // ---- START DEBUG LOGGING ----
+        Log.d(TAG, "isVersionLower - currentVersion: " + currentVersion + ", currentParts.length: " + currentParts.length + ", currentParts: " + Arrays.toString(currentParts));
+        Log.d(TAG, "isVersionLower - targetVersion: " + targetVersion + ", targetParts.length: " + targetParts.length + ", targetParts: " + Arrays.toString(targetParts));
+        // ---- END DEBUG LOGGING ----
+
+        int length = Math.max(currentParts.length, targetParts.length);
+        for (int i = 0; i < length; i++) {
+            try {
+                int currentPart = (i < currentParts.length && !currentParts[i].isEmpty()) ? Integer.parseInt(currentParts[i]) : 0;
+                int targetPart = (i < targetParts.length && !targetParts[i].isEmpty()) ? Integer.parseInt(targetParts[i]) : 0;
+
+                if (currentPart < targetPart) return true;
+                if (currentPart > targetPart) return false;
+            } catch (NumberFormatException e) {
+                Log.e(TAG, "Error parsing version part to integer. currentPart: " + (i < currentParts.length ? currentParts[i] : "N/A") +
+                           ", targetPart: " + (i < targetParts.length ? targetParts[i] : "N/A"), e);
+                return false; // Error in version format, treat as no update
+            }
+        }
+        return false; // Versions are equal
+    }
+
+    /**
+     * Helper method to compare two version strings for equality.
+     * @param currentVersion The current version string.
+     * @param targetVersion The target version string.
+     * @return true if currentVersion equals targetVersion, false otherwise.
+     */
+    private boolean isVersionEqual(String currentVersion, String targetVersion) {
+        if (currentVersion == null || currentVersion.isEmpty() || targetVersion == null || targetVersion.isEmpty()) {
+            Log.w(TAG, "Cannot compare versions, one or both are null/empty. current: " + currentVersion + ", target: " + targetVersion);
+            return false; // Treat null/empty as not equal
+        }
+        
+        // Normalize versions by trimming whitespace
+        String normalizedCurrent = currentVersion.trim();
+        String normalizedTarget = targetVersion.trim();
+        
+        Log.d(TAG, "isVersionEqual - comparing normalized versions: current='" + normalizedCurrent + "', target='" + normalizedTarget + "'");
+        
+        return normalizedCurrent.equals(normalizedTarget);
+    }
+
+    /**
+     * Starts the DSP firmware update process.
+     * This method should be called after checkUpdate() returns true.
+     */
+    public boolean update() {
+        Log.i(TAG, "DspUpdate.update() called.");
+        if (mIsUpdating) {
+            Log.w(TAG, "DspUpdate.update() called while an update is already in progress.");
+            return false;
+        }
+
+        if (mCommunicationManager == null) {
+            Log.e(TAG, "DspUpdate.update() called but IDspCommunicationManager is null. Cannot proceed.");
+            if (mExternalCallback != null) {
+                mExternalCallback.onUpdateFail("Communication manager not available for DSP update.");
+                mExternalCallback.onAllUpdateFinish();
+            }
+            return false;
+        }
+
+        if (mResolvedFirmwarePath == null || mResolvedFirmwarePath.isEmpty()) {
+            Log.e(TAG, "DspUpdate.update() called but mResolvedFirmwarePath is not set. Was checkUpdate() successful?");
+             if (mExternalCallback != null) {
+                mExternalCallback.onUpdateFail("Firmware path for DSP update not resolved.");
+                mExternalCallback.onAllUpdateFinish();
+            }
+            return false;
+        }
+
+        Log.i(TAG, "Initializing DspFirmwareUpdater with firmware path: " + mResolvedFirmwarePath);
+        // Pass the Context and the injected IDspCommunicationManager instance
+        // DspFirmwareUpdater is created here as it's specific to an update operation.
+        mDspFirmwareUpdater = new DspFirmwareUpdater(mContext, mCommunicationManager);
+        mIsUpdating = true;
+
+        // Start the update process. DspFirmwareUpdater will run on its own thread.
+        // The DspFirmwareUpdater.FirmwareUpdateListener (mInternalDspListener) will handle callbacks.
+        boolean initiated = mDspFirmwareUpdater.startUpdate(mResolvedFirmwarePath, mInternalDspListener, mCurrentDspVersion);
+        if (!initiated) {
+            Log.e(TAG, "DspFirmwareUpdater.startUpdate failed to initiate for path: " + mResolvedFirmwarePath);
+            mIsUpdating = false; // Reset flag if initiation failed immediately
+            // Failure callback would have been triggered by DspFirmwareUpdater.startUpdate if it returned false
+            // and called its listener.onFailure.
+        } else {
+            Log.i(TAG, "DspFirmwareUpdater.startUpdate initiated successfully for: " + mResolvedFirmwarePath);
+        }
+        return initiated;
+    }
+
+    /**
+     * Releases any resources used by the DspUpdate process.
+     * Currently, this mainly involves nullifying references.
+     */
+    public void release() {
+        Log.d(TAG, "DspUpdate.release() called.");
+        mExternalCallback = null;
+        mResolvedFirmwarePath = null; // Clear resolved path
+        mCurrentDspVersion = null; // Clear saved DSP version
+        // mDspFirmwareUpdater internal state will be managed by its own logic if an update is ongoing.
+        // If DspFirmwareUpdater held significant resources that need explicit release outside an update, add here.
+        mIsUpdating = false; 
+    }
+
+    /**
+     * Checks if the DSP update process is currently active.
+     * @return true if an update is in progress, false otherwise.
+     */
+    public boolean isUpdating() {
+        return mIsUpdating;
+    }
+} 
\ No newline at end of file
diff --git a/ota/src/main/java/com/eeo/ota/dsp/IDspCommunicationManager.java b/ota/src/main/java/com/eeo/ota/dsp/IDspCommunicationManager.java
new file mode 100644
index 0000000..ad6c7df
--- /dev/null
+++ b/ota/src/main/java/com/eeo/ota/dsp/IDspCommunicationManager.java
@@ -0,0 +1,64 @@
+package com.eeo.ota.dsp;
+
+import java.util.concurrent.atomic.AtomicInteger;
+
+/**
+ * Interface for DSP communication during firmware upgrade process.
+ * This interface abstracts the underlying UsbSerialManager for DSP upgrade purposes only.
+ * Regular DSP operations use the direct UsbSerialManager methods (write/read),
+ * while DSP upgrade operations use the specialized methods in this interface.
+ */
+public interface IDspCommunicationManager {
+    AtomicInteger getReadingCount();
+
+    /**
+     * Signals the start of a read operation that expects a response.
+     * This can be used by the manager to pause background polling.
+     */
+    void enterReadOperation();
+
+    /**
+     * Signals the end of a read operation.
+     * This can be used by the manager to resume background polling.
+     */
+    void exitReadOperation();
+
+    /**
+     * Notifies the communication manager to enter DSP firmware upgrade mode.
+     * In this mode, regular DSP communication will be blocked to ensure
+     * exclusive access for the upgrade process.
+     */
+    void enterUpgradeMode();
+
+    /**
+     * Notifies the communication manager to exit DSP firmware upgrade mode.
+     * Regular DSP communication will resume after this call.
+     */
+    void exitUpgradeMode();
+
+    /**
+     * Writes data to the DSP for firmware upgrade purposes.
+     * This method is intended for use only during an active upgrade process
+     * and bypasses normal communication locks and checks.
+     *
+     * @param data The byte array to write.
+     * @param timeout The timeout for the write operation in milliseconds.
+     * @return The number of bytes written, or -1 on error.
+     */
+    int writeForUpgrade(byte[] data, int timeout);
+
+    /**
+     * Reads data from the DSP for firmware upgrade purposes.
+     * This method is intended for use only during an active upgrade process
+     * and bypasses normal communication locks and checks.
+     *
+     * @param buffer The buffer to read data into.
+     * @param timeout The timeout for the read operation in milliseconds.
+     * @return The number of bytes read, or -1 on error.
+     */
+    int readForUpgrade(byte[] buffer, int timeout);
+
+    // boolean isDeviceConnected(); // Example of other methods that might be needed
+    // void startCommunication(); // Example
+    // void stopCommunication();  // Example
+}
\ No newline at end of file
diff --git a/ota/src/main/java/com/eeo/ota/dsp/UpdateStepException.java b/ota/src/main/java/com/eeo/ota/dsp/UpdateStepException.java
new file mode 100644
index 0000000..26054d0
--- /dev/null
+++ b/ota/src/main/java/com/eeo/ota/dsp/UpdateStepException.java
@@ -0,0 +1,19 @@
+package com.eeo.ota.dsp;
+
+public class UpdateStepException extends Exception {
+    private int dspErrorCode;
+
+    public UpdateStepException(String message, int dspErrorCode) {
+        super(message);
+        this.dspErrorCode = dspErrorCode;
+    }
+
+    public UpdateStepException(String message, int dspErrorCode, Throwable cause) {
+        super(message, cause);
+        this.dspErrorCode = dspErrorCode;
+    }
+
+    public int getDspErrorCode() {
+        return dspErrorCode;
+    }
+}
diff --git a/ota/src/main/java/com/eeo/ota/service/DspUpdateService.java b/ota/src/main/java/com/eeo/ota/service/DspUpdateService.java
new file mode 100644
index 0000000..f233485
--- /dev/null
+++ b/ota/src/main/java/com/eeo/ota/service/DspUpdateService.java
@@ -0,0 +1,213 @@
+package com.eeo.ota.service;
+
+import android.app.Notification;
+import android.app.NotificationChannel;
+import android.app.NotificationManager;
+import android.app.Service;
+import android.content.Intent;
+import android.os.Binder;
+import android.os.Build;
+import android.os.IBinder;
+import android.util.Log;
+
+import com.eeo.ota.callback.SubDeviceUpdateCallback;
+import com.eeo.ota.dsp.DspUpdate;
+import com.eeo.ota.dsp.DspFirmwareUpdater;
+import com.eeo.ota.dsp.IDspCommunicationManager; // Import the interface
+
+public class DspUpdateService extends Service {
+    public static final String TAG = "DspUpdateService";
+    private DspUpdate mDspUpdate;
+    private static final String CHANNEL_ID_DSP = "eeo_dsp_ota_channel_id"; // Unique channel ID
+    private static final int NOTIFICATION_ID_DSP = 2; // Unique notification ID
+
+    private SubDeviceUpdateCallback mExternalDspCallback = null;
+
+    // Static field to hold the communication manager provided by systemsetting
+    private static IDspCommunicationManager sProvidedCommManager;
+
+    /**
+     * Called by systemsetting module before starting this service to provide the
+     * necessary communication manager.
+     */
+    public static void provideCommunicationManager(IDspCommunicationManager manager) {
+        Log.d(TAG, "IDspCommunicationManager provided: " + (manager != null ? manager.getClass().getSimpleName() : "null"));
+        sProvidedCommManager = manager;
+    }
+
+    private final SubDeviceUpdateCallback dspServiceCallback = new SubDeviceUpdateCallback() {
+        @Override
+        public void onUpdateSuccess() {
+            Log.i(TAG, "DspUpdateService: onUpdateSuccess callback received.");
+            if (mExternalDspCallback != null) {
+                mExternalDspCallback.onUpdateSuccess();
+            }
+            // DSP update successful - communication will resume automatically when upgrade mode exits
+        }
+
+        @Override
+        public void onUpdateFail(String errMsg) {
+            Log.i(TAG, "DspUpdateService: onUpdateFail callback received - " + errMsg);
+            if (mExternalDspCallback != null) {
+                mExternalDspCallback.onUpdateFail(errMsg);
+            }
+            // DSP update failed - communication will resume automatically when upgrade mode exits
+        }
+
+        @Override
+        public void onAllUpdateFinish() {
+            Log.i(TAG, "DspUpdateService: onAllUpdateFinish callback received. Stopping service.");
+            if (mExternalDspCallback != null) {
+                mExternalDspCallback.onAllUpdateFinish();
+            }
+            stopSelf(); // Stop the service when all updates are finished
+        }
+
+        @Override
+        public void onUpdateProgressChanged(int progress) {
+            if (DspFirmwareUpdater.DEBUG) Log.d(TAG, "DspUpdateService: onUpdateProgressChanged: " + progress + "%");
+            if (mExternalDspCallback != null) {
+                mExternalDspCallback.onUpdateProgressChanged(progress);
+            }
+        }
+    };
+
+    @Override
+    public IBinder onBind(Intent intent) {
+        Log.d(TAG, "onBind called");
+        return new DspUpdateService.MyDspBinder();
+    }
+
+    public class MyDspBinder extends Binder {
+        public DspUpdateService getService() {
+            return DspUpdateService.this;
+        }
+    }
+
+    @Override
+    public void onCreate() {
+        super.onCreate();
+        Log.d(TAG, "onCreate called");
+        createNotificationChannelAndStartForeground();
+
+        if (sProvidedCommManager == null) {
+            Log.e(TAG, "CRITICAL: IDspCommunicationManager was not provided before DspUpdateService onCreate. Stopping service.");
+            // Optionally, notify a failure or log extensively here
+            // stopSelf(); // Cannot call stopSelf() here if we want onStartCommand to run for bindService cases.
+                       // Let onStartCommand handle the null check if that's the primary entry point for logic.
+            return; 
+        }
+        // Initialize DspUpdate here if it's always needed on create,
+        // or delay to onStartCommand / onBind if manager might be set later.
+        // For simplicity, let's assume onStartCommand is the main trigger for the update logic.
+    }
+
+    private void createNotificationChannelAndStartForeground() {
+        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
+            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
+            if (notificationManager != null) {
+                NotificationChannel channel = new NotificationChannel(CHANNEL_ID_DSP,
+                        "DSP Firmware Update Channel", NotificationManager.IMPORTANCE_LOW);
+                notificationManager.createNotificationChannel(channel);
+                Notification notification = new Notification.Builder(this, CHANNEL_ID_DSP)
+                        .setContentTitle("DSP Update Service")
+                        .setContentText("DSP firmware update in progress...")
+                        // .setSmallIcon(R.drawable.ic_notification_icon) // Add an icon if you have one
+                        .build();
+                startForeground(NOTIFICATION_ID_DSP, notification);
+                Log.d(TAG, "Service started in foreground with notification channel.");
+            } else {
+                Log.e(TAG, "NotificationManager not available.");
+            }
+        } else {
+             // For older versions, startForeground without a channel (though less common now)
+            startForeground(NOTIFICATION_ID_DSP, new Notification()); // Basic notification
+            Log.d(TAG, "Service started in foreground for older Android version.");
+        }
+    }
+
+    /**
+     * Starts the DSP device update process.
+     * This is called from MainActivity after binding to the service.
+     */
+    public void startDspDeviceUpdate() {
+        Log.d(TAG, "startDspDeviceUpdate called.");
+        if (sProvidedCommManager == null) {
+            Log.e(TAG, "IDspCommunicationManager not provided. Cannot start DSP update.");
+            dspServiceCallback.onUpdateFail("Communication manager not ready for DSP.");
+            dspServiceCallback.onAllUpdateFinish(); // This will trigger stopSelf
+            return;
+        }
+
+        if (mDspUpdate == null) {
+            Log.d(TAG, "Creating new DspUpdate instance.");
+            // Pass the application context, the provided communication manager, and the service's callback
+            mDspUpdate = new DspUpdate(getApplicationContext(), sProvidedCommManager, dspServiceCallback);
+        } else {
+            Log.d(TAG, "Reusing existing DspUpdate instance.");
+        }
+
+        // Check if an update is needed
+        if (mDspUpdate.checkUpdate()) {
+            Log.i(TAG, "DSP update required. Communication will be controlled by upgrade mode.");
+
+            Log.i(TAG, "Starting DSP update process via DspUpdate.update().");
+            boolean updateStarted = mDspUpdate.update();
+            if (!updateStarted) {
+                Log.e(TAG, "mDspUpdate.update() returned false. Update did not start as expected.");
+                // If update didn't start, UsbSerialManager might need to be restarted immediately.
+                // DspUpdate's internal listener (onFailure) should trigger the restart via dspServiceCallback.
+                // If mDspUpdate.update() itself fails and doesn't call its listener, we might need to restart here.
+                // However, DspUpdate.update() is expected to call its listener on immediate failure.
+            }
+        } else {
+            Log.i(TAG, "No DSP update required based on checkUpdate(). Service will stop.");
+            // No need to stop/start UsbSerialManager if no update is performed.
+            dspServiceCallback.onUpdateFail("No DSP update needed (checkUpdate false)."); // Or onAllUpdateFinish directly
+            dspServiceCallback.onAllUpdateFinish(); // This will trigger stopSelf
+        }
+        // Clear the static reference once it's used to initialize DspUpdate to prevent memory leaks
+        // and ensure it's freshly provided for any subsequent, independent service starts.
+        // However, if the service can be bound multiple times and DspUpdate is reused,
+        // clearing it might be an issue. For a service that runs one update and stops,
+        // clearing after DspUpdate is initialized is safer.
+        // Let's clear it if DspUpdate is created. If startDspDeviceUpdate can be called multiple times
+        // on the same service instance with the same DspUpdate, then clearing needs care.
+        // Assuming one full update cycle per service instance that's started for an update.
+        // sProvidedCommManager should NOT be cleared here if it's needed by the callbacks (onSuccess/onFailure)
+        // to restart the UsbSerialManager. It will be cleared in onDestroy.
+        // if(mDspUpdate != null) { 
+        //      sProvidedCommManager = null; 
+        // }
+    }
+
+    @Override
+    public int onStartCommand(Intent intent, int flags, int startId) {
+        Log.d(TAG, "onStartCommand called. Action: " + (intent != null ? intent.getAction() : "null intent"));
+        // If the service is started directly (not just bound), and intended to perform an update,
+        // this is where the update logic should be triggered if not handled by binding.
+        // For now, assuming binding and calling startDspDeviceUpdate is the main path from MainActivity.
+        // If systemsetting starts this service directly to perform an update without binding first:
+        if (intent != null && "com.eeo.ota.action.START_DSP_UPDATE_IMMEDIATE".equals(intent.getAction())) {
+             Log.i(TAG, "Received START_DSP_UPDATE_IMMEDIATE action. Calling startDspDeviceUpdate().");
+             startDspDeviceUpdate(); 
+        }
+        return START_STICKY; // Or START_NOT_STICKY if it shouldn't restart automatically
+    }
+
+    @Override
+    public void onDestroy() {
+        Log.d(TAG, "onDestroy called. Releasing DspUpdate if it exists.");
+        if (mDspUpdate != null) {
+            mDspUpdate.release();
+            mDspUpdate = null;
+        }
+        sProvidedCommManager = null; // Clear static ref on destroy too
+        super.onDestroy();
+    }
+
+    public void setDspUpdateListener(SubDeviceUpdateCallback callback) {
+        Log.d(TAG, "setDspUpdateListener called.");
+        this.mExternalDspCallback = callback;
+    }
+} 
\ No newline at end of file
diff --git a/setup/src/main/java/cn/eeo/classin/setup/MainActivity.java b/setup/src/main/java/cn/eeo/classin/setup/MainActivity.java
index 6d34944..bd13d4b 100644
--- a/setup/src/main/java/cn/eeo/classin/setup/MainActivity.java
+++ b/setup/src/main/java/cn/eeo/classin/setup/MainActivity.java
@@ -2,6 +2,7 @@ package cn.eeo.classin.setup;
 
 import android.app.Activity;
 import android.content.ComponentName;
+import android.content.Context;
 import android.content.Intent;
 import android.content.ServiceConnection;
 import android.content.res.Configuration;
@@ -37,6 +38,8 @@ import com.cvte.tv.api.aidl.ITvApiManager;
 import com.eeo.ota.callback.SubDeviceUpdateCallback;
 import com.eeo.ota.service.SubDeviceUpdateService;
 import com.eeo.ota.touch.SubDeviceUpdate;
+import com.eeo.ota.dsp.DspUpdate;
+import com.eeo.ota.service.DspUpdateService;
 import com.elvishew.xlog.XLog;
 
 import java.lang.ref.WeakReference;
@@ -49,6 +52,10 @@ import cn.eeo.classin.setup.utils.CommonUtils;
 public class MainActivity extends AppCompatActivity {
 
     private static final String TAG = "SetupMainActivity";
+    private static final int CODE_DRAW_OVER_OTHER_APP_PERMISSION = 2099;
+    // Action for the broadcast to request DSP update check
+    private static final String ACTION_REQUEST_DSP_UPDATE_CHECK = "com.eeo.systemsetting.action.CHECK_DSP_UPDATE";
+
     private AppBarConfiguration appBarConfiguration;
     private ActivityMainBinding binding;
 
@@ -71,33 +78,37 @@ public class MainActivity extends AppCompatActivity {
      * ota子设备更新相关
      * 系统显示前先检测子设备更新
      */
-    private boolean mHasBound;
-    private SubDeviceUpdateService.MyBinder mBinder;
+    private boolean mHasBoundSubDeviceUpdate;
+    private SubDeviceUpdateService.MyBinder mSubDeviceUpdateBinder;
     private SubDeviceUpdateService mSubDeviceUpdateService;
-    private ServiceConnection mServiceConnection = new ServiceConnection() {
+    private Context mContext;
+    private ServiceConnection mSubDeviceServiceConnection = new ServiceConnection() {
         @Override
         public void onServiceConnected(ComponentName name, IBinder service) {
-            Log.d(TAG, "onServiceConnected: ");
-            mBinder = (SubDeviceUpdateService.MyBinder) service;
-            if (mBinder != null) {
-                mSubDeviceUpdateService = mBinder.getService();
+            Log.d(TAG, "SubDeviceUpdateService onServiceConnected: ");
+            mSubDeviceUpdateBinder = (SubDeviceUpdateService.MyBinder) service;
+            if (mSubDeviceUpdateBinder != null) {
+                mSubDeviceUpdateService = mSubDeviceUpdateBinder.getService();
                 mSubDeviceUpdateService.setSubDeviceUpdateListener(new SubDeviceUpdateCallback() {
                     @Override
                     public void onUpdateSuccess() {
-
+                        Log.i(TAG, "SubDevice (Touch) update SUCCEEDED.");
                     }
 
                     @Override
                     public void onUpdateFail(String errMsg) {
+                        Log.e(TAG, "SubDevice (Touch) update FAILED: " + errMsg);
                         unbindSubDeviceUpdateService();
-
                         //更新失败或者没有更新：执行检查到有更新前的流程
                         mMainView.setVisibility(View.VISIBLE);
                     }
 
                     @Override
                     public void onAllUpdateFinish() {
-
+                        Log.i(TAG, "SubDevice (Touch) onAllUpdateFinish.");
+                        unbindSubDeviceUpdateService(); // Unbind after touch update finishes
+                        // Optionally, trigger DSP update check *after* touch update is fully done
+                        // checkAndStartDspUpdate(); 
                     }
                 });
                 mSubDeviceUpdateService.updateSubDevice();
@@ -106,14 +117,64 @@ public class MainActivity extends AppCompatActivity {
 
         @Override
         public void onServiceDisconnected(ComponentName name) {
-            mBinder = null;
+            Log.d(TAG, "SubDeviceUpdateService onServiceDisconnected.");
+            mSubDeviceUpdateBinder = null;
             mSubDeviceUpdateService = null;
         }
     };
 
+    // DSP Update Service Related
+    private boolean mHasBoundDspUpdate;
+    private DspUpdateService.MyDspBinder mDspUpdateBinder;
+    private DspUpdateService mDspUpdateService;
+    private ServiceConnection mDspServiceConnection = new ServiceConnection() {
+        @Override
+        public void onServiceConnected(ComponentName name, IBinder service) {
+            Log.d(TAG, "DspUpdateService onServiceConnected: ");
+            mDspUpdateBinder = (DspUpdateService.MyDspBinder) service;
+            if (mDspUpdateBinder != null) {
+                mDspUpdateService = mDspUpdateBinder.getService();
+                mDspUpdateService.setDspUpdateListener(new SubDeviceUpdateCallback() {
+                    @Override
+                    public void onUpdateSuccess() {
+                        Log.i(TAG, "DSP update SUCCEEDED.");
+                    }
+
+                    @Override
+                    public void onUpdateFail(String errMsg) {
+                        Log.e(TAG, "DSP update FAILED: " + errMsg);
+                        // Decide if mMainView should be shown or if an error specific to DSP is handled
+                    }
+
+                    @Override
+                    public void onAllUpdateFinish() {
+                        Log.i(TAG, "DSP onAllUpdateFinish.");
+                        unbindDspUpdateService(); // Unbind after DSP update finishes
+                    }
+
+                    @Override
+                    public void onUpdateProgressChanged(int progress) {
+                        Log.d(TAG, "DSP update progress: " + progress + "%");
+                    }
+                });
+                Log.d(TAG, "Calling DspUpdateService.startDspDeviceUpdate()");
+                mDspUpdateService.startDspDeviceUpdate();
+            }
+        }
+
+        @Override
+        public void onServiceDisconnected(ComponentName name) {
+            Log.d(TAG, "DspUpdateService onServiceDisconnected.");
+            mDspUpdateBinder = null;
+            mDspUpdateService = null;
+        }
+    };
+
     @Override
     protected void onCreate(Bundle savedInstanceState) {
         super.onCreate(savedInstanceState);
+        Log.d(TAG, "onCreate");
+        mContext = this;
         XLog.d("MainActivity onCreate ");
         getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
         binding = ActivityMainBinding.inflate(getLayoutInflater());
@@ -133,6 +194,24 @@ public class MainActivity extends AppCompatActivity {
         } else {
             mMainView.setVisibility(View.VISIBLE);
         }
+
+        // Check and start DSP update. This can run after or in parallel to SubDeviceUpdate
+        // For now, let's call it after the SubDevice check.
+
+        // // Send a broadcast to systemsetting to initiate the DSP update check and service start
+        // Log.i(TAG, "Sending broadcast to request DSP update check.");
+        // Intent dspUpdateIntent = new Intent(ACTION_REQUEST_DSP_UPDATE_CHECK);
+        // dspUpdateIntent.setPackage("com.eeo.systemsetting"); // Make it explicit
+        // sendBroadcast(dspUpdateIntent);
+
+        // // The binding to DspUpdateService is still useful for receiving callbacks if MainActivity needs them.
+        // // The actual start of the update logic is now triggered by the DspUpdateCheckReceiver in systemsetting.
+        // if (checkDspDeviceUpdate()) { // This method now just returns true
+        //     Log.i(TAG, "DSP update potentially required, binding DspUpdateService for callbacks.");
+        //     bindDspUpdateService();
+        // } else {
+        //     Log.i(TAG, "No DSP update required based on checkDspDeviceUpdate(), not binding service.");
+        // }
     }
 
     @Override
@@ -182,6 +261,9 @@ public class MainActivity extends AppCompatActivity {
     protected void onStop() {
         super.onStop();
         CommonUtils.setTouchState(getApplicationContext(), true);
+        // Unbind services if they were bound
+        unbindSubDeviceUpdateService();
+        unbindDspUpdateService();
     }
 
 
@@ -380,23 +462,66 @@ public class MainActivity extends AppCompatActivity {
      */
     private boolean checkSubDeviceUpdate() {
         SubDeviceUpdate subDeviceUpdate = new SubDeviceUpdate(this, null);
-        return subDeviceUpdate.checkUpdate();
+        boolean needsUpdate = subDeviceUpdate.checkUpdate();
+        Log.d(TAG, "checkSubDeviceUpdate (Touch) returning: " + needsUpdate);
+        subDeviceUpdate.release(); // Release after check
+        return needsUpdate;
     }
 
     private void bindSubDeviceUpdateService() {
-        if (mHasBound) {
+        if (mHasBoundSubDeviceUpdate) {
             return;
         }
         Log.d(TAG, "bindSubDeviceUpdateService: ");
         Intent intent = new Intent(this, SubDeviceUpdateService.class);
-        bindService(intent, mServiceConnection, BIND_AUTO_CREATE);
-        mHasBound = true;
+        bindService(intent, mSubDeviceServiceConnection, BIND_AUTO_CREATE);
+        mHasBoundSubDeviceUpdate = true;
     }
 
     private void unbindSubDeviceUpdateService() {
-        if (mHasBound) {
-            unbindService(mServiceConnection);
-            mHasBound = false;
+        if (mHasBoundSubDeviceUpdate) {
+            Log.d(TAG, "unbindSubDeviceUpdateService: ");
+            unbindService(mSubDeviceServiceConnection);
+            mHasBoundSubDeviceUpdate = false;
+            mSubDeviceUpdateService = null; // Clean up service instance
+            mSubDeviceUpdateBinder = null;
+        }
+    }
+
+    // Methods for DSP Update Service
+    private boolean checkDspDeviceUpdate() {
+        // Pass null for callback as we only need the check result here.
+        // The service will have its own callback handling.
+        // DspUpdate dspUpdate = new DspUpdate(getApplicationContext(), null); 
+        // boolean needsUpdate = dspUpdate.checkUpdate(); // This currently always returns true
+        // Log.d(TAG, "checkDspDeviceUpdate returning: " + needsUpdate);
+        // dspUpdate.release(); // Release after check
+        // return needsUpdate;
+
+        // For now, MainActivity defers the actual check to DspUpdateService.
+        // It will always attempt to bind to the service if DSP updates are conceptually enabled.
+        // The DspUpdate.checkUpdate() is currently hardcoded to true for testing purposes.
+        Log.d(TAG, "checkDspDeviceUpdate (DSP) - returning true to proceed with service binding for callbacks.");
+        return true; 
+    }
+
+    private void bindDspUpdateService() {
+        if (mHasBoundDspUpdate) {
+            return;
+        }
+        Log.d(TAG, "bindDspUpdateService: ");
+        Intent intent = new Intent(this, DspUpdateService.class);
+        bindService(intent, mDspServiceConnection, BIND_AUTO_CREATE);
+        mHasBoundDspUpdate = true;
+    }
+
+    private void unbindDspUpdateService() {
+        if (mHasBoundDspUpdate) {
+            Log.d(TAG, "unbindDspUpdateService: ");
+            unbindService(mDspServiceConnection);
+            mHasBoundDspUpdate = false;
+            mDspUpdateService = null; // Clean up service instance
+            mDspUpdateBinder = null;
         }
     }
 }
\ No newline at end of file
diff --git a/systemsetting/src/main/AndroidManifest.xml b/systemsetting/src/main/AndroidManifest.xml
index ab91fd1..bf0e56d 100644
--- a/systemsetting/src/main/AndroidManifest.xml
+++ b/systemsetting/src/main/AndroidManifest.xml
@@ -153,6 +153,15 @@
 
             </intent-filter>
         </receiver>
+
+        <receiver
+            android:name=".receiver.DspUpdateCheckReceiver"
+            android:exported="true"
+            android:enabled="true">
+            <intent-filter>
+                <action android:name="com.eeo.systemsetting.action.CHECK_DSP_UPDATE" />
+            </intent-filter>
+        </receiver>
     </application>
 
 </manifest>
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/EeoApplication.java b/systemsetting/src/main/java/com/eeo/systemsetting/EeoApplication.java
index e9b9b6d..2a4c21a 100644
--- a/systemsetting/src/main/java/com/eeo/systemsetting/EeoApplication.java
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/EeoApplication.java
@@ -160,6 +160,15 @@ public class EeoApplication extends Application {
         //startProvisionApp();
         //允许无障碍服务功能-来监听其它应用的弹窗
         CommonUtils.enablePopupAlertService(EeoApplication.this, true);
+
+        // Add this section to send broadcast for DSP update check
+        Log.i(TAG, "EeoApplication:onCreate - Sending broadcast to internal DspUpdateCheckReceiver.");
+        Intent dspUpdateIntent = new Intent("com.eeo.systemsetting.action.CHECK_DSP_UPDATE");
+        // dspUpdateIntent.setPackage(getPackageName()); // Ensured by receiver's exported="false" if not for other apps, or explicit if exported="true"
+        // Since DspUpdateCheckReceiver is in the same app and exported=true, explicit setPackage is good practice.
+        dspUpdateIntent.setPackage(getApplicationContext().getPackageName());
+        sendBroadcast(dspUpdateIntent);
+        // End of added section
     }
 
     @Override
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/receiver/DspUpdateCheckReceiver.java b/systemsetting/src/main/java/com/eeo/systemsetting/receiver/DspUpdateCheckReceiver.java
new file mode 100644
index 0000000..84e2653
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/receiver/DspUpdateCheckReceiver.java
@@ -0,0 +1,55 @@
+package com.eeo.systemsetting.receiver;
+
+import android.content.BroadcastReceiver;
+import android.content.ComponentName;
+import android.content.Context;
+import android.content.Intent;
+import android.util.Log;
+
+import com.eeo.systemsetting.usbserial.UsbSerialManager;
+import com.eeo.ota.dsp.IDspCommunicationManager;
+import com.eeo.ota.service.DspUpdateService;
+
+public class DspUpdateCheckReceiver extends BroadcastReceiver {
+
+    private static final String TAG = "DspUpdateCheckReceiver";
+    public static final String ACTION_CHECK_DSP_UPDATE = "com.eeo.systemsetting.action.CHECK_DSP_UPDATE";
+
+    @Override
+    public void onReceive(Context context, Intent intent) {
+        if (intent != null && ACTION_CHECK_DSP_UPDATE.equals(intent.getAction())) {
+            Log.i(TAG, "Received action to check DSP update.");
+
+            // Get the UsbSerialManager instance (which implements IDspCommunicationManager)
+            IDspCommunicationManager dspCommManager = UsbSerialManager.getInstance(context.getApplicationContext());
+
+            if (dspCommManager == null) {
+                Log.e(TAG, "UsbSerialManager (IDspCommunicationManager) instance is null. Cannot proceed with DSP update.");
+                return;
+            }
+
+            // Provide the communication manager to the DspUpdateService
+            DspUpdateService.provideCommunicationManager(dspCommManager);
+
+            // Start the DspUpdateService
+            Intent serviceIntent = new Intent(context, DspUpdateService.class);
+            // Optionally, if DspUpdateService needs to know it's started for an immediate update vs. just binding:
+            serviceIntent.setAction("com.eeo.ota.action.START_DSP_UPDATE_IMMEDIATE"); 
+            
+            ComponentName serviceComponentName = null;
+            try {
+                serviceComponentName = context.startService(serviceIntent);
+            } catch (Exception e) {
+                 Log.e(TAG, "Error starting DspUpdateService: " + e.getMessage(), e);
+            }
+            
+            if (serviceComponentName != null) {
+                Log.i(TAG, "DspUpdateService started successfully by DspUpdateCheckReceiver.");
+            } else {
+                Log.e(TAG, "Failed to start DspUpdateService. ComponentName is null. Check service declaration in Manifest and permissions.");
+            }
+        } else {
+            Log.w(TAG, "Received unexpected intent: " + (intent != null ? intent.getAction() : "null intent"));
+        }
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/DspUpgradeFrameParser.java b/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/DspUpgradeFrameParser.java
new file mode 100644
index 0000000..b492142
--- /dev/null
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/DspUpgradeFrameParser.java
@@ -0,0 +1,294 @@
+package com.eeo.systemsetting.usbserial;
+import com.eeo.ota.dsp.DspFirmwareUpdater;
+
+import android.util.Log;
+import java.util.ArrayList;
+import java.util.LinkedList;
+import java.util.List;
+import java.util.Queue;
+
+/**
+ * DSP升级专用帧解析器
+ * 专门处理DSP升级过程中的"粘包"问题
+ * 支持多帧缓存和单帧返回机制
+ */
+public class DspUpgradeFrameParser {
+    private static final String TAG = "DspUpgradeFrameParser";
+    
+    // DSP协议帧头和帧尾
+    private static final byte FRAME_HEADER_START = 0x7B;
+    private static final byte FRAME_HEADER_END = 0x7D;
+    private static final byte FRAME_TRAILER_START = 0x7D;
+    private static final byte FRAME_TRAILER_END = 0x7B;
+    
+    // 最小帧长度：帧头(2字节) + 最少内容(1字节) + 帧尾(2字节) = 5字节
+    private static final int MIN_FRAME_LENGTH = 5;
+    
+    // 缓冲区管理
+    private byte[] incompleteFrameBuffer;  // 存储不完整的帧数据
+    private int bufferPosition;            // 缓冲区当前位置
+    private final int maxBufferSize;       // 最大缓冲区大小
+    
+    // 完整帧队列 - 关键设计：存储已解析出的完整帧
+    private final Queue<byte[]> completeFrameQueue;
+    
+    // 统计信息
+    private int totalFramesParsed;
+    private int stickyPacketCount;  // 粘包次数统计
+    
+    /**
+     * 构造函数
+     * @param maxBufferSize 最大缓冲区大小，建议512字节
+     */
+    public DspUpgradeFrameParser(int maxBufferSize) {
+        this.maxBufferSize = maxBufferSize;
+        this.incompleteFrameBuffer = new byte[maxBufferSize];
+        this.bufferPosition = 0;
+        this.completeFrameQueue = new LinkedList<>();
+        this.totalFramesParsed = 0;
+        this.stickyPacketCount = 0;
+        
+        Log.i(TAG, "DspUpgradeFrameParser initialized with buffer size: " + maxBufferSize);
+    }
+    
+    /**
+     * 默认构造函数，使用512字节缓冲区
+     */
+    public DspUpgradeFrameParser() {
+        this(512);
+    }
+    
+    /**
+     * 核心方法：处理新接收的数据，可能包含粘包
+     * 
+     * @param newData 新接收的原始数据
+     * @param dataLength 有效数据长度
+     * @return 是否成功处理数据
+     */
+    public boolean processIncomingData(byte[] newData, int dataLength) {
+        if (newData == null || dataLength <= 0) {
+            return true; // 空数据不算错误
+        }
+        
+        if (DspFirmwareUpdater.DEBUG) Log.d(TAG, "Processing " + dataLength + " bytes: " + bytesToHexString(newData, dataLength));
+        
+        // 检查缓冲区空间
+        if (bufferPosition + dataLength > maxBufferSize) {
+            Log.e(TAG, "Buffer overflow! Current: " + bufferPosition + ", New: " + dataLength + ", Max: " + maxBufferSize);
+            clearBuffer(); // 清空缓冲区，重新开始
+            return false;
+        }
+        
+        // 将新数据追加到缓冲区
+        System.arraycopy(newData, 0, incompleteFrameBuffer, bufferPosition, dataLength);
+        bufferPosition += dataLength;
+        
+        // 尝试解析出完整帧
+        parseCompleteFrames();
+        
+        return true;
+    }
+    
+    /**
+     * 获取下一个完整的DSP协议帧
+     * 这是UsbSerialManager调用的主要接口
+     * 
+     * @return 完整的DSP协议帧，如果没有可用帧则返回null
+     */
+    public byte[] getNextCompleteFrame() {
+        byte[] frame = completeFrameQueue.poll();
+        if (frame != null) {
+            if (DspFirmwareUpdater.DEBUG) Log.d(TAG, "Returning complete frame (" + frame.length + " bytes): " + bytesToHexString(frame, frame.length));
+        }
+        return frame;
+    }
+    
+    /**
+     * 检查是否有可用的完整帧
+     * 
+     * @return true如果有完整帧可用
+     */
+    public boolean hasCompleteFrame() {
+        return !completeFrameQueue.isEmpty();
+    }
+    
+    /**
+     * 获取当前队列中完整帧的数量
+     * 
+     * @return 队列中帧的数量
+     */
+    public int getCompleteFrameCount() {
+        return completeFrameQueue.size();
+    }
+    
+    /**
+     * 检查是否有未处理完的缓冲数据
+     * 
+     * @return true如果缓冲区中有数据
+     */
+    public boolean hasBufferedData() {
+        return bufferPosition > 0;
+    }
+    
+    /**
+     * 获取缓冲区中的数据长度
+     * 
+     * @return 缓冲区数据长度
+     */
+    public int getBufferLength() {
+        return bufferPosition;
+    }
+    
+    /**
+     * 清空所有缓存数据
+     */
+    public void clearAll() {
+        clearBuffer();
+        completeFrameQueue.clear();
+        Log.i(TAG, "All buffers and queues cleared. Stats - Total frames: " + totalFramesParsed + 
+                  ", Sticky packets: " + stickyPacketCount);
+    }
+    
+    /**
+     * 清空不完整帧缓冲区
+     */
+    public void clearBuffer() {
+        bufferPosition = 0;
+        Log.d(TAG, "Incomplete frame buffer cleared");
+    }
+    
+    /**
+     * 获取统计信息
+     * 
+     * @return 统计信息字符串
+     */
+    public String getStatistics() {
+        return String.format("Frames parsed: %d, Sticky packets detected: %d, Queue size: %d, Buffer: %d bytes",
+                           totalFramesParsed, stickyPacketCount, completeFrameQueue.size(), bufferPosition);
+    }
+    
+    /**
+     * 从缓冲区中解析出所有完整的帧
+     * 这是核心的粘包处理逻辑 - 修复版本
+     */
+    private void parseCompleteFrames() {
+        int parsePosition = 0;
+        int framesFoundInThisBatch = 0;
+        
+        while (parsePosition <= bufferPosition - MIN_FRAME_LENGTH) {
+            // 寻找帧头 7B 7D
+            if (incompleteFrameBuffer[parsePosition] == FRAME_HEADER_START && 
+                parsePosition + 1 < bufferPosition && 
+                incompleteFrameBuffer[parsePosition + 1] == FRAME_HEADER_END) {
+                
+                // 找到帧头，从此位置开始寻找对应的帧尾
+                int frameStartPos = parsePosition;
+                int searchPos = parsePosition + 2; // 从帧头后开始搜索帧尾
+                
+                // 寻找匹配的帧尾 7D 7B
+                while (searchPos <= bufferPosition - 2) {
+                    if (incompleteFrameBuffer[searchPos] == FRAME_TRAILER_START && 
+                        incompleteFrameBuffer[searchPos + 1] == FRAME_TRAILER_END) {
+                        
+                        // 找到完整帧: frameStartPos 到 searchPos + 2
+                        int frameEndPos = searchPos + 2;
+                        int frameLength = frameEndPos - frameStartPos;
+                        
+                        if (frameLength >= MIN_FRAME_LENGTH) {
+                            // 提取完整帧
+                            extractCompleteFrame(frameStartPos, frameEndPos);
+                            framesFoundInThisBatch++;
+                        }
+                        
+                        // 从帧尾后继续搜索下一个帧
+                        parsePosition = frameEndPos;
+                        break;
+                    }
+                    searchPos++;
+                }
+                
+                // 如果没找到匹配的帧尾，说明帧不完整，跳过这个帧头
+                if (searchPos > bufferPosition - 2) {
+                    parsePosition++;
+                }
+            } else {
+                parsePosition++;
+            }
+        }
+        
+        // 如果找到了多个帧，说明发生了粘包
+        if (framesFoundInThisBatch > 1) {
+            stickyPacketCount++;
+            Log.i(TAG, "Sticky packet detected! Found " + framesFoundInThisBatch + " frames in one batch. Total sticky packets: " + stickyPacketCount);
+        }
+        
+        // 处理剩余数据：将未处理的数据移到缓冲区开头
+        if (parsePosition < bufferPosition) {
+            int remainingLength = bufferPosition - parsePosition;
+            if (remainingLength > 0 && parsePosition > 0) {
+                System.arraycopy(incompleteFrameBuffer, parsePosition, incompleteFrameBuffer, 0, remainingLength);
+                bufferPosition = remainingLength;
+                Log.d(TAG, "Moved incomplete frame to buffer start, " + remainingLength + " bytes remaining");
+            }
+        } else {
+            // 所有数据都已处理，清空缓冲区
+            bufferPosition = 0;
+        }
+    }
+    
+    /**
+     * 提取并添加完整帧到队列
+     * 
+     * @param startPos 帧开始位置
+     * @param endPos 帧结束位置（不包含）
+     */
+    private void extractCompleteFrame(int startPos, int endPos) {
+        int frameLength = endPos - startPos;
+        byte[] completeFrame = new byte[frameLength];
+        System.arraycopy(incompleteFrameBuffer, startPos, completeFrame, 0, frameLength);
+        
+        // 验证帧格式
+        if (isValidFrame(completeFrame)) {
+            completeFrameQueue.offer(completeFrame);
+            totalFramesParsed++;
+            if (DspFirmwareUpdater.DEBUG) Log.d(TAG, "Complete frame extracted (" + frameLength + " bytes): " + bytesToHexString(completeFrame, frameLength));
+        } else {
+            Log.w(TAG, "Invalid frame format detected, discarding: " + bytesToHexString(completeFrame, frameLength));
+        }
+    }
+    
+    /**
+     * 验证帧格式是否正确
+     * 
+     * @param frame 待验证的帧
+     * @return true如果帧格式正确
+     */
+    private boolean isValidFrame(byte[] frame) {
+        if (frame == null || frame.length < MIN_FRAME_LENGTH) {
+            return false;
+        }
+        
+        return frame[0] == FRAME_HEADER_START && 
+               frame[1] == FRAME_HEADER_END &&
+               frame[frame.length - 2] == FRAME_TRAILER_START && 
+               frame[frame.length - 1] == FRAME_TRAILER_END;
+    }
+    
+    /**
+     * 工具方法：将字节数组转换为十六进制字符串
+     * 
+     * @param bytes 字节数组
+     * @param length 有效长度
+     * @return 十六进制字符串
+     */
+    private String bytesToHexString(byte[] bytes, int length) {
+        if (bytes == null || length <= 0) return "";
+        
+        StringBuilder sb = new StringBuilder();
+        for (int i = 0; i < Math.min(length, bytes.length); i++) {
+            if (i > 0) sb.append(" ");
+            sb.append(String.format("%02X", bytes[i]));
+        }
+        return sb.toString();
+    }
+} 
\ No newline at end of file
diff --git a/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/UsbSerialManager.java b/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/UsbSerialManager.java
index 36b4f35..c12b407 100644
--- a/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/UsbSerialManager.java
+++ b/systemsetting/src/main/java/com/eeo/systemsetting/usbserial/UsbSerialManager.java
@@ -11,13 +11,19 @@ import android.util.Log;
 
 import com.eeo.systemsetting.utils.CommonUtils;
 import com.eeo.systemsetting.utils.Constant;
+import com.eeo.systemsetting.usbserial.AudioMixerConstant;
+import com.eeo.systemsetting.usbserial.ParseUtil;
 
 import java.util.ArrayList;
 import java.util.Arrays;
 import java.util.HashMap;
 import java.util.concurrent.atomic.AtomicInteger;
+import java.util.concurrent.locks.ReentrantReadWriteLock;
+import java.util.concurrent.TimeUnit;
 
-public class UsbSerialManager implements Runnable {
+import com.eeo.ota.dsp.IDspCommunicationManager;
+
+public class UsbSerialManager implements Runnable, IDspCommunicationManager {
 
     public enum State {
         STOPPED,
@@ -32,6 +38,9 @@ public class UsbSerialManager implements Runnable {
 
     public static final int TIMEOUT_DEFAULT = 500;
 
+    // Lock timeout for preventing deadlocks (in seconds)
+    private static final int LOCK_TIMEOUT_SECONDS = 5;
+
     private static UsbSerialManager mUsbSerialManager = null;
     private Context mContext;
     private UsbManager mUsbManager;
@@ -53,6 +62,14 @@ public class UsbSerialManager implements Runnable {
 
     private ArrayList<Listener> listeners = new ArrayList<>();
 
+    // DSP Upgrade mode concurrent control mechanism
+    private volatile boolean isInUpgradeMode = false;
+    private final ReentrantReadWriteLock dspAccessLock = new ReentrantReadWriteLock();
+    private final Object upgradeModeLock = new Object();
+    
+    // DSP升级专用粘包处理器
+    private DspUpgradeFrameParser upgradeFrameParser = new DspUpgradeFrameParser();
+
     public interface Listener {
         /**
          * Called when new incoming data is available.
@@ -113,8 +130,8 @@ public class UsbSerialManager implements Runnable {
         }
         if (getState() == State.RUNNING) {
             Log.e(TAG, "start: already running");
-            return;
-        }
+                return;
+            }
         new Thread(this).start();
     }
 
@@ -131,53 +148,143 @@ public class UsbSerialManager implements Runnable {
 
     @Override
     public void run() {
-        synchronized (this) {
+       synchronized (this) {
             if (getState() == State.RUNNING) {
                 Log.e(TAG, "run: already running");
-                return;
-            }
-            mState = State.RUNNING;
-        }
-        try {
+               return;
+           }
+           mState = State.RUNNING;
+       }
+       try {
             while (true) {
-                if (mState != State.RUNNING) {
+               if (mState != State.RUNNING) {
                     Log.d(TAG, "run: stop mState=" + mState);
-                    break;
-                }
-                if (readingCount.get() <= 0) {
-                    read();
+                   break;
+               }
+                
+                // Check upgrade mode and pause background thread if needed
+                boolean canProceed = false;
+                try {
+                    if (dspAccessLock.readLock().tryLock(50, TimeUnit.MILLISECONDS)) {
+                        try {
+                            // Check upgrade mode under lock protection
+                            canProceed = !isInUpgradeMode;
+                        } finally {
+                            dspAccessLock.readLock().unlock();
+                        }
+                    }
+                } catch (InterruptedException e) {
+                    Log.w(TAG, "Background reader thread interrupted while checking upgrade mode");
+                    if (mState == State.RUNNING) {
+                        Thread.currentThread().interrupt();
+                        continue;
+                    } else {
+                        break;
+                    }
                 }
+                
+                // If in upgrade mode, pause the background thread completely
+                if (!canProceed) {
+               synchronized (upgradeModeLock) {
+                   if (isInUpgradeMode) {
+                       try {
+                           Log.d(TAG, "DSP in upgrade mode, background reader pausing via wait().");
+                           upgradeModeLock.wait(); 
+                           Log.d(TAG, "Background reader unpaused.");
+                           // After waking up, re-check conditions before continuing the loop
+                           if (mState != State.RUNNING || isInUpgradeMode) { 
+                               continue; // Skip to next iteration if still in upgrade mode or not running
+                           }
+                       } catch (InterruptedException e) {
+                           Log.w(TAG, "Background reader thread interrupted while waiting in upgrade mode. State: " + mState);
+                           if (mState == State.RUNNING) {
+                               Thread.currentThread().interrupt(); // Re-interrupt if still running
+                               continue; // Continue to check loop condition
+                           } else { 
+                               break; // Exit loop if not running anymore
+                           }
+                       }
+                   }
+                    }
+                    continue; // Skip the current loop iteration
+               }
+               if (mState != State.RUNNING) break; // Re-check state after potential wait
+
+               if (readingCount.get() <= 0) {
+                   read();
+               }
                 Thread.sleep(10);
-            }
-        } catch (Exception e) {
+           }
+       } catch (Exception e) {
             e.printStackTrace();
             Log.e(TAG, "run exception:" + e);
-        } finally {
-            synchronized (this) {
-                mState = State.STOPPED;
+       } finally {
+           synchronized (this) {
+                   mState = State.STOPPED;
                 Log.d(TAG, "run: Stopped");
-            }
-        }
+           }
+       }
     }
 
-    /**
+     /**
      * 发送串口指令
      * 如设置扬声器静音：7B 7D 01 A2 CA 01 7D 7B
      * 这里使用的是字节数组：[123, 125, 1, -94, -54, 2, 125, 123]
      */
     public void write(byte[] data, int timeout) {
+        // Try to acquire read lock for regular DSP operations
+        try {
+            if (!dspAccessLock.readLock().tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
+                Log.w(TAG, "write: Failed to acquire read lock within timeout. DSP may be in upgrade mode.");
+                return;
+            }
+        } catch (InterruptedException e) {
+            Thread.currentThread().interrupt();
+            Log.w(TAG, "write: Interrupted while waiting for read lock");
+            return;
+        }
+        
+        try {
+            // Double-check upgrade mode under lock
+            if (isInUpgradeMode) {
+                Log.w(TAG, "DSP in upgrade mode. Write operation (byte[]) from regular context rejected.");
+                return;
+            }
+            
         if (mConnection == null) {
             Log.e(TAG, "write: mConnection is null");
-            return;
+                return;
         }
         //写入数据
         int bytesWritten = mConnection.bulkTransfer(mWriteEndpoint, data, data.length, timeout);
         if (Constant.IS_USERDEBUG) {
             Log.d(TAG, "write: " + Arrays.toString(data) + "(" + CommonUtils.byteArrayToHexString(data) + ") , result = " + bytesWritten);
         }
+        } finally {
+            dspAccessLock.readLock().unlock();
+        }
     }
 
     public void write(String key, int timeout) {
+        // Try to acquire read lock for regular DSP operations
+        try {
+            if (!dspAccessLock.readLock().tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
+                Log.w(TAG, "write: Failed to acquire read lock within timeout. DSP may be in upgrade mode.");
+                return;
+            }
+        } catch (InterruptedException e) {
+            Thread.currentThread().interrupt();
+            Log.w(TAG, "write: Interrupted while waiting for read lock");
+            return;
+        }
+        
+        try {
+            // Double-check upgrade mode under lock
+            if (isInUpgradeMode) {
+                Log.w(TAG, "DSP in upgrade mode. Write operation (String) from regular context rejected.");
+                return;
+            }
+            
         if (mConnection == null) {
             Log.e(TAG, "write: mConnection is null");
             return;
@@ -187,6 +294,9 @@ public class UsbSerialManager implements Runnable {
         int bytesWritten = mConnection.bulkTransfer(mWriteEndpoint, data, data.length, timeout);
         if (Constant.IS_USERDEBUG) {
             Log.d(TAG, "write: " + key + " , result = " + bytesWritten);
+            }
+        } finally {
+            dspAccessLock.readLock().unlock();
         }
     }
 
@@ -196,11 +306,38 @@ public class UsbSerialManager implements Runnable {
     }
 
     public int read(byte[] results, int timeout) {
+        // Try to acquire read lock for regular DSP operations
+        try {
+            if (!dspAccessLock.readLock().tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
+                Log.w(TAG, "read: Failed to acquire read lock within timeout. DSP may be in upgrade mode.");
+                return -1;
+            }
+        } catch (InterruptedException e) {
+            Thread.currentThread().interrupt();
+            Log.w(TAG, "read: Interrupted while waiting for read lock");
+            return -1;
+        }
+        
+        try {
+            // Double-check upgrade mode under lock
+            if (isInUpgradeMode) {
+                Log.w(TAG, "DSP in upgrade mode. Read operation (byte[]) from regular context rejected.");
+                return -1;
+            }
+            
         if (mConnection == null) {
             Log.e(TAG, "read: mConnection is null");
             return -1;
         }
-        return mConnection.bulkTransfer(mReadEndpoint, results, results.length, timeout);
+        //读取数据
+        int result = mConnection.bulkTransfer(mReadEndpoint, results, results.length, timeout);
+        if (Constant.IS_USERDEBUG) {
+            Log.d(TAG, "read: (" + CommonUtils.byteArrayToHexString(results) + "), result = " + result);
+        }
+        return result;
+        } finally {
+            dspAccessLock.readLock().unlock();
+        }
     }
 
     public void read() {
@@ -279,9 +416,14 @@ public class UsbSerialManager implements Runnable {
         boolean isBuildInSpeakerEnable = false;
         write(AudioMixerConstant.KEY_SPEAKER_GET_MUTE_BUILD_IN, 100);
         //第一个回复可能被自动read读取了
+        try {
         if (read(result1, 100) > 0) {
             isBuildInSpeakerEnable = ParseUtil.parseSpeakerEnable(result1);
         } else {
+                isBuildInSpeakerEnable = ParseUtil.parseSpeakerEnable(mReadBuffer);
+            }
+        } catch (Exception e) {
+            Log.e(TAG, "Error reading speaker status", e);
             isBuildInSpeakerEnable = ParseUtil.parseSpeakerEnable(mReadBuffer);
         }
         readingCount.decrementAndGet();
@@ -783,4 +925,180 @@ public class UsbSerialManager implements Runnable {
         Log.d(TAG, "setMicBChannel: " + value);
         write(data, 100);
     }
+
+    // ===== IDspCommunicationManager Interface Implementation =====
+
+    @Override
+    public AtomicInteger getReadingCount() {
+        return readingCount;
+    }
+
+    @Override
+    public void enterUpgradeMode() {
+        Log.i(TAG, "Entering DSP upgrade mode - acquiring write lock.");
+        
+        // Acquire write lock to block all regular DSP operations
+        try {
+            if (!dspAccessLock.writeLock().tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
+                Log.e(TAG, "Failed to acquire write lock for upgrade mode within timeout.");
+                throw new RuntimeException("Cannot enter DSP upgrade mode - failed to acquire exclusive access");
+            }
+        } catch (InterruptedException e) {
+            Thread.currentThread().interrupt();
+            throw new RuntimeException("Interrupted while entering DSP upgrade mode", e);
+        }
+        
+        synchronized (upgradeModeLock) {
+            isInUpgradeMode = true;
+            // 清空粘包处理器，开始全新的升级会话
+            upgradeFrameParser.clearAll();
+            Log.i(TAG, "DSP upgrade mode activated - all regular operations blocked.");
+            // Notify the background reader thread if it's waiting
+            upgradeModeLock.notifyAll(); 
+        }
+    }
+
+    @Override
+    public void exitUpgradeMode() {
+        synchronized (upgradeModeLock) {
+            Log.i(TAG, "Exiting DSP upgrade mode.");
+            isInUpgradeMode = false;
+            // 清空粘包处理器，结束升级会话
+            upgradeFrameParser.clearAll();
+            // Notify the background reader thread to resume
+            upgradeModeLock.notifyAll(); 
+        }
+        
+        // Release write lock to allow regular DSP operations to resume
+        // Check if we actually hold the lock before attempting to release it
+        if (dspAccessLock.writeLock().isHeldByCurrentThread()) {
+            try {
+                dspAccessLock.writeLock().unlock();
+                Log.i(TAG, "DSP upgrade mode deactivated - regular operations resumed.");
+            } catch (IllegalMonitorStateException e) {
+                Log.w(TAG, "Unexpected error while releasing write lock", e);
+            }
+        } else {
+            Log.w(TAG, "exitUpgradeMode called but current thread doesn't hold the write lock - possibly already released.");
+        }
+    }
+
+    @Override
+    public int writeForUpgrade(byte[] data, int timeout) {
+        if (mConnection == null) {
+            Log.e(TAG, "writeForUpgrade: mConnection is null");
+            return -1;
+        }
+        if (mWriteEndpoint == null) {
+            Log.e(TAG, "writeForUpgrade: mWriteEndpoint is null");
+            return -1;
+        }
+        // This is a direct write, bypassing normal lock checks for upgrade operations
+        int bytesWritten = mConnection.bulkTransfer(mWriteEndpoint, data, data.length, timeout);
+        if (Constant.IS_USERDEBUG) {
+            Log.d(TAG, "writeForUpgrade: " + java.util.Arrays.toString(data) + " (" + CommonUtils.byteArrayToHexString(data) + ") , result = " + bytesWritten);
+        }
+        return bytesWritten;
+    }
+
+    @Override
+    public int readForUpgrade(byte[] buffer, int timeout) {
+        if (mConnection == null) {
+            Log.e(TAG, "readForUpgrade: mConnection is null");
+            return -1;
+        }
+        if (mReadEndpoint == null) {
+            Log.e(TAG, "readForUpgrade: mReadEndpoint is null");
+            return -1;
+        }
+        if (buffer == null) {
+            Log.e(TAG, "readForUpgrade: buffer is null");
+            return -1;
+        }
+        
+        // 首先检查粘包处理器中是否有可用的完整帧
+        byte[] cachedFrame = upgradeFrameParser.getNextCompleteFrame();
+        if (cachedFrame != null) {
+            if (cachedFrame.length <= buffer.length) {
+                System.arraycopy(cachedFrame, 0, buffer, 0, cachedFrame.length);
+        if (Constant.IS_USERDEBUG) {
+                    Log.d(TAG, "readForUpgrade: Returning cached frame (" + CommonUtils.byteArrayToHexString(java.util.Arrays.copyOf(buffer, cachedFrame.length)) + "), bytes = " + cachedFrame.length);
+                }
+                return cachedFrame.length;
+            } else {
+                Log.e(TAG, "readForUpgrade: Cached frame too large for buffer. Frame: " + cachedFrame.length + ", Buffer: " + buffer.length);
+                return -1;
+            }
+        }
+        
+        // 没有缓存帧，需要从USB读取新数据并处理粘包
+        long startTime = System.currentTimeMillis();
+        byte[] rawBuffer = new byte[512]; // 临时缓冲区用于读取原始数据
+        
+        while (System.currentTimeMillis() - startTime < timeout) {
+            // 直接从USB读取原始数据
+            int bytesRead = mConnection.bulkTransfer(mReadEndpoint, rawBuffer, rawBuffer.length, 
+                                                   Math.min(100, (int)(timeout - (System.currentTimeMillis() - startTime))));
+            
+            if (bytesRead > 0) {
+                if (Constant.IS_USERDEBUG) {
+                    Log.d(TAG, "readForUpgrade: Raw USB read (" + CommonUtils.byteArrayToHexString(java.util.Arrays.copyOf(rawBuffer, bytesRead)) + "), bytes = " + bytesRead);
+                }
+                
+                // 将读取的数据输入粘包处理器
+                boolean processed = upgradeFrameParser.processIncomingData(rawBuffer, bytesRead);
+                if (!processed) {
+                    Log.e(TAG, "readForUpgrade: Failed to process incoming data");
+                    return -1;
+                }
+                
+                // 检查是否有完整帧可用
+                byte[] completeFrame = upgradeFrameParser.getNextCompleteFrame();
+                if (completeFrame != null) {
+                    if (completeFrame.length <= buffer.length) {
+                        System.arraycopy(completeFrame, 0, buffer, 0, completeFrame.length);
+                        
+                        if (Constant.IS_USERDEBUG) {
+                            Log.d(TAG, "readForUpgrade: Parsed complete frame (" + CommonUtils.byteArrayToHexString(java.util.Arrays.copyOf(buffer, completeFrame.length)) + "), bytes = " + completeFrame.length);
+                            
+                            // 如果队列中还有更多帧，记录日志
+                            int remainingFrames = upgradeFrameParser.getCompleteFrameCount();
+                            if (remainingFrames > 0) {
+                                Log.i(TAG, "readForUpgrade: " + remainingFrames + " additional frames cached from sticky packet");
+                            }
+                        }
+                        return completeFrame.length;
+                    } else {
+                        Log.e(TAG, "readForUpgrade: Parsed frame too large for buffer. Frame: " + completeFrame.length + ", Buffer: " + buffer.length);
+                        return -1;
+                    }
+                }
+                // 没有完整帧，继续读取更多数据
+            } else if (bytesRead < 0) {
+                // USB读取错误 - 记录警告但继续等待，不立即退出
+                Log.w(TAG, "readForUpgrade: USB read error: " + bytesRead + " - continuing to wait for response");
+            }
+            // bytesRead == 0 或 < 0 都表示暂时没有数据，继续等待
+        }
+        
+        // 超时，检查是否有不完整的数据需要处理
+        if (upgradeFrameParser.hasBufferedData()) {
+            Log.w(TAG, "readForUpgrade: Timeout with " + upgradeFrameParser.getBufferLength() + " bytes buffered. Incomplete frame?");
+        }
+        
+        if (Constant.IS_USERDEBUG) {
+            Log.d(TAG, "readForUpgrade: Timeout, no complete frame received. " + upgradeFrameParser.getStatistics());
+        }
+        return 0; // 超时
+    }
+
+    @Override
+    public void enterReadOperation() {
+        readingCount.incrementAndGet();
+    }
+
+    @Override
+    public void exitReadOperation() {
+        readingCount.decrementAndGet();
+    }
 }
