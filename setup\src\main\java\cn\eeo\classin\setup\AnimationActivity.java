package cn.eeo.classin.setup;

import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.elvishew.xlog.XLog;


public class AnimationActivity extends AppCompatActivity {
    private static final String TAG = "AnimationActivity";
    private ImageView imageView;
    private LinearLayout ll_check_update;
    private AnimationDrawable master_control_animationDrawable1;
    private AnimationDrawable animationDrawable2;
    private AnimationDrawable animationDrawable3;
    private int master_control_animationDrawable_flag = 0;
    private int animationDrawable2_flag = 0;
    private int animationDrawable3_flag = 0;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.animationlayout);
        imageView = findViewById(R.id.imageView);
        ll_check_update = findViewById(R.id.ll_system_init);
        //loadingLottieView = findViewById(R.id.loadingLottieView);
        // 创建第一个帧动画  妙控屏-滑条半屏-大屏控制
        master_control_animationDrawable1 = new AnimationDrawable();

    }

    @Override
    protected void onStart() {
        super.onStart();
        for (int i = 1; i < 20; i++) {
            // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
            int resourceId;
            if (i < 9) {
                XLog.d( "master_control_000" + i);
                resourceId = getResources().getIdentifier("master_control_000" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                }
            } else if (i == 9) {
                XLog.d("master_control_000" + i);
                resourceId = getResources().getIdentifier("master_control_000" + i, "drawable", getPackageName());
                XLog.d("resourceId"+resourceId+"    I="+i);
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 1500); // 设置每帧的持续时间
                }
            } else if (i > 9) {
                XLog.d( "master_control_00" + i);
                resourceId = getResources().getIdentifier("master_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    master_control_animationDrawable1.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                }
            }

        }


        // 创建第二个帧动画 滑条半屏
        animationDrawable2 = new AnimationDrawable();
        animationDrawable2.setOneShot(true);
        // 遍历添加 21 张帧动画图片
        for (int i = 1; i < 22; i++) {
            // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
            int resourceId;
            if (i < 10) {
                XLog.d( "screen_control_000" + i);
                resourceId = getResources().getIdentifier("screen_control_000" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 100);
                }
            } else if (i==11){
                XLog.d( "screen_control_00" + i);
                resourceId = getResources().getIdentifier("screen_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0) {
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 1500);
                }
            }else {
                resourceId = getResources().getIdentifier("screen_control_00" + i, "drawable", getPackageName());
                if (resourceId != 0){
                    animationDrawable2.addFrame(getResources().getDrawable(resourceId), 100);
                }
            }
        }

        // 创建第三个帧动画 大屏控制
        animationDrawable3 = new AnimationDrawable();
        animationDrawable3.setOneShot(true);
        // 遍历添加 20 张帧动画图片
        for (int i = 1; i < 25; i++) {
            // 通过循环获取资源 ID，假设图片名称为 frame1, frame2, ..., frame20
            int resourceId;
            if (i < 10) {
                XLog.d( "half_screen_000" + i);
                resourceId = getResources().getIdentifier("half_screen_000" + i, "drawable", getPackageName());
                if(resourceId != 0) {
                    animationDrawable3.addFrame(getResources().getDrawable(resourceId), 100); // 设置每帧的持续时间
                }
            }else if (i==15){
                resourceId = getResources().getIdentifier("half_screen_00" + i, "drawable", getPackageName());
                if(resourceId != 0) {
                    animationDrawable3.addFrame(getResources().getDrawable(resourceId), 1500); // 设置每帧的持续时间
                }
            } else {
                XLog.d( "half_screen_00" + i);
                resourceId = getResources().getIdentifier("half_screen_00" + i, "drawable", getPackageName());
                if(resourceId != 0) {
                    animationDrawable3.addFrame(getResources().getDrawable(resourceId), 100);
                }
            }
        }
        //loadingLottieView.playAnimation();
        startAnimation(master_control_animationDrawable1);
        ll_check_update.setVisibility(View.VISIBLE);

    }

    private void startAnimation(final AnimationDrawable animationDrawable) {
        imageView.setImageDrawable(animationDrawable);
        // 设置动画监听器
        Drawable  mLastFrame = animationDrawable.getFrame(animationDrawable.getNumberOfFrames() - 1);
        animationDrawable.setCallback(new Drawable.Callback() {
            @Override
            public void invalidateDrawable(Drawable who) {
                imageView.invalidateDrawable(who);
                XLog.d( "invalidateDrawable");
                if (mLastFrame.equals(who.getCurrent())){
                    XLog.d("最后一帧");
                    if (who == master_control_animationDrawable1) {
                        XLog.d( "master_control_animationDrawable1");
                        if (master_control_animationDrawable_flag<=2){
                            master_control_animationDrawable_flag ++;
                            XLog.d( "startAnimation master_control_animationDrawable1");
                            master_control_animationDrawable1.stop();
                            startAnimation(master_control_animationDrawable1);
                        }else {
                            XLog.d( "startAnimation animationDrawable2");
                            master_control_animationDrawable_flag=0;
                            startAnimation(animationDrawable2);
                        }

                    } else if (who == animationDrawable2) {
                        XLog.d( "animationDrawable2");
                        if (animationDrawable2_flag<=2){
                            animationDrawable2_flag ++;
                            animationDrawable2.stop();
                            startAnimation(animationDrawable2);
                        }else {
                            animationDrawable2_flag =0;
                            startAnimation(animationDrawable3);
                        }
                    }else if (who == animationDrawable3){
                        XLog.d( "animationDrawable3");
                        if (animationDrawable3_flag<=2){
                            animationDrawable3_flag ++;
                            animationDrawable3.stop();
                            startAnimation(animationDrawable3);
                        }else {
                            animationDrawable3_flag=0;
                            startAnimation(master_control_animationDrawable1);
                        }
                    }
                }

            }

            @Override
            public void scheduleDrawable(Drawable who, Runnable what, long when) {
                imageView.scheduleDrawable(who,what,when);
                XLog.d( "scheduleDrawable");

            }

            @Override
            public void unscheduleDrawable(android.graphics.drawable.Drawable who, Runnable what) {
                imageView.unscheduleDrawable(who,what);
                XLog.d( "unscheduleDrawable");
            }
        });

        animationDrawable.setOneShot(true);
        animationDrawable.start();

    }
}
