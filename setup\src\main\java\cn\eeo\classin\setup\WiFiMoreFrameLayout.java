package cn.eeo.classin.setup;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.DhcpInfo;
import android.net.LinkAddress;
import android.net.LinkProperties;
import android.net.NetworkUtils;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.elvishew.xlog.XLog;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.OnFocusChange;
import butterknife.OnTextChanged;
import cn.eeo.classin.setup.popupwindow.AutoManualPopupWindow;
import cn.eeo.classin.setup.utils.CommonUtils;

public class WiFiMoreFrameLayout extends FrameLayout {
    public static final String TAG = "WiFiMoreFrameLayout===";
    View rootView = null;
    Activity activity;
    @BindView(R.id.img_back)
    ImageView imgBack;
    @BindView(R.id.txt_save)
    TextView txtSave;
    @BindView(R.id.txt_disconnect)
    TextView txtDisconnect;
    @BindView(R.id.txt_forget)
    TextView txtForget;
    @BindView(R.id.img_arrow)
    ImageView imgArrow;
    @BindView(R.id.rl_manual)
    RelativeLayout rlManual;
    @BindView(R.id.edt_ip_address)
    EditText edtIpAddress;
    @BindView(R.id.img_ip)
    ImageView imgIp;
    @BindView(R.id.edt_subnet_mask)
    EditText edtSubnetMask;
    @BindView(R.id.img_subnet_mask)
    ImageView imgSubnetMask;
    @BindView(R.id.edt_gateway)
    EditText edtGateway;
    @BindView(R.id.img_gateway)
    ImageView imgGateway;
    @BindView(R.id.edt_DNS)
    EditText edtDNS;
    @BindView(R.id.img_DNS)
    ImageView imgDNS;
    @BindView(R.id.txt_ip_setting)
    TextView txtIpSetting;
    private WifiInfo wifiInfo;
    private final WifiManager wifiManager;

    //自动还是手动标记
    private int selectIndex = 0;
    private final int AUTO = 0;
    private final int MANUAL = 1;

    //当手动时候点击确定之后，给个标记，让按钮置灰不让点击
    private boolean isSetting = true;

    private final int IP_ADDRESS = 0;
    private final int SUBNET_MASK = 1;
    private final int GATEWAY = 2;
    private final int DNS1 = 3;

    private DhcpInfo dhcpInfo;

    public WiFiMoreFrameLayout(@NonNull Activity activity, WifiInfo wifiInfo) {
        super(activity);
        wifiManager = (WifiManager) activity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        this.activity = activity;
        this.wifiInfo = wifiInfo;
        if (rootView == null) {
            rootView = LayoutInflater.from(activity).inflate(R.layout.item_wifi_more, null);
        }

        ButterKnife.bind(this, rootView);


        addView(rootView);

        imgIp.setVisibility(INVISIBLE);
        imgSubnetMask.setVisibility(INVISIBLE);
        imgGateway.setVisibility(INVISIBLE);
        imgDNS.setVisibility(INVISIBLE);

        rlManual.setVisibility(GONE);
        txtSave.setClickable(false);
        txtSave.setEnabled(false);

        dhcpInfo = wifiManager.getDhcpInfo();
        //判断是静态还是动态
        if (dhcpInfo.leaseDuration == 0) {
            selectIndex = MANUAL;
        } else {
            selectIndex = AUTO;
        }
        updateAutoOrManualView();
    }

    @OnClick(value = {R.id.img_back, R.id.txt_save, R.id.txt_disconnect, R.id.txt_forget, R.id.txt_ip_setting, R.id.img_arrow})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.img_back:
                CommonUtils.backByWiFiMore(activity);
                break;

            case R.id.txt_save:
                setStaticIp(edtIpAddress.getText().toString(), edtSubnetMask.getText().toString(), edtGateway.getText().toString(), edtDNS.getText().toString(), "");
                break;

            case R.id.txt_disconnect:
                boolean disconnectWifiResult = disconnectWifi(wifiInfo.getNetworkId());
                if (disconnectWifiResult) {
                    CommonUtils.backByWiFiMore(activity);
                }

                break;

            case R.id.txt_forget:
                boolean forgetWifiResult = forgetWifi(wifiInfo.getNetworkId());
                if (forgetWifiResult) {
                    CommonUtils.backByWiFiMore(activity);
                }

                break;

            case R.id.txt_ip_setting:
            case R.id.img_arrow:
                setAutoOrManual();
                break;


            default:
                break;
        }
    }

    @OnTextChanged(value = {R.id.edt_ip_address, R.id.edt_subnet_mask, R.id.edt_gateway, R.id.edt_DNS})
    public void onEditTextChange() {
        if (!TextUtils.isEmpty(edtIpAddress.getText().toString()) && !TextUtils.isEmpty(edtSubnetMask.getText().toString()) && !TextUtils.isEmpty(edtGateway.getText().toString()) && !TextUtils.isEmpty(edtDNS.getText().toString())) {

            if (checkoutEditIpByIndex(IP_ADDRESS) && checkoutEditIpByIndex(SUBNET_MASK) && checkoutEditIpByIndex(GATEWAY) && checkoutEditIpByIndex(DNS1) && isSetting) {
                XLog.i( "onEditTextChange: true ");
                imgIp.setVisibility(View.INVISIBLE);
                imgSubnetMask.setVisibility(View.INVISIBLE);
                imgGateway.setVisibility(View.INVISIBLE);
                imgDNS.setVisibility(View.INVISIBLE);

                txtSave.setClickable(true);
                txtSave.setEnabled(true);

            } else {
                XLog.i( "onEditTextChange: false ");
                txtSave.setClickable(false);
                txtSave.setEnabled(false);

            }

        } else {
            txtSave.setClickable(false);
            txtSave.setEnabled(false);
        }
    }

    @OnFocusChange(value = {R.id.edt_ip_address, R.id.edt_subnet_mask, R.id.edt_gateway, R.id.edt_DNS})
    public void expandAppBarOnFocusChangeListener(View view, boolean hasFocus) {
        switch (view.getId()) {
            case R.id.edt_ip_address:

                if (!hasFocus) {
                    boolean isIpAddress = checkoutEditIpByIndex(IP_ADDRESS);
                    XLog.i( "expandAppBarOnFocusChangeListener: edt_ip_address : " + isIpAddress);
                    if (isIpAddress) {
                        imgIp.setVisibility(View.INVISIBLE);
                    } else {
                        imgIp.setVisibility(View.VISIBLE);
                    }
                }
                break;

            case R.id.edt_subnet_mask:
                if (!hasFocus) {

                    boolean isSubNetMask = checkoutEditIpByIndex(SUBNET_MASK);
                    XLog.i( "expandAppBarOnFocusChangeListener: edt_subnet_mask : " + isSubNetMask);
                    if (isSubNetMask) {
                        imgSubnetMask.setVisibility(View.INVISIBLE);
                    } else {
                        imgSubnetMask.setVisibility(View.VISIBLE);
                    }

                }

                break;

            case R.id.edt_gateway:
                if (!hasFocus) {
                    boolean isGateWay = checkoutEditIpByIndex(GATEWAY);
                    XLog.i( "expandAppBarOnFocusChangeListener: edt_gateway : " + isGateWay);
                    if (isGateWay) {
                        imgGateway.setVisibility(View.INVISIBLE);
                    } else {
                        imgGateway.setVisibility(View.VISIBLE);
                    }
                }

                break;

            case R.id.edt_DNS:
                if (!hasFocus) {

                    boolean isDns1 = checkoutEditIpByIndex(DNS1);
                    XLog.i( "expandAppBarOnFocusChangeListener: edt_dns1 : " + isDns1);
                    if (isDns1) {
                        imgDNS.setVisibility(View.INVISIBLE);
                    } else {
                        imgDNS.setVisibility(View.VISIBLE);
                    }
                }

                break;

            default:
                break;
        }
    }


    /**
     * 设置手动还是自动
     */
    private void setAutoOrManual() {
        //隐藏键盘
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(this.getWindowToken(), 0);

        AutoManualPopupWindow autoManualPopupWindow = new AutoManualPopupWindow(activity, imgArrow, selectIndex);
        autoManualPopupWindow.setAutoManualClickCallback(new AutoManualPopupWindow.OnClickCallback() {
            @Override
            public void onClickCallback(int index) {
                XLog.i( "onClickCallback: index : " + index);
                if (index == AUTO) {
                    isSetting = true;
                    selectIndex = AUTO;
                    setDynamicIp();
                } else {
                    selectIndex = MANUAL;
                    dhcpInfo = wifiManager.getDhcpInfo();
                }
                updateAutoOrManualView();
            }
        });
    }

    private void updateAutoOrManualView() {
        if (selectIndex == AUTO) {
            txtIpSetting.setText(getContext().getString(R.string.auto));
            rlManual.setVisibility(View.GONE);
        } else {
            txtIpSetting.setText(getContext().getString(R.string.manual));
            rlManual.setVisibility(View.VISIBLE);
            edtIpAddress.setText(NetworkUtils.intToInetAddress(dhcpInfo.ipAddress).getHostAddress());
            edtGateway.setText(NetworkUtils.intToInetAddress(dhcpInfo.gateway).getHostAddress());
            edtDNS.setText(NetworkUtils.intToInetAddress(dhcpInfo.dns1).getHostAddress());
            String subnetMask = getSubnetMask();
            edtSubnetMask.setText(subnetMask == null ? NetworkUtils.intToInetAddress(dhcpInfo.netmask).getHostAddress() : subnetMask);
        }
    }

    /**
     * 连接指定网络
     *
     * @param networkId
     */
    private void connect(int networkId) {

        wifiManager.enableNetwork(networkId, true);
    }


    /**
     * 断开链接
     *
     * @param netId
     * @return
     */
    public boolean disconnectWifi(int netId) {

        if (null != wifiManager) {

            boolean isDisable = wifiManager.disableNetwork(netId);

            boolean isDisconnect = wifiManager.disconnect();

            return isDisable && isDisconnect;

        }

        return false;

    }

    /**
     * 忘记密码
     *
     * @param networkId
     */
    public boolean forgetWifi(int networkId) {
        return wifiManager.removeNetwork(networkId);
//        boolean result = wifiManager.removeNetwork(accessPoint.wifiConfiguration.networkId);
//        Toast.makeText(this, result ? "取消保存成功" : "取消保存失败", Toast.LENGTH_LONG).show();
    }


    /**
     * 检测editText是否符合标准
     *
     * @param index
     * @return
     */
    private boolean checkoutEditIpByIndex(int index) {
        XLog.i( "checkoutEditIpByIndex: " + index);
        if (index == IP_ADDRESS) {

            if (TextUtils.isEmpty(edtIpAddress.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtIpAddress.getText().toString());
            }

        }

        if (index == SUBNET_MASK) {

            if (TextUtils.isEmpty(edtSubnetMask.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtSubnetMask.getText().toString());
            }

        }

        if (index == GATEWAY) {

            if (TextUtils.isEmpty(edtGateway.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtGateway.getText().toString());
            }

        }

        if (index == DNS1) {

            if (TextUtils.isEmpty(edtDNS.getText().toString())) {
                return true;
            } else {
                return CommonUtils.isBooleanIp(edtDNS.getText().toString());
            }

        }


        return true;
    }

    /**
     * 设置静态ip
     *
     * @param staticIp
     * @param staticNetMask 子网掩码是由连续的1和连续的0组成
     * @param staticGateWay
     * @param staticDNS1
     * @param staticDNS2
     */
    private void setStaticIp(String staticIp, String staticNetMask, String staticGateWay, String staticDNS1, String staticDNS2) {
        try {
            Inet4Address iPv4Address = getIPv4Address(staticIp);
            int prefixLength = subnetMaskToPrefixLength(staticNetMask);
            InetAddress gatewayAddress = getIPv4Address(staticGateWay);
            InetAddress dnsAddress = getIPv4Address(staticDNS1);

            Class[] cl = new Class[]{InetAddress.class, int.class};
            Constructor cons = null;

            Class<?> clazz = Class.forName("android.net.LinkAddress");

            //取得所有构造函数
            try {
                cons = clazz.getConstructor(cl);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }

            if (cons == null) {
                return;
            }
            //给传入参数赋初值
            Object[] x = {iPv4Address, prefixLength};
            //构造StaticIpConfiguration对象
            Class<?> staticIpConfigurationCls = Class.forName("android.net.StaticIpConfiguration");
            //实例化StaticIpConfiguration
            Object staticIpConfiguration = null;

            staticIpConfiguration = staticIpConfigurationCls.newInstance();
            Field ipAddress = staticIpConfigurationCls.getField("ipAddress");
            Field gateway = staticIpConfigurationCls.getField("gateway");
            Field dnsServers = staticIpConfigurationCls.getField("dnsServers");

            //设置ipAddress
            ipAddress.set(staticIpConfiguration, (LinkAddress) cons.newInstance(x));
            //设置网关
            gateway.set(staticIpConfiguration, gatewayAddress);
            //设置dns
            ArrayList<InetAddress> dnsList = (ArrayList<InetAddress>) dnsServers.get(staticIpConfiguration);
            dnsList.add(dnsAddress);
            if (!staticDNS2.isEmpty()) {
                dnsList.add(getIPv4Address(staticDNS2));
            }


            WifiConfiguration wifiConfig = null;
//            WifiInfo connectionInfo = wifiManager.getConnectionInfo();  //得到连接的wifi网络

            @SuppressLint("MissingPermission")
            List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
            for (WifiConfiguration conf : configuredNetworks) {
                if (conf.networkId == wifiInfo.getNetworkId()) {
                    wifiConfig = conf;
                    break;
                }
            }

            @SuppressLint("PrivateApi") Class ipAssignmentCls = Class.forName("android.net.IpConfiguration$IpAssignment");
            Object ipAssignment = Enum.valueOf(ipAssignmentCls, "STATIC");
            Method setIpAssignmentMethod = wifiConfig.getClass().getDeclaredMethod("setIpAssignment", ipAssignmentCls);
            setIpAssignmentMethod.invoke(wifiConfig, ipAssignment);
            Method setStaticIpConfigurationMethod = wifiConfig.getClass().getDeclaredMethod("setStaticIpConfiguration", staticIpConfiguration.getClass());
            //设置静态IP，将StaticIpConfiguration设置给WifiConfiguration
            setStaticIpConfigurationMethod.invoke(wifiConfig, staticIpConfiguration);
            //WifiConfiguration重新添加到WifiManager
            int netId = wifiManager.addNetwork(wifiConfig);
            wifiManager.disableNetwork(netId);
            boolean flag = wifiManager.enableNetwork(netId, true);
            XLog.i( "setStaticIp: flag : " + flag);
            if (flag) {
                connect(netId);
            }

            CommonUtils.backByWiFiMore(activity);

        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | InvocationTargetException | ClassNotFoundException | NoSuchMethodException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置动态ip
     */
    private void setDynamicIp() {
        try {
            WifiConfiguration wifiConfig = null;
            @SuppressLint("MissingPermission")
            List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
            for (WifiConfiguration conf : configuredNetworks) {
                if (conf.networkId == wifiInfo.getNetworkId()) {
                    wifiConfig = conf;
                    break;
                }
            }
            @SuppressLint("PrivateApi") Class ipAssignmentCls = Class.forName("android.net.IpConfiguration$IpAssignment");
            Object ipAssignment = Enum.valueOf(ipAssignmentCls, "DHCP");
            Method setIpAssignmentMethod = wifiConfig.getClass().getDeclaredMethod("setIpAssignment", ipAssignmentCls);
            setIpAssignmentMethod.invoke(wifiConfig, ipAssignment);
            //WifiConfiguration重新添加到WifiManager
            int netId = wifiManager.addNetwork(wifiConfig);
            wifiManager.disableNetwork(netId);
            boolean flag = wifiManager.enableNetwork(netId, true);
            XLog.i( "setDynamicIp: flag : " + flag);
            if (flag) {
                connect(netId);
            }
//            CommonUtils.backByWiFiMore(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Inet4Address getIPv4Address(String text) {
        try {
            return (Inet4Address) NetworkUtils.numericToInetAddress(text);
        } catch (IllegalArgumentException | ClassCastException e) {
            return null;
        }
    }

    /*
     * convert subMask string to prefix length
     */
    private int maskStr2InetMask(String maskStr) {
        StringBuffer sb;
        String str;
        int inetmask = 0;
        int count = 0;
        /*
         * check the subMask format
         */
        Pattern pattern = Pattern.compile("(^((\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])$)|^(\\d|[1-2]\\d|3[0-2])$");
        if (pattern.matcher(maskStr).matches() == false) {
            Log.e(TAG, "subMask is error");
            return 0;
        }

        String[] ipSegment = maskStr.split("\\.");
        for (int n = 0; n < ipSegment.length; n++) {
            sb = new StringBuffer(Integer.toBinaryString(Integer.parseInt(ipSegment[n])));
            str = sb.reverse().toString();
            count = 0;
            for (int i = 0; i < str.length(); i++) {
                i = str.indexOf("1", i);
                if (i == -1)
                    break;
                count++;
            }
            inetmask += count;
        }
        return inetmask;
    }

    public static int subnetMaskToPrefixLength(String subnetMask) {
        InetAddress inetAddress = null;
        try {
            inetAddress = InetAddress.getByName(subnetMask);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        byte[] bytes = inetAddress.getAddress();
        int prefixLength = 0;
        for (byte b : bytes) {
            for (int i = 7; i >= 0; i--) {
                int bit = (b >> i) & 1;
                if (bit == 1) {
                    prefixLength++;
                } else {
                    break;
                }
            }
        }
        return prefixLength;
    }

    private String getSubnetMask() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        LinkProperties linkProperties = connectivityManager.getLinkProperties(connectivityManager.getActiveNetwork());
        if (linkProperties != null) {
            List<LinkAddress> linkAddressList = linkProperties.getLinkAddresses();
            for (LinkAddress linkAddress : linkAddressList) {
                if (linkAddress.getAddress() instanceof Inet4Address) {
                    return ipv4PrefixLengthToSubnetMask(linkAddress.getPrefixLength());
                }
            }
        }
        return null;
    }

    private String ipv4PrefixLengthToSubnetMask(int i) {
        try {
            return NetworkUtils.getNetworkPart(InetAddress.getByAddress(new byte[]{-1, -1, -1, -1}), i).getHostAddress();
        } catch (UnknownHostException e) {
            return null;
        }
    }
}
