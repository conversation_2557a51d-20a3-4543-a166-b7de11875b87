package com.eeo.systemsetting.view;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.PowerUtil;


/**
 * 重启
 * @deprecated 统一用windows系统还原的dialog风格
 */
public class RestartFrameLayout extends FrameLayout implements View.OnClickListener {
    public static final String TAG = "RestartFrameLayout===";
    View rootView = null;
    Activity activity;

    public RestartFrameLayout(Activity activity, Context context) {
        super(context);
        this.activity = activity;

        if (rootView == null) {
            rootView = LayoutInflater.from(context).inflate(R.layout.activity_restart, null);
        }

        findView();
        addView(rootView);
        Log.i(TAG, "RestartFrameLayout: ");

    }

    private void findView() {
        Button btnShutDown = rootView.findViewById(R.id.btn_restart);
        btnShutDown.setOnClickListener(this);
        Button btnCancel = rootView.findViewById(R.id.btn_cancel);
        btnCancel.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_restart:
                activity.finish();
                PowerUtil.getInstance(getContext()).shutdownOrReboot(true, "");
                break;

            case R.id.btn_cancel:
                Intent intent = new Intent();
                intent.setAction(Constant.ACTION_BACK);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        activity.sendBroadcast(intent);
                    }
                }, 200);
                break;

            default:
                break;
        }
    }
}
