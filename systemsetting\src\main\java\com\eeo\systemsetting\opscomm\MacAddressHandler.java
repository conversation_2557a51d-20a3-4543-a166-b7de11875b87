package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.net.NetworkInterface;
import java.util.Collections;
import java.util.List;

/**
 * MAC地址处理器
 * 负责获取Android设备的WiFi和以太网MAC地址
 * 针对Android 11的MAC地址访问限制进行优化
 */
public class MacAddressHandler {
    private static final String TAG = "MacAddressHandler";
    
    private Context mContext;
    
    // 网络接口名称模式
    private static final String[] WIFI_INTERFACE_PATTERNS = {"wlan", "wl"};
    private static final String[] ETH_INTERFACE_PATTERNS = {"eth", "enp", "ens"};
    
    // 系统网络接口路径
    private static final String NET_CLASS_PATH = "/sys/class/net/";
    
    // 缓存的MAC地址
    private String mCachedWifiMac = null;
    private String mCachedEthMac = null;
    private long mLastCacheTime = 0;
    private static final long CACHE_TIMEOUT = 30000; // 30秒缓存超时

    public MacAddressHandler(Context context) {
        mContext = context;
        Log.d(TAG, "MacAddressHandler initialized");
    }

    /**
     * 获取WiFi MAC地址
     */
    public String getWifiMacAddress() {
        Log.d(TAG, "Getting WiFi MAC address...");
        
        try {
            // 检查缓存
            if (isCacheValid() && mCachedWifiMac != null) {
                Log.d(TAG, "Returning cached WiFi MAC: " + mCachedWifiMac);
                return mCachedWifiMac;
            }
            
            String wifiMac = getWifiMacFromMultipleSources();
            
            if (wifiMac != null && !wifiMac.isEmpty()) {
                mCachedWifiMac = wifiMac;
                mLastCacheTime = System.currentTimeMillis();
                Log.d(TAG, "WiFi MAC address obtained: " + wifiMac);
                return wifiMac;
            } else {
                Log.w(TAG, "Failed to obtain WiFi MAC address");
                return null;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while getting WiFi MAC address", e);
            return null;
        }
    }

    /**
     * 获取以太网MAC地址
     */
    public String getEthMacAddress() {
        Log.d(TAG, "Getting Ethernet MAC address...");
        
        try {
            // 检查缓存
            if (isCacheValid() && mCachedEthMac != null) {
                Log.d(TAG, "Returning cached Ethernet MAC: " + mCachedEthMac);
                return mCachedEthMac;
            }
            
            String ethMac = getEthMacFromMultipleSources();
            
            if (ethMac != null && !ethMac.isEmpty()) {
                mCachedEthMac = ethMac;
                mLastCacheTime = System.currentTimeMillis();
                Log.d(TAG, "Ethernet MAC address obtained: " + ethMac);
                return ethMac;
            } else {
                Log.w(TAG, "Failed to obtain Ethernet MAC address");
                return null;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while getting Ethernet MAC address", e);
            return null;
        }
    }

    /**
     * 检查缓存是否有效
     */
    private boolean isCacheValid() {
        return (System.currentTimeMillis() - mLastCacheTime) < CACHE_TIMEOUT;
    }

    /**
     * 从多个来源获取WiFi MAC地址
     */
    private String getWifiMacFromMultipleSources() {
        Log.d(TAG, "Trying multiple sources for WiFi MAC...");
        
        // 方法1：通过NetworkInterface获取
        String mac = getMacFromNetworkInterface(WIFI_INTERFACE_PATTERNS);
        if (mac != null) {
            Log.d(TAG, "WiFi MAC from NetworkInterface: " + mac);
            return mac;
        }
        
        // 方法2：通过文件系统获取
        mac = getMacFromFileSystem(WIFI_INTERFACE_PATTERNS);
        if (mac != null) {
            Log.d(TAG, "WiFi MAC from FileSystem: " + mac);
            return mac;
        }
        
        // 方法3：通过WifiManager获取（Android 11可能受限）
        mac = getMacFromWifiManager();
        if (mac != null) {
            Log.d(TAG, "WiFi MAC from WifiManager: " + mac);
            return mac;
        }
        
        Log.w(TAG, "All WiFi MAC acquisition methods failed");
        return null;
    }

    /**
     * 从多个来源获取以太网MAC地址
     */
    private String getEthMacFromMultipleSources() {
        Log.d(TAG, "Trying multiple sources for Ethernet MAC...");
        
        // 方法1：通过NetworkInterface获取
        String mac = getMacFromNetworkInterface(ETH_INTERFACE_PATTERNS);
        if (mac != null) {
            Log.d(TAG, "Ethernet MAC from NetworkInterface: " + mac);
            return mac;
        }
        
        // 方法2：通过文件系统获取
        mac = getMacFromFileSystem(ETH_INTERFACE_PATTERNS);
        if (mac != null) {
            Log.d(TAG, "Ethernet MAC from FileSystem: " + mac);
            return mac;
        }
        
        Log.w(TAG, "All Ethernet MAC acquisition methods failed");
        return null;
    }

    /**
     * 通过NetworkInterface获取MAC地址
     */
    private String getMacFromNetworkInterface(String[] patterns) {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            
            for (NetworkInterface networkInterface : interfaces) {
                String interfaceName = networkInterface.getName();
                
                // 检查接口名称是否匹配模式
                if (matchesPatterns(interfaceName, patterns)) {
                    byte[] macBytes = networkInterface.getHardwareAddress();
                    
                    if (macBytes != null && macBytes.length == 6) {
                        String mac = formatMacAddress(macBytes);
                        if (isValidMacAddress(mac)) {
                            Log.d(TAG, "Found MAC for interface " + interfaceName + ": " + mac);
                            return mac;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            Log.w(TAG, "Exception in getMacFromNetworkInterface", e);
        }
        
        return null;
    }

    /**
     * 通过文件系统获取MAC地址
     */
    private String getMacFromFileSystem(String[] patterns) {
        try {
            File netDir = new File(NET_CLASS_PATH);
            if (!netDir.exists() || !netDir.isDirectory()) {
                Log.w(TAG, "Network class directory does not exist: " + NET_CLASS_PATH);
                return null;
            }
            
            File[] interfaces = netDir.listFiles();
            if (interfaces == null) {
                Log.w(TAG, "No network interfaces found in " + NET_CLASS_PATH);
                return null;
            }
            
            for (File interfaceDir : interfaces) {
                String interfaceName = interfaceDir.getName();
                
                // 检查接口名称是否匹配模式
                if (matchesPatterns(interfaceName, patterns)) {
                    File addressFile = new File(interfaceDir, "address");
                    
                    if (addressFile.exists() && addressFile.canRead()) {
                        String mac = readMacFromFile(addressFile);
                        if (isValidMacAddress(mac)) {
                            Log.d(TAG, "Found MAC for interface " + interfaceName + " from file: " + mac);
                            return mac;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            Log.w(TAG, "Exception in getMacFromFileSystem", e);
        }
        
        return null;
    }

    /**
     * 通过WifiManager获取MAC地址（Android 11可能受限）
     */
    private String getMacFromWifiManager() {
        try {
            WifiManager wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
            if (wifiManager == null) {
                Log.w(TAG, "WifiManager is null");
                return null;
            }
            
            // Android 11+可能返回固定的值"02:00:00:00:00:00"
            android.net.wifi.WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            if (wifiInfo != null) {
                String mac = wifiInfo.getMacAddress();
                if (isValidMacAddress(mac) && !"02:00:00:00:00:00".equals(mac)) {
                    return formatMacAddress(mac);
                } else {
                    Log.w(TAG, "WifiManager returned invalid or restricted MAC: " + mac);
                }
            }
            
        } catch (Exception e) {
            Log.w(TAG, "Exception in getMacFromWifiManager", e);
        }
        
        return null;
    }

    /**
     * 从文件读取MAC地址
     */
    private String readMacFromFile(File file) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String line = reader.readLine();
            if (line != null) {
                return line.trim();
            }
        } catch (Exception e) {
            Log.w(TAG, "Exception reading MAC from file: " + file.getPath(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
        return null;
    }

    /**
     * 检查接口名称是否匹配模式
     */
    private boolean matchesPatterns(String interfaceName, String[] patterns) {
        if (interfaceName == null || interfaceName.isEmpty()) {
            return false;
        }
        
        String lowerName = interfaceName.toLowerCase();
        for (String pattern : patterns) {
            if (lowerName.startsWith(pattern.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 验证MAC地址是否有效
     */
    private boolean isValidMacAddress(String mac) {
        if (mac == null || mac.isEmpty()) {
            return false;
        }
        
        // 移除分隔符
        String cleanMac = mac.replace(":", "").replace("-", "").replace(" ", "");
        
        // 检查长度
        if (cleanMac.length() != 12) {
            return false;
        }
        
        // 检查是否为全零MAC
        if ("000000000000".equals(cleanMac)) {
            return false;
        }
        
        // 检查是否为Android 11的限制MAC
        if ("020000000000".equals(cleanMac)) {
            return false;
        }
        
        // 检查是否包含有效的十六进制字符
        try {
            Long.parseLong(cleanMac, 16);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 格式化MAC地址
     */
    private String formatMacAddress(byte[] macBytes) {
        if (macBytes == null || macBytes.length != 6) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < macBytes.length; i++) {
            if (i > 0) {
                sb.append(":");
            }
            sb.append(String.format("%02X", macBytes[i] & 0xFF));
        }
        
        return sb.toString();
    }

    /**
     * 格式化MAC地址字符串
     */
    private String formatMacAddress(String mac) {
        if (mac == null || mac.isEmpty()) {
            return null;
        }
        
        // 移除所有分隔符
        String cleanMac = mac.replace(":", "").replace("-", "").replace(" ", "").toUpperCase();
        
        if (cleanMac.length() != 12) {
            return null;
        }
        
        // 添加冒号分隔符
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < cleanMac.length(); i += 2) {
            if (i > 0) {
                sb.append(":");
            }
            sb.append(cleanMac.substring(i, i + 2));
        }
        
        return sb.toString();
    }

    /**
     * 清除缓存（用于测试或强制刷新）
     */
    public void clearCache() {
        mCachedWifiMac = null;
        mCachedEthMac = null;
        mLastCacheTime = 0;
        Log.d(TAG, "MAC address cache cleared");
    }
}
 