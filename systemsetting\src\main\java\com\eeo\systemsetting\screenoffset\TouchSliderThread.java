package com.eeo.systemsetting.screenoffset;

import static com.eeo.systemsetting.launcher.TifPlayerActivity.MSG_HIDE_SCREEN_OFFSET_UI;
import static com.eeo.systemsetting.launcher.TifPlayerActivity.MSG_SHOW_SCREEN_OFFSET_UI_LEFT;
import static com.eeo.systemsetting.launcher.TifPlayerActivity.MSG_SHOW_SCREEN_OFFSET_UI_RIGHT;

import android.content.Context;
import android.os.Handler;
import android.util.Log;

import com.droidlogic.app.SystemControlManager;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.utils.CommonUtils;

import java.util.ArrayList;

public class TouchSliderThread extends Thread {
    private static final String TAG = "TouchSliderThread";
    private Context mContext;
    private Handler mHandler;
    private SystemControlManager mSystemControl;
    private boolean mIsStopFlag = false;
    private ArrayList<Integer> setData = new ArrayList<Integer>();
    private int leftTouchStatus;
    private int rightTouchStatus;
    private final static int I2C_DEVICES_BUS = 2;
    private final static int TOUCH_LEFT_ADDR = 0x10;
    private final static int TOUCH_RIGHT_ADDR = 0x15;
    private final static int TOUCH_SLIDE_UP = 1; //上滑
    private final static int TOUCH_SLIDE_DOWN = 2;//下滑
    private final static int TOUCH_PRESS_DOWN = 0;//手指触摸按下
    private final static int TOUCH_PRESS_UP = 1;//手指触摸抬起
    private final static int TOUCH_PRESS_SLIDER = 2;//手指触摸滑动中

    private boolean left;

    // 新增：用于标记硬件是否存在的标志位
    private boolean mSlidersHardwarePresent = false;

    public TouchSliderThread(Context context, Handler handler) {
        mContext = context;
        mHandler = handler;
        mSystemControl = SystemControlManager.getInstance();
        setData.add(0);
        leftTouchStatus = TOUCH_PRESS_UP;
        rightTouchStatus = TOUCH_PRESS_UP;
        Log.d(TAG, "init");
        // 调用新的静态方法检查硬件并设置标志位
        mSlidersHardwarePresent = queryHardwarePresence();
    }

    // 新增：公开的静态方法，用于在外部查询硬件是否存在
    public static boolean queryHardwarePresence() {
        final String DEBUG_TAG = TAG + "_QueryHardware";
        SystemControlManager systemControl = SystemControlManager.getInstance();

        if (systemControl == null) {
            Log.e(DEBUG_TAG, "SystemControlManager is null, cannot detect capacitive slider hardware.");
            return false; // 硬件不存在或无法检测
        }

        Log.i(DEBUG_TAG, "Starting hardware detection for capacitive sliders...");
        boolean hardwarePresent = false; // 此静态方法内的局部变量

        try {
            // --- 检测右侧滑条 ---
            Log.d(DEBUG_TAG, "Attempting to read right slider (address: " + String.format("0x%02X", TOUCH_RIGHT_ADDR) + ", register: 0x00)");
            ArrayList<Integer> dataRight = systemControl.getI2cData(I2C_DEVICES_BUS, TOUCH_RIGHT_ADDR, 0x00, 1);
            Log.i(DEBUG_TAG, "Right slider getI2cData returned: " + (dataRight == null ? "null" : dataRight.toString()));

            if (dataRight != null && !dataRight.isEmpty() && !isLikelyErrorResponse(dataRight, "Right")) {
                hardwarePresent = true;
                Log.i(DEBUG_TAG, "[SUCCESS] Detected response from right capacitive slider.");
            } else {
                Log.w(DEBUG_TAG, "No valid response or suspected error data from right slider.");
            }

            if (!hardwarePresent) { // 只有当右侧未成功检测到时，才依赖左侧的检测结果
                Log.d(DEBUG_TAG, "Attempting to read left slider (address: " + String.format("0x%02X", TOUCH_LEFT_ADDR) + ", register: 0x00)");
                ArrayList<Integer> dataLeft = systemControl.getI2cData(I2C_DEVICES_BUS, TOUCH_LEFT_ADDR, 0x00, 1);
                Log.i(DEBUG_TAG, "Left slider getI2cData returned: " + (dataLeft == null ? "null" : dataLeft.toString()));

                if (dataLeft != null && !dataLeft.isEmpty() && !isLikelyErrorResponse(dataLeft, "Left")) {
                    hardwarePresent = true; // 如果左侧成功，则设置
                    Log.i(DEBUG_TAG, "[SUCCESS] Detected response from left capacitive slider.");
                } else {
                    Log.w(DEBUG_TAG, "No valid response or suspected error data from left slider.");
                }
            }

            if (hardwarePresent) {
                Log.i(DEBUG_TAG, "Capacitive slider hardware detection complete. At least one slider responded. hardwarePresent = true");
            } else {
                Log.w(DEBUG_TAG, "[FAILURE] Capacitive slider hardware detection complete. Neither slider responded effectively. hardwarePresent = false");
            }

        } catch (Exception e) {
            Log.e(DEBUG_TAG, "Exception during capacitive slider hardware detection: " + e.getMessage(), e);
            hardwarePresent = false; // 出现异常时，保守地认为硬件不存在或不可用
        }
        return hardwarePresent;
    }

    // 新增：辅助方法，判断返回的 ArrayList 是否可能表示一个错误或无效响应
    // sideIdentifier 参数仅用于日志，指明是哪个滑条的响应
    private static boolean isLikelyErrorResponse(ArrayList<Integer> response, String sideIdentifier) {
        final String HELPER_TAG = TAG + "_IsErrorResp"; // 辅助方法的日志TAG

        // 1. 明确的错误条件: null或空响应
        if (response == null) {
            Log.d(HELPER_TAG, sideIdentifier + " slider: Response is null. [Determined as error/device not present]");
            return true;
        }
        if (response.isEmpty()){
            Log.d(HELPER_TAG, sideIdentifier + " slider: Response is an empty list []. [Determined as error/device not present]");
            return true;
        }

        // 2. 明确的成功条件 (针对本次1字节读寄存器0x01的硬件检测): 响应为 [0]
        // 根据日志，[0] 是设备存在的有效响应
        if (response.size() == 1 && response.get(0) != null && response.get(0) == 0) {
            Log.d(HELPER_TAG, sideIdentifier + " slider: Response is [0]. [Determined as valid, device present]");
            return false; // SUCCESS (Not an error)
        }

        // 3. 其他明确的错误条件 (例如已知的错误码 -1, 255)
        if (response.size() == 1 && response.get(0) != null) {
            Integer val = response.get(0);
            if (val == -1 || val == 255) { // 假设-1和255是已知的错误码
                Log.w(HELPER_TAG, sideIdentifier + " slider: Response is a single known error code: [" + val + "]. [Determined as error]");
                return true;
            }
        }
        
        // 检查是否所有元素都是0xFF (255)
        // (如果size为1且为255，已被上面条件覆盖)
        boolean allFF = true;
        if (response.size() > 0) { // 再次确认非空，尽管前面已检查
            for (Integer val : response) {
                if (val == null || val != 255) {
                    allFF = false;
                    break;
                }
            }
            if (allFF) {
                Log.w(HELPER_TAG, sideIdentifier + " slider: All returned values are 255 (0xFF): " + response.toString() + ". [Determined as error]");
                return true;
            }
        }

        // 4. 未知或不期望的响应: 
        // 如果响应不是上述任何一种情况 (例如，不是null/empty, 也不是明确的成功信号[0], 也不是明确的错误信号[-1]或[255]或全255).
        // 对于硬件检测来说，我们当前只把 [0] 作为唯一的成功信号。
        // 其他任何未明确识别为成功的响应，都应保守地视为检测失败或设备状态不确定。
        Log.w(HELPER_TAG, sideIdentifier + " slider: Received an unrecognized response: " + response.toString() + ". [Determined as error/device state uncertain]");
        return true; // Default to error/failure if not the specific success signal [0] or other known errors.
    }

    @Override
    public void run() {
        final String RUN_TAG = TAG + "_Run"; // 用于 run 方法的日志TAG
        if (!mSlidersHardwarePresent) {
            Log.i(RUN_TAG, "Capacitive slider hardware not detected or did not respond effectively. TouchSliderThread will not run.");
            return;
        }

        Log.i(RUN_TAG, "TouchSliderThread running...");
        while (!mIsStopFlag) {
            // 安全检查，尽管 mSystemControl应在构造函数中初始化
            if (mSystemControl == null) {
                 Log.e(RUN_TAG, "SystemControlManager is null in run loop. Exiting thread.");
                 break; // 退出 while 循环
            }
            // 读取右侧滑条数据
            touchSliderData(mSystemControl.getI2cData(I2C_DEVICES_BUS, TOUCH_RIGHT_ADDR, 0x01, 3), TOUCH_RIGHT_ADDR);
            // 更频繁地检查停止标志
            if (mIsStopFlag) {
                break;
            }

            // 读取左侧滑条数据
            touchSliderData(mSystemControl.getI2cData(I2C_DEVICES_BUS, TOUCH_LEFT_ADDR, 0x01, 3), TOUCH_LEFT_ADDR);
            if (mIsStopFlag) {
                break;
            }

            try {
                sleep(5); // 轮询间隔
            } catch (InterruptedException e) {
                Log.w(RUN_TAG, "TouchSliderThread was interrupted", e);
                Thread.currentThread().interrupt(); // 恢复中断状态
                break; // 如果被中断则退出循环
            }
        }
        Log.i(RUN_TAG, "TouchSliderThread stopped.");
    }

    private void touchSliderData(ArrayList<Integer> data, int addr) {
        if (data == null || data.size() == 0) {
            return;
        }
        int touchStatus = (data.get(2) >> 6);
        if (addr == TOUCH_LEFT_ADDR && leftTouchStatus != touchStatus) {
            leftTouchStatus = touchStatus;
            left = true;
            Log.d(TAG, "leftTouchStatus=" + leftTouchStatus);
        } else if (addr == TOUCH_RIGHT_ADDR && rightTouchStatus != touchStatus) {
            rightTouchStatus = touchStatus;
            left = false;
            Log.d(TAG, "rightTouchStatus=" + rightTouchStatus);
        }
        int gestureStatus = data.get(0);
        if (addr == TOUCH_LEFT_ADDR) {//对接地的滑条数据取反保证滑动方向一致
            switch (gestureStatus) {
                case TOUCH_SLIDE_UP:
                    gestureStatus = TOUCH_SLIDE_DOWN;
                    break;
                case TOUCH_SLIDE_DOWN:
                    gestureStatus = TOUCH_SLIDE_UP;
                    break;
                default:
                    break;
            }
        }
        if (gestureStatus == TOUCH_SLIDE_UP) {
            mSystemControl.setI2cData(I2C_DEVICES_BUS, TOUCH_LEFT_ADDR, 0x01, 1, 1, setData);
            mSystemControl.setI2cData(I2C_DEVICES_BUS, TOUCH_RIGHT_ADDR, 0x01, 1, 1, setData);
            if (ScreenMoveView.getScreenOffset() != ScreenMoveView.SCREEN_ORIGIN) {
                if (EeoApplication.mIsFingerDown) {
                    CommonUtils.setTouchState(mContext, false);
                    EeoApplication.mShouldSetTouchEnable = true;
                    mHandler.sendEmptyMessageDelayed(MSG_HIDE_SCREEN_OFFSET_UI, 50); //加个延迟，避免禁触摸响应比半屏慢
                } else {
                    mHandler.sendEmptyMessage(MSG_HIDE_SCREEN_OFFSET_UI);
                }
            }
        } else if (gestureStatus == TOUCH_SLIDE_DOWN) {
            mSystemControl.setI2cData(I2C_DEVICES_BUS, TOUCH_LEFT_ADDR, 0x01, 1, 1, setData);
            mSystemControl.setI2cData(I2C_DEVICES_BUS, TOUCH_RIGHT_ADDR, 0x01, 1, 1, setData);
            if (ScreenMoveView.getScreenOffset() != ScreenMoveView.SCREEN_YOFFSET) {
                if (EeoApplication.mIsFingerDown) {
                    CommonUtils.setTouchState(mContext, false);
                    EeoApplication.mShouldSetTouchEnable = true;
                    mHandler.sendEmptyMessageDelayed(left ? MSG_SHOW_SCREEN_OFFSET_UI_LEFT : MSG_SHOW_SCREEN_OFFSET_UI_RIGHT, 50);//加个延迟，避免禁触摸响应比半屏慢
                } else {
                    mHandler.sendEmptyMessage(left ? MSG_SHOW_SCREEN_OFFSET_UI_LEFT : MSG_SHOW_SCREEN_OFFSET_UI_RIGHT);
                }
            }
        }
    }

    public void setStop() {
        mIsStopFlag = true;
    }
}