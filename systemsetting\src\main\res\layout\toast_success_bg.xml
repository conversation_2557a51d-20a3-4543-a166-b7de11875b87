<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/toast_width"
    android:layout_height="@dimen/toast_height"
    android:background="@drawable/shape_network_toast_bg"
    android:paddingHorizontal="@dimen/toast_padding_horizontal"
    android:paddingVertical="@dimen/toast_padding_vertical"
    android:gravity="center"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="@dimen/dialog_factory_reset_iv_warn_width"
        android:layout_height="@dimen/dialog_factory_reset_iv_warn_height"
        android:background="@drawable/ic_succeed" />

    <TextView
        android:id="@+id/txt_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/toast_msg_margin_start"
        android:text="@string/toast_success"
        android:textColor="@color/black_100"
        android:textSize="@dimen/toast_msg_text_size" />

</LinearLayout>