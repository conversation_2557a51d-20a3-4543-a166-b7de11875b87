package com.eeo.systemsetting.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.eeo.systemsetting.R;

import java.util.List;


public class CustomSpinnerAdapter extends BaseAdapter {
    public static final String TAG = "CustomSpinnerAdapter";
    private Context context;
    private List<String> dataList;
    private int selectedPosition = -1;

    public CustomSpinnerAdapter(Context context, List<String> dataList) {
        this.context = context;
        this.dataList = dataList;
    }

    @Override
    public int getCount() {
        return dataList.size();
    }

    @Override
    public Object getItem(int position) {
        return dataList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.spinner_item, parent, false);
        }
        TextView textView = convertView.findViewById(R.id.tv_item);
        textView.setText(dataList.get(selectedPosition == -1 ? position : selectedPosition)); //避免点击展开时错显示
        return convertView;
    }

    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        // 填充下拉列表视图中的数据，可以与 getView 中的数据不同
        View view = null;
        DropDownViewHolder viewHolder;
        if (convertView != null) {
            view = convertView;
            viewHolder = (DropDownViewHolder) view.getTag();
        } else {
            view = LayoutInflater.from(context).inflate(R.layout.spinner_dropdown_item, parent, false);
            viewHolder = new DropDownViewHolder(view);
            view.setTag(viewHolder);
        }

        viewHolder.textView.setText(dataList.get(position));
        if (position == selectedPosition) {
            viewHolder.textView.setTextColor(context.getResources().getColor(R.color.black_100));
            viewHolder.imageView.setVisibility(View.VISIBLE);
        } else {
            viewHolder.textView.setTextColor(context.getResources().getColor(R.color.text_black_100));
            viewHolder.imageView.setVisibility(View.INVISIBLE);
        }
        viewHolder.line.setVisibility((position == dataList.size() - 1 ? View.GONE : View.VISIBLE));
        return view;
    }

    public void setSelectedPosition(int position) {
        setSelectedPosition(position, false);
    }

    public void setSelectedPosition(int position, boolean shouldRefresh) {
        selectedPosition = position;
        if (shouldRefresh) {
            //立刻刷新，避免不同步
            notifyDataSetChanged();
        }
    }

    static class DropDownViewHolder extends RecyclerView.ViewHolder {

        TextView textView;
        ImageView imageView;
        View line;

        public DropDownViewHolder(View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.tv_item);
            imageView = itemView.findViewById(R.id.iv_select);
            line = itemView.findViewById(R.id.line);
        }
    }
}
