package com.eeo.ota.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import com.eeo.ota.callback.SubDeviceUpdateCallback;
import com.eeo.ota.dialog.UpdateDialog;
import com.eeo.ota.touch.SubDeviceUpdate;
import com.eeo.ota.util.SharedPreferencesUtil;
import com.eeo.ota.util.Util;

public class SubDeviceUpdateService extends Service {
    public static final String TAG = "SubDeviceUpdateService";
    private SubDeviceUpdate mSubDeviceUpdate;
    private static final String CHANNEL_ID = "eeo_ota_channel_id";

    /**
     * 供其它绑定该service的使用
     */
    private SubDeviceUpdateCallback mSubDeviceUpdateListener = null;

    private final SubDeviceUpdateCallback subDeviceUpdateCallback = new SubDeviceUpdateCallback() {
        @Override
        public void onUpdateSuccess() {
            if (mSubDeviceUpdateListener != null) {
                mSubDeviceUpdateListener.onUpdateSuccess();
            }
        }

        @Override
        public void onUpdateFail(String errMsg) {
            Log.d(TAG, "onUpdateFail: " + errMsg);
            if (mSubDeviceUpdateListener != null) {
                mSubDeviceUpdateListener.onUpdateFail(errMsg);
            }
        }

        @Override
        public void onAllUpdateFinish() {
            if (mSubDeviceUpdateListener != null) {
                mSubDeviceUpdateListener.onAllUpdateFinish();
            }
            Log.e(TAG, "onAllUpdateFinish,stop self.");
            stopSelf();
        }

        @Override
        public void onUpdateProgressChanged(int progress) {
            if (mSubDeviceUpdateListener != null) {
                mSubDeviceUpdateListener.onUpdateProgressChanged(progress);
            }
        }
    };

    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "onBind");
        return new SubDeviceUpdateService.MyBinder();
    }

    public class MyBinder extends Binder {
        public SubDeviceUpdateService getService() {
            return SubDeviceUpdateService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel mChannel = new NotificationChannel(CHANNEL_ID, "OtaChannel", NotificationManager.IMPORTANCE_NONE);
            notificationManager.createNotificationChannel(mChannel);
            Notification notification = new Notification.Builder(this, CHANNEL_ID).build();
            startForeground(1, notification);
        }
    }

    public void updateSubDevice() {
        if (mSubDeviceUpdate == null) {
            mSubDeviceUpdate = new SubDeviceUpdate(this, subDeviceUpdateCallback);
        }
        boolean hasUpdate = mSubDeviceUpdate.update();
        if (!hasUpdate) {
            if (Util.shouldShowUpdateDialog(this)) {
                UpdateDialog.showUpdateSuccessDialog(this, true);
                SharedPreferencesUtil.setShowUpdateDialog(this, false);
            } else {
                Log.e(TAG, "no update,stop self.");
            }
            if (mSubDeviceUpdateListener != null) {
                mSubDeviceUpdateListener.onUpdateFail("no update");
            }
            stopSelf();
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand");
        updateSubDevice();
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
        mSubDeviceUpdate.release();
    }

    public void setSubDeviceUpdateListener(SubDeviceUpdateCallback callback) {
        mSubDeviceUpdateListener = callback;
    }

    /**
     * Get SubDeviceUpdate instance, used for array mic service restart after completion
     * @return SubDeviceUpdate instance or null if not initialized
     */
    public SubDeviceUpdate getSubDeviceUpdate() {
        return mSubDeviceUpdate;
    }
}
