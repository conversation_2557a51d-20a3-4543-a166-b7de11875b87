06-30 10:37:29.121   650   723 D system_server: process[77] device8 mBtnTouch=1
06-30 10:37:29.122   360   360 I android.hardware.power-service.libperfmgr: Power setBoost: INTERACTION duration: 0
06-30 10:37:29.122   360   360 I libperfmgr: Do Powerhint: INTERACTION
06-30 10:37:29.123   360   360 I android.hardware.power-service.libperfmgr: Power setBoost: INTERACTION duration: 0
06-30 10:37:29.123   360   360 I libperfmgr: Do Powerhint: INTERACTION
06-30 10:37:29.124   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:37:29.133  1565  1834 E RemoteLockCount_: remotex app disable
06-30 10:37:29.136  1155  1246 D UdiServer-SDKService: com.seewo.osservice sendMessage {"paramsJson":"{\"isAllowIntercept\":true,\"on\":true}","requestName":"com.seewo.sdk.internal.command.device.CmdSetScreenStatus"}
06-30 10:37:29.138  1155  1246 D UdiServer-App: onRequest: POST v1/system/screen/status
06-30 10:37:29.139  1155  1246 D UdiServer-Transfer: before process request: com.ifpdos.udi.base.UdiRequest@b5b5a6@v1/system/screen/status[6871307f-0a04-4ebc-bca2-b2c42c6b02ae]
06-30 10:37:29.139  1155  1246 D UdiServer-Transfer-BinderHttpd: process POST v1/system/screen/status/set
06-30 10:37:29.140   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:37:29.141  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.141  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: │ on POST : v1/system/screen/status/set
06-30 10:37:29.142  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.143  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.144  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: │ com.seewo.osservice isMute: SetScreenStatusBody(isOn=true, isAllowIntercept=true, isMute=null)
06-30 10:37:29.144  1074  1102 D UdiServiceCore-DefaultSystemServiceStrategy: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.145   331   501 I SystemControl: getPanelPower: panelstatus = 1
06-30 10:37:29.146  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.147  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: │ on POST v1/system/screen/status/set : response(200,) , duration 4
06-30 10:37:29.147  1074  1102 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 10:37:29.149  1155  1246 D UdiServer-Transfer: after process request: com.ifpdos.udi.base.UdiRequest@b5b5a6@v1/system/screen/status[6871307f-0a04-4ebc-bca2-b2c42c6b02ae], response: UdiResponse{OK, }, duration: 10
06-30 10:37:29.152  1155  1246 D UdiServer-SDKService: {"paramsJson":"{\"result\":true}","responseName":"RespBooleanResult","status":"SUCCESS"}
06-30 10:37:29.157   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:37:29.174   342  1902 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 10:37:29.175   650   723 D system_server: process[77] device8 mBtnTouch=0