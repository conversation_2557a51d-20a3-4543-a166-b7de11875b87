<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dialog_update_width"
    android:layout_height="@dimen/dialog_update_height"
    android:layout_gravity="center"
    android:background="@drawable/bg_shortcut">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_update_fail_tv_title_margin_top"
        android:drawableStart="@drawable/ota_ic_failure"
        android:drawablePadding="@dimen/dialog_update_fail_tv_title_drawable_padding"
        android:gravity="center_vertical"
        android:text="@string/update_fail"
        android:textColor="@color/black"
        android:textSize="@dimen/dialog_update_fail_tv_title_text_size" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dialog_update_fail_tv_content_margin_top"
        android:lineSpacingExtra="@dimen/dialog_update_fail_tv_content_line_spacing"
        android:text="@string/update_fail_content"
        android:textColor="@color/text_black_100"
        android:textSize="@dimen/dialog_update_fail_tv_content_text_size" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dialog_update_fail_ll_btn_margin_start"
        android:layout_marginBottom="@dimen/dialog_update_fail_ll_btn_margin_bottom"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Ota_Update_Button"
            android:layout_width="@dimen/dialog_update_fail_btn_width"
            android:layout_height="@dimen/dialog_update_fail_btn_height"
            android:text="@string/cancel_update_fail"
            android:textColor="@color/text_black_100" />

        <Button
            android:id="@+id/btn_reboot"
            style="@style/Ota_Update_Button"
            android:layout_width="@dimen/dialog_update_fail_btn_width"
            android:layout_height="@dimen/dialog_update_fail_btn_height"
            android:layout_marginStart="@dimen/dialog_update_fail_btn_confirm_margin_start"
            android:background="@drawable/bg_btn_confirm"
            android:text="@string/confirm_update_fail"
            android:textColor="@color/white_100"
            android:textSize="@dimen/dialog_update_fail_btn_confirm_text_size" />

    </LinearLayout>

</RelativeLayout>