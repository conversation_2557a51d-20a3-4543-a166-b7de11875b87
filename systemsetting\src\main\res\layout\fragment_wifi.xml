<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbarSize="@dimen/fragment_about_scrollbar_size"
    android:scrollbarThumbVertical="@drawable/shape_network_scrollview">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent">

        <LinearLayout
            android:id="@+id/ll_switch"
            style="@style/NetWork_Linear"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/fragment_extra_wireless_screen_margin_top"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                style="@style/NetWork_TextView"
                android:layout_width="@dimen/fragment_wifi_title_wifi_width"
                android:text="@string/switch_wifi" />

            <Switch
                android:id="@+id/sw_network"
                style="@style/NetWork_Switch"
                android:layout_marginStart="@dimen/fragment_wifi_sw_margin_start"
                android:checked="false"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

        </LinearLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_connected"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/fragment_wifi_rv_connected_margin_top"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/ll_switch" />

        <View
            android:id="@+id/line1"
            style="@style/About_Line"
            android:layout_marginStart="@dimen/fragment_wifi_line1_margin_start"
            android:layout_marginTop="@dimen/fragment_wifi_line1_margin_top"
            android:layout_marginEnd="@dimen/fragment_wifi_line1_margin_end"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rv_connected" />


        <LinearLayout
            android:id="@+id/ll_network"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/fragment_wifi_ll_network_margin_start"
            android:layout_marginTop="@dimen/fragment_wifi_ll_network_margin_top"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line1"
            app:layout_goneMarginTop="@dimen/fragment_wifi_ll_network_gone_margin_top">

            <TextView
                android:id="@+id/txt_other_network"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/wifi_network"
                android:textColor="@color/black_100"
                android:textSize="@dimen/fragment_wifi_tv_other_network_text_size" />

            <ProgressBar
                android:id="@+id/progressbar_check_update"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="@dimen/fragment_wifi_pb_check_update_width"
                android:layout_height="@dimen/fragment_wifi_pb_check_update_height"
                android:layout_marginStart="@dimen/fragment_wifi_pb_check_update_margin_start"
                android:indeterminateTint="@color/press_color"
                android:visibility="invisible" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_wifi_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/fragment_wifi_rv_wifi_list_margin_top"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_network" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.core.widget.NestedScrollView>