<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="384dp"
        android:layout_height="384dp"
        android:background="@drawable/shape_main_bg">


        <ImageView
            android:id="@+id/img_back"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="21dp"
            android:layout_marginTop="11dp"
            android:background="@drawable/ic_arrow_left_w" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="107dp"
            android:layout_marginTop="13dp"
            android:layout_toEndOf="@id/img_back"
            android:text="@string/wifi_more"
            android:textColor="@color/black_100"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/txt_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="18dp"
            android:layout_marginTop="18dp"
            android:layout_marginEnd="18dp"
            android:layout_marginBottom="18dp"
            android:clickable="true"
            android:enabled="true"
            android:text="@string/save"
            android:textColor="@color/text_enable_green"
            android:textSize="9sp" />

        <View
            android:id="@+id/line"
            style="@style/Line"
            android:layout_below="@id/txt_save"
            android:layout_marginTop="14dp" />

        <TextView
            android:id="@+id/txt_disconnect"
            style="@style/WiFi_Text"
            android:drawableStart="@drawable/select_disconnect_network_icon"
            android:drawablePadding="8dp"
            android:layout_below="@id/line"
            android:text="@string/disconnect_network" />

        <TextView
            android:id="@+id/txt_forget"
            style="@style/WiFi_Text"
            android:drawableStart="@drawable/select_forget_network_icon"
            android:drawablePadding="8dp"
            android:layout_below="@id/txt_disconnect"
            android:layout_marginTop="11dp"
            android:text="@string/forget_network" />


        <View
            android:id="@+id/line1"
            style="@style/About_Line"
            android:layout_below="@id/txt_forget"
            android:layout_marginTop="19dp" />


        <TextView
            style="@style/NetWork_TextView"
            android:layout_below="@id/line1"
            android:layout_marginTop="27dp"
            android:text="@string/ip_setting" />

        <TextView
            android:id="@+id/txt_ip_setting"
            style="@style/NetWork_TextView"
            android:layout_width="147dp"
            android:layout_below="@id/line1"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="27dp"
            android:layout_marginEnd="35dp"
            android:gravity="end"
            android:text="@string/auto"
            android:textColor="@color/black_70" />

        <ImageView
            android:id="@+id/img_arrow"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_below="@id/line1"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="21dp"
            android:background="@drawable/ic_arrow_right" />

        <RelativeLayout
            android:id="@+id/rl_manual"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/txt_ip_setting"
            android:layout_marginTop="19dp">

            <TextView
                style="@style/NetWork_TextView"
                android:layout_marginTop="14dp"
                android:text="@string/ip_address" />

            <EditText
                android:id="@+id/edt_ip_address"
                style="@style/Network_EditText"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="21dp"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_ip"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="8dp"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_below="@id/edt_ip_address"
                android:layout_marginTop="19dp"
                android:text="@string/subnet_mask" />

            <EditText
                android:id="@+id/edt_subnet_mask"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_ip_address"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="11dp"
                android:layout_marginEnd="21dp"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_subnet_mask"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_below="@id/edt_ip_address"
                android:layout_marginTop="19dp"
                android:layout_marginEnd="8dp"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_marginTop="19dp"
                android:text="@string/gateway" />

            <EditText
                android:id="@+id/edt_gateway"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="11dp"
                android:layout_marginEnd="21dp"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_gateway"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_below="@id/edt_subnet_mask"
                android:layout_marginTop="19dp"
                android:layout_marginEnd="8dp"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />


            <TextView
                style="@style/NetWork_TextView"
                android:layout_width="47dp"
                android:layout_below="@id/edt_gateway"
                android:layout_marginTop="19dp"
                android:text="@string/DNS" />

            <EditText
                android:id="@+id/edt_DNS"
                style="@style/Network_EditText"
                android:layout_below="@id/edt_gateway"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="11dp"
                android:layout_marginEnd="21dp"
                android:background="@drawable/network_edit_bg"
                android:text="***********" />

            <ImageView
                android:id="@+id/img_DNS"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_below="@id/edt_gateway"
                android:layout_marginTop="19dp"
                android:layout_marginEnd="8dp"
                android:layout_toStartOf="@id/edt_ip_address"
                android:background="@drawable/ic_status_wrong" />
        </RelativeLayout>

    </RelativeLayout>
</FrameLayout>