package com.eeo.systemsetting.popupwindow;

import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.eeo.systemsetting.R;
import com.eeo.systemsetting.utils.CommonUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class AutoManualPopupWindow extends PopupWindow {
    public final String TAG = "AutoManualPopupWindow";
    private int selectItem;
    private final View view;
    @BindView(R.id.ll_auto)
    LinearLayout llAuto;
    @BindView(R.id.img_auto)
    ImageView imgAuto;
    @BindView(R.id.txt_auto)
    TextView txtAuto;
    @BindView(R.id.ll_manual)
    LinearLayout llManual;
    @BindView(R.id.img_manual)
    ImageView imgManual;
    @BindView(R.id.txt_manual)
    TextView txtManual;

    private Context context;

    private final int AUTO = 0;
    private final int MANUAL = 1;

    private final float width = 120;
    private final int marginX = -100;
    private final int marginY = 2;

    public OnClickCallback onClickCallback;

    public void setAutoManualClickCallback(OnClickCallback onClickCallback) {
        this.onClickCallback = onClickCallback;
    }

    public AutoManualPopupWindow(Context context, View showParentView, int selectItem) {
        this.context = context;
        view = LayoutInflater.from(context).inflate(R.layout.dialog_network_auto_manual, null);
        ButterKnife.bind(this, view);
        this.selectItem = selectItem;
        setContentView(view);
        setWidth(CommonUtils.dp2px(context, width));
        setHeight(LinearLayout.LayoutParams.WRAP_CONTENT);
        setTouchable(true);
        setOutsideTouchable(true);

        setBackgroundDrawable(new ColorDrawable(0x00000000));
        showAsDropDown(showParentView, CommonUtils.dp2px(context, marginX), CommonUtils.dp2px(context, marginY));

        initData();


    }

    private void initData() {
        if (selectItem == 0) {
            imgAuto.setVisibility(View.VISIBLE);
            txtAuto.setTextColor(context.getColor(R.color.black_100));
            txtAuto.setTypeface(Typeface.DEFAULT_BOLD);

            imgManual.setVisibility(View.INVISIBLE);
            txtManual.setTextColor(context.getColor(R.color.text_black_100));
        } else {
            imgAuto.setVisibility(View.INVISIBLE);
            txtAuto.setTextColor(context.getColor(R.color.text_black_100));

            imgManual.setVisibility(View.VISIBLE);
            txtManual.setTextColor(context.getColor(R.color.black_100));
            txtManual.setTypeface(Typeface.DEFAULT_BOLD);
        }
    }

    @OnClick(value = {R.id.ll_auto, R.id.ll_manual})
    public void onClick(View view) {
        dismiss();
        switch (view.getId()) {
            case R.id.ll_auto:
                onClickCallback.onClickCallback(AUTO);

                break;

            case R.id.ll_manual:
                onClickCallback.onClickCallback(MANUAL);

                break;

            default:
                break;

        }
    }

    public interface OnClickCallback {
        void onClickCallback(int index);
    }

}
