07-01 09:58:30.723  1565  1586 E RemoteLockCount_: remotex app disable
07-01 09:58:30.728  1130  1214 D UdiServer-SDKService: com.seewo.osservice sendMessage {"paramsJson":"{\"isAllowIntercept\":true,\"on\":true}","requestName":"com.seewo.sdk.internal.command.device.CmdSetScreenStatus"}
07-01 09:58:30.731  1130  1214 D UdiServer-App: onRequest: POST v1/system/screen/status
07-01 09:58:30.731  1130  1214 D UdiServer-Transfer: before process request: com.ifpdos.udi.base.UdiRequest@4a7e23c@v1/system/screen/status[75edd97a-8fca-429f-ba78-92197fdece44]
07-01 09:58:30.731  1130  1214 D UdiServer-Transfer-BinderHttpd: process POST v1/system/screen/status/set
07-01 09:58:30.735  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.737  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: │ on POST : v1/system/screen/status/set
07-01 09:58:30.737  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.739  1042  1605 D UdiServiceCore-DefaultSystemServiceStrategy: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.736   658   658 I android.ui: type=1400 audit(0.0:702): avc: denied { call } for scontext=u:r:system_server:s0 tcontext=u:r:system_control:s0 tclass=binder permissive=1
07-01 09:58:30.739  1042  1605 D UdiServiceCore-DefaultSystemServiceStrategy: │ com.seewo.osservice isMute: SetScreenStatusBody(isOn=true, isAllowIntercept=true, isMute=null)
07-01 09:58:30.739  1042  1605 D UdiServiceCore-DefaultSystemServiceStrategy: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.740   339   616 I SystemControl: getPanelPower: panelstatus = 1
07-01 09:58:30.742  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.743  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: │ on POST v1/system/screen/status/set : response(200,) , duration 5
07-01 09:58:30.743  1042  1605 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
07-01 09:58:30.745  1130  1214 D UdiServer-Transfer: after process request: com.ifpdos.udi.base.UdiRequest@4a7e23c@v1/system/screen/status[75edd97a-8fca-429f-ba78-92197fdece44], response: UdiResponse{OK, }, duration: 14
07-01 09:58:30.746  1130  1214 D UdiServer-SDKService: {"paramsJson":"{\"result\":true}","responseName":"RespBooleanResult","status":"SUCCESS"}
07-01 09:58:30.767   658   730 D system_server: process[77] device8 mBtnTouch=0
07-01 09:58:30.786  2558  2598 I SerialPortManager: === RAW DATA PACKET #3 ===
07-01 09:58:30.787  2558  2598 I SerialPortManager: Timestamp: 1751335110786
07-01 09:58:30.787  2558  2598 I SerialPortManager: Raw bytes received (10 bytes): 7F 08 99 A2 B3 E4 02 FF 01 32
07-01 09:58:30.787  2558  2598 I SerialPortManager: Accumulated raw data: 7F0899A2B3E402FF0132EF7F0899A2B3E402FF0132
07-01 09:58:30.787  2558  2598 I SerialPortManager: Total accumulated length: 42 hex chars (21 bytes)
07-01 09:58:30.787  2558  2598 I SerialPortManager: === END RAW DATA PACKET #3 ===
07-01 09:58:30.787  2558  2598 D SerialPortManager: Received data (10 bytes): 7F 08 99 A2 B3 E4 02 FF 01 32
07-01 09:58:30.793  2558  2598 D ProtocolHandler: Processing received data: 7F 08 99 A2 B3 E4 02 FF 01 32
07-01 09:58:30.793  2558  2598 W ProtocolHandler: Invalid packet: frame tail not found
07-01 09:58:30.795  2558  2598 I SerialPortManager: === RAW DATA PACKET #4 ===
07-01 09:58:30.795  2558  2598 I SerialPortManager: Timestamp: 1751335110795
07-01 09:58:30.795  2558  2598 I SerialPortManager: Raw bytes received (1 bytes): EF
07-01 09:58:30.795  2558  2598 I SerialPortManager: Accumulated raw data: 7F0899A2B3E402FF0132EF7F0899A2B3E402FF0132EF
07-01 09:58:30.795  2558  2598 I SerialPortManager: Total accumulated length: 44 hex chars (22 bytes)
07-01 09:58:30.795  2558  2598 I SerialPortManager: === END RAW DATA PACKET #4 ===
07-01 09:58:30.795  2558  2598 D SerialPortManager: Received data (1 bytes): EF
07-01 09:58:30.796  2558  2598 D ProtocolHandler: Processing received data: EF
07-01 09:58:30.796  2558  2598 W ProtocolHandler: Invalid packet: frame tail not found
07-01 09:58:30.796  2558  2598 W ProtocolHandler: Invalid packet: frame tail not found