plugins {
    id 'com.android.application'
}

android {
    compileSdk 32

    defaultConfig {
        applicationId "com.eeo.annotation"
        minSdk 26
        targetSdk 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            keyAlias 'android'
            keyPassword 'android'
            storeFile file("../app_signature/eeo_t982.jks")
            storePassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }
        debug {
            keyAlias 'android'
            keyPassword 'android'
            storeFile file("../app_signature/eeo_t982.jks")
            storePassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java', 'src/main/aidl']
            resources.srcDirs = ['src/main/java', 'src/main/aidl']
            aidl.srcDirs = ['src/main/aidl']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 自定义apk命名
    applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def fileName = "Annotation.apk"
                    outputFileName = fileName
            }
    }
}

dependencies {

    implementation 'com.google.android.material:material:1.8.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation project(':libudisdk')
    implementation files('../commonlib/binderhttpd-0.0.17.aar')
    implementation files('../commonlib/client-sdk-1.0.25.aar')
    implementation files('../commonlib/droidlogic.jar')
}