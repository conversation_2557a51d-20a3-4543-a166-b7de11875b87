package com.eeo.ota.util;

import android.text.TextUtils;
import android.util.Log;

import com.eeo.ota.bean.VersionInfo;

import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

public class ParseUtil {
    public static final String TAG = "ota-ParseUtil";
    /**
     * 配置文件路径
     */
    public static final String PATH_CONFIG = "system/ota/config.json";

    public static final String KEY_NAME = "name";
    public static final String KEY_VERSION = "version";
    public static final String KEY_MD5 = "md5";
    public static final String KEY_PATH = "path";

    /**
     * [
     * {
     * "name":"2022.05.05.M468.EBG110B-D-A2-P-YIOU-UU.GD405.SUndefined.20P.V4900.0x7F5B.bin",
     * "version":"4900_7F5B",
     * "md5":"30454D15C80A017A4E13019145013AA4",
     * "path":"system/ota/touch_EBG110B/2022.05.05.M468.EBG110B-D-A2-P-YIOU-UU.GD405.SUndefined.20P.V4900.0x7F5B.bin"
     * },
     * {
     * "name":"sub_device1_xxx_v12",
     * "version":"12",
     * "md5":"90704DAA81F7DEDA888222838B7368C1",
     * "path":"sub_device1_xxx/sub_device1_xxx_v12.zip"
     * }
     * ]
     *
     * @param modelName 通过model去区分不同的子设备
     */
    public static VersionInfo getVersionInfo(String modelName) {
        VersionInfo versionInfo = new VersionInfo();
        if (modelName == null) {
            return versionInfo;
        }
        File file = new File(PATH_CONFIG);
        if (!file.exists()) {
            return versionInfo;
        }
        String jsonString = readFile(file);
        Log.d(TAG, "readFile:" + jsonString);
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.optJSONObject(i);
                if (jsonObject != null) {
                    String name = jsonObject.optString(KEY_NAME);
                    if (name != null && name.contains(modelName)) {
                        versionInfo.versionName = jsonObject.optString(KEY_VERSION);
                        versionInfo.md5 = jsonObject.optString(KEY_MD5);
                        versionInfo.filePath = jsonObject.optString(KEY_PATH);
                        return versionInfo;
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "parse json array exception:" + e);
        }
        return versionInfo;
    }

    public static String readFile(File file) {
        FileInputStream fileInputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        String line;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            fileInputStream = new FileInputStream(file);
            inputStreamReader = new InputStreamReader(fileInputStream);
            bufferedReader = new BufferedReader(inputStreamReader);
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            Log.e(TAG, "parse exception: " + e);
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (inputStreamReader != null) {
                    inputStreamReader.close();
                }
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 解析提取更新说明，如：
     * {
     * "file_size": 105343,
     * "fw_type": "mcu",
     * "md5sum": "394c9aecbc5864c9cd5b36a1b771a8a9",
     * "type": "update_firmware",
     * "url": "http://ota-1255858890.cos.ap-guangzhou.myqcloud.com/3507436298_HTBO794JIV_BS65A_A_2_1_18_test?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDBJMBlIfvzplz1pY6SfePw6FCTXOWQ7ao%26q-sign-time%3D1726132910%3B1726219310%26q-key-time%3D1726132910%3B1726219310%26q-header-list%3D%26q-url-param-list%3D%26q-signature%3Da8de858de41842624db1e242e6af8b73d89afb37",
     * "user_defined": "{\"description\":\"这是测试文字，1.一二三四五六七八九十\\n2.一二三四五六七八九十 \\n3.一二三四五六七八九十\\n4.一二三四五六七八九十\"}",
     * "version": "BS65A_A_2_1_18_test"
     * }
     */
    public static String[] parseDescription(MqttMessage message) {
        if (message == null) {
            return null;
        }
        String[] description = new String[2];
        try {
            byte[] payload = message.getPayload();
            JSONObject jsonObject = new JSONObject(new String(payload));
            String type = jsonObject.optString("type");
            if (type.equalsIgnoreCase("update_firmware")) {
                String userDefined = jsonObject.getString("user_defined");
                if (!TextUtils.isEmpty(userDefined)) {
                    JSONObject userDefinedObject = new JSONObject(userDefined);
                    description[0] = userDefinedObject.optString("description");
                    description[1] = userDefinedObject.optString("description_en");
                    return description;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "parseDescription: " + e);
        }
        return null;
    }
}
