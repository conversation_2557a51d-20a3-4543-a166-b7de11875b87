package com.eeo.systemsetting.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.eeo.udisdk.network.EthernetConfig;

public class SaveDateUtils {
    private static final String TAG = "SaveDateUtils";
    private static final String EEO_IS_ENTER_OTHER_PAGE = "com.eeo.is.enter.other.page";

    //判断是否超过24小时，如果超过24小时，更新弹窗出现
    private static final String EEO_TIME = "com.eeo.time";

    /**
     * 护眼模式相关
     */
    private static final String SP_EYE_CARE = "eye_care";
    private static final String EEO_PROTECT_EYE = "com.eeo.protect.eye";
    private static final String KEY_EYE_CARE_ENABLE = "eye_care_enable";
    private static final String KEY_COLOR_TEMPERATURE_RED_GAIN = "color_temperature_red_gain";
    private static final String KEY_COLOR_TEMPERATURE_GREEN_GAIN = "color_temperature_green_gain";
    private static final String KEY_COLOR_TEMPERATURE_BLUE_GAIN = "color_temperature_blue_gain";

    //设备是否被激活
    private static final String EEO_DEVICE_ACTIVATION = "com.eeo.device.activation";

    //无线投屏
    private static final String EEO_WIRELESS_SCREEN = "com.eeo.wireless.screen";
    private static final String EEO_WIRELESS_SCREEN_PIN_CODE = "com.eeo.wireless.screen.pin_code";

    //落笔直接写
    private static final String EEO_WRITE_WITHOUT_SCREEN_ON = "com.eeo.write.without.screen.on";

    //呼吸灯是否常亮
    private static final String EEO_BREATH_LED_ON = "com.eeo.breath.led.on";

    //触摸滑条
    private static final String EEO_TOUCH_SLIDER = "com.eeo.touch.slider";

    /**
     * 开机通道相关
     * 系统实际开机通道用的是persist.ctm.force.source和persist.ctm.tv_source_id
     * 后台启动内置电脑功能，实现方式为开机通道先设PC，后再切到目标通道
     * 这里用sp记录目标通道
     */
    private static final String SP_BOOT_SOURCE = "boot_source";
    private static final String KEY_BOOT_FORCE_SOURCE = "boot_force_source";
    private static final String KEY_AUTO_SWITCH_CHANNEL = "auto_switch_channel";

    /**
     * 网络相关
     */
    private static final String SP_NETWORK = "network";
    private static final String KEY_ETHERNET_ENABLE = "ethernet_enable";

    /**
     * 上一次的网络配置
     * 供手动设置时读到为0.0.0.0时使用
     */
    private static final String KEY_ETHERNET_IP = "ip";
    private static final String KEY_ETHERNET_GATEWAY = "gateway";
    private static final String KEY_ETHERNET_MASK = "mask";
    private static final String KEY_ETHERNET_DNS1 = "dns1";
    private static final String KEY_ETHERNET_DNS2 = "dns2";


    public static void saveIsEnterOtherPage(Context context, Boolean isEnterOtherPage) {
        context.getSharedPreferences(EEO_IS_ENTER_OTHER_PAGE, Context.MODE_PRIVATE).edit().putBoolean(EEO_IS_ENTER_OTHER_PAGE, isEnterOtherPage).apply();
    }

    public static Boolean getIsEnterOtherPage(Context context) {
        return context.getSharedPreferences(EEO_IS_ENTER_OTHER_PAGE, Context.MODE_PRIVATE).getBoolean(EEO_IS_ENTER_OTHER_PAGE, false);
    }

    public static void saveTime(Context context, Long time) {
        context.getSharedPreferences(EEO_TIME, Context.MODE_PRIVATE).edit().putLong(EEO_TIME, time).apply();
    }

    public static Long getTime(Context context) {

        return context.getSharedPreferences(EEO_TIME, Context.MODE_PRIVATE).getLong(EEO_TIME, -1);
    }

    /**
     * 启用护眼模式
     *
     * @param context
     */
    public static void setEyeCareEnable(Context context, boolean enable) {
        context.getSharedPreferences(SP_EYE_CARE, Context.MODE_PRIVATE).edit().putBoolean(KEY_EYE_CARE_ENABLE, enable).apply();
    }

    /**
     * 获取护眼模式
     *
     * @param context
     * @return
     */
    public static Boolean isEyeCareEnable(Context context) {
        return context.getSharedPreferences(SP_EYE_CARE, Context.MODE_PRIVATE).getBoolean(KEY_EYE_CARE_ENABLE, false);
    }

    /**
     * 是否使用过新护眼
     * 旧的使用udi
     *
     * @param context
     * @return
     */
    public static Boolean isNewEyeCare(Context context) {
        return context.getSharedPreferences(SP_EYE_CARE, Context.MODE_PRIVATE).contains(KEY_EYE_CARE_ENABLE);
    }

    /**
     * 保存护眼模式前的色温Rgb增益值
     */
    public static void saveColorTemperatureRgbGain(Context context, int redGain, int greenGain, int blueGain) {
        Log.d(TAG, "saveColorTemperatureRgbGain: redGain=" + redGain + ",greenGain=" + greenGain + ",blueGain=" + blueGain);
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_EYE_CARE, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(KEY_COLOR_TEMPERATURE_RED_GAIN, redGain);
        editor.putInt(KEY_COLOR_TEMPERATURE_GREEN_GAIN, greenGain);
        editor.putInt(KEY_COLOR_TEMPERATURE_BLUE_GAIN, blueGain);
        editor.apply();
    }

    /**
     * 获取护眼模式前的色温Rgb增益值
     */
    public static int[] getColorTemperatureRgbGain(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_EYE_CARE, Context.MODE_PRIVATE);
        int redGain = sharedPreferences.getInt(KEY_COLOR_TEMPERATURE_RED_GAIN, 0);
        int greenGain = sharedPreferences.getInt(KEY_COLOR_TEMPERATURE_GREEN_GAIN, 0);
        int blueGain = sharedPreferences.getInt(KEY_COLOR_TEMPERATURE_BLUE_GAIN, 0);
        Log.d(TAG, "getColorTemperatureRgbGain: redGain=" + redGain + ",greenGain=" + greenGain + ",blueGain=" + blueGain);
        return new int[]{redGain, greenGain, blueGain};
    }

    /**
     * 设置设备激活
     *
     * @param context
     */
    public static void setDeviceActivation(Context context) {
        context.getSharedPreferences(EEO_DEVICE_ACTIVATION, Context.MODE_PRIVATE).edit().putBoolean(EEO_DEVICE_ACTIVATION, true).apply();
    }

    /**
     * 获取设备激活
     *
     * @param context
     * @return
     */
    public static Boolean getDeviceActivation(Context context) {
        return context.getSharedPreferences(EEO_DEVICE_ACTIVATION, Context.MODE_PRIVATE).getBoolean(EEO_DEVICE_ACTIVATION, false);
    }

    /**
     * 设置无线投屏开关
     *
     * @param context
     * @param enabled
     * @deprecated 跨应用改用CommonUtils.isWirelessScreenEnabled
     */
    public static void enableWirelessScreen(Context context, boolean enabled) {
        context.getSharedPreferences(EEO_WIRELESS_SCREEN, Context.MODE_PRIVATE).edit().putBoolean(EEO_WIRELESS_SCREEN, enabled).apply();
    }

    /**
     * 获取无线投屏开关状态
     *
     * @param context
     * @return
     * @deprecated 跨应用改用CommonUtils.isWirelessScreenEnabled
     */
    public static Boolean isWirelessScreenEnabled(Context context) {
        return context.getSharedPreferences(EEO_WIRELESS_SCREEN, Context.MODE_PRIVATE).getBoolean(EEO_WIRELESS_SCREEN, true);
    }

    /**
     * 设置投屏码开关
     *
     * @param context
     * @param enabled
     */
    public static void enableWirelessScreenPinCode(Context context, boolean enabled) {
        context.getSharedPreferences(EEO_WIRELESS_SCREEN_PIN_CODE, Context.MODE_PRIVATE).edit().putBoolean(EEO_WIRELESS_SCREEN_PIN_CODE, enabled).apply();
    }

    /**
     * 获取投屏码开关状态
     *
     * @param context
     * @return
     */
    public static Boolean isWirelessScreenPinCodeEnabled(Context context) {
        return context.getSharedPreferences(EEO_WIRELESS_SCREEN_PIN_CODE, Context.MODE_PRIVATE).getBoolean(EEO_WIRELESS_SCREEN_PIN_CODE, true);
    }

    /**
     * 设置落笔直接写开关
     *
     * @param context
     * @param enabled
     */
    public static void enableWriteWithoutScreenOn(Context context, boolean enabled) {
        context.getSharedPreferences(EEO_WRITE_WITHOUT_SCREEN_ON, Context.MODE_PRIVATE).edit().putBoolean(EEO_WRITE_WITHOUT_SCREEN_ON, enabled).apply();
    }

    /**
     * 获取落笔直接写开关状态
     *
     * @param context
     * @return
     */
    public static Boolean isWriteWithoutScreenOnEnabled(Context context) {
        return context.getSharedPreferences(EEO_WRITE_WITHOUT_SCREEN_ON, Context.MODE_PRIVATE).getBoolean(EEO_WRITE_WITHOUT_SCREEN_ON, false);
    }

    /**
     * 设置呼吸灯常亮状态
     *
     * @param context
     * @param isOn
     */
    public static void setBreathLedOn(Context context, boolean isOn) {
        context.getSharedPreferences(EEO_BREATH_LED_ON, Context.MODE_PRIVATE).edit().putBoolean(EEO_BREATH_LED_ON, isOn).apply();
    }

    /**
     * 获取呼吸灯常亮状态
     *
     * @param context
     * @return
     */
    public static Boolean getBreathLedOn(Context context) {
        return context.getSharedPreferences(EEO_BREATH_LED_ON, Context.MODE_PRIVATE).getBoolean(EEO_BREATH_LED_ON, false);
    }

    /**
     * 设置触摸滑条开关
     *
     * @param context
     * @param enabled
     */
    public static void enableTouchSlider(Context context, boolean enabled) {
        context.getSharedPreferences(EEO_TOUCH_SLIDER, Context.MODE_PRIVATE).edit().putBoolean(EEO_TOUCH_SLIDER, enabled).apply();
    }

    /**
     * 获取触摸滑条开关状态
     *
     * @param context
     * @return
     */
    public static Boolean isTouchSliderEnabled(Context context) {
        return context.getSharedPreferences(EEO_TOUCH_SLIDER, Context.MODE_PRIVATE).getBoolean(EEO_TOUCH_SLIDER, true);
    }

    /**
     * 之前的开机通道
     */
    public static String getBootForceSource(Context context) {
        return context.getSharedPreferences(SP_BOOT_SOURCE, Context.MODE_PRIVATE).getString(KEY_BOOT_FORCE_SOURCE, "");
    }

    public static void setBootForceSource(Context context, String value) {
        context.getSharedPreferences(SP_BOOT_SOURCE, Context.MODE_PRIVATE).edit().putString(KEY_BOOT_FORCE_SOURCE, value).apply();
    }

    /**
     * 通道插入自动跳转
     */
    public static String getAutoSwitchChannel(Context context) {
        return context.getSharedPreferences(SP_BOOT_SOURCE, Context.MODE_PRIVATE).getString(KEY_AUTO_SWITCH_CHANNEL, "");
    }

    public static void setAutoSwitchChannel(Context context, String value) {
        context.getSharedPreferences(SP_BOOT_SOURCE, Context.MODE_PRIVATE).edit().putString(KEY_AUTO_SWITCH_CHANNEL, value).apply();
    }

    /**
     * 有线网络开关是否打开
     */
    public static boolean isEthernetEnable(Context context) {
        return context.getSharedPreferences(SP_NETWORK, Context.MODE_PRIVATE).getBoolean(KEY_ETHERNET_ENABLE, true);
    }

    public static void setEthernetEnable(Context context, boolean value) {
        context.getSharedPreferences(SP_NETWORK, Context.MODE_PRIVATE).edit().putBoolean(KEY_ETHERNET_ENABLE, value).apply();
    }

    public static EthernetConfig getEthernetConfig(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(SP_NETWORK, Context.MODE_PRIVATE);
        String ip = sharedPreferences.getString(KEY_ETHERNET_IP, "***********");
        String gateway = sharedPreferences.getString(KEY_ETHERNET_GATEWAY, "***********");
        String mask = sharedPreferences.getString(KEY_ETHERNET_MASK, "*************");
        String dns1 = sharedPreferences.getString(KEY_ETHERNET_DNS1, "*******");
        String dns2 = sharedPreferences.getString(KEY_ETHERNET_DNS2, "*******");
        return new EthernetConfig(ip, gateway, mask, dns1, dns2);
    }

    public static void setEthernetConfig(Context context, EthernetConfig ethernetConfig) {
        SharedPreferences.Editor editor = context.getSharedPreferences(SP_NETWORK, Context.MODE_PRIVATE).edit();
        editor.putString(KEY_ETHERNET_IP, ethernetConfig.getIp());
        editor.putString(KEY_ETHERNET_GATEWAY, ethernetConfig.getGateway());
        editor.putString(KEY_ETHERNET_MASK, ethernetConfig.getMask());
        editor.putString(KEY_ETHERNET_DNS1, ethernetConfig.getDns1());
        editor.putString(KEY_ETHERNET_DNS2, ethernetConfig.getDns2());
        editor.apply();
    }
}
