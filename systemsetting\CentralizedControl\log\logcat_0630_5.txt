06-30 14:23:53.210  1663  2236 E RemoteLockCount_: remotex app disable
06-30 14:23:53.212  1150  1232 D UdiServer-SDKService: com.seewo.osservice sendMessage {"paramsJson":"{\"isAllowIntercept\":true,\"on\":true}","requestName":"com.seewo.sdk.internal.command.device.CmdSetScreenStatus"}
06-30 14:23:53.214  1150  1232 D UdiServer-App: onRequest: POST v1/system/screen/status
06-30 14:23:53.214  1150  1232 D UdiServer-Transfer: before process request: com.ifpdos.udi.base.UdiRequest@6230602@v1/system/screen/status[c167526d-7222-470c-a128-32f842d059ed]
06-30 14:23:53.214  1150  1232 D UdiServer-Transfer-BinderHttpd: process POST v1/system/screen/status/set
06-30 14:23:53.217  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.218  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: │ on POST : v1/system/screen/status/set
06-30 14:23:53.218  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.220  1078  1115 D UdiServiceCore-DefaultSystemServiceStrategy: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.220  1078  1115 D UdiServiceCore-DefaultSystemServiceStrategy: │ com.seewo.osservice isMute: SetScreenStatusBody(isOn=true, isAllowIntercept=true, isMute=null)
06-30 14:23:53.220  1078  1115 D UdiServiceCore-DefaultSystemServiceStrategy: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.221   330  1149 I SystemControl: getPanelPower: panelstatus = 1
06-30 14:23:53.223  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.223  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: │ on POST v1/system/screen/status/set : response(200,) , duration 4
06-30 14:23:53.223  1078  1115 D UdiServiceCore-Transfer-BinderHttpd: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
06-30 14:23:53.224   343  1484 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 14:23:53.225  1150  1232 D UdiServer-Transfer: after process request: com.ifpdos.udi.base.UdiRequest@6230602@v1/system/screen/status[c167526d-7222-470c-a128-32f842d059ed], response: UdiResponse{OK, }, duration: 11
06-30 14:23:53.227  1150  1232 D UdiServer-SDKService: {"paramsJson":"{\"result\":true}","responseName":"RespBooleanResult","status":"SUCCESS"}
06-30 14:23:53.241   343  1484 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 14:23:53.257   343  1484 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 14:23:53.273   611   710 D system_server: process[77] device8 mBtnTouch=0
06-30 14:23:53.274   343  1484 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 14:23:53.289  3824  3863 I SerialPortManager: === RAW DATA PACKET #3 ===
06-30 14:23:53.289  3824  3863 I SerialPortManager: Timestamp: 1751264633289
06-30 14:23:53.289  3824  3863 I SerialPortManager: Raw bytes received (3 bytes): FF FF 0A
06-30 14:23:53.289  3824  3863 I SerialPortManager: Accumulated raw data: 0A99A2B3E402FFF1FFFF0A
06-30 14:23:53.289  3824  3863 I SerialPortManager: Total accumulated length: 22 hex chars (11 bytes)
06-30 14:23:53.291   343  1484 D hwc_nn  : asyncProcess: UVM_IOC_GET_INFO failed.
06-30 14:23:53.290  3824  3863 I SerialPortManager: === END RAW DATA PACKET #3 ===
06-30 14:23:53.291  3824  3863 D SerialPortManager: Received data (3 bytes): FF FF 0A
06-30 14:23:53.293  3824  3863 D ProtocolHandler: Processing received data: FF FF 0A
06-30 14:23:53.298  3824  3863 I SerialPortManager: === RAW DATA PACKET #4 ===
06-30 14:23:53.298  3824  3863 I SerialPortManager: Timestamp: 1751264633298
06-30 14:23:53.299  3824  3863 I SerialPortManager: Raw bytes received (7 bytes): 99 A2 B3 E4 02 FF F1
06-30 14:23:53.299  3824  3863 I SerialPortManager: Accumulated raw data: 0A99A2B3E402FFF1FFFF0A99A2B3E402FFF1
06-30 14:23:53.299  3824  3863 I SerialPortManager: Total accumulated length: 36 hex chars (18 bytes)
06-30 14:23:53.299  3824  3863 I SerialPortManager: === END RAW DATA PACKET #4 ===
06-30 14:23:53.299  3824  3863 D SerialPortManager: Received data (7 bytes): 99 A2 B3 E4 02 FF F1
06-30 14:23:53.302  3824  3863 D ProtocolHandler: Processing received data: 99 A2 B3 E4 02 FF F1