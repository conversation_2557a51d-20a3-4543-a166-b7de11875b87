package com.eeo.ota.arraymic;

import android.content.Context;
import android.util.Log;

import com.eeo.ota.callback.SubDeviceUpdateCallback;

/**
 * Top-level manager for the Array Microphone update process.
 * Coordinates version checking and initiates the update.
 */
public class ArrayMicUpdate {

    private static final String TAG = "ArrayMicOTA";

    private final Context mContext;
    private final SubDeviceUpdateCallback mCallback;
    private ArrayMicFirmwareUpdater mUpdater;

    public ArrayMicUpdate(Context context, SubDeviceUpdateCallback callback) {
        this.mContext = context.getApplicationContext();
        this.mCallback = callback;
    }

    /**
     * This method is now deprecated as the version check is integrated into the updater's flow.
     * It will now always return true to allow the service to proceed.
     * @deprecated Use the integrated flow in ArrayMicFirmwareUpdater.
     */
    @Deprecated
    public boolean checkUpdate() {
        return true; // Always proceed, check is now done inside the updater.
    }

    /**
     * Starts the update process.
     * @return true if the update process was started, false otherwise.
     */
    public boolean update() {
        Log.d(TAG, "Starting array mic update process via updater...");
        if (mUpdater == null) {
            // Configuration is now parsed inside the updater.
            mUpdater = new ArrayMicFirmwareUpdater(mContext, mCallback);
        }
        mUpdater.startUpdate();
        return true;
    }

    public void release() {
        Log.d(TAG, "Releasing resources.");
        if (mUpdater != null) {
            mUpdater.release();
        }
    }
}
