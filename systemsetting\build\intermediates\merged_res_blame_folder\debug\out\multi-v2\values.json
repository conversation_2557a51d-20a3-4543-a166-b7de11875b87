{"logs": [{"outputFile": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values\\values.xml", "map": [{"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\ota\\build\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "171,173,176,213,221,222,223,225,263,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,1031,1039,1054,1055,1056,1069,1073,1078,1140,1147,1158,1217,1218,1238,1239,1241,1242,1243,1244,2558,3033", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8045,8136,8271,10609,10957,10999,11041,11132,13363,25855,25930,26002,26063,26123,26194,26264,26337,26408,26478,26553,26623,26691,26744,26820,26893,26945,27002,27058,27121,60557,60942,61918,61978,62037,62932,63185,63453,67669,68448,69116,72914,72982,74324,74388,74721,74792,74869,74946,157229,190789", "endLines": "171,173,176,213,221,222,223,225,263,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,1031,1039,1054,1055,1056,1069,1073,1078,1140,1147,1158,1217,1218,1238,1239,1241,1242,1243,1244,2564,3036", "endColumns": "44,44,44,40,41,41,44,47,45,74,71,60,59,70,69,72,70,69,74,69,67,52,75,72,51,56,55,62,59,44,53,59,58,112,59,85,96,60,59,73,67,74,63,190,70,76,76,101,12,12", "endOffsets": "8085,8176,8311,10645,10994,11036,11081,11175,13404,25925,25997,26058,26118,26189,26259,26332,26403,26473,26548,26618,26686,26739,26815,26888,26940,26997,27053,27116,27176,60597,60991,61973,62032,62145,62987,63266,63545,67725,68503,69185,72977,73052,74383,74574,74787,74864,74941,75043,157595,190931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\023b8ab3be5e105d1ca8b1fc8939c68c\\transformed\\jetified-glide-4.9.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "970", "startColumns": "4", "startOffsets": "57006", "endColumns": "57", "endOffsets": "57059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631c254547bfc5df4d5287473bf942d8\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "28,192,193,194,195,424,425,426,1350,2407,2409,2412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "972,9353,9414,9476,9538,22952,23011,23068,81941,148872,148936,149062", "endLines": "28,192,193,194,195,424,425,426,1356,2408,2411,2414", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1019,9409,9471,9533,9597,23006,23063,23117,82350,148931,149057,149185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfcbf161df86e8959e8196b0cb0afd0e\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,39,40,149,150,151,152,153,154,155,156,157,158,159,166,167,168,169,178,179,180,181,182,183,190,191,204,205,206,207,208,209,211,212,227,228,234,235,236,237,238,239,240,241,242,243,244,245,249,250,251,252,253,254,255,256,264,265,267,268,269,270,273,274,275,276,282,283,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,494,495,592,593,594,595,596,597,598,875,876,877,878,879,880,881,882,966,967,968,969,971,975,976,977,989,990,991,992,993,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1197,1324,1325,1326,1327,1328,1329,1337,1338,1342,1346,1357,1362,1368,1375,1379,1383,1388,1392,1396,1400,1404,1408,1412,1418,1422,1428,1432,1438,1442,1447,1451,1454,1458,1464,1468,1474,1478,1484,1487,1491,1495,1499,1503,1507,1508,1509,1510,1513,1516,1519,1522,1526,1527,1528,1529,1530,1533,1535,1537,1539,1544,1545,1549,1555,1559,1560,1562,1573,1574,1578,1584,1588,1589,1590,1594,1621,1625,1626,1630,1658,1827,1853,2022,2048,2079,2087,2093,2107,2129,2134,2139,2149,2158,2167,2171,2178,2186,2193,2194,2203,2206,2209,2213,2217,2221,2224,2225,2230,2235,2245,2250,2257,2263,2264,2267,2271,2276,2278,2280,2283,2286,2288,2292,2295,2302,2305,2308,2312,2314,2318,2320,2322,2324,2328,2336,2344,2356,2362,2371,2374,2385,2388,2389,2394,2395,2565,2634,2704,2705,2715,2724,2746,2748,2752,2755,2758,2761,2764,2767,2770,2773,2777,2780,2783,2786,2790,2793,2797,2930,2931,2932,2933,2934,2935,2936,2937,2938,2939,2940,2941,2942,2943,2944,2945,2946,2947,2948,2949,2950,2952,2954,2955,2956,2957,2958,2959,2960,2961,2963,2964,2966,2967,2969,2971,2972,2974,2975,2976,2977,2978,2979,2981,2982,2983,2984,2985,2997,2999,3001,3003,3004,3005,3006,3007,3008,3009,3010,3011,3012,3013,3014,3015,3017,3018,3019,3020,3021,3022,3023,3025,3029,3050,3051,3052,3053,3054,3055,3059,3060,3061,3095,3097,3099,3101,3103,3105,3106,3107,3108,3110,3112,3114,3115,3116,3117,3118,3119,3120,3121,3122,3123,3124,3125,3128,3129,3130,3131,3133,3135,3136,3138,3139,3141,3143,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3156,3158,3159,3160,3161,3163,3164,3165,3166,3167,3169,3171,3173,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,1474,1519,6613,6654,6709,6768,6830,6894,6964,7025,7100,7176,7253,7684,7769,7851,7927,8364,8441,8519,8625,8731,8810,9238,9295,10017,10091,10166,10231,10297,10357,10464,10536,11224,11291,11587,11646,11705,11764,11823,11882,11936,11990,12043,12097,12151,12205,12439,12513,12592,12665,12739,12810,12882,12954,13409,13466,13587,13660,13734,13808,13988,14060,14133,14203,14505,14565,15976,16045,16114,16184,16258,16334,16398,16475,16551,16628,16693,16762,16839,16914,16983,17051,17128,17194,17255,17352,17417,17486,17585,17656,17715,17773,17830,17889,17953,18024,18096,18168,18240,18312,18379,18447,18515,18574,18637,18701,18791,18882,18942,19008,19075,19141,19211,19275,19328,19395,19456,19523,19636,19694,19757,19822,19887,19962,20035,20107,20156,20217,20278,20339,20401,20465,20529,20593,20658,20721,20781,20842,20908,20967,21027,21089,21160,21220,27581,27667,33980,34070,34157,34245,34327,34410,34500,51107,51159,51217,51262,51328,51392,51449,51506,56801,56858,56906,56955,57064,57234,57281,57330,57936,57968,58032,58094,58154,58402,58476,58546,58624,58678,58748,58833,58881,58927,58988,59051,59117,59181,59252,59315,59380,59444,59505,59566,59618,59691,59765,59834,59909,59983,60057,60198,71667,80048,80126,80216,80304,80400,80490,81072,81161,81408,81689,82355,82640,83033,83510,83732,83954,84230,84457,84687,84917,85147,85377,85604,86023,86249,86674,86904,87332,87551,87834,88042,88173,88400,88826,89051,89478,89699,90124,90244,90520,90821,91145,91436,91750,91887,92018,92123,92365,92532,92736,92944,93215,93327,93439,93544,93661,93875,94021,94161,94247,94595,94683,94929,95347,95596,95678,95776,96368,96468,96720,97144,97399,97493,97582,97819,99843,100085,100187,100440,102596,113037,114553,125092,126620,128377,129003,129423,130484,131749,132005,132241,132788,133282,133887,134085,134665,135229,135604,135722,136260,136417,136613,136886,137142,137312,137453,137517,137882,138249,138925,139189,139527,139880,139974,140160,140466,140728,140853,140980,141219,141430,141549,141742,141919,142374,142555,142677,142936,143049,143236,143338,143445,143574,143849,144357,144853,145730,146024,146594,146743,147475,147647,147731,148067,148159,157600,162846,168235,168297,168875,169459,170775,170888,171117,171277,171429,171600,171766,171935,172102,172265,172508,172678,172851,173022,173296,173495,173700,181681,181765,181861,181957,182055,182155,182257,182359,182461,182563,182665,182765,182861,182973,183102,183225,183356,183487,183585,183699,183793,183933,184067,184163,184275,184375,184491,184587,184699,184799,184939,185075,185239,185369,185527,185677,185818,185962,186097,186209,186359,186487,186615,186751,186883,187013,187143,187255,188153,188299,188443,188581,188647,188737,188813,188917,189007,189109,189217,189325,189425,189505,189597,189695,189805,189857,189935,190041,190133,190237,190347,190469,190632,191438,191518,191618,191708,191818,191908,192149,192243,192349,194349,194449,194561,194675,194791,194907,195001,195115,195227,195329,195449,195571,195653,195757,195877,196003,196101,196195,196283,196395,196511,196633,196745,196920,197036,197122,197214,197326,197450,197517,197643,197711,197839,197983,198111,198180,198275,198390,198503,198602,198711,198822,198933,199034,199139,199239,199369,199460,199583,199677,199789,199875,199979,200075,200163,200281,200385,200489,200615,200703,200811,200911,201001,201111,201195,201297,201381,201435,201499,201605,201691,201801,201885", "endLines": "34,39,40,149,150,151,152,153,154,155,156,157,158,159,166,167,168,169,178,179,180,181,182,183,190,191,204,205,206,207,208,209,211,212,227,228,234,235,236,237,238,239,240,241,242,243,244,245,249,250,251,252,253,254,255,256,264,265,267,268,269,270,273,274,275,276,282,283,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,494,495,592,593,594,595,596,597,598,875,876,877,878,879,880,881,882,966,967,968,969,971,975,976,977,989,990,991,992,993,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1197,1324,1325,1326,1327,1328,1336,1337,1341,1345,1349,1361,1367,1374,1378,1382,1387,1391,1395,1399,1403,1407,1411,1417,1421,1427,1431,1437,1441,1446,1450,1453,1457,1463,1467,1473,1477,1483,1486,1490,1494,1498,1502,1506,1507,1508,1509,1512,1515,1518,1521,1525,1526,1527,1528,1529,1532,1534,1536,1538,1543,1544,1548,1554,1558,1559,1561,1572,1573,1577,1583,1587,1588,1589,1593,1620,1624,1625,1629,1657,1826,1852,2021,2047,2078,2086,2092,2106,2128,2133,2138,2148,2157,2166,2170,2177,2185,2192,2193,2202,2205,2208,2212,2216,2220,2223,2224,2229,2234,2244,2249,2256,2262,2263,2266,2270,2275,2277,2279,2282,2285,2287,2291,2294,2301,2304,2307,2311,2313,2317,2319,2321,2323,2327,2335,2343,2355,2361,2370,2373,2384,2387,2388,2393,2394,2399,2633,2703,2704,2714,2723,2724,2747,2751,2754,2757,2760,2763,2766,2769,2772,2776,2779,2782,2785,2789,2792,2796,2800,2930,2931,2932,2933,2934,2935,2936,2937,2938,2939,2940,2941,2942,2943,2944,2945,2946,2947,2948,2949,2951,2953,2954,2955,2956,2957,2958,2959,2960,2962,2963,2965,2966,2968,2970,2971,2973,2974,2975,2976,2977,2978,2980,2981,2982,2983,2984,2985,2998,3000,3002,3003,3004,3005,3006,3007,3008,3009,3010,3011,3012,3013,3014,3016,3017,3018,3019,3020,3021,3022,3024,3028,3032,3050,3051,3052,3053,3054,3058,3059,3060,3061,3096,3098,3100,3102,3104,3105,3106,3107,3109,3111,3113,3114,3115,3116,3117,3118,3119,3120,3121,3122,3123,3124,3127,3128,3129,3130,3132,3134,3135,3137,3138,3140,3142,3144,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3157,3158,3159,3160,3162,3163,3164,3165,3166,3168,3170,3172,3174,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "1342,1514,1563,6649,6704,6763,6825,6889,6959,7020,7095,7171,7248,7326,7764,7846,7922,7998,8436,8514,8620,8726,8805,8885,9290,9348,10086,10161,10226,10292,10352,10413,10531,10604,11286,11354,11641,11700,11759,11818,11877,11931,11985,12038,12092,12146,12200,12254,12508,12587,12660,12734,12805,12877,12949,13022,13461,13519,13655,13729,13803,13878,14055,14128,14198,14269,14560,14621,16040,16109,16179,16253,16329,16393,16470,16546,16623,16688,16757,16834,16909,16978,17046,17123,17189,17250,17347,17412,17481,17580,17651,17710,17768,17825,17884,17948,18019,18091,18163,18235,18307,18374,18442,18510,18569,18632,18696,18786,18877,18937,19003,19070,19136,19206,19270,19323,19390,19451,19518,19631,19689,19752,19817,19882,19957,20030,20102,20151,20212,20273,20334,20396,20460,20524,20588,20653,20716,20776,20837,20903,20962,21022,21084,21155,21215,21283,27662,27749,34065,34152,34240,34322,34405,34495,34586,51154,51212,51257,51323,51387,51444,51501,51555,56853,56901,56950,57001,57093,57276,57325,57371,57963,58027,58089,58149,58206,58471,58541,58619,58673,58743,58828,58876,58922,58983,59046,59112,59176,59247,59310,59375,59439,59500,59561,59613,59686,59760,59829,59904,59978,60052,60193,60263,71715,80121,80211,80299,80395,80485,81067,81156,81403,81684,81936,82635,83028,83505,83727,83949,84225,84452,84682,84912,85142,85372,85599,86018,86244,86669,86899,87327,87546,87829,88037,88168,88395,88821,89046,89473,89694,90119,90239,90515,90816,91140,91431,91745,91882,92013,92118,92360,92527,92731,92939,93210,93322,93434,93539,93656,93870,94016,94156,94242,94590,94678,94924,95342,95591,95673,95771,96363,96463,96715,97139,97394,97488,97577,97814,99838,100080,100182,100435,102591,113032,114548,125087,126615,128372,128998,129418,130479,131744,132000,132236,132783,133277,133882,134080,134660,135224,135599,135717,136255,136412,136608,136881,137137,137307,137448,137512,137877,138244,138920,139184,139522,139875,139969,140155,140461,140723,140848,140975,141214,141425,141544,141737,141914,142369,142550,142672,142931,143044,143231,143333,143440,143569,143844,144352,144848,145725,146019,146589,146738,147470,147642,147726,148062,148154,148432,162841,168230,168292,168870,169454,169545,170883,171112,171272,171424,171595,171761,171930,172097,172260,172503,172673,172846,173017,173291,173490,173695,174025,181760,181856,181952,182050,182150,182252,182354,182456,182558,182660,182760,182856,182968,183097,183220,183351,183482,183580,183694,183788,183928,184062,184158,184270,184370,184486,184582,184694,184794,184934,185070,185234,185364,185522,185672,185813,185957,186092,186204,186354,186482,186610,186746,186878,187008,187138,187250,187390,188294,188438,188576,188642,188732,188808,188912,189002,189104,189212,189320,189420,189500,189592,189690,189800,189852,189930,190036,190128,190232,190342,190464,190627,190784,191513,191613,191703,191813,191903,192144,192238,192344,192436,194444,194556,194670,194786,194902,194996,195110,195222,195324,195444,195566,195648,195752,195872,195998,196096,196190,196278,196390,196506,196628,196740,196915,197031,197117,197209,197321,197445,197512,197638,197706,197834,197978,198106,198175,198270,198385,198498,198597,198706,198817,198928,199029,199134,199234,199364,199455,199578,199672,199784,199870,199974,200070,200158,200276,200380,200484,200610,200698,200806,200906,200996,201106,201190,201292,201376,201430,201494,201600,201686,201796,201880,202000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b71206ba2da564120cdfd0a6235d6d6f\\transformed\\jetified-cardview-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "308,309,310,311,928,929,930,2400,3203,3205,3208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15715,15779,15844,15909,54499,54561,54621,148437,202732,202800,202933", "endLines": "308,309,310,311,928,929,930,2406,3204,3207,3210", "endColumns": "63,64,64,66,61,59,56,12,12,12,12", "endOffsets": "15774,15839,15904,15971,54556,54616,54673,148867,202795,202928,203063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\27f02ae307087a092deaed406cbb04d7\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "499,500,501,603,604,605,972", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27861,27920,27968,34819,34894,34970,57098", "endColumns": "58,47,55,74,75,71,65", "endOffsets": "27915,27963,28019,34889,34965,35037,57159"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "160,161,162,163,170,172,174,175,177,184,185,186,187,188,189,210,214,215,216,217,218,219,220,224,226,229,230,231,232,233,248,257,258,259,260,261,262,266,271,272,277,278,279,280,281,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7331,7374,7426,7476,8003,8090,8181,8226,8316,8890,8947,9004,9063,9122,9180,10418,10650,10694,10739,10781,10825,10869,10913,11086,11180,11359,11401,11443,11487,11533,12391,13027,13087,13158,13222,13269,13316,13524,13883,13932,14274,14319,14364,14408,14459,14626,14677,14729,14771,14815,14860,14906,14951,14996,15041,15086,15131,15176,15221,15265,15310,15355,15400,15445,15490,15535,15580,15625,15670", "endColumns": "42,51,49,47,41,45,44,44,47,56,56,58,58,57,57,45,43,44,41,43,43,43,43,45,43,41,41,43,45,53,47,59,70,63,46,46,46,62,48,55,44,44,43,50,45,50,51,41,43,44,45,44,44,44,44,44,44,44,43,44,44,44,44,44,44,44,44,44,44", "endOffsets": "7369,7421,7471,7519,8040,8131,8221,8266,8359,8942,8999,9058,9117,9175,9233,10459,10689,10734,10776,10820,10864,10908,10952,11127,11219,11396,11438,11482,11528,11582,12434,13082,13153,13217,13264,13311,13358,13582,13927,13983,14314,14359,14403,14454,14500,14672,14724,14766,14810,14855,14901,14946,14991,15036,15081,15126,15171,15216,15260,15305,15350,15395,15440,15485,15530,15575,15620,15665,15710"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,162,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,742,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10849,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,45,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,781,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10890,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,13,995,996,997,1025,1026,1027,1028,1029,1030,1032,1033,1034,1035,1036,1037,1038,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1070,1071,1072,1074,1075,1076,1077,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1141,1142,1143,1144,1145,1146,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1215,1216,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1240,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,424,58281,58324,58363,60268,60308,60351,60404,60464,60506,60602,60640,60692,60740,60788,60849,60891,60996,61055,61168,61216,61281,61327,61391,61472,61528,61600,61688,61744,61793,61879,62150,62217,62286,62359,62432,62503,62597,62647,62691,62743,62796,62867,62992,63087,63141,63271,63310,63351,63405,63550,63599,63662,63708,63752,63818,63873,63938,63995,64062,64123,64178,64241,64304,64370,64429,64486,64544,64699,64738,64856,64894,64943,65004,65093,65166,65313,65363,65485,65527,65577,65615,65672,65726,65797,65841,65882,65930,65988,66040,66082,66128,66173,66285,66376,66440,66508,66622,66693,66763,66837,66916,66978,67024,67112,67203,67290,67350,67417,67482,67584,67730,67903,68076,68122,68250,68337,68508,68577,68623,68683,68747,68825,68874,68934,69019,69067,69190,69328,69385,69429,69523,69590,69652,69694,69734,69786,69870,69924,70009,70059,70142,70186,70234,70292,70330,70386,70459,70531,70610,70662,70765,70830,70890,70946,71000,71070,71150,71250,71300,71355,71428,71489,71555,71607,71720,71776,71832,71880,71930,71975,72022,72117,72178,72248,72392,72452,72517,72557,72609,72666,72796,72852,73057,73109,73155,73241,73299,73364,73427,73487,73565,73625,73670,73734,73794,73859,73915,74034,74158,74200,74257,74579,75048,75087,75134,75259,75334,75386,75435,75541,75625,75680,75780,75865,75909,75969,76033,76103,76165,76228,76315,76456,76533,76615,76683,76749,76793", "endLines": "5,12,18,995,996,997,1025,1026,1027,1028,1029,1030,1032,1033,1034,1035,1036,1037,1038,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1070,1071,1072,1074,1075,1076,1077,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1141,1142,1143,1144,1145,1146,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1215,1216,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1240,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269", "endColumns": "19,19,19,42,38,38,39,42,52,59,41,50,37,51,47,47,60,41,50,58,112,47,64,45,63,80,55,71,87,55,48,85,38,66,68,72,72,70,93,49,43,51,52,70,64,94,53,43,38,40,53,47,48,62,45,43,65,54,64,56,66,60,54,62,62,65,58,56,57,154,38,117,37,48,60,88,72,146,49,121,41,49,37,56,53,70,43,40,47,57,51,41,45,44,111,90,63,67,113,70,69,73,78,61,45,87,90,86,59,66,64,101,84,172,172,45,127,86,110,68,45,59,63,77,48,59,84,47,48,137,56,43,93,66,61,41,39,51,83,53,84,49,82,43,47,57,37,55,72,71,78,51,102,64,59,55,53,69,79,99,49,54,72,60,65,51,59,55,55,47,49,44,46,94,60,69,143,59,64,39,51,56,58,55,61,51,45,85,57,64,62,59,77,59,44,63,59,64,55,118,123,41,56,66,141,38,46,124,74,51,48,105,83,54,99,84,43,59,63,69,61,62,86,140,76,81,67,65,43,75", "endOffsets": "211,419,615,58319,58358,58397,60303,60346,60399,60459,60501,60552,60635,60687,60735,60783,60844,60886,60937,61050,61163,61211,61276,61322,61386,61467,61523,61595,61683,61739,61788,61874,61913,62212,62281,62354,62427,62498,62592,62642,62686,62738,62791,62862,62927,63082,63136,63180,63305,63346,63400,63448,63594,63657,63703,63747,63813,63868,63933,63990,64057,64118,64173,64236,64299,64365,64424,64481,64539,64694,64733,64851,64889,64938,64999,65088,65161,65308,65358,65480,65522,65572,65610,65667,65721,65792,65836,65877,65925,65983,66035,66077,66123,66168,66280,66371,66435,66503,66617,66688,66758,66832,66911,66973,67019,67107,67198,67285,67345,67412,67477,67579,67664,67898,68071,68117,68245,68332,68443,68572,68618,68678,68742,68820,68869,68929,69014,69062,69111,69323,69380,69424,69518,69585,69647,69689,69729,69781,69865,69919,70004,70054,70137,70181,70229,70287,70325,70381,70454,70526,70605,70657,70760,70825,70885,70941,70995,71065,71145,71245,71295,71350,71423,71484,71550,71602,71662,71771,71827,71875,71925,71970,72017,72112,72173,72243,72387,72447,72512,72552,72604,72661,72720,72847,72909,73104,73150,73236,73294,73359,73422,73482,73560,73620,73665,73729,73789,73854,73910,74029,74153,74195,74252,74319,74716,75082,75129,75254,75329,75381,75430,75536,75620,75675,75775,75860,75904,75964,76028,76098,76160,76223,76310,76451,76528,76610,76678,76744,76788,76864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ad7bae491904079428e9176eb58a127d\\transformed\\core-1.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "164,165,246,247,427,428,429,430,431,432,433,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,973,974,978,979,980,981,982,983,984,985,986,987,988,994,1214,2986,2987,2991,2992,2996,3190,3191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7524,7596,12259,12328,23122,23192,23260,23332,23402,23463,23537,40462,40523,40584,40646,40710,40772,40833,40901,41001,41061,41127,41200,41269,41326,41378,54678,54750,54826,54891,54950,55009,55069,55129,55189,55249,55309,55369,55429,55489,55549,55609,55668,55728,55788,55848,55908,55968,56028,56088,56148,56208,56268,56327,56387,56447,56506,56565,56624,56683,56742,57164,57199,57376,57431,57494,57549,57607,57664,57714,57775,57832,57866,57901,58211,72725,187395,187512,187713,187823,188024,202005,202077", "endLines": "164,165,246,247,427,428,429,430,431,432,433,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,973,974,978,979,980,981,982,983,984,985,986,987,988,994,1214,2986,2990,2991,2995,2996,3190,3191", "endColumns": "71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "7591,7679,12323,12386,23187,23255,23327,23397,23458,23532,23605,40518,40579,40641,40705,40767,40828,40896,40996,41056,41122,41195,41264,41321,41373,41435,54745,54821,54886,54945,55004,55064,55124,55184,55244,55304,55364,55424,55484,55544,55604,55663,55723,55783,55843,55903,55963,56023,56083,56143,56203,56263,56322,56382,56442,56501,56560,56619,56678,56737,56796,57194,57229,57426,57489,57544,57602,57659,57709,57770,57827,57861,57896,57931,58276,72791,187507,187708,187818,188019,188148,202072,202139"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\style.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,29,38,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,68,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,362", "startColumns": "-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,1402,1846,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,3329,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19030", "endLines": "-1,-1,-1,-1,-1,-1,35,46,26,-1,-1,-1,-1,-1,-1,-1,-1,-1,74,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,370", "endColumns": "-1,-1,-1,-1,-1,-1,12,12,12,-1,-1,-1,-1,-1,-1,-1,-1,-1,12,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,12", "endOffsets": "-1,-1,-1,-1,-1,-1,1803,2361,1359,-1,-1,-1,-1,-1,-1,-1,-1,-1,3723,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19568"}, "to": {"startLines": "1270,1277,1286,1293,1305,1315,2415,2422,2431,2455,2464,2472,2478,2484,2492,2497,2502,2507,2514,2521,2528,2534,2541,2548,2725,2730,2739,2801,2814,2831,2837,2850,2857,2867,2877,2883,2892,2900,2906,2913,2921,3062,3069,3080,3086,3195,3211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "76869,77309,77877,78308,78993,79543,149190,149590,150102,151384,151914,152384,152738,153071,153527,153765,154014,154275,154702,155095,155477,155706,156090,156526,169550,169827,170393,174030,174779,175752,176100,176823,177222,177915,178610,178974,179454,179948,180283,180686,181148,192441,192810,193493,193818,202289,203068", "endLines": "1276,1285,1292,1304,1314,1323,2421,2430,2454,2463,2471,2477,2483,2491,2496,2501,2506,2513,2520,2527,2533,2540,2547,2557,2729,2738,2745,2813,2830,2836,2849,2856,2866,2876,2882,2891,2899,2905,2912,2920,2929,3068,3079,3085,3094,3202,3219", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "77304,77872,78303,78988,79538,80043,149585,150097,151379,151909,152379,152733,153066,153522,153760,154009,154270,154697,155090,155472,155701,156085,156521,157224,169822,170388,170770,174774,175747,176095,176818,177217,177910,178605,178969,179449,179943,180278,180681,181143,181676,192805,193488,193813,194344,202727,203598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25062f21db2e09c0f5c1d6cca5fceda8\\transformed\\constraintlayout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,29,30,31,32,35,41,42,43,44,47,48,51,54,55,56,57,58,61,64,65,66,67,72,75,78,79,80,85,86,87,90,93,94,97,100,103,106,107,110,113,114,119,120,125,128,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "620,681,1024,1072,1124,1185,1347,1568,1629,1689,1759,1892,1960,2089,2215,2277,2342,2410,2477,2600,2725,2792,2857,2922,3103,3224,3345,3411,3478,3688,3757,3823,3948,4074,4141,4267,4394,4519,4646,4711,4837,4960,5025,5233,5300,5480,5600,5720,5785,5847,5909,5971,6030,6090,6151,6212,6271", "endLines": "19,27,29,30,31,32,38,41,42,43,46,47,50,53,54,55,56,57,60,63,64,65,66,71,74,77,78,79,84,85,86,89,92,93,96,99,102,105,106,109,112,113,118,119,124,127,130,131,132,133,134,135,136,137,138,139,148", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11", "endOffsets": "676,967,1067,1119,1180,1226,1469,1624,1684,1754,1887,1955,2084,2210,2272,2337,2405,2472,2595,2720,2787,2852,2917,3098,3219,3340,3406,3473,3683,3752,3818,3943,4069,4136,4262,4389,4514,4641,4706,4832,4955,5020,5228,5295,5475,5595,5715,5780,5842,5904,5966,6025,6085,6146,6207,6266,6608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e57b8fb71f4a475f9f21d587a549e7c\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "33,3192", "startColumns": "4,4", "startOffsets": "1231,202144", "endLines": "33,3194", "endColumns": "60,12", "endOffsets": "1287,202284"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3037", "startColumns": "4", "startOffsets": "190936", "endLines": "3049", "endColumns": "12", "endOffsets": "191433"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\libtouchsdk\\build\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "196,197,198,199,200,421,422,423", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9602,9648,9695,9746,9798,22824,22866,22911", "endColumns": "45,46,50,51,56,41,44,40", "endOffsets": "9643,9690,9741,9793,9850,22861,22906,22947"}}, {"source": "D:\\ClassIn_Board_S_Pro\\code\\t982-app-for-2.0\\systemsetting\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,420,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,53,54,56,-1,-1,-1,-1,58,57,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24107,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2762,2652,2708,2819,-1,-1,-1,-1,2925,2872,2980,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,76,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,54,52,51,-1,-1,-1,-1,53,51,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2646,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24179,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2813,2702,2756,2866,-1,-1,-1,-1,2974,2919,3030,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,487,488,489,490,491,492,493,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,599,600,601,602,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21288,21334,21387,21438,21486,21531,21574,21629,21676,21727,21780,21830,21876,21923,21973,22029,22083,22132,22187,22242,22293,22351,22401,22452,22506,22551,22600,22644,22695,22742,22784,23610,23684,23755,23822,23888,23958,24027,24102,24176,24244,24311,24369,24436,24501,24558,24634,24705,24764,24822,24881,24946,25011,25081,25149,25223,25290,25365,25441,25514,25584,25649,25715,25791,27181,27233,27294,27354,27410,27471,27529,28024,28082,28143,28204,28273,28348,28407,28471,28533,28593,28659,28719,28774,28833,28898,28961,29019,29097,29163,29225,29281,29341,29396,29458,29516,29589,29659,29729,29791,29857,29927,30000,30071,30139,30202,30275,30339,30397,30460,30522,30582,30640,30704,30765,30832,30892,30957,31024,31091,31149,31214,31271,31328,31395,31455,31524,31593,31663,31731,31811,31887,31950,32024,32094,32164,32235,32306,32381,32451,32525,32597,32673,32753,32833,32909,32978,33045,33106,33169,33231,33303,33372,33439,33507,33580,33647,33716,33785,33847,33909,34591,34646,34706,34764,35042,35098,35163,35227,35302,35362,35410,35473,35529,35589,35644,35700,35762,35829,35901,35972,36039,36101,36163,36229,36302,36363,36430,36493,36555,36622,36688,36755,36828,36901,36960,37027,37095,37160,37227,37292,37355,37415,37474,37530,37589,37650,37715,37761,37813,37863,37908,37956,38010,38062,38109,38155,38203,38255,38302,38352,38408,38463,38512,38563,38625,38691,38753,38819,38889,38954,39017,39079,39153,39226,39286,39348,39408,39471,39538,39601,39671,39739,39806,39872,39920,39979,40030,40076,40123,40170,40216,40260,40310,40363,40419,41440,41487,41537,41583,41635,41680,41724,41781,41833,41888,41943,41987,42037,42086,42135,42186,42237,42282,42354,42421,42472,42520,42574,42626,42678,42730,42784,42832,42883,42945,43013,43085,43157,43217,43283,43347,43406,43466,43529,43593,43652,43714,43780,43841,43907,43973,44034,44110,44188,44263,44331,44403,44475,44553,44635,44721,44807,44888,44949,45014,45078,45141,45203,45277,45338,45403,45464,45524,45591,45657,45719,45786,45853,45918,45983,46045,46111,46171,46229,46285,46344,46407,46466,46531,46594,46656,46714,46773,46830,46889,46948,47005,47059,47114,47172,47220,47275,47325,47373,47432,47478,47523,47572,47627,47680,47737,47794,47848,47898,47947,48001,48053,48106,48162,48218,48273,48321,48375,48424,48474,48524,48583,48641,48702,48766,48826,48895,48955,49037,49111,49173,49250,49310,49371,49436,49501,49568,49629,49694,49765,49833,49877,49930,49981,50037,50091,50152,50207,50268,50329,50383,50445,50514,50582,50644,50692,50743,50800,50850,50905,50961,51015,51063,51560,51616,51671,51724,51776,51828,51877,51932,51983,52037,52089,52144,52201,52249,52301,52351,52398,52452,52505,52554,52608,52667,52718,52791,52868,52949,53031,53114,53193,53270,53342,53421,53497,53568,53647,53724,53800,53875,53948,54033,54118,54207,54293,54378,54439", "endColumns": "45,52,50,47,44,42,54,46,50,52,49,45,46,49,55,53,48,54,54,50,57,49,50,53,44,48,43,50,46,41,39,73,70,66,65,69,68,74,73,67,66,57,66,64,56,75,70,58,57,58,64,64,69,67,73,66,74,75,72,69,64,65,75,63,51,60,59,55,60,57,51,57,60,60,68,74,58,63,61,59,65,59,54,58,64,62,57,77,65,61,55,59,54,61,57,72,69,69,61,65,69,72,70,67,62,72,63,57,62,61,59,57,63,60,66,59,64,66,66,57,64,56,56,66,59,68,68,69,67,79,75,62,73,69,69,70,70,74,69,73,71,75,79,79,75,68,66,60,62,61,71,68,66,67,72,66,68,68,61,61,70,54,59,57,54,55,64,63,74,59,47,62,55,59,54,55,61,66,71,70,66,61,61,65,72,60,66,62,61,66,65,66,72,72,58,66,67,64,66,64,62,59,58,55,58,60,64,45,51,49,44,47,53,51,46,45,47,51,46,49,55,54,48,50,61,65,61,65,69,64,62,61,73,72,59,61,59,62,66,62,69,67,66,65,47,58,50,45,46,46,45,43,49,52,55,42,46,49,45,51,44,43,56,51,54,54,43,49,48,48,50,50,44,71,66,50,47,53,51,51,51,53,47,50,61,67,71,71,59,65,63,58,59,62,63,58,61,65,60,65,65,60,75,77,74,67,71,71,77,81,85,85,80,60,64,63,62,61,73,60,64,60,59,66,65,61,66,66,64,64,61,65,59,57,55,58,62,58,64,62,61,57,58,56,58,58,56,53,54,57,47,54,49,47,58,45,44,48,54,52,56,56,53,49,48,53,51,52,55,55,54,47,53,48,49,49,58,57,60,63,59,68,59,81,73,61,76,59,60,64,64,66,60,64,70,67,43,52,50,55,53,60,54,60,60,53,61,68,67,61,47,50,56,49,54,55,53,47,43,55,54,52,51,51,48,54,50,53,51,54,56,47,51,49,46,53,52,48,53,58,50,72,76,80,81,82,78,76,71,78,75,70,78,76,75,74,72,84,84,88,85,84,60,59", "endOffsets": "21329,21382,21433,21481,21526,21569,21624,21671,21722,21775,21825,21871,21918,21968,22024,22078,22127,22182,22237,22288,22346,22396,22447,22501,22546,22595,22639,22690,22737,22779,22819,23679,23750,23817,23883,23953,24022,24097,24171,24239,24306,24364,24431,24496,24553,24629,24700,24759,24817,24876,24941,25006,25076,25144,25218,25285,25360,25436,25509,25579,25644,25710,25786,25850,27228,27289,27349,27405,27466,27524,27576,28077,28138,28199,28268,28343,28402,28466,28528,28588,28654,28714,28769,28828,28893,28956,29014,29092,29158,29220,29276,29336,29391,29453,29511,29584,29654,29724,29786,29852,29922,29995,30066,30134,30197,30270,30334,30392,30455,30517,30577,30635,30699,30760,30827,30887,30952,31019,31086,31144,31209,31266,31323,31390,31450,31519,31588,31658,31726,31806,31882,31945,32019,32089,32159,32230,32301,32376,32446,32520,32592,32668,32748,32828,32904,32973,33040,33101,33164,33226,33298,33367,33434,33502,33575,33642,33711,33780,33842,33904,33975,34641,34701,34759,34814,35093,35158,35222,35297,35357,35405,35468,35524,35584,35639,35695,35757,35824,35896,35967,36034,36096,36158,36224,36297,36358,36425,36488,36550,36617,36683,36750,36823,36896,36955,37022,37090,37155,37222,37287,37350,37410,37469,37525,37584,37645,37710,37756,37808,37858,37903,37951,38005,38057,38104,38150,38198,38250,38297,38347,38403,38458,38507,38558,38620,38686,38748,38814,38884,38949,39012,39074,39148,39221,39281,39343,39403,39466,39533,39596,39666,39734,39801,39867,39915,39974,40025,40071,40118,40165,40211,40255,40305,40358,40414,40457,41482,41532,41578,41630,41675,41719,41776,41828,41883,41938,41982,42032,42081,42130,42181,42232,42277,42349,42416,42467,42515,42569,42621,42673,42725,42779,42827,42878,42940,43008,43080,43152,43212,43278,43342,43401,43461,43524,43588,43647,43709,43775,43836,43902,43968,44029,44105,44183,44258,44326,44398,44470,44548,44630,44716,44802,44883,44944,45009,45073,45136,45198,45272,45333,45398,45459,45519,45586,45652,45714,45781,45848,45913,45978,46040,46106,46166,46224,46280,46339,46402,46461,46526,46589,46651,46709,46768,46825,46884,46943,47000,47054,47109,47167,47215,47270,47320,47368,47427,47473,47518,47567,47622,47675,47732,47789,47843,47893,47942,47996,48048,48101,48157,48213,48268,48316,48370,48419,48469,48519,48578,48636,48697,48761,48821,48890,48950,49032,49106,49168,49245,49305,49366,49431,49496,49563,49624,49689,49760,49828,49872,49925,49976,50032,50086,50147,50202,50263,50324,50378,50440,50509,50577,50639,50687,50738,50795,50845,50900,50956,51010,51058,51102,51611,51666,51719,51771,51823,51872,51927,51978,52032,52084,52139,52196,52244,52296,52346,52393,52447,52500,52549,52603,52662,52713,52786,52863,52944,53026,53109,53188,53265,53337,53416,53492,53563,53642,53719,53795,53870,53943,54028,54113,54202,54288,54373,54434,54494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d39ef44050431162b870de0e584b2aa2\\transformed\\jetified-ShadowLayout-3.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "201,202,203,496,497,498", "startColumns": "4,4,4,4,4,4", "startOffsets": "9855,9912,9968,27754,27789,27826", "endColumns": "56,55,48,34,36,34", "endOffsets": "9907,9963,10012,27784,27821,27856"}}]}]}