package com.eeo.systemsetting.opscomm;

import android.content.Context;
import android.util.Log;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * JNI版本串口设备管理器
 * 使用JNI直接操作/dev/ttyS2物理串口设备，避免Java层字符编码转换
 * 
 * 主要优势：
 * 1. 绕过Java层的编码转换，确保字节级数据完整性
 * 2. 使用Linux原生termios精确配置串口参数
 * 3. 直接内存操作，避免多层缓冲导致的数据问题
 * 4. 更高的实时性和更低的延迟
 */
public class JniSerialPortManager {
    private static final String TAG = "JniSerialPortManager";
    
    // 加载本地库
    static {
        try {
            System.loadLibrary("serialport");
            Log.d(TAG, "Native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library", e);
        }
    }
    
    // 串口设备路径
    private static final String SERIAL_DEVICE = "/dev/ttyS2";
    
    // 串口参数
    private static final int BAUD_RATE = 9600;
    private static final int DATA_BITS = 8;
    private static final int STOP_BITS = 1;
    private static final int PARITY = 0; // 无校验
    
    // stty命令用于确保串口初始状态正确（作为JNI配置的补充）
    private static final String STTY_RAW_CMD = "stty -F /dev/ttyS2 raw " +
            "-echo -echoe -echok -echonl -echoctl -echoprt -echoke " +
            "-icrnl -inlcr -igncr -ixon -ixoff -ixany " +
            "-ocrnl -onlcr -opost -ofill -olcuc " +
            "-isig -icanon -iexten -brkint -inpck -istrip " +
            "-parenb -parodd cs8 -hupcl -cstopb clocal cread " +
            "min 1 time 0";
    
    private Context mContext;
    private ProtocolHandler mProtocolHandler;
    
    // 本地串口文件描述符
    private int mSerialFd = -1;
    
    // 线程控制
    private Thread mReadThread;
    private final AtomicBoolean mIsRunning = new AtomicBoolean(false);
    private final AtomicBoolean mShouldStop = new AtomicBoolean(false);
    
    // 读取缓冲区大小
    private static final int BUFFER_SIZE = 1024;
    
    // 统计信息
    private long mTotalBytesReceived = 0;
    private long mTotalBytesSent = 0;
    private int mDataPacketCount = 0;

    public JniSerialPortManager(Context context, ProtocolHandler protocolHandler) {
        mContext = context;
        mProtocolHandler = protocolHandler;
    }

    /**
     * 启动JNI串口管理器
     */
    public synchronized boolean start() {
        if (mIsRunning.get()) {
            Log.w(TAG, "JniSerialPortManager is already running");
            return true;
        }

        Log.d(TAG, "Starting JniSerialPortManager...");

        try {
            // 1. TODO: 暂时注释掉，视情况使用stty命令
            // if (!executeSttyCommand()) {
            //     Log.w(TAG, "Warning: stty command failed, continuing with JNI configuration");
            // }

            // 2. 打开串口设备
            if (!openSerialPort()) {
                Log.e(TAG, "Failed to open serial port");
                return false;
            }

            // 3. 配置串口参数（JNI原生配置）
            if (!configureSerialPort()) {
                Log.e(TAG, "Failed to configure serial port");
                closeSerialPort();
                return false;
            }

            // 4. 清空缓冲区
            if (!flushSerialBuffers()) {
                Log.w(TAG, "Warning: Failed to flush serial buffers");
            }

            // 5. 启动读取线程
            if (!startReadThread()) {
                Log.e(TAG, "Failed to start read thread");
                closeSerialPort();
                return false;
            }

            mIsRunning.set(true);
            Log.d(TAG, "JniSerialPortManager started successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Exception while starting JniSerialPortManager", e);
            closeSerialPort();
            return false;
        }
    }

    /**
     * 停止JNI串口管理器
     */
    public synchronized void stop() {
        if (!mIsRunning.get()) {
            Log.w(TAG, "JniSerialPortManager is not running");
            return;
        }

        Log.d(TAG, "Stopping JniSerialPortManager...");

        try {
            // 1. 设置停止标志
            mShouldStop.set(true);

            // 2. 等待读取线程结束
            if (mReadThread != null && mReadThread.isAlive()) {
                try {
                    mReadThread.interrupt();
                    mReadThread.join(3000); // 等待3秒
                } catch (InterruptedException e) {
                    Log.w(TAG, "Interrupted while waiting for read thread to stop");
                    Thread.currentThread().interrupt();
                }
            }

            // 3. 关闭串口设备
            closeSerialPort();

            // 4. 重置状态
            mIsRunning.set(false);
            mShouldStop.set(false);
            mTotalBytesReceived = 0;
            mTotalBytesSent = 0;
            mDataPacketCount = 0;
            
            Log.d(TAG, "JniSerialPortManager stopped successfully");

        } catch (Exception e) {
            Log.e(TAG, "Exception while stopping JniSerialPortManager", e);
        }
    }

    /**
     * 执行stty命令确保串口初始状态正确
     */
    private boolean executeSttyCommand() {
        try {
            Log.d(TAG, "Executing stty command to ensure serial port initial state");
            Process process = Runtime.getRuntime().exec(STTY_RAW_CMD);
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                Log.d(TAG, "stty command executed successfully");
                return true;
            } else {
                Log.w(TAG, "stty command failed with exit code: " + exitCode);
                return false;
            }
        } catch (Exception e) {
            Log.w(TAG, "Exception executing stty command", e);
            return false;
        }
    }

    /**
     * 打开串口设备
     */
    private boolean openSerialPort() {
        try {
            mSerialFd = nativeOpenSerialPort(SERIAL_DEVICE);
            if (mSerialFd < 0) {
                Log.e(TAG, "Failed to open serial port: " + SERIAL_DEVICE);
                return false;
            }
            
            Log.d(TAG, "Serial port opened successfully, fd: " + mSerialFd);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while opening serial port", e);
            return false;
        }
    }

    /**
     * 配置串口参数
     */
    private boolean configureSerialPort() {
        try {
            int result = nativeConfigureSerialPort(mSerialFd, BAUD_RATE, DATA_BITS, STOP_BITS, PARITY);
            if (result != 0) {
                Log.e(TAG, "Failed to configure serial port, error code: " + result);
                return false;
            }
            
            Log.d(TAG, "Serial port configured successfully: " + BAUD_RATE + " 8N1");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while configuring serial port", e);
            return false;
        }
    }

    /**
     * 清空串口缓冲区
     */
    private boolean flushSerialBuffers() {
        try {
            int result = nativeFlushSerialBuffers(mSerialFd);
            if (result != 0) {
                Log.w(TAG, "Failed to flush serial buffers, error code: " + result);
                return false;
            }
            
            Log.d(TAG, "Serial buffers flushed successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while flushing serial buffers", e);
            return false;
        }
    }

    /**
     * 关闭串口设备
     */
    private void closeSerialPort() {
        if (mSerialFd >= 0) {
            try {
                nativeCloseSerialPort(mSerialFd);
                Log.d(TAG, "Serial port closed, fd: " + mSerialFd);
            } catch (Exception e) {
                Log.e(TAG, "Exception while closing serial port", e);
            } finally {
                mSerialFd = -1;
            }
        }
    }

    /**
     * 启动读取线程
     */
    private boolean startReadThread() {
        try {
            mReadThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    readDataLoop();
                }
            }, "JniSerialPortReader");

            mReadThread.start();
            Log.d(TAG, "JNI read thread started successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Exception while starting read thread", e);
            return false;
        }
    }

    /**
     * JNI数据读取循环
     */
    private void readDataLoop() {
        Log.d(TAG, "JNI read thread started");
        
        byte[] buffer = new byte[BUFFER_SIZE];

        while (!mShouldStop.get() && !Thread.currentThread().isInterrupted()) {
            try {
                if (mSerialFd >= 0) {
                    // 使用JNI方法读取数据，返回实际读取的字节数
                    int bytesRead = nativeReadData(mSerialFd, buffer, BUFFER_SIZE);
                    
                    if (bytesRead > 0) {
                        // 创建实际长度的数据副本
                        byte[] receivedData = new byte[bytesRead];
                        System.arraycopy(buffer, 0, receivedData, 0, bytesRead);
                        
                        // 更新统计信息
                        mTotalBytesReceived += bytesRead;
                        mDataPacketCount++;
                        
                        // 处理接收到的数据
                        handleReceivedData(receivedData);
                        
                    } else if (bytesRead == 0) {
                        // 没有数据，短暂休眠
                        Thread.sleep(10);
                    } else {
                        // 读取错误
                        Log.e(TAG, "Error reading data, error code: " + bytesRead);
                        break;
                    }
                } else {
                    Log.e(TAG, "Invalid serial port fd");
                    break;
                }

            } catch (InterruptedException e) {
                Log.d(TAG, "JNI read thread interrupted");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                Log.e(TAG, "Unexpected exception in JNI read loop", e);
                break;
            }
        }

        Log.d(TAG, "JNI read thread finished");
    }

    /**
     * 处理接收到的数据
     */
    private void handleReceivedData(byte[] data) {
        try {
            // String hexString = bytesToHexString(data);
            // Log.d(TAG, "Received data (" + data.length + " bytes): " + hexString);
            
            /* === 详细RAW数据日志（已注释，调试时可启用） ===
            Log.i(TAG, "=== JNI RAW DATA PACKET #" + mDataPacketCount + " ===");
            Log.i(TAG, "Raw bytes received (" + data.length + " bytes): " + hexString);
            
            // 字节完整性验证
            StringBuilder byteAnalysis = new StringBuilder();
            byteAnalysis.append("JNI byte analysis: ");
            boolean hasConversion = false;
            
            for (int i = 0; i < data.length; i++) {
                int byteValue = data[i] & 0xFF;
                byteAnalysis.append(String.format("[%d]=%02X ", i, byteValue));
                
                // 检查是否有字节转换
                if (byteValue == 0xE4 || byteValue == 0xEF) {
                    hasConversion = true;
                    byteAnalysis.append("(CONVERTED) ");
                } else if (byteValue == 0xC4 || byteValue == 0xCF) {
                    byteAnalysis.append("(ORIGINAL) ");
                }
            }
            
            Log.i(TAG, byteAnalysis.toString());
            
            if (hasConversion) {
                Log.w(TAG, "***BYTE CONVERSION DETECTED IN JNI DATA ***");
            } else {
                Log.i(TAG, "***JNI DATA INTEGRITY CONFIRMED ***");
            }
            
            Log.i(TAG, "Total received: " + mTotalBytesReceived + " bytes, " + mDataPacketCount + " packets");
            Log.i(TAG, "=== END JNI RAW DATA PACKET ===");
            === 详细RAW数据日志结束 === */

            // 交给协议处理器处理
            if (mProtocolHandler != null) {
                mProtocolHandler.handleReceivedData(data);
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while handling received data", e);
        }
    }

    /**
     * 发送数据到串口
     */
    public boolean sendData(byte[] data) {
        if (!mIsRunning.get() || mSerialFd < 0) {
            Log.e(TAG, "Cannot send data: JniSerialPortManager not running or invalid fd");
            return false;
        }

        try {
            int bytesWritten = nativeWriteData(mSerialFd, data, data.length);
            
            if (bytesWritten == data.length) {
                mTotalBytesSent += bytesWritten;
                String hexString = bytesToHexString(data);
                Log.d(TAG, "JNI sent data (" + data.length + " bytes): " + hexString);
                return true;
            } else {
                Log.e(TAG, "Failed to send all data. Expected: " + data.length + ", Sent: " + bytesWritten);
                return false;
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while sending data via JNI", e);
            return false;
        }
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("JNI Serial Statistics - Received: %d bytes (%d packets), Sent: %d bytes", 
                mTotalBytesReceived, mDataPacketCount, mTotalBytesSent);
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b & 0xFF));
        }
        return sb.toString().trim();
    }

    /**
     * 检查运行状态
     */
    public boolean isRunning() {
        return mIsRunning.get();
    }

    // ======================== JNI 本地方法声明 ========================
    
    /**
     * 打开串口设备
     * @param devicePath 设备路径
     * @return 文件描述符，失败返回-1
     */
    private native int nativeOpenSerialPort(String devicePath);

    /**
     * 配置串口参数
     * @param fd 文件描述符
     * @param baudRate 波特率
     * @param dataBits 数据位
     * @param stopBits 停止位
     * @param parity 校验位
     * @return 0成功，其他为错误码
     */
    private native int nativeConfigureSerialPort(int fd, int baudRate, int dataBits, int stopBits, int parity);

    /**
     * 清空串口缓冲区
     * @param fd 文件描述符
     * @return 0成功，其他为错误码
     */
    private native int nativeFlushSerialBuffers(int fd);

    /**
     * 读取数据
     * @param fd 文件描述符
     * @param buffer 接收缓冲区
     * @param bufferSize 缓冲区大小
     * @return 实际读取字节数，0表示无数据，负数表示错误
     */
    private native int nativeReadData(int fd, byte[] buffer, int bufferSize);

    /**
     * 写入数据
     * @param fd 文件描述符
     * @param data 要发送的数据
     * @param length 数据长度
     * @return 实际写入字节数，负数表示错误
     */
    private native int nativeWriteData(int fd, byte[] data, int length);

    /**
     * 关闭串口设备
     * @param fd 文件描述符
     */
    private native void nativeCloseSerialPort(int fd);
} 