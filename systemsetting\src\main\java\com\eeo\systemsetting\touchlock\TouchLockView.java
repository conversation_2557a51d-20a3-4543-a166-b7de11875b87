package com.eeo.systemsetting.touchlock;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;


public class TouchLockView extends LinearLayout {
    private Handler mHandler;

    public TouchLockView(Context context) {
        super(context);
    }

    public TouchLockView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TouchLockView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();
        //触控锁显示时，点击才消失
        if (x >= 0 && x <= getWidth() && y >= 0 && y <= getHeight() && getAlpha() == 1) {
            return super.dispatchTouchEvent(event);
        }
        // 在这里处理outside事件
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (getAlpha() == 0) {
                setAlpha(1);
            }
            ObjectAnimator animator = ObjectAnimator.ofFloat(this, "translationX", 0, 20, 0, -20, 0, -20, 0, 20, 0);
            animator.setDuration(200);
            animator.start();
            //2s后隐藏中间的锁
            if (mHandler != null) {
                mHandler.removeMessages(TouchLockManager.MSG_HIDE_TOUCH_LOCK_VIEW);
                mHandler.sendEmptyMessageDelayed(TouchLockManager.MSG_HIDE_TOUCH_LOCK_VIEW, 2000);
            }
        }
        return true;
    }

    public void setHandler(Handler handler) {
        mHandler = handler;
    }
}
