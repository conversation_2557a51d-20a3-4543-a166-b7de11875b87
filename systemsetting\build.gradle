plugins {
    id 'com.android.application'
}

android {
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "com.eeo.systemsetting"
        minSdk rootProject.ext.minSdkVersion
        targetSdk 25
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true // 启用 MultiDex
		
		// 添加JNI支持
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++14"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
	
	// 配置外部原生构建
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.10.2'
        }
    }

    //-------------添加android签名打包 begin -------------
    signingConfigs {
        platform {
            storeFile file("../app_signature/eeo_t982.jks")
            storePassword "android"
            keyAlias "android"
            keyPassword "android"
        }
    }
    buildTypes.each { bt ->
        bt.signingConfig = signingConfigs.platform
    }
    //----------------签名 end-------------------
    // 自定义apk命名
    applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def fileName = "eeo_systemsetting.apk"
                    outputFileName = fileName
            }
    }
}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.android.support:appcompat-v7:' + rootProject.ext.supportV4
    implementation 'com.android.support:recyclerview-v7:' + rootProject.ext.supportV4
    implementation 'com.android.support.constraint:constraint-layout:1.1.2'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.github.lihangleo2:ShadowLayout:3.3.2'
    implementation files('libs/cardview-1.0.1.aar')

    implementation 'com.jakewharton:butterknife:10.0.0'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.0.0'
    implementation project(path: ':commonlibrary')
    api project(':libtouchsdk')

    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.1'

    compileOnly files('../commonlib/framework.jar')
    implementation files('../commonlib/droidlogic.jar')
    implementation files('../commonlib/droidlogic-tv.jar')


    implementation project(path: ':libudisdk')
    implementation files('../commonlib/binderhttpd-0.0.17.aar')
    implementation files('../commonlib/client-sdk-1.0.25.aar')
    implementation files('../commonlib/com.cvte.tvapi-lib.jar')
    implementation 'com.google.code.gson:gson:2.8.2'
    implementation project(':ota')
    implementation 'androidx.multidex:multidex:2.0.1' // 添加 MultiDex 依赖
}