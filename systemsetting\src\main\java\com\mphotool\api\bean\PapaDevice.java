package com.mphotool.api.bean;

import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import java.io.Serializable;

/* loaded from: classes.dex */
public class PapaDevice implements Parcelable, Serializable, Cloneable {
    public static final Parcelable.Creator<PapaDevice> CREATOR = new Parcelable.Creator<PapaDevice>() { // from class: com.mphotool.api.bean.PapaDevice.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public PapaDevice createFromParcel(Parcel parcel) {
            return new PapaDevice(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public PapaDevice[] newArray(int i) {
            return new PapaDevice[i];
        }
    };
    private int copy;
    private long createTime;
    private int deviceCmdPort;
    private long dlnaConnectedTimeStamp;
    private int dlna_type;
    private String ip;
    private String name;
    private int playDeviceType;
    private int status;
    private long tffConnectedTimeStamp;
    private int tff_type;
    private int zk_host;
    private int zk_lock;

    protected PapaDevice(Parcel parcel) {
        this.deviceCmdPort = 0;
        this.playDeviceType = -1;
        this.dlna_type = 0;
        this.tff_type = 0;
        this.ip = parcel.readString();
        this.name = parcel.readString();
        this.deviceCmdPort = parcel.readInt();
        this.playDeviceType = parcel.readInt();
        this.copy = parcel.readInt();
        this.status = parcel.readInt();
        this.tffConnectedTimeStamp = parcel.readLong();
        this.dlnaConnectedTimeStamp = parcel.readLong();
        this.createTime = parcel.readLong();
        this.zk_lock = parcel.readInt();
        this.zk_host = parcel.readInt();
        this.dlna_type = parcel.readInt();
        this.tff_type = parcel.readInt();
    }

    public PapaDevice(String str, String str2, int i, int i2, long j) {
        int i3 = 0;
        this.deviceCmdPort = 0;
        this.playDeviceType = -1;
        this.dlna_type = 0;
        this.tff_type = 0;
        this.ip = str;
        this.name = str2;
        this.createTime = SystemClock.uptimeMillis();
        int i4 = 1;
        if (i2 == 10) {
            this.dlnaConnectedTimeStamp = j;
            this.dlna_type = i2 == 10 ? 1 : i3;
            return;
        }
        this.deviceCmdPort = i;
        this.playDeviceType = i2;
        this.tffConnectedTimeStamp = j;
        this.tff_type = i2 == 7 ? 2 : i4;
    }

    public PapaDevice clone() {
        try {
            return (PapaDevice) super.clone();
        } catch (CloneNotSupportedException unused) {
            return this;
        }
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public int getCopy() {
        return this.copy;
    }

    public long getCreateTime() {
        return this.createTime;
    }

    public int getDeviceCmdPort() {
        return this.deviceCmdPort;
    }

    public long getDlnaConnectedTimeStamp() {
        return this.dlnaConnectedTimeStamp;
    }

    public int getDlna_type() {
        return this.dlna_type;
    }

    public String getIp() {
        return this.ip;
    }

    public String getName() {
        return this.name;
    }

    public int getPlayDeviceType() {
        return this.playDeviceType;
    }

    public int getStatus() {
        return this.status;
    }

    public long getTffConnectedTimeStamp() {
        return this.tffConnectedTimeStamp;
    }

    public int getTff_type() {
        return this.tff_type;
    }

    public int getZk_host() {
        return this.zk_host;
    }

    public int getZk_lock() {
        return this.zk_lock;
    }

    public void setCopy(int i) {
        this.copy = i;
    }

    public void setCreateTime(long j) {
        this.createTime = j;
    }

    public void setDeviceCmdPort(int i) {
        this.deviceCmdPort = i;
    }

    public void setDlnaConnectedTimeStamp(long j) {
        this.dlnaConnectedTimeStamp = j;
    }

    public void setDlna_type(int i) {
        this.dlna_type = i;
    }

    public void setIp(String str) {
        this.ip = str;
    }

    public void setName(String str) {
        this.name = str;
    }

    public void setPlayDeviceType(int i) {
        this.playDeviceType = i;
    }

    public void setStatus(int i) {
        this.status = i;
    }

    public void setTffConnectedTimeStamp(long j) {
        this.tffConnectedTimeStamp = j;
    }

    public void setTff_type(int i) {
        this.tff_type = i;
    }

    public void setZk_host(int i) {
        this.zk_host = i;
    }

    public void setZk_lock(int i) {
        this.zk_lock = i;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.ip);
        parcel.writeString(this.name);
        parcel.writeInt(this.deviceCmdPort);
        parcel.writeInt(this.playDeviceType);
        parcel.writeInt(this.copy);
        parcel.writeInt(this.status);
        parcel.writeLong(this.tffConnectedTimeStamp);
        parcel.writeLong(this.dlnaConnectedTimeStamp);
        parcel.writeLong(this.createTime);
        parcel.writeInt(this.zk_lock);
        parcel.writeInt(this.zk_host);
        parcel.writeInt(this.dlna_type);
        parcel.writeInt(this.tff_type);
    }
}