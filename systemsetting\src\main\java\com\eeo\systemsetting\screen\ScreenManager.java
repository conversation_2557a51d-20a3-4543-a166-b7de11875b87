package com.eeo.systemsetting.screen;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.net.wifi.WifiConfiguration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Switch;
import android.widget.TextView;

import com.cvte.tv.api.TvApiSDKManager;
import com.cvte.tv.api.aidl.ITvNotifyListener;
import com.eeo.systemsetting.EeoApplication;
import com.eeo.systemsetting.R;
import com.eeo.systemsetting.bean.AvailableSourceBean;
import com.eeo.systemsetting.touchlock.TouchLockManager;
import com.eeo.systemsetting.utils.CommonUtils;
import com.eeo.systemsetting.utils.Constant;
import com.eeo.systemsetting.utils.HotspotUtil;
import com.eeo.systemsetting.utils.SaveDateUtils;
import com.eeo.systemsetting.view.ViewGroupWrapper;
import com.eeo.udisdk.UdiConstant;
import com.google.gson.Gson;
import com.zyp.cardview.YcCardView;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * 投屏引导页相关
 */
public class ScreenManager implements View.OnClickListener {
    private static final String TAG = "ScreenManager";
    private static final boolean SHOW_PIN_CODE = false;
    private static ScreenManager mScreenManager = null;
    private Context mContext;
    private WindowManager mWindowManager;
    private HotspotUtil mHotspotUtil;

    private View mMultiScreenView;
    private ImageView mBackIv;

    /**
     * 无线投屏
     */
    private YcCardView mWirelessScreenCardView;
    private RelativeLayout mWirelessScreenSubLayout;
    private TextView mWirelessScreenEnableTv;
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private Switch mWirelessScreenEnableSwitch;
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private Switch mWirelessScreenEnablePinCodeSwitch;
    private TextView mStep2Tv;
    private TextView mStep3Tv;
    private TextView mPinCodeTv;
    private ViewGroupWrapper mWirelessScreenViewGroupWrapper;

    /**
     * 有线投屏
     */
    private YcCardView mWiredScreenCardView;
    private RelativeLayout mWiredScreenSubLayout;
    private FrameLayout mWindowsLayout;
    private TextView mWindowsTv;
    private FrameLayout mTypeCLayout;
    private FrameLayout mBehindHdmi1Layout;
    private FrameLayout mBehindHdmi2Layout;
    private ViewGroupWrapper mWiredScreenViewGroupWrapper;

    private boolean mIsWirelessScreenEnabled;
    private boolean mIsPinCodeEnabled;

    /**
     * 当前信号和可用信号列表
     * 由于快速显示有线投屏界面
     */
    private List<AvailableSourceBean.SourcesBean> mAvailableSourceList = null;

    /**
     * 用来轮询是否在飞图投屏中
     */
    private Timer mTimer;
    /**
     * 全局TVAPI回调监听器
     */
    private ITvNotifyListener mTvNotifyListener = null;

    private PinCodeReceiver mPinCodeReceiver;

    private String mCurrentSource;

    private int mRetryTimes = 6;
    private static final int MSG_ENABLE_PIN_CODE = 0x001;
    private static final int MSG_DISABLE_PIN_CODE = 0x002;

    @SuppressLint("HandlerLeak")
    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_ENABLE_PIN_CODE:
                    if (mIsWirelessScreenEnabled && mRetryTimes > 0) {
                        mRetryTimes--;
                        CommonUtils.enablePinCode(mContext, true);
                        removeMessages(MSG_ENABLE_PIN_CODE);
                        sendEmptyMessageDelayed(MSG_ENABLE_PIN_CODE, 1000);
                    } else {
                        mRetryTimes = 6;
                    }
                    break;
                case MSG_DISABLE_PIN_CODE:
                    if (mIsWirelessScreenEnabled && mRetryTimes > 0) {
                        mRetryTimes--;
                        CommonUtils.enablePinCode(mContext, false);
                        removeMessages(MSG_DISABLE_PIN_CODE);
                        sendEmptyMessageDelayed(MSG_DISABLE_PIN_CODE, 1000);
                    } else {
                        mRetryTimes = 6;
                    }
                    break;
            }
        }
    };

    private ScreenManager(Context context) {
        mContext = context;
    }

    public static ScreenManager getInstance(Context context) {
        if (mScreenManager == null) {
            mScreenManager = new ScreenManager(context);
            CommonUtils.updateDensity(context);
        }
        return mScreenManager;
    }


    /**
     * 投屏提示dialog
     */
    public void showScreenDialog(List<AvailableSourceBean.SourcesBean> availableSourceList) {
        mIsWirelessScreenEnabled = CommonUtils.isWirelessScreenEnabled(mContext);
        mIsPinCodeEnabled = SaveDateUtils.isWirelessScreenPinCodeEnabled(mContext);
        if (mIsWirelessScreenEnabled) {
            CommonUtils.requestPinCode(mContext);
        }
        mAvailableSourceList = availableSourceList;
        if (mMultiScreenView == null) {
            mMultiScreenView = LayoutInflater.from(mContext).inflate(R.layout.screen_dialog, null);
            mMultiScreenView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {

                    if (motionEvent.getAction() == MotionEvent.ACTION_OUTSIDE) {
                        Log.i(TAG, "outside");
                        dismissScreenDialog();
                    }
                    return false;
                }
            });
            mBackIv = (ImageView) mMultiScreenView.findViewById(R.id.iv_back);
            mBackIv.setOnClickListener(this);
            //有线投屏
            mWiredScreenCardView = (YcCardView) mMultiScreenView.findViewById(R.id.cv_wired_screen);
            mWiredScreenCardView.setOnClickListener(this);
            mWiredScreenCardView.setClickable(true);
            mWiredScreenViewGroupWrapper = new ViewGroupWrapper(mWiredScreenCardView);
            mWiredScreenSubLayout = (RelativeLayout) mMultiScreenView.findViewById(R.id.rl_sub_wired_screen);
            initWiredScreenSubLayout();

            //无线投屏
            mWirelessScreenCardView = (YcCardView) mMultiScreenView.findViewById(R.id.cv_wireless_screen);
            mWirelessScreenCardView.setOnClickListener(this);
            mWirelessScreenCardView.setClickable(false);
            mWirelessScreenViewGroupWrapper = new ViewGroupWrapper(mWirelessScreenCardView);
            mWirelessScreenSubLayout = (RelativeLayout) mMultiScreenView.findViewById(R.id.rl_sub_wireless_screen);
            mWirelessScreenEnableTv = (TextView) mMultiScreenView.findViewById(R.id.tv_enable_wireless_screen);
            mWirelessScreenEnableSwitch = (Switch) mMultiScreenView.findViewById(R.id.sw_enable_wireless_screen);
            mWirelessScreenEnableSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (mIsWirelessScreenEnabled != isChecked) {
                        mIsWirelessScreenEnabled = isChecked;
                        handleWirelessScreenEnableCheckedChanged(isChecked);
                    }
                }
            });
            mWirelessScreenEnableSwitch.setChecked(mIsWirelessScreenEnabled);
            mWirelessScreenEnablePinCodeSwitch = (Switch) mMultiScreenView.findViewById(R.id.sw_enable_pin_code);
            mWirelessScreenEnablePinCodeSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (mIsPinCodeEnabled != isChecked) {
                        mIsPinCodeEnabled = isChecked;
                        CommonUtils.enablePinCode(mContext, isChecked);
                        SaveDateUtils.enableWirelessScreenPinCode(mContext, isChecked);
                        mRetryTimes = 6;
                        mHandler.removeMessages(MSG_ENABLE_PIN_CODE);
                        mHandler.removeMessages(MSG_DISABLE_PIN_CODE);
                        mHandler.sendEmptyMessageDelayed(isChecked ? MSG_ENABLE_PIN_CODE : MSG_DISABLE_PIN_CODE, 1000);
                    }
                }
            });
            mWirelessScreenEnablePinCodeSwitch.setChecked(mIsPinCodeEnabled);
            mStep2Tv = (TextView) mMultiScreenView.findViewById(R.id.tv_step_2);
            mStep3Tv = (TextView) mMultiScreenView.findViewById(R.id.tv_step_3);
            mPinCodeTv = (TextView) mMultiScreenView.findViewById(R.id.tv_pin_code);
            if (mIsWirelessScreenEnabled) {
                updateHotspotView();
            } else {
                mWirelessScreenSubLayout.setVisibility(View.GONE);
            }
        }

        if (!mMultiScreenView.isAttachedToWindow()) {
            if (mWindowManager == null) {
                mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
            }
            WindowManager.LayoutParams viewParam = new WindowManager.LayoutParams();
            viewParam.width = CommonUtils.dp2px(mContext, Constant.DIALOG_WIDTH_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.height = CommonUtils.dp2px(mContext, Constant.DIALOG_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2);
            viewParam.gravity = Gravity.END | Gravity.BOTTOM;
            viewParam.x = CommonUtils.dp2px(mContext, Constant.DIALOG_MARIN_END_IN_DP);
            viewParam.y = CommonUtils.dp2px(mContext, Constant.DIALOG_MARIN_BOTTOM_IN_DP);
            viewParam.format = PixelFormat.RGBA_8888;
            viewParam.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            viewParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;

            mWindowManager.addView(mMultiScreenView, viewParam);
        }

        if (!CommonUtils.isMultiScreen(mContext)) {
            if (mTimer == null) {
                mTimer = new Timer();
            }
            mTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    //投屏时
                    if (CommonUtils.isMultiScreen(mContext)) {
                        Log.d(TAG, "run: find MultiScreenActivity");
                        dismissScreenDialog();
                    }
                }
            }, 0, 100);
        }
        registerTvNotifyListener();
        registerPinCodeReceiver();
        EeoApplication.isShowScreenDialog = true;
        CommonUtils.enableOsd(mContext, true);
    }

    private void updateHotspotView() {
        WifiConfiguration wifiConfiguration = CommonUtils.getWifiApConfiguration(mContext);
        if (wifiConfiguration != null) {
            mStep2Tv.setText(String.format(mContext.getString(R.string.screen_step_2), wifiConfiguration.SSID, wifiConfiguration.preSharedKey));
        }
        mStep3Tv.setText(String.format(mContext.getString(R.string.screen_step_3), CommonUtils.getDeviceName()));
    }

    private void handleWirelessScreenEnableCheckedChanged(boolean isChecked) {
        if (mHotspotUtil == null) {
            mHotspotUtil = new HotspotUtil(mContext);
        }
        CommonUtils.enableWirelessScreen(mContext, isChecked);
        if (isChecked) {
            ScreenUtil.enableWirelessScreen(mContext);
            mHotspotUtil.checkAndStartHotspot();
            mWirelessScreenSubLayout.setVisibility(View.VISIBLE);
            updateHotspotView();
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    //关闭投屏授权开关
                    CommonUtils.enableMirrorPermission(mContext, false);
                }
            }, 1000);
        } else {
            if (CommonUtils.isMultiScreen(mContext)) {
                //移除触控锁
                if (!EeoApplication.isLockBeforeMirror) {
                    EeoApplication.isLock = false;
                    TouchLockManager.getInstance(mContext).dismissTouchLockView();
                }
            }
            ScreenUtil.disableWirelessScreen(mContext);
            mHotspotUtil.stopSoftAp();
            mWirelessScreenSubLayout.setVisibility(View.GONE);
            mHandler.removeMessages(MSG_ENABLE_PIN_CODE);
            mHandler.removeMessages(MSG_DISABLE_PIN_CODE);
        }
    }

    @SuppressLint("CheckResult")
    private void initWiredScreenSubLayout() {
        mWindowsLayout = mWiredScreenSubLayout.findViewById(R.id.fl_windows);
        mWindowsLayout.setOnClickListener(this);
        mWindowsTv = mWiredScreenSubLayout.findViewById(R.id.txt_windows);
        enableWindows(false);
        mTypeCLayout = mWiredScreenSubLayout.findViewById(R.id.fl_typec);
        mTypeCLayout.setOnClickListener(this);
        enableItem(mTypeCLayout, false);
        mBehindHdmi1Layout = mWiredScreenSubLayout.findViewById(R.id.fl_behind_hdmi1);
        mBehindHdmi1Layout.setOnClickListener(this);
        enableItem(mBehindHdmi1Layout, false);
        mBehindHdmi2Layout = mWiredScreenSubLayout.findViewById(R.id.fl_behind_hdmi2);
        mBehindHdmi2Layout.setOnClickListener(this);
        enableItem(mBehindHdmi2Layout, false);
        if (mAvailableSourceList != null && mAvailableSourceList.size() > 0) {
            updateAvailableSourceView(mAvailableSourceList);
        }
        getAvailableSource();
        if (EeoApplication.mCurrentSource != null) {
            getCurrentSource(EeoApplication.mCurrentSource);
        } else {
            //获取当前信号
            Observable.create(new ObservableOnSubscribe<String>() {
                        @Override
                        public void subscribe(ObservableEmitter<String> emitter) throws Exception {
                            String currentSource = EeoApplication.udi.getCurrentSource();
                            emitter.onNext(currentSource);
                        }
                    }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<String>() {
                        @Override
                        public void accept(String s) throws Exception {
                            getCurrentSource(s);
                        }
                    });
        }
    }

    public void dismissScreenDialog() {
        if (mWindowManager != null && mMultiScreenView != null && mMultiScreenView.isAttachedToWindow()) {
            mWindowManager.removeView(mMultiScreenView);
            mMultiScreenView = null;
            if (mTimer != null) {
                mTimer.cancel();
                mTimer = null;
            }
            unregisterTvNotifyListener();
            if (mHandler.hasMessages(MSG_ENABLE_PIN_CODE) || mHandler.hasMessages(MSG_DISABLE_PIN_CODE)) {
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        unregisterPinCodeReceiver();
                    }
                }, 3000);
            } else {
                unregisterPinCodeReceiver();
            }
            EeoApplication.isShowScreenDialog = false;
            CommonUtils.enableOsd(mContext, false);
        }
    }

    @Override
    public void onClick(View v) {
        if (CommonUtils.isFastClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.iv_back:
                dismissScreenDialog();
                CommonUtils.startMainActivity(mContext);
                break;
            case R.id.cv_wired_screen:
                changeToWiredScreen();
                break;
            case R.id.cv_wireless_screen:
                changeToWirelessScreen();
                break;
            case R.id.fl_windows:
                handleChangeSource(UdiConstant.SOURCE_PC);
                break;

            case R.id.fl_typec:
                handleChangeSource(Constant.SOURCE_TYPE_C);
                break;

            case R.id.fl_behind_hdmi1:
                handleChangeSource(UdiConstant.SOURCE_HDMI1);
                break;

            case R.id.fl_behind_hdmi2:
                handleChangeSource(UdiConstant.SOURCE_HDMI2);
                break;
            default:
                break;
        }
    }

    public void handleChangeSource(String newSource) {
        //点击当前通道也显示信源分辨率弹窗
        if (newSource.equals(mCurrentSource)) {
            CommonUtils.sendBroadcastToShowResolution(mContext);
        }
        EeoApplication.udi.changeSource(newSource);
        if (CommonUtils.isMultiScreen(mContext)) {
            CommonUtils.finishMultiScreen(mContext);
            dismissScreenDialog();
        }
    }

    /**
     * 展开无线投屏
     */
    private void changeToWirelessScreen() {
        //展开无线投屏
        mWirelessScreenCardView.setClickable(false);
        ObjectAnimator wirelessScreenUnfoldAnimator = ObjectAnimator.ofInt(mWirelessScreenViewGroupWrapper, "trueHeight",
                CommonUtils.dp2px(mContext, Constant.DIALOG_WIRELESS_SCREEN_UNFOLD_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2));
        wirelessScreenUnfoldAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mWirelessScreenEnableTv.setVisibility(View.VISIBLE);
                mWirelessScreenEnableSwitch.setVisibility(View.VISIBLE);
                mWirelessScreenSubLayout.setVisibility(mIsWirelessScreenEnabled ? View.VISIBLE : View.GONE);
                mWiredScreenCardView.setClickable(true);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        wirelessScreenUnfoldAnimator.setDuration(300).start();

        //收齐有线投屏
        mWiredScreenSubLayout.setVisibility(View.GONE);
        ObjectAnimator.ofInt(mWiredScreenViewGroupWrapper, "trueHeight",
                CommonUtils.dp2px(mContext, Constant.DIALOG_WIRELESS_SCREEN_FOLD_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2)).setDuration(300).start();
    }

    /**
     * 展开有线投屏
     */
    private void changeToWiredScreen() {
        //收起无线投屏
        mWirelessScreenEnableTv.setVisibility(View.GONE);
        mWirelessScreenEnableSwitch.setVisibility(View.GONE);
        mWirelessScreenSubLayout.setVisibility(View.GONE);
        ObjectAnimator.ofInt(mWirelessScreenViewGroupWrapper, "trueHeight",
                CommonUtils.dp2px(mContext, Constant.DIALOG_WIRELESS_SCREEN_FOLD_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2)).setDuration(300).start();

        //展开有线投屏
        mWiredScreenCardView.setClickable(false);
        ObjectAnimator wiredScreenUnfoldAnimator = ObjectAnimator.ofInt(mWiredScreenViewGroupWrapper, "trueHeight",
                CommonUtils.dp2px(mContext, Constant.DIALOG_WIRELESS_SCREEN_UNFOLD_HEIGHT_IN_DP + Constant.SHADOW_WIDTH_IN_DP * 2));
        wiredScreenUnfoldAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mWiredScreenSubLayout.setVisibility(View.VISIBLE);
                mWirelessScreenCardView.setClickable(true);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        wiredScreenUnfoldAnimator.setDuration(300).start();
    }

    private void updateAvailableSourceView(List<AvailableSourceBean.SourcesBean> sources) {
        for (int i = 0; i < sources.size(); i++) {
            if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_PC)) {
                enableWindows(sources.get(i).isHasSignal());
            }

            if (sources.get(i).getSourceItem().equals(Constant.SOURCE_TYPE_C)) {
                enableItem(mTypeCLayout, sources.get(i).isHasSignal());
            }

            if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_HDMI1)) {
                enableItem(mBehindHdmi1Layout, sources.get(i).isHasSignal());
            }

            if (sources.get(i).getSourceItem().equals(UdiConstant.SOURCE_HDMI2)) {
                enableItem(mBehindHdmi2Layout, sources.get(i).isHasSignal());
            }
        }
    }

    /**
     * 获取当前信号
     */
    private void getCurrentSource(String currentSource) {
        Log.i(TAG, "ScreenManager: currentSource : " + currentSource);
        mCurrentSource = currentSource;
        switch (currentSource) {
            case UdiConstant.SOURCE_PC:
                mWindowsLayout.setSelected(true);
                break;

            case UdiConstant.SOURCE_TYPE_C1:
            case UdiConstant.SOURCE_TYPE_C2:
                mTypeCLayout.setSelected(true);
                break;

            case UdiConstant.SOURCE_HDMI1:
                mBehindHdmi1Layout.setSelected(true);
                break;

            case UdiConstant.SOURCE_HDMI2:
                mBehindHdmi2Layout.setSelected(true);
                break;
        }
        mWiredScreenSubLayout.setVisibility(View.VISIBLE);
    }

    private void enableItem(FrameLayout item, boolean enable) {
        if (item == null) {
            return;
        }
        if (enable) {
//            item.setEnabled(true);
            item.setAlpha(1.0f);
        } else {
//            item.setEnabled(false);
            item.setAlpha(0.3f);
        }
    }

    /**
     * 由于加了禁用内置电脑功能
     * 内置电脑通道item单独处理
     */
    private void enableWindows(boolean enable) {
        if (mWindowsLayout == null) {
            return;
        }
        if (CommonUtils.isOpsDisable()) {
            mWindowsTv.setText(mContext.getString(R.string.windows_disabled));
            mWindowsLayout.setAlpha(0.3f);
            mWindowsLayout.setClickable(false);
        } else {
            if (enable) {
//            item.setEnabled(true);
                mWindowsLayout.setAlpha(1.0f);
            } else {
//            item.setEnabled(false);
                mWindowsLayout.setAlpha(0.3f);
            }
            mWindowsTv.setText(mContext.getString(R.string.windows));
            mWindowsLayout.setClickable(true);
        }
    }

    private void getAvailableSource() {
        //获取可用信号列表
        Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                        String availableSource = EeoApplication.udi.getAvailableSource();
                        if (availableSource != null) {
                            Gson gson = new Gson();
                            AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                            List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                            emitter.onNext(sources);
                        }
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
                    @Override
                    public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                        updateAvailableSourceView(sourcesBeans);
                    }
                });
    }

    private void registerTvNotifyListener() {
        mTvNotifyListener = new ITvNotifyListener() {
            @Override
            public void onTvNotify(String action, Bundle bundle) {
                Log.d(TAG, "onTvNotify:" + action);
                if ("notifySystemSignalChange".equals(action)) {
                    //信号源变化
                    dismissScreenDialog();
                } else if ("notifySystemInputSourcePlugInOutStatus".equals(action)) {
                    //有插拔状态变化
                    //获取当前信号哪些可用
                    Observable.create(new ObservableOnSubscribe<List<AvailableSourceBean.SourcesBean>>() {
                                @Override
                                public void subscribe(ObservableEmitter<List<AvailableSourceBean.SourcesBean>> emitter) throws Exception {
                                    String availableSource = EeoApplication.udi.getAvailableSource();
                                    if (availableSource != null) {
                                        Gson gson = new Gson();
                                        AvailableSourceBean availableSourceBean = gson.fromJson(availableSource, AvailableSourceBean.class);
                                        List<AvailableSourceBean.SourcesBean> sources = availableSourceBean.getSources();
                                        emitter.onNext(sources);
                                    }
                                }
                            }).subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Consumer<List<AvailableSourceBean.SourcesBean>>() {
                                @Override
                                public void accept(List<AvailableSourceBean.SourcesBean> sourcesBeans) throws Exception {
                                    mAvailableSourceList.clear();
                                    mAvailableSourceList.addAll(sourcesBeans);
                                    updateAvailableSourceView(mAvailableSourceList);
                                }
                            });
                }
            }
        };
        TvApiSDKManager.getInstance().addNotifyHandle(mTvNotifyListener);
    }

    private void unregisterTvNotifyListener() {
        if (mTvNotifyListener != null) {
            TvApiSDKManager.getInstance().removeNotifyHandle(mTvNotifyListener);
            mTvNotifyListener = null;
        }
    }

    private void registerPinCodeReceiver() {
        mPinCodeReceiver = new PinCodeReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constant.ACTION_MULTI_SCREEN_PINCODE_RESPONE);
        intentFilter.addAction(Constant.ACTION_MULTI_SCREEN_MIRROR_PERMISSION_RESPONSE);
        mContext.getApplicationContext().registerReceiver(mPinCodeReceiver, intentFilter);
        CommonUtils.requestPinCode(mContext); //发送广播请求pinCode
    }

    private void unregisterPinCodeReceiver() {
        if (mPinCodeReceiver != null) {
            mContext.getApplicationContext().unregisterReceiver(mPinCodeReceiver);
            mPinCodeReceiver = null;
        }
    }

    public class PinCodeReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onReceive: " + intent.getAction() + ",pkg=" + intent.getPackage());
            if (Constant.ACTION_MULTI_SCREEN_PINCODE_RESPONE.equals(intent.getAction())) {
                //“status”: pincode是否开启：0-未开启，1-已开启
                // ”pin_code“: pincode
                int status = intent.getIntExtra("status", 0);
                String pinCode = intent.getStringExtra("pin_code");
                Log.d(TAG, " status=" + status + ",pin_code=" + pinCode);
                if (SHOW_PIN_CODE) {
                    if (status == 0) {
                        if (mPinCodeTv != null) {
                            mPinCodeTv.setVisibility(View.GONE);
                        }
                    } else if (status == 1) {
                        if (mPinCodeTv != null) {
                            mPinCodeTv.setVisibility(View.VISIBLE);
                            mPinCodeTv.setText(String.format(mContext.getString(R.string.screen_pin_code), pinCode));
                        }
                    }
                }
                mHandler.removeMessages(MSG_ENABLE_PIN_CODE);
                mHandler.removeMessages(MSG_DISABLE_PIN_CODE);
                if (status == 0) {
                    if (mIsPinCodeEnabled) {
                        mHandler.sendEmptyMessage(MSG_ENABLE_PIN_CODE);
                    }
                } else if (status == 1) {
                    if (!mIsPinCodeEnabled) {
                        mHandler.sendEmptyMessage(MSG_DISABLE_PIN_CODE);
                    }
                }
//                mIsPinCodeEnabled = status == 1;
            } else if (Constant.ACTION_MULTI_SCREEN_MIRROR_PERMISSION_RESPONSE.equals(intent.getAction())) {
                //“status”: 投屏授权是否开启：0-未开启，1-已开启
                int status = intent.getIntExtra("status", 0);
                Log.d(TAG, " status=" + status);
            }
        }
    }
}
