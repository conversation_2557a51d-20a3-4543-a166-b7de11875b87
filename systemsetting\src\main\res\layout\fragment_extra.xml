<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_wireless_screen_margin_top">

        <TextView
            style="@style/Extra_TextView"
            android:text="@string/write_without_screen_on" />

        <Switch
            android:id="@+id/sw_write_without_screen_on"
            style="@style/Extra_Switch"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top">

        <TextView
            style="@style/Extra_TextView"
            android:text="@string/breath_led_on" />

        <Switch
            android:id="@+id/sw_breath_led_on"
            style="@style/Extra_Switch"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_touch_slider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top">

        <TextView
            style="@style/Extra_TextView"
            android:text="@string/touch_slider" />

        <Switch
            android:id="@+id/sw_touch_slider"
            style="@style/Extra_Switch"
            android:checked="true"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_windows_disable"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top">

        <TextView
            style="@style/Extra_TextView"
            android:text="@string/windows_disable" />

        <Switch
            android:id="@+id/sw_windows_disable"
            style="@style/Extra_Switch"
            android:checked="false"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top">

        <TextView
            android:id="@+id/tv_startup_channel"
            style="@style/Extra_TextView"
            android:text="@string/startup_channel" />

        <Spinner
            android:id="@+id/spinner_startup_channel"
            android:layout_width="@dimen/fragment_locale_spinner_width"
            android:layout_height="@dimen/fragment_locale_tv_language_height"
            android:layout_alignTop="@id/tv_startup_channel"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/fragment_locale_iv_language_margin_end"
            android:background="@null"
            android:dropDownVerticalOffset="25dp"
            android:gravity="center_vertical|end"
            android:popupBackground="@drawable/shape_network_auto_manual"
            android:popupElevation="@dimen/dp_0" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_hardware_self_test"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_hardware_self_test"
            style="@style/Extra_TextView"
            android:text="@string/hardware_self_test" />

        <ImageView
            android:id="@+id/img_window"
            android:layout_width="@dimen/adb_iv_reset_width"
            android:layout_height="@dimen/adb_iv_reset_height"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/fragment_locale_iv_language_margin_end"
            android:background="@drawable/ic_arrow_right"
            android:clickable="false" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_windows_task_manager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_extra_item_margin_top">

        <TextView
            android:id="@+id/tv_windows_task_manager"
            style="@style/Extra_TextView"
            android:text="@string/windows_task_manager" />

        <ImageView
            android:layout_width="@dimen/adb_iv_reset_width"
            android:layout_height="@dimen/adb_iv_reset_height"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/fragment_extra_sw_margin_end"
            android:background="@drawable/ic_arrow_right"
            android:clickable="false" />
    </RelativeLayout>

</LinearLayout>