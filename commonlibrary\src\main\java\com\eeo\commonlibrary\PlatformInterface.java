package com.eeo.commonlibrary;

import android.content.Context;

import com.eeo.commonlibrary.eeointerface.PlatformBeanInterface;
import com.eeo.commonlibrary.eyeconfort.EyeConfortInterface;
import com.eeo.commonlibrary.eyeconfort.EyeConfortInterfaceImpl;
import com.eeo.commonlibrary.hdmi.HdmiInterface;
import com.eeo.commonlibrary.hdmi.HdmiInterfaceImpl;
import com.eeo.commonlibrary.ops.OpsInterface;
import com.eeo.commonlibrary.ops.OpsInterfaceImpl;
import com.eeo.commonlibrary.screen.ScreenInterface;
import com.eeo.commonlibrary.screen.ScreenInterfaceImpl;
import com.eeo.commonlibrary.sound.SoundInterface;
import com.eeo.commonlibrary.sound.SoundInterfaceImpl;
import com.eeo.commonlibrary.source.SourceInterface;
import com.eeo.commonlibrary.source.SourceInterfaceImpl;

public class PlatformInterface implements PlatformBeanInterface {

    private Context mContext;

    private SourceInterfaceImpl sourceInterfaceImpl;
    private SoundInterfaceImpl soundInterfaceImpl;
    private HdmiInterfaceImpl hdmiInterfaceImpl;
    private EyeConfortInterfaceImpl eyeConfortInterfaceImpl;
    private OpsInterfaceImpl opsInterfaceImpl;
    private ScreenInterfaceImpl screenInterfaceImpl;

    private static PlatformInterface platformInterface = null;


    private PlatformInterface(Context context) {
        mContext = context.getApplicationContext();

        if (sourceInterfaceImpl == null) {
            sourceInterfaceImpl = new SourceInterfaceImpl();
        }

        if (soundInterfaceImpl == null) {
            soundInterfaceImpl = new SoundInterfaceImpl();
        }

        if (hdmiInterfaceImpl == null) {
            hdmiInterfaceImpl = new HdmiInterfaceImpl();
        }

        if (eyeConfortInterfaceImpl == null) {
            eyeConfortInterfaceImpl = new EyeConfortInterfaceImpl();
        }

        if (opsInterfaceImpl == null) {
            opsInterfaceImpl = new OpsInterfaceImpl();
        }

        if (screenInterfaceImpl == null) {
            screenInterfaceImpl = new ScreenInterfaceImpl();
        }

    }

    public static PlatformInterface getInstance(Context context) {
        if (platformInterface == null) {
            synchronized (PlatformInterface.class) {
                if (platformInterface == null) {
                    platformInterface = new PlatformInterface(context);
                }
            }
        }

        return platformInterface;
    }


    @Override
    public SourceInterface getSourceInterfaceImpl() {
        return sourceInterfaceImpl;
    }

    @Override
    public SoundInterface getSoundInterfaceImpl() {
        return soundInterfaceImpl;
    }

    @Override
    public HdmiInterface getHdmiInterfaceImpl() {
        return hdmiInterfaceImpl;
    }

    @Override
    public EyeConfortInterface getEyeConfortInterfaceImpl() {
        return eyeConfortInterfaceImpl;
    }

    @Override
    public OpsInterface getOpsInterfaceImpl() {
        return opsInterfaceImpl;
    }

    @Override
    public ScreenInterface getScreenImpl() {
        return screenInterfaceImpl;
    }
}
